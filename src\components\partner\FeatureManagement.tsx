import React, { useState, useEffect } from 'react';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Trash2, Save, AlertCircle } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { supabase } from '@/lib/supabase';

// Feature type
interface Feature {
  id: string;
  feature_id: string;
  display_name: string;
  description: string;
  default_credit_cost: number;
  metric_type: 'time' | 'api_calls' | 'resources' | 'custom';
}

// New feature form type
interface FeatureForm {
  feature_id: string;
  display_name: string;
  description: string;
  default_credit_cost: number;
  metric_type: 'time' | 'api_calls' | 'resources' | 'custom';
}

interface FeatureManagementProps {
  toolId?: string;
}

const FeatureManagement: React.FC<FeatureManagementProps> = ({ toolId }) => {
  const { partner } = usePartner();
  const [features, setFeatures] = useState<Feature[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // New feature form
  const [newFeature, setNewFeature] = useState<FeatureForm>({
    feature_id: '',
    display_name: '',
    description: '',
    default_credit_cost: 1,
    metric_type: 'custom'
  });

  // Load features
  useEffect(() => {
    if (partner?.id) {
      loadFeatures();
    }
  }, [partner, toolId]);

  // Load features from the database
  const loadFeatures = async () => {
    setIsLoading(true);

    try {
      let query = supabase
        .from('partner_features')
        .select('*')
        .eq('partner_id', partner.id);

      // If toolId is provided, filter by tool_id
      if (toolId) {
        query = query.eq('tool_id', toolId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setFeatures(data || []);
    } catch (error: any) {
      console.error('Error loading features:', error);
      notify.error('Failed to load features', {
        description: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new feature
  const handleAddFeature = async () => {
    // Validate form
    if (!newFeature.feature_id || !newFeature.display_name) {
      notify.error('Missing required fields', {
        description: 'Feature ID and Display Name are required'
      });
      return;
    }

    setIsSaving(true);

    try {
      // Insert the new feature
      const { data, error } = await supabase
        .from('partner_features')
        .insert({
          partner_id: partner.id,
          tool_id: toolId,
          feature_id: newFeature.feature_id,
          display_name: newFeature.display_name,
          description: newFeature.description,
          default_credit_cost: newFeature.default_credit_cost,
          metric_type: newFeature.metric_type
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Add the new feature to the list
      setFeatures([data, ...features]);

      // Reset the form
      setNewFeature({
        feature_id: '',
        display_name: '',
        description: '',
        default_credit_cost: 1,
        metric_type: 'custom'
      });

      // Hide the form
      setIsAdding(false);

      notify.success('Feature added successfully');
    } catch (error: any) {
      console.error('Error adding feature:', error);
      notify.error('Failed to add feature', {
        description: error.message
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Delete a feature
  const handleDeleteFeature = async (id: string) => {
    if (!confirm('Are you sure you want to delete this feature?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('partner_features')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      // Remove the feature from the list
      setFeatures(features.filter(feature => feature.id !== id));

      notify.success('Feature deleted successfully');
    } catch (error: any) {
      console.error('Error deleting feature:', error);
      notify.error('Failed to delete feature', {
        description: error.message
      });
    }
  };

  return (
    <Card className="firenest-card">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Metered Features</CardTitle>
            <CardDescription>Define the premium features that will consume FireNest credits</CardDescription>
          </div>
          <Button
            onClick={() => setIsAdding(!isAdding)}
            className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
          >
            <PlusCircle className="w-4 h-4 mr-2" />
            Add Feature
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {isAdding && (
          <div className="mb-8 p-4 border border-white/10 rounded-lg bg-dark-800">
            <h3 className="text-lg font-medium mb-4">New Feature</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <Label htmlFor="feature_id">Feature ID</Label>
                <Input
                  id="feature_id"
                  placeholder="e.g., export-csv-pro"
                  value={newFeature.feature_id}
                  onChange={(e) => setNewFeature({ ...newFeature, feature_id: e.target.value })}
                  className="mt-1"
                />
                <p className="text-xs text-white/60 mt-1">
                  Unique, machine-readable identifier used in SDK calls
                </p>
              </div>

              <div>
                <Label htmlFor="display_name">Display Name</Label>
                <Input
                  id="display_name"
                  placeholder="e.g., Pro CSV Export"
                  value={newFeature.display_name}
                  onChange={(e) => setNewFeature({ ...newFeature, display_name: e.target.value })}
                  className="mt-1"
                />
                <p className="text-xs text-white/60 mt-1">
                  Human-readable name shown to users
                </p>
              </div>
            </div>

            <div className="mb-4">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Brief explanation of the feature"
                value={newFeature.description}
                onChange={(e) => setNewFeature({ ...newFeature, description: e.target.value })}
                className="mt-1"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <Label htmlFor="default_credit_cost">Default Credit Cost</Label>
                <Input
                  id="default_credit_cost"
                  type="number"
                  min="1"
                  value={newFeature.default_credit_cost}
                  onChange={(e) => setNewFeature({ ...newFeature, default_credit_cost: parseInt(e.target.value) || 1 })}
                  className="mt-1"
                />
                <p className="text-xs text-white/60 mt-1">
                  Number of credits consumed per usage by default
                </p>
              </div>

              <div>
                <Label htmlFor="metric_type">Metric Type</Label>
                <Select
                  value={newFeature.metric_type}
                  onValueChange={(value) => setNewFeature({ ...newFeature, metric_type: value as any })}
                >
                  <SelectTrigger id="metric_type" className="mt-1">
                    <SelectValue placeholder="Select metric type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="time">Time-based</SelectItem>
                    <SelectItem value="api_calls">API Calls</SelectItem>
                    <SelectItem value="resources">Resource Consumption</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-white/60 mt-1">
                  Type of usage metric for this feature
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <Button
                variant="outline"
                onClick={() => setIsAdding(false)}
                disabled={isSaving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddFeature}
                disabled={isSaving}
                className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
              >
                {isSaving ? 'Saving...' : 'Save Feature'}
              </Button>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin w-8 h-8 border-2 border-fiery border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-white/70">Loading features...</p>
          </div>
        ) : features.length === 0 ? (
          <div className="text-center py-12 border border-white/10 rounded-lg bg-dark-800">
            <AlertCircle className="w-12 h-12 text-white/40 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-white mb-2">No Features Defined</h3>
            <p className="text-white/70 max-w-md mx-auto mb-6">
              You haven't defined any metered features yet. Features are used to track and bill for specific premium functionality in your application.
            </p>
            <Button
              onClick={() => setIsAdding(true)}
              className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              Add Your First Feature
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Feature ID</TableHead>
                <TableHead>Display Name</TableHead>
                <TableHead>Credit Cost</TableHead>
                <TableHead>Metric Type</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {features.map((feature) => (
                <TableRow key={feature.id}>
                  <TableCell className="font-mono text-sm">{feature.feature_id}</TableCell>
                  <TableCell>{feature.display_name}</TableCell>
                  <TableCell>{feature.default_credit_cost}</TableCell>
                  <TableCell>
                    {feature.metric_type === 'time' && 'Time-based'}
                    {feature.metric_type === 'api_calls' && 'API Calls'}
                    {feature.metric_type === 'resources' && 'Resource Consumption'}
                    {feature.metric_type === 'custom' && 'Custom'}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteFeature(feature.id)}
                      className="text-white/70 hover:text-white hover:bg-white/5"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default FeatureManagement;
