import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Save, ArrowRight, ArrowLeft } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';

interface WizardNavigationButtonsProps {
  setupStep: number;
  handleSave: () => void;
  isFormValid?: boolean;
}

const WizardNavigationButtons: React.FC<WizardNavigationButtonsProps> = ({
  setupStep,
  handleSave,
  isFormValid = true
}) => {
  const isFinalStep = setupStep === 4;

  const handleComplete = () => {
    if (isFinalStep) {
      handleSave();
      notify.success("Setup completed successfully!");
    }
  };

  return (
    <div className="flex justify-end mt-8 pt-6 border-t border-white/10">
      {isFinalStep ? (
        <Button
          className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
          onClick={handleComplete}
          disabled={!isFormValid}
        >
          <Save className="w-4 h-4 mr-2" />
          Complete Setup
        </Button>
      ) : null}
    </div>
  );
};

export default WizardNavigationButtons;
