import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend, LineChart, Line, AreaChart, Area } from 'recharts';
import { BarChart3, PieChart as PieChartIcon, Calendar, TrendingUp, TrendingDown, Zap, Clock, DollarSign, AlertCircle, Activity, Target, Download, Filter, RefreshCw, Layers, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';

/**
 * Usage Metrics page with professional design patterns
 * Inspired by industry leaders like Zapier and HubSpot
 */
const UsageMetrics = () => {
  const { user, credits } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('week');
  const [chartType, setChartType] = useState('bar');

  // Enhanced mock data for usage metrics
  const usageData = [
    { day: 'Mon', date: '11/13', credits: 25, tools: { 'ChatGPT': 15, 'Midjourney': 5, 'Claude': 5 }, successRate: 98, costPerCredit: 0.12 },
    { day: 'Tue', date: '11/14', credits: 40, tools: { 'ChatGPT': 20, 'Midjourney': 10, 'Claude': 10 }, successRate: 97, costPerCredit: 0.11 },
    { day: 'Wed', date: '11/15', credits: 30, tools: { 'ChatGPT': 10, 'Midjourney': 15, 'Claude': 5 }, successRate: 99, costPerCredit: 0.13 },
    { day: 'Thu', date: '11/16', credits: 50, tools: { 'ChatGPT': 25, 'Midjourney': 15, 'Claude': 10 }, successRate: 96, costPerCredit: 0.10 },
    { day: 'Fri', date: '11/17', credits: 45, tools: { 'ChatGPT': 20, 'Midjourney': 10, 'Claude': 15 }, successRate: 98, costPerCredit: 0.11 },
    { day: 'Sat', date: '11/18', credits: 20, tools: { 'ChatGPT': 10, 'Midjourney': 5, 'Claude': 5 }, successRate: 100, costPerCredit: 0.14 },
    { day: 'Sun', date: '11/19', credits: 15, tools: { 'ChatGPT': 5, 'Midjourney': 5, 'Claude': 5 }, successRate: 100, costPerCredit: 0.15 },
  ];

  // Previous week data for comparison
  const previousWeekData = [
    { day: 'Mon', credits: 22 },
    { day: 'Tue', credits: 35 },
    { day: 'Wed', credits: 28 },
    { day: 'Thu', credits: 42 },
    { day: 'Fri', credits: 38 },
    { day: 'Sat', credits: 18 },
    { day: 'Sun', credits: 12 },
  ];

  // Combined data for comparison charts
  const comparisonData = usageData.map((item, index) => ({
    day: item.day,
    date: item.date,
    current: item.credits,
    previous: previousWeekData[index].credits,
    difference: item.credits - previousWeekData[index].credits
  }));

  const monthlyData = [
    { name: 'Week 1', credits: 225, successRate: 97, costPerCredit: 0.12 },
    { name: 'Week 2', credits: 180, successRate: 98, costPerCredit: 0.13 },
    { name: 'Week 3', credits: 275, successRate: 96, costPerCredit: 0.11 },
    { name: 'Week 4', credits: 210, successRate: 99, costPerCredit: 0.12 },
  ];

  // Previous month data for comparison
  const previousMonthData = [
    { name: 'Week 1', credits: 195 },
    { name: 'Week 2', credits: 210 },
    { name: 'Week 3', credits: 230 },
    { name: 'Week 4', credits: 185 },
  ];

  // Monthly trend data (last 6 months)
  const monthlyTrendData = [
    { name: 'Jun', credits: 620, users: 1 },
    { name: 'Jul', credits: 740, users: 1 },
    { name: 'Aug', credits: 850, users: 1 },
    { name: 'Sep', credits: 790, users: 1 },
    { name: 'Oct', credits: 950, users: 1 },
    { name: 'Nov', credits: 890, users: 1 },
  ];

  const toolUsageData = [
    { name: 'ChatGPT', value: 105, color: '#FF4405', growth: 12, costPerUse: 0.35 },
    { name: 'Midjourney', value: 65, color: '#3B82F6', growth: 8, costPerUse: 1.2 },
    { name: 'Claude', value: 55, color: '#8B5CF6', growth: -3, costPerUse: 0.45 },
    { name: 'DALL-E', value: 35, color: '#10B981', growth: 15, costPerUse: 0.95 },
    { name: 'Other', value: 25, color: '#F59E0B', growth: 5, costPerUse: 0.55 },
  ];

  const COLORS = toolUsageData.map(item => item.color);

  // Hourly usage distribution
  const hourlyUsageData = [
    { hour: '12am', credits: 5 },
    { hour: '2am', credits: 3 },
    { hour: '4am', credits: 2 },
    { hour: '6am', credits: 8 },
    { hour: '8am', credits: 15 },
    { hour: '10am', credits: 25 },
    { hour: '12pm', credits: 30 },
    { hour: '2pm', credits: 45 },
    { hour: '4pm', credits: 40 },
    { hour: '6pm', credits: 30 },
    { hour: '8pm', credits: 20 },
    { hour: '10pm', credits: 10 },
  ];

  // Usage by user (for team accounts)
  const userUsageData = [
    { name: 'Current User', value: 225, color: '#FF4405' },
    { name: 'Team Member 1', value: 0, color: '#3B82F6' },
    { name: 'Team Member 2', value: 0, color: '#8B5CF6' },
  ];

  // Cost efficiency data
  const costEfficiencyData = toolUsageData.map(tool => ({
    name: tool.name,
    costPerUse: tool.costPerUse,
    totalCost: tool.value * tool.costPerUse,
    color: tool.color
  }));

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const renderActiveChart = () => {
    if (chartType === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={timeRange === 'week' ? usageData : monthlyData}
            margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#333" />
            <XAxis dataKey={timeRange === 'week' ? 'day' : 'name'} stroke="#888" />
            <YAxis stroke="#888" />
            <Tooltip
              contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
              labelStyle={{ color: '#F9FAFB' }}
              itemStyle={{ color: '#F9FAFB' }}
            />
            <Bar dataKey="credits" fill="#FF4405" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      );
    } else {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={toolUsageData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {toolUsageData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
              labelStyle={{ color: '#F9FAFB' }}
              itemStyle={{ color: '#F9FAFB' }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-fiery"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Usage Metrics</h1>
          <p className="text-white/70">Track and analyze your AI tool usage</p>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data as CSV or PDF</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px] bg-dark-800 border-white/10">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent className="bg-dark-800 border-white/10">
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>

          <div className="bg-dark-800 rounded-md border border-white/10 p-1 flex">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`px-2 ${chartType === 'bar' ? 'bg-white/10 text-white' : 'text-white/70'}`}
                    onClick={() => setChartType('bar')}
                  >
                    <BarChart3 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Bar Chart</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`px-2 ${chartType === 'pie' ? 'bg-white/10 text-white' : 'text-white/70'}`}
                    onClick={() => setChartType('pie')}
                  >
                    <PieChartIcon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Pie Chart</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`px-2 ${chartType === 'line' ? 'bg-white/10 text-white' : 'text-white/70'}`}
                    onClick={() => setChartType('line')}
                  >
                    <Activity className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Line Chart</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Filter data by tool or date</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Summary cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-fiery/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <Zap className="h-4 w-4 mr-2 text-fiery" />
              Total Credits Used
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {timeRange === 'week' ?
                usageData.reduce((sum, day) => sum + day.credits, 0) :
                monthlyData.reduce((sum, week) => sum + week.credits, 0)
              }
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-white/70 flex items-center">
                {timeRange === 'week' ?
                  (usageData.reduce((sum, day) => sum + day.credits, 0) > previousWeekData.reduce((sum, day) => sum + day.credits, 0)) ?
                    <TrendingUp className="h-3 w-3 mr-1 text-green-400" /> :
                    <TrendingDown className="h-3 w-3 mr-1 text-red-400" /> :
                  (monthlyData.reduce((sum, week) => sum + week.credits, 0) > previousMonthData.reduce((sum, week) => sum + week.credits, 0)) ?
                    <TrendingUp className="h-3 w-3 mr-1 text-green-400" /> :
                    <TrendingDown className="h-3 w-3 mr-1 text-red-400" />
                }
                <span className={timeRange === 'week' ?
                  (usageData.reduce((sum, day) => sum + day.credits, 0) > previousWeekData.reduce((sum, day) => sum + day.credits, 0)) ?
                    'text-green-400' : 'text-red-400' :
                  (monthlyData.reduce((sum, week) => sum + week.credits, 0) > previousMonthData.reduce((sum, week) => sum + week.credits, 0)) ?
                    'text-green-400' : 'text-red-400'
                }>
                  {timeRange === 'week' ?
                    Math.round((usageData.reduce((sum, day) => sum + day.credits, 0) - previousWeekData.reduce((sum, day) => sum + day.credits, 0)) / previousWeekData.reduce((sum, day) => sum + day.credits, 0) * 100) :
                    Math.round((monthlyData.reduce((sum, week) => sum + week.credits, 0) - previousMonthData.reduce((sum, week) => sum + week.credits, 0)) / previousMonthData.reduce((sum, week) => sum + week.credits, 0) * 100)
                  }% vs last {timeRange}
                </span>
              </div>
              <Badge variant="outline" className="bg-fiery/10 text-fiery border-fiery/20 text-xs">
                {timeRange === 'week' ? 'Weekly' : 'Monthly'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <Calendar className="h-4 w-4 mr-2 text-blue-400" />
              Average Daily Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {timeRange === 'week' ?
                Math.round(usageData.reduce((sum, day) => sum + day.credits, 0) / 7) :
                Math.round(monthlyData.reduce((sum, week) => sum + week.credits, 0) / 28)
              }
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-white/70">
                credits per day
              </div>
              <div className="text-xs text-white/70 flex items-center">
                <Clock className="h-3 w-3 mr-1 text-blue-400" />
                <span>Peak: {timeRange === 'week' ? '2-4 PM' : 'Thursdays'}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <Target className="h-4 w-4 mr-2 text-purple-400" />
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {timeRange === 'week' ?
                Math.round(usageData.reduce((sum, day) => sum + day.successRate, 0) / usageData.length) :
                Math.round(monthlyData.reduce((sum, week) => sum + week.successRate, 0) / monthlyData.length)
              }%
            </div>
            <div className="mt-2">
              <div className="flex justify-between text-xs text-white/70 mb-1">
                <span>Requests completed successfully</span>
              </div>
              <Progress value={timeRange === 'week' ?
                Math.round(usageData.reduce((sum, day) => sum + day.successRate, 0) / usageData.length) :
                Math.round(monthlyData.reduce((sum, week) => sum + week.successRate, 0) / monthlyData.length)
              } className="h-1.5 bg-white/10" indicatorClassName="bg-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-500/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <DollarSign className="h-4 w-4 mr-2 text-teal-400" />
              Cost Efficiency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              ${timeRange === 'week' ?
                (usageData.reduce((sum, day) => sum + day.costPerCredit, 0) / usageData.length).toFixed(2) :
                (monthlyData.reduce((sum, week) => sum + week.costPerCredit, 0) / monthlyData.length).toFixed(2)
              }
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-white/70">
                avg. cost per credit
              </div>
              <Badge variant="outline" className="bg-teal-500/10 text-teal-400 border-teal-500/20 text-xs">
                Good
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-dark-800 border border-white/10">
          <TabsTrigger value="overview" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="tools" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            By Tool
          </TabsTrigger>
          <TabsTrigger value="time" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            By Time
          </TabsTrigger>
          <TabsTrigger value="cost" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Cost Analysis
          </TabsTrigger>
          <TabsTrigger value="users" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            By User
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Main chart - takes 2/3 of the width on large screens */}
            <Card className="bg-dark-800 border-white/10 lg:col-span-2">
              <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle className="text-lg text-white">
                    {timeRange === 'week' ? 'Weekly' : 'Monthly'} Usage Comparison
                  </CardTitle>
                  <CardDescription>
                    Credits used compared to previous {timeRange}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-3 mt-2 sm:mt-0">
                  <div className="flex items-center gap-1.5">
                    <div className="w-3 h-3 rounded-sm bg-fiery"></div>
                    <span className="text-xs text-white/70">Current</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <div className="w-3 h-3 rounded-sm bg-white/30"></div>
                    <span className="text-xs text-white/70">Previous</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  {chartType === 'bar' ? (
                    <BarChart
                      data={timeRange === 'week' ? comparisonData : monthlyData}
                      margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                      <XAxis dataKey={timeRange === 'week' ? 'day' : 'name'} stroke="#888" />
                      <YAxis stroke="#888" />
                      <RechartsTooltip
                        contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                        labelStyle={{ color: '#F9FAFB' }}
                        itemStyle={{ color: '#F9FAFB' }}
                      />
                      {timeRange === 'week' ? (
                        <>
                          <Bar dataKey="current" name="Current" fill="#FF4405" radius={[4, 4, 0, 0]} />
                          <Bar dataKey="previous" name="Previous" fill="rgba(255,255,255,0.3)" radius={[4, 4, 0, 0]} />
                        </>
                      ) : (
                        <Bar dataKey="credits" fill="#FF4405" radius={[4, 4, 0, 0]} />
                      )}
                    </BarChart>
                  ) : chartType === 'line' ? (
                    <LineChart
                      data={timeRange === 'week' ? comparisonData : monthlyTrendData}
                      margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                      <XAxis dataKey={timeRange === 'week' ? 'day' : 'name'} stroke="#888" />
                      <YAxis stroke="#888" />
                      <RechartsTooltip
                        contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                        labelStyle={{ color: '#F9FAFB' }}
                        itemStyle={{ color: '#F9FAFB' }}
                      />
                      {timeRange === 'week' ? (
                        <>
                          <Line type="monotone" dataKey="current" name="Current" stroke="#FF4405" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
                          <Line type="monotone" dataKey="previous" name="Previous" stroke="rgba(255,255,255,0.3)" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
                        </>
                      ) : (
                        <Line type="monotone" dataKey="credits" stroke="#FF4405" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
                      )}
                    </LineChart>
                  ) : (
                    <AreaChart
                      data={timeRange === 'week' ? comparisonData : monthlyTrendData}
                      margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                      <XAxis dataKey={timeRange === 'week' ? 'day' : 'name'} stroke="#888" />
                      <YAxis stroke="#888" />
                      <RechartsTooltip
                        contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                        labelStyle={{ color: '#F9FAFB' }}
                        itemStyle={{ color: '#F9FAFB' }}
                      />
                      {timeRange === 'week' ? (
                        <>
                          <Area type="monotone" dataKey="current" name="Current" stroke="#FF4405" fill="#FF4405" fillOpacity={0.2} />
                          <Area type="monotone" dataKey="previous" name="Previous" stroke="rgba(255,255,255,0.3)" fill="rgba(255,255,255,0.1)" fillOpacity={0.1} />
                        </>
                      ) : (
                        <Area type="monotone" dataKey="credits" stroke="#FF4405" fill="#FF4405" fillOpacity={0.2} />
                      )}
                    </AreaChart>
                  )}
                </ResponsiveContainer>

                {timeRange === 'week' && (
                  <div className="mt-4 grid grid-cols-7 gap-1 text-xs">
                    {comparisonData.map((item, index) => (
                      <div key={index} className="text-center">
                        <div className={`font-medium ${item.difference > 0 ? 'text-green-400' : item.difference < 0 ? 'text-red-400' : 'text-white/70'}`}>
                          {item.difference > 0 ? '+' : ''}{item.difference}
                        </div>
                        <div className="text-white/50">{item.day}</div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Trend analysis card */}
            <Card className="bg-dark-800 border-white/10">
              <CardHeader>
                <CardTitle className="text-lg text-white">
                  Usage Insights
                </CardTitle>
                <CardDescription>
                  Key metrics and trends
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Peak usage */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2 flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2 text-fiery" />
                    Peak Usage
                  </h3>
                  <div className="bg-dark-700 rounded-lg p-3 border border-white/5">
                    <div className="text-xl font-bold text-white">
                      {timeRange === 'week' ?
                        usageData.reduce((max, day) => day.credits > max.credits ? day : max, usageData[0]).day :
                        'Week ' + (monthlyData.reduce((max, week, index) => week.credits > monthlyData[max].credits ? index : max, 0) + 1)
                      }
                    </div>
                    <div className="text-sm text-white/70 mt-1 flex items-center justify-between">
                      <span>
                        {timeRange === 'week' ?
                          usageData.reduce((max, day) => day.credits > max.credits ? day : max, usageData[0]).credits :
                          monthlyData.reduce((max, week) => week.credits > max.credits ? week : max, monthlyData[0]).credits
                        } credits
                      </span>
                      <Badge variant="outline" className="bg-fiery/10 text-fiery border-fiery/20 text-xs">
                        {timeRange === 'week' ? 'Day' : 'Week'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Growth rate */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2 text-fiery" />
                    Growth Rate
                  </h3>
                  <div className="bg-dark-700 rounded-lg p-3 border border-white/5">
                    <div className="flex items-center">
                      <div className="text-xl font-bold text-white">
                        {timeRange === 'week' ?
                          Math.round((usageData.reduce((sum, day) => sum + day.credits, 0) - previousWeekData.reduce((sum, day) => sum + day.credits, 0)) / previousWeekData.reduce((sum, day) => sum + day.credits, 0) * 100) :
                          Math.round((monthlyData.reduce((sum, week) => sum + week.credits, 0) - previousMonthData.reduce((sum, week) => sum + week.credits, 0)) / previousMonthData.reduce((sum, week) => sum + week.credits, 0) * 100)
                        }%
                      </div>
                      <div className="ml-2">
                        {(timeRange === 'week' ?
                          (usageData.reduce((sum, day) => sum + day.credits, 0) > previousWeekData.reduce((sum, day) => sum + day.credits, 0)) :
                          (monthlyData.reduce((sum, week) => sum + week.credits, 0) > previousMonthData.reduce((sum, week) => sum + week.credits, 0))
                        ) ?
                          <TrendingUp className="h-5 w-5 text-green-400" /> :
                          <TrendingDown className="h-5 w-5 text-red-400" />
                        }
                      </div>
                    </div>
                    <div className="text-sm text-white/70 mt-1">
                      compared to previous {timeRange}
                    </div>
                  </div>
                </div>

                {/* Most used tool */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2 flex items-center">
                    <Layers className="h-4 w-4 mr-2 text-fiery" />
                    Most Used Tool
                  </h3>
                  <div className="bg-dark-700 rounded-lg p-3 border border-white/5">
                    <div className="text-xl font-bold text-white">
                      {toolUsageData.reduce((max, tool) => tool.value > max.value ? tool : max, toolUsageData[0]).name}
                    </div>
                    <div className="text-sm text-white/70 mt-1 flex items-center justify-between">
                      <span>
                        {toolUsageData.reduce((max, tool) => tool.value > max.value ? tool : max, toolUsageData[0]).value} credits
                      </span>
                      <Badge
                        variant="outline"
                        className="text-xs"
                        style={{
                          backgroundColor: `${toolUsageData.reduce((max, tool) => tool.value > max.value ? tool : max, toolUsageData[0]).color}20`,
                          color: toolUsageData.reduce((max, tool) => tool.value > max.value ? tool : max, toolUsageData[0]).color,
                          borderColor: `${toolUsageData.reduce((max, tool) => tool.value > max.value ? tool : max, toolUsageData[0]).color}40`
                        }}
                      >
                        {Math.round((toolUsageData.reduce((max, tool) => tool.value > max.value ? tool : max, toolUsageData[0]).value / toolUsageData.reduce((sum, tool) => sum.value + tool.value, {value: 0})) * 100)}%
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tool distribution */}
          <Card className="bg-dark-800 border-white/10">
            <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <CardTitle className="text-lg text-white">
                  Tool Usage Distribution
                </CardTitle>
                <CardDescription>
                  Credits used by each tool
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
                <div className="lg:col-span-2">
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={toolUsageData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {toolUsageData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip
                        contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                        labelStyle={{ color: '#F9FAFB' }}
                        itemStyle={{ color: '#F9FAFB' }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>

                <div className="lg:col-span-3 space-y-3">
                  {toolUsageData.map((tool, index) => (
                    <div key={index} className="bg-dark-700 rounded-lg p-3 border border-white/5">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center">
                          <div className="h-3 w-3 rounded-sm mr-2" style={{ backgroundColor: tool.color }}></div>
                          <span className="font-medium text-white">{tool.name}</span>
                        </div>
                        <div className="text-sm text-white/70">{tool.value} credits</div>
                      </div>
                      <div className="relative pt-1">
                        <div className="flex items-center justify-between text-xs text-white/50 mb-1">
                          <span>{Math.round((tool.value / toolUsageData.reduce((sum, t) => sum + t.value, 0)) * 100)}% of total</span>
                          <div className="flex items-center">
                            {tool.growth > 0 ? (
                              <TrendingUp className="h-3 w-3 mr-1 text-green-400" />
                            ) : (
                              <TrendingDown className="h-3 w-3 mr-1 text-red-400" />
                            )}
                            <span className={tool.growth > 0 ? 'text-green-400' : 'text-red-400'}>
                              {tool.growth > 0 ? '+' : ''}{tool.growth}%
                            </span>
                          </div>
                        </div>
                        <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                          <div
                            className="h-full rounded-full"
                            style={{
                              width: `${(tool.value / toolUsageData.reduce((sum, t) => sum + t.value, 0)) * 100}%`,
                              backgroundColor: tool.color
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tools" className="space-y-6">
          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                Tool Usage Distribution
              </CardTitle>
              <CardDescription>
                Credits used by each tool
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={toolUsageData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {toolUsageData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                    labelStyle={{ color: '#F9FAFB' }}
                    itemStyle={{ color: '#F9FAFB' }}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {toolUsageData.map((tool, index) => (
              <Card key={index} className="bg-dark-800 border-white/10">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center text-white/70">
                    <div className="h-4 w-4 mr-2 rounded-full" style={{ backgroundColor: tool.color }}></div>
                    {tool.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">
                    {tool.value} credits
                  </div>
                  <div className="text-xs text-white/70 mt-1">
                    {Math.round((tool.value / toolUsageData.reduce((sum, t) => sum + t.value, 0)) * 100)}% of total usage
                  </div>
                  <div className="mt-2 h-1.5 bg-white/10 rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full"
                      style={{
                        width: `${(tool.value / toolUsageData.reduce((sum, t) => sum + t.value, 0)) * 100}%`,
                        backgroundColor: tool.color
                      }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="time" className="space-y-6">
          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                Usage Over Time
              </CardTitle>
              <CardDescription>
                Credits used over {timeRange === 'week' ? 'the past week' : 'the past month'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={timeRange === 'week' ? usageData : monthlyData}
                  margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                  <XAxis dataKey={timeRange === 'week' ? 'day' : 'name'} stroke="#888" />
                  <YAxis stroke="#888" />
                  <Tooltip
                    contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                    labelStyle={{ color: '#F9FAFB' }}
                    itemStyle={{ color: '#F9FAFB' }}
                  />
                  <Bar dataKey="credits" fill="#FF4405" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                Usage Patterns
              </CardTitle>
              <CardDescription>
                When you use AI tools the most
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-2 mb-4">
                {usageData.map((day, index) => (
                  <div key={index} className="text-center">
                    <div className="text-xs text-white/70 mb-1">{day.day}</div>
                    <div className="h-24 bg-white/5 rounded-md relative overflow-hidden">
                      <div
                        className="absolute bottom-0 w-full bg-fiery transition-all"
                        style={{ height: `${(day.credits / 50) * 100}%` }}
                      ></div>
                    </div>
                    <div className="text-xs font-medium text-white mt-1">{day.credits}</div>
                  </div>
                ))}
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-sm font-medium text-white mb-2">Key Insights</h3>
                <ul className="space-y-2 text-sm text-white/70">
                  <li className="flex items-start">
                    <div className="h-4 w-4 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                    </div>
                    <span>Your peak usage is on <span className="text-white font-medium">Thursday</span></span>
                  </li>
                  <li className="flex items-start">
                    <div className="h-4 w-4 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                    </div>
                    <span>Weekend usage is <span className="text-white font-medium">60% lower</span> than weekdays</span>
                  </li>
                  <li className="flex items-start">
                    <div className="h-4 w-4 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                    </div>
                    <span>Most active time is <span className="text-white font-medium">2-4 PM</span></span>
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UsageMetrics;
