/**
 * Login Page
 * Authentication entry point with identity provider integration
 */

import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth0 } from '@auth0/auth0-react'
import { Shield, Zap, Database, Lock } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import toast from 'react-hot-toast'

export function LoginPage() {
  const navigate = useNavigate()
  const { login } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)

  const {
    loginWithRedirect,
    isAuthenticated,
    isLoading: auth0Loading,
    user,
    getAccessTokenSilently
  } = useAuth0()

  // Handle Auth0 authentication state
  useEffect(() => {
    const handleAuth0Login = async () => {
      if (isAuthenticated && user) {
        try {
          // Get the access token from Auth0
          const token = await getAccessTokenSilently()

          // Store in our auth store
          await login(token, 'auth0')
          navigate('/')
        } catch (error) {
          console.error('Auth0 token error:', error)
          toast.error('Authentication failed. Please try again.')
        }
      }
    }

    if (!auth0Loading) {
      handleAuth0Login()
    }
  }, [isAuthenticated, user, auth0Loading, getAccessTokenSilently, login, navigate])

  const handleLogin = async () => {
    setIsLoading(true)
    try {
      // Check if we're in development mode and allow demo login
      if (import.meta.env.MODE === 'development' && import.meta.env.VITE_ENABLE_DEMO_LOGIN === 'true') {
        // Demo login for development
        const mockTokenPayload = {
          sub: 'demo-user-123',
          email: '<EMAIL>',
          name: 'Demo User',
          role: 'user',
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
        }

        const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }))
        const payload = btoa(JSON.stringify(mockTokenPayload))
        const signature = btoa('mock-signature')
        const mockToken = `${header}.${payload}.${signature}`

        await login(mockToken, 'auth0')
        navigate('/')
      } else {
        // Use Auth0 for production login
        await loginWithRedirect({
          appState: { returnTo: window.location.pathname }
        })
      }
    } catch (error) {
      console.error('Login error:', error)
      toast.error('Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading if Auth0 is still loading
  if (auth0Loading) {
    return (
      <div className="min-h-screen bg-gradient-dark flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-dark flex">
      {/* Left Side - Content */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="max-w-md w-full space-y-8">
          <div>
            <div className="flex items-center space-x-2 mb-8">
              <div className="w-10 h-10 bg-fiery rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">FS</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Firenest Sandbox</h1>
                <p className="text-sm text-gray-400">Pricing Intelligence Platform</p>
              </div>
            </div>
            
            <h2 className="text-3xl font-bold text-white">
              Welcome back
            </h2>
            <p className="mt-2 text-gray-400">
              Sign in to access your pricing intelligence workspace
            </p>
          </div>

          <div className="space-y-6">
            <Button
              onClick={handleLogin}
              disabled={isLoading}
              className="w-full h-12 text-base"
              variant="fiery"
            >
              {isLoading ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Shield className="w-5 h-5 mr-2" />
              )}
              {isLoading ? 'Signing in...' : 'Sign in with SSO'}
            </Button>

            <div className="text-center">
              <p className="text-xs text-gray-400">
                By signing in, you agree to our Terms of Service and Privacy Policy
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Features */}
      <div className="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center bg-black/20 backdrop-blur">
        <div className="max-w-md space-y-8 p-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">
              Enterprise-Grade Security
            </h3>
            <p className="text-gray-300">
              Built for sensitive pricing data with zero-trust architecture and SOC 2 compliance
            </p>
          </div>

          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">Zero-Trust Security</h4>
                <p className="text-sm text-gray-400">
                  Every request authenticated with immutable audit trails
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Database className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">Data Isolation</h4>
                <p className="text-sm text-gray-400">
                  Absolute separation between customer data with RLS
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Lock className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">End-to-End Encryption</h4>
                <p className="text-sm text-gray-400">
                  Data encrypted in transit and at rest with AES-256
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Zap className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">Real-Time Processing</h4>
                <p className="text-sm text-gray-400">
                  Instant validation and simulation results at scale
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-white/10 pt-6">
            <div className="text-center">
              <p className="text-xs text-gray-400">
                Trusted by pricing teams at leading SaaS companies
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
