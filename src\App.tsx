import { NotificationToaster } from "@/components/ui/notification-system";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import PageVisibilityManager from "@/components/PageVisibilityManager";


// Error handling components
import ErrorBoundary from "@/components/error/ErrorBoundary";
import GlobalErrorHandler from "@/components/error/GlobalErrorHandler";
import DebugProvider from "@/contexts/DebugContext";
import DebugButton from "@/components/error/DebugButton";


// Pages
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import RouterErrorPage from "./pages/RouterErrorPage";
import ErrorDiagnosticPage from "./pages/ErrorDiagnosticPage";
import NetworkErrorPage from "./pages/NetworkErrorPage";
import ServerErrorPage from "./pages/ServerErrorPage";
import TestErrorPage from "./pages/TestErrorPage";
import Privacy from "./pages/Privacy";
import Terms from "./pages/Terms";
import Cookies from "./pages/Cookies";
import Security from "./pages/Security";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import EmailVerification from "./pages/EmailVerification";
import VerifyEmailConfirm from "./pages/VerifyEmailConfirm";
import ManualVerification from "./pages/ManualVerification";
// Dashboard pages
import EnterpriseDashboard from "./pages/EnterpriseDashboard";
import NewWorkbench from "./pages/NewWorkbench";
import NewCredits from "./pages/NewCredits";
import NewProfileSettings from "./pages/NewProfileSettings";
import Support from "./pages/Support";
import UsageMetrics from "./pages/analytics/UsageMetrics";
import PerformanceAnalytics from "./pages/analytics/Performance";
import Members from "./pages/team/Members";
import Invites from "./pages/team/Invites";
import Notifications from "./pages/Notifications";
import ConnectionManager from "./pages/ConnectionManager";
import AuthCallback from "./pages/AuthCallback";
import ComponentsDemo from "./pages/ComponentsDemo";
import SupabaseSetup from "./pages/SupabaseSetup";
import NotificationTest from "./pages/NotificationTest";
import CreditUsageDemo from "./pages/CreditUsageDemo";
// Partner Portal pages
import PartnerPortal from "./pages/PartnerPortal";
import PartnerDashboard from "./pages/PartnerDashboard";
import PartnerToolManagement from "./pages/PartnerToolManagement";
import PartnerAuthentication from "./pages/PartnerAuthentication";
import PartnerIntegration from "./pages/PartnerIntegration";
import PartnerSettings from "./pages/PartnerSettings";
import PartnerFeatures from "./pages/PartnerFeatures";
import PartnerWebhooks from "./pages/PartnerWebhooks";
import PartnerDocumentation from "./pages/PartnerDocumentation";
import PartnerSupport from "./pages/PartnerSupport";
import PartnerOIDCDocumentation from "./pages/PartnerOIDCDocumentation";
import PartnerSDKDocumentation from "./pages/PartnerSDKDocumentation";
// OAuth Authorization pages
import AuthorizePage from "./pages/AuthorizePage";
import TokenEndpoint from "./pages/TokenEndpoint";

// Dashboard layout
import SimpleDashboardLayout from "./components/dashboard/SimpleDashboardLayout";
import ProtectedRoute from "./components/ProtectedRoute";
import PartnerProtectedRoute from "./components/PartnerProtectedRoute";
import { AuthProvider } from "./contexts/AuthContext";

// Configure the query client with settings to prevent unnecessary refetches
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Don't refetch on window focus (prevents unnecessary refetches when switching tabs)
      refetchOnWindowFocus: false,
      // Keep data fresh for 5 minutes
      staleTime: 5 * 60 * 1000,
      // Cache data for 30 minutes
      gcTime: 30 * 60 * 1000,
      // Retry failed requests 3 times
      retry: 3,
      // Use the network connection status to determine if queries should retry
      networkMode: 'online',
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <DebugProvider>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <NotificationToaster />
            <GlobalErrorHandler />
            <PageVisibilityManager>
              <BrowserRouter>
              <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/verify-email" element={<EmailVerification />} />
            <Route path="/verify-email-confirm" element={<VerifyEmailConfirm />} />
            <Route path="/manual-verification" element={<ManualVerification />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/cookies" element={<Cookies />} />
            <Route path="/security" element={<Security />} />
            <Route path="/supabase-setup" element={<SupabaseSetup />} />

            {/* Protected Dashboard Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <SimpleDashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<EnterpriseDashboard />} />
              <Route path="workbench" element={<NewWorkbench />} />
              <Route path="credits" element={<NewCredits />} />
              <Route path="settings" element={<NewProfileSettings />} />
              <Route path="support" element={<Support />} />
              <Route path="notifications" element={<Notifications />} />
              <Route path="components" element={<ComponentsDemo />} />
              <Route path="notification-test" element={<NotificationTest />} />
              <Route path="credit-usage-demo" element={<CreditUsageDemo />} />
              <Route path="analytics/usage" element={<UsageMetrics />} />
              <Route path="analytics/performance" element={<PerformanceAnalytics />} />
              <Route path="team/members" element={<Members />} />
              <Route path="team/invites" element={<Invites />} />
              <Route path="connections" element={<ConnectionManager />} />
            </Route>

            {/* Partner Portal Routes */}
            <Route path="/partner" element={<PartnerPortal />} />
            <Route element={<PartnerProtectedRoute />}>
              <Route path="/partner/dashboard" element={<PartnerDashboard />} />
              <Route path="/partner/tools/:toolId" element={<PartnerToolManagement />} />
              <Route path="/partner/authentication" element={<PartnerAuthentication />} />
              <Route path="/partner/integration" element={<PartnerIntegration />} />
              <Route path="/partner/features" element={<PartnerFeatures />} />
              <Route path="/partner/webhooks" element={<PartnerWebhooks />} />
              <Route path="/partner/settings" element={<PartnerSettings />} />
              <Route path="/partner/documentation" element={<PartnerDocumentation />} />
              <Route path="/partner/support" element={<PartnerSupport />} />
              <Route path="/partner/documentation/oidc" element={<PartnerOIDCDocumentation />} />
              <Route path="/partner/documentation/sdk" element={<PartnerSDKDocumentation />} />
            </Route>

            {/* OAuth Authorization routes */}
            <Route path="/auth/authorize" element={<AuthorizePage />} />
            <Route path="/auth/token" element={<TokenEndpoint />} />

            {/* Auth callback routes */}
            <Route path="/auth/callback/:service" element={<AuthCallback />} />

                {/* Error pages */}
                <Route path="/error" element={<RouterErrorPage error={new Error('Generic Error')} />} />
                <Route path="/error-diagnostics" element={<ErrorDiagnosticPage />} />
                <Route path="/network-error" element={<NetworkErrorPage />} />
                <Route path="/server-error" element={<ServerErrorPage />} />
                <Route path="/test-error" element={<TestErrorPage />} />

                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
              </BrowserRouter>
              <DebugButton />
            </PageVisibilityManager>
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    </DebugProvider>
  </ErrorBoundary>
);

export default App;
