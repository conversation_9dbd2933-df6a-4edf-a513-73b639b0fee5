import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { FileText, HelpCircle, Mail, Github } from 'lucide-react';

interface PartnerFooterProps {
  className?: string;
}

const PartnerFooter: React.FC<PartnerFooterProps> = ({ className }) => {
  const navigate = useNavigate();
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`mt-auto py-6 border-t border-white/10 bg-dark-900 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          {/* Copyright */}
          <div className="text-white/60 text-sm mb-4 md:mb-0">
            © {currentYear} Firenest. All rights reserved.
          </div>

          {/* Navigation Links */}
          <div className="flex items-center space-x-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/60 hover:text-white hover:bg-white/5 flex items-center transition-all duration-300 px-3 py-2 rounded-md"
                    onClick={() => navigate('/partner/documentation')}
                  >
                    <FileText className="w-4 h-4 flex-shrink-0" />
                    <span className="ml-2">Documentation</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Access developer documentation</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/60 hover:text-white hover:bg-white/5 flex items-center transition-all duration-300 px-3 py-2 rounded-md"
                    onClick={() => navigate('/partner/support')}
                  >
                    <HelpCircle className="w-4 h-4 flex-shrink-0" />
                    <span className="ml-2">Support</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Get help with your integration</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white/60 hover:text-white hover:bg-white/5 flex items-center transition-all duration-300 px-3 py-2 rounded-md"
                    onClick={() => window.open('mailto:<EMAIL>')}
                  >
                    <Mail className="w-4 h-4 flex-shrink-0" />
                    <span className="ml-2">Contact</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Contact the Firenest team</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default PartnerFooter;
