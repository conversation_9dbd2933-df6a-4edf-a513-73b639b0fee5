import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, Home } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

// This component needs to be a class component because error boundaries must be classes
class ErrorBoundaryClass extends Component<ErrorBoundaryProps & { navigate: Function }, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps & { navigate: Function }) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console
    console.error('Partner Portal Error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, navigate } = this.props;

    if (hasError) {
      return (
        <div className="min-h-screen bg-dark-950 flex items-center justify-center">
          <div className="flex flex-col items-center justify-center p-8 max-w-md">
            <div className="w-16 h-16 bg-fiery/10 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-fiery" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Something went wrong</h2>
            <p className="text-white/70 text-center mb-2">
              We encountered an unexpected error in the partner portal:
            </p>
            <p className="text-fiery text-center mb-6 p-4 bg-fiery/10 rounded-md overflow-auto max-h-32 w-full">
              {error?.message || 'Unknown error'}
            </p>
            <div className="flex gap-4">
              <Button 
                onClick={() => window.location.reload()} 
                className="flex items-center gap-2 bg-fiery hover:bg-fiery/90"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Reload Page</span>
              </Button>
              <Button 
                onClick={() => navigate('/partner')} 
                variant="outline" 
                className="flex items-center gap-2 border-white/10 hover:bg-white/5"
              >
                <Home className="h-4 w-4" />
                <span>Go to Portal Home</span>
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return children;
  }
}

// This wrapper component provides the navigate function to the class component
export default function PartnerErrorBoundary({ children }: ErrorBoundaryProps): JSX.Element {
  const navigate = useNavigate();
  return <ErrorBoundaryClass navigate={navigate}>{children}</ErrorBoundaryClass>;
}
