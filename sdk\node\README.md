# Firenest SDK for Node.js

This SDK provides a simple interface for integrating your Node.js application with Firenest, handling authentication, authorization checks, and usage reporting.

## Installation

```bash
npm install @firenest/sdk-node
```

## Usage

### Initialization

```javascript
const { FirenestSDK } = require('@firenest/sdk-node');
// or using ES modules:
// import { FirenestSDK } from '@firenest/sdk-node';

const firenest = new FirenestSDK({
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  environment: 'sandbox' // or 'production'
});
```

### Authentication Flow

#### 1. Generate Login URL

```javascript
// In your login route handler
app.get('/login/firenest', (req, res) => {
  const redirectUri = 'https://your-app.com/auth/firenest/callback';
  const state = 'random-state-string'; // Optional, for security

  const loginUrl = firenest.getLoginUrl(redirectUri, state);
  res.redirect(loginUrl);
});
```

#### 2. Handle Callback

```javascript
// In your callback route handler
app.get('/auth/firenest/callback', async (req, res) => {
  try {
    // Get the full URL of the request
    const requestUrl = `${req.protocol}://${req.get('host')}${req.originalUrl}`;

    // Handle the callback
    const userData = await firenest.handleCallback(requestUrl);

    // userData contains:
    // - firenestUserId: The Firenest user ID
    // - email: The user's email
    // - name: The user's name
    // - accessToken: The access token for API calls
    // - idToken: The OpenID Connect ID token
    // - refreshToken: The refresh token for getting new access tokens
    // - expiresIn: The token expiration time in seconds
    // - tokenResponse: The complete token response for advanced use cases

    // Store the Firenest user ID and tokens in your user's session or database
    req.session.firenestUserId = userData.firenestUserId;
    req.session.firenestAccessToken = userData.accessToken;
    req.session.firenestIdToken = userData.idToken;

    // Redirect to your app's dashboard
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).send('Authentication failed');
  }
});
```

### Checking Access to Premium Features

Before executing premium features in your application, check if the user has access:

```javascript
async function generatePremiumReport(userId, reportParams) {
  // Get the Firenest user ID from your database or session
  const user = await getUserFromDatabase(userId);
  const firenestUserId = user.firenestUserId;

  // Check if the user has access to this premium feature
  const accessResult = await firenest.checkAccess(firenestUserId, 'premium-report-generation');

  if (!accessResult.allowed) {
    // User doesn't have access, handle accordingly
    throw new Error(`Access denied: ${accessResult.reason}`);
  }

  // User has access, proceed with the premium feature
  const report = await generateReport(reportParams);

  // Report usage after successful execution
  await firenest.reportUsage(firenestUserId, 'premium-report-generation');

  return report;
}
```

### Reporting Usage with Custom Units

If your feature has variable costs, you can specify the units consumed:

```javascript
// For features with dynamic costs
const imageSize = calculateImageSize(generatedImage);
const unitsConsumed = Math.ceil(imageSize / 1024); // 1 credit per KB

await firenest.reportUsage(firenestUserId, 'image-generation', {
  unitsConsumed,
  metadata: {
    imageType: 'png',
    dimensions: '1024x1024',
    prompt: 'A beautiful sunset'
  }
});
```

## Error Handling

The SDK includes robust error handling. It's recommended to implement retry logic for `reportUsage` calls to ensure usage is properly tracked even during network issues.

```javascript
async function reportUsageWithRetry(firenestUserId, featureId, options, maxRetries = 3) {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const result = await firenest.reportUsage(firenestUserId, featureId, options);
      if (result.success) {
        return result;
      }
      retries++;
      await new Promise(resolve => setTimeout(resolve, 1000 * retries)); // Exponential backoff
    } catch (error) {
      retries++;
      if (retries >= maxRetries) {
        console.error('Failed to report usage after multiple attempts:', error);
        // Consider logging to a dead-letter queue for manual reconciliation
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 1000 * retries));
    }
  }
}
```

## API Reference

### `new FirenestSDK(config)`

Creates a new instance of the Firenest SDK.

**Parameters:**
- `config` (object):
  - `clientId` (string): Your Firenest client ID
  - `clientSecret` (string): Your Firenest client secret
  - `environment` (string, optional): 'sandbox' or 'production' (default: 'production')
  - `apiBaseUrl` (string, optional): Custom API base URL

### `getLoginUrl(redirectUri, state, scope)`

Generates a login URL for redirecting users to Firenest.

**Parameters:**
- `redirectUri` (string): The URI to redirect to after authentication
- `state` (string, optional): A random string to prevent CSRF attacks
- `scope` (string[], optional): The scopes to request (default: ['openid', 'profile'])

**Returns:** The authorization URL (string)

### `handleCallback(requestUrl)`

Handles the callback from Firenest and exchanges the authorization code for tokens.

**Parameters:**
- `requestUrl` (string): The full URL of the callback request

**Returns:** Promise resolving to user information

### `checkAccess(firenestUserId, featureId)`

Checks if a user has access to a feature.

**Parameters:**
- `firenestUserId` (string): The Firenest user ID
- `featureId` (string): The feature ID to check access for

**Returns:** Promise resolving to an access check result

### `reportUsage(firenestUserId, featureId, options)`

Reports usage of a feature.

**Parameters:**
- `firenestUserId` (string): The Firenest user ID
- `featureId` (string): The feature ID that was used
- `options` (object, optional):
  - `unitsConsumed` (number, optional): The number of units consumed
  - `metadata` (object, optional): Additional metadata about the usage

**Returns:** Promise resolving to a usage report result

## License

MIT
