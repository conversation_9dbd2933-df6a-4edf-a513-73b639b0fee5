import { supabase } from './supabase';

/**
 * Debug function to check the current authentication state
 * This can be called from the browser console to help diagnose authentication issues
 */
export async function debugAuth() {
  console.group('Auth Debug Information');
  
  try {
    // Check if there's a session in localStorage
    console.log('Checking localStorage for session...');
    const localStorageSession = localStorage.getItem('supabase.auth.token');
    console.log('localStorage session:', localStorageSession ? 'exists' : 'not found');
    
    // Check for mock auth data
    const mockUser = localStorage.getItem('mockUser');
    const mockSession = localStorage.getItem('mockSession');
    console.log('Mock auth data:', {
      mockUser: mockUser ? 'exists' : 'not found',
      mockSession: mockSession ? 'exists' : 'not found'
    });
    
    // Check current session from Supabase
    console.log('Checking Supabase session...');
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session:', error);
    } else if (data.session) {
      console.log('Supabase session found:', {
        user: {
          id: data.session.user.id,
          email: data.session.user.email,
          metadata: data.session.user.user_metadata
        },
        expires_at: new Date(data.session.expires_at * 1000).toLocaleString()
      });
    } else {
      console.log('No active Supabase session found');
    }
    
    // Check if auth tables exist
    console.log('Checking if auth tables exist...');
    
    const tables = [
      'users',
      'user_profiles',
      'user_settings',
      'user_credits',
      'credit_transactions'
    ];
    
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1);
        
      console.log(`Table '${table}':`, error ? 'Error or not found' : 'Exists');
    }
  } catch (error) {
    console.error('Unexpected error during auth debugging:', error);
  }
  
  console.groupEnd();
  
  return 'Auth debug complete. Check the console for details.';
}

// Add the debug function to the window object so it can be called from the console
if (typeof window !== 'undefined') {
  (window as any).debugAuth = debugAuth;
}
