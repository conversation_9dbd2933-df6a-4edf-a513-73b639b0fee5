/**
 * Token Refresh Utility
 * 
 * This file contains utilities for refreshing access tokens on the client side.
 */

import { supabase } from '@/lib/supabase';

// Store for refresh tokens
const tokenStore = {
  accessToken: '',
  refreshToken: '',
  expiresAt: 0,
  clientId: '',
  isRefreshing: false,
  refreshPromise: null as Promise<any> | null
};

/**
 * Initialize the token store with the current tokens
 * 
 * @param tokens The current tokens
 */
export function initializeTokens(tokens: {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  client_id: string;
}) {
  tokenStore.accessToken = tokens.access_token;
  tokenStore.refreshToken = tokens.refresh_token;
  tokenStore.expiresAt = Date.now() + tokens.expires_in * 1000;
  tokenStore.clientId = tokens.client_id;
}

/**
 * Get the current access token, refreshing if necessary
 * 
 * @returns The current access token
 */
export async function getAccessToken(): Promise<string> {
  // If token is still valid, return it
  if (tokenStore.accessToken && tokenStore.expiresAt > Date.now() + 60000) { // 1 minute buffer
    return tokenStore.accessToken;
  }

  // If already refreshing, wait for that to complete
  if (tokenStore.isRefreshing && tokenStore.refreshPromise) {
    await tokenStore.refreshPromise;
    return tokenStore.accessToken;
  }

  // Start refreshing
  tokenStore.isRefreshing = true;
  tokenStore.refreshPromise = refreshToken();

  try {
    await tokenStore.refreshPromise;
    return tokenStore.accessToken;
  } finally {
    tokenStore.isRefreshing = false;
    tokenStore.refreshPromise = null;
  }
}

/**
 * Refresh the access token using the refresh token
 * 
 * @returns The new tokens
 */
async function refreshToken(): Promise<void> {
  try {
    if (!tokenStore.refreshToken || !tokenStore.clientId) {
      console.error('No refresh token or client ID available');
      throw new Error('No refresh token available');
    }

    // Call the token endpoint to refresh the token
    const response = await fetch('/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        grant_type: 'refresh_token',
        refresh_token: tokenStore.refreshToken,
        client_id: tokenStore.clientId
      })
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error refreshing token:', error);
      throw new Error('Failed to refresh token');
    }

    const data = await response.json();
    
    // Update the token store
    tokenStore.accessToken = data.access_token;
    tokenStore.refreshToken = data.refresh_token;
    tokenStore.expiresAt = Date.now() + data.expires_in * 1000;

    console.log('Token refreshed successfully');
  } catch (error) {
    console.error('Error in refreshToken:', error);
    // Clear the token store on error
    tokenStore.accessToken = '';
    tokenStore.refreshToken = '';
    tokenStore.expiresAt = 0;
    throw error;
  }
}

/**
 * Create an authenticated fetch function that automatically refreshes tokens
 * 
 * @returns A fetch function that includes the access token
 */
export function createAuthFetch() {
  return async (url: string, options: RequestInit = {}): Promise<Response> => {
    try {
      // Get a valid access token
      const accessToken = await getAccessToken();
      
      // Clone the options to avoid modifying the original
      const authOptions = { ...options };
      
      // Add the Authorization header
      authOptions.headers = {
        ...authOptions.headers,
        'Authorization': `Bearer ${accessToken}`
      };
      
      // Make the request
      return fetch(url, authOptions);
    } catch (error) {
      console.error('Error in authFetch:', error);
      throw error;
    }
  };
}
