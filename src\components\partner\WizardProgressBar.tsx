import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FileText, Key, Code, BarChart2 } from 'lucide-react';

interface WizardProgressBarProps {
  setupStep: number;
  activeTab: string;
  toolId?: string;
  onTabChange?: (tab: string) => void;
  onSetupStepChange?: (step: number) => void;
}

const WizardProgressBar: React.FC<WizardProgressBarProps> = ({
  setupStep,
  activeTab,
  toolId,
  onTabChange,
  onSetupStepChange
}) => {
  const navigate = useNavigate();

  return (
    <div className="my-6 bg-dark-900/50 rounded-lg p-6 border border-white/10 shadow-md">
      <div className="flex items-center justify-between mb-4">
        {/* Progress Bar */}
        <div className="w-full bg-dark-800/50 h-2 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-fiery to-cool rounded-full transition-all duration-500 ease-out"
            style={{ width: `${(setupStep / 4) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Step Indicators with Tab Integration */}
      <div className="flex justify-between mt-4">
        {[
          { step: 1, label: 'Basic Info', tab: 'setup', icon: FileText },
          { step: 2, label: 'Authentication', tab: 'authentication', icon: Key },
          { step: 3, label: 'Integration', tab: 'integration', icon: Code },
          { step: 4, label: 'Analytics', tab: 'analytics', icon: BarChart2 }
        ].map(({ step, label, tab, icon: Icon }) => {
          // Determine if this step should be highlighted based on both setupStep and activeTab
          const isActive = tab === activeTab;
          const isCompleted = setupStep > step;
          const isAccessible = setupStep >= step;

          return (
            <div
              key={step}
              className={`flex flex-col items-center cursor-pointer transition-all duration-300 ${
                isActive ? 'text-fiery scale-105' : isCompleted ? 'text-fiery/80' : 'text-white/40'
              } ${!isAccessible ? 'opacity-60 cursor-not-allowed' : 'hover:scale-105'}`}
              onClick={() => {
                if (isAccessible) {
                  onTabChange?.(tab);
                  onSetupStepChange?.(step);
                  if (toolId) {
                    navigate(`/partner/tools/${toolId}?tab=${tab}`, { replace: true });
                  }
                }
              }}
            >
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                  isActive
                    ? 'bg-fiery text-white shadow-lg shadow-fiery/20'
                    : isCompleted
                    ? 'bg-fiery/20 text-fiery border border-fiery'
                    : 'bg-dark-800 text-white/40 border border-white/10'
                }`}
              >
                <Icon className="w-5 h-5" />
              </div>
              <span className="text-sm font-medium">{label}</span>
              <span className="text-xs mt-1 opacity-80">Step {step}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default WizardProgressBar;
