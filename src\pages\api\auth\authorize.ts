/**
 * API Route for Auth Authorize Endpoint
 * 
 * This file redirects to the v1 authorize endpoint.
 */

import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Redirect to the v1 authorize endpoint
  const queryParams = new URLSearchParams(req.query as Record<string, string>).toString();
  res.redirect(`/api/v1/auth/authorize?${queryParams}`);
}
