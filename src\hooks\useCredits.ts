import { useState } from 'react';
import { 
  purchaseCredits as purchaseCreditsApi,
  useCredits as useCreditsApi,
  addBonusCredits as addBonusCreditsApi,
  refundCredits as refundCreditsApi,
  PurchaseCreditsOptions,
  UseCreditsOptions,
  CreditTransactionResult
} from '@/lib/credits';

export function useCredits() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Purchase credits for a user
   */
  const purchaseCredits = async (options: PurchaseCreditsOptions): Promise<CreditTransactionResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await purchaseCreditsApi(options);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      return { 
        success: false, 
        error: { message: error.message } 
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Use credits for a service
   */
  const useCredits = async (options: UseCreditsOptions): Promise<CreditTransactionResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await useCreditsApi(options);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      return { 
        success: false, 
        error: { message: error.message } 
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Add bonus credits to a user
   */
  const addBonusCredits = async (
    userId: string, 
    amount: number, 
    description?: string
  ): Promise<CreditTransactionResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await addBonusCreditsApi(userId, amount, description);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      return { 
        success: false, 
        error: { message: error.message } 
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refund credits to a user
   */
  const refundCredits = async (
    userId: string, 
    amount: number, 
    description?: string
  ): Promise<CreditTransactionResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await refundCreditsApi(userId, amount, description);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      return { 
        success: false, 
        error: { message: error.message } 
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    purchaseCredits,
    useCredits,
    addBonusCredits,
    refundCredits,
    isLoading,
    error
  };
}
