// Mock usage tracking system

// Mock hook for usage summaries
export const useUsageSummaries = (period: 'day' | 'week' | 'month' | 'year') => {
  // Mock usage data
  const totalCredits = 225;
  const usageByService = [
    { serviceId: 'chatgpt', serviceName: 'ChatGPT', credits: 120 },
    { serviceId: 'midjourney', serviceName: 'Midjourney', credits: 75 },
    { serviceId: 'claude', serviceName: 'Claude', credits: 30 }
  ];

  return { totalCredits, usageByService };
};
