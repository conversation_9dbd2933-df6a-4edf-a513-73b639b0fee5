// Patch for server.js to handle the RPC error more gracefully
// Replace the user data fetching section with this code

// Get user information from Supabase Auth
let userData = null;
try {
  // Try to get user from auth.users table using RPC
  const { data, error } = await supabase.rpc('get_auth_user_by_id', {
    user_id_param: authCodeData.user_id
  });

  if (error) {
    console.error('Error fetching user with RPC:', error);
    
    // Try to get user directly from the users table instead
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, display_name')
      .eq('id', authCodeData.user_id)
      .maybeSingle();
      
    if (!userError && userData) {
      userData = {
        id: userData.id,
        email: userData.email,
        name: userData.display_name || 'User',
        display_name: userData.display_name || 'User'
      };
      console.log('User data fetched from users table:', userData);
    } else {
      // Fallback: Try direct query to auth schema
      try {
        const { data: authData, error: authError } = await supabase.auth.admin.getUserById(
          authCodeData.user_id
        );

        if (authError) {
          console.error('Error fetching user with admin API:', authError);
        } else if (authData?.user) {
          userData = {
            id: authData.user.id,
            email: authData.user.email,
            name: authData.user.user_metadata?.name || 'User',
            display_name: authData.user.user_metadata?.name || 'User'
          };
          console.log('User data fetched from auth admin API:', userData);
        }
      } catch (authApiError) {
        console.error('Exception in auth API call:', authApiError);
      }
    }
  } else if (data && data.length > 0) {
    userData = {
      id: data[0].id,
      email: data[0].email,
      name: data[0].raw_user_meta_data?.name || 'User',
      display_name: data[0].raw_user_meta_data?.name || 'User'
    };
    console.log('User data fetched from RPC function:', userData);
  }
} catch (userError) {
  console.error('Exception fetching user information:', userError);
}

// If we couldn't get user data, use the user ID we have
if (!userData) {
  console.log('Using minimal user data from auth code');
  userData = {
    id: authCodeData.user_id,
    email: `user-${authCodeData.user_id.substring(0, 8)}@example.com`,
    name: `User ${authCodeData.user_id.substring(0, 8)}`,
    display_name: `User ${authCodeData.user_id.substring(0, 8)}`
  };
}

console.log('User data for token response:', userData);
