/**
 * Integration Adapters
 * 
 * This file contains the adapters for integrations.
 * Each adapter implements the IntegrationAdapter interface.
 */

import { IntegrationAdapter } from './types';

// Mock adapter implementation
const createMockAdapter = (id: string, partnerId: string): IntegrationAdapter => {
  return {
    id,
    partnerId,
    version: '1.0.0',
    
    authenticate: async (userId: string, credentials: any) => {
      console.log(`[${id}] Authenticating user ${userId}`);
      
      // Simulate authentication
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        expiresAt: new Date(Date.now() + 3600 * 1000) // 1 hour from now
      };
    },
    
    refreshAuth: async (userId: string, authData: any) => {
      console.log(`[${id}] Refreshing auth for user ${userId}`);
      
      // Simulate refreshing authentication
      await new Promise(resolve => setTimeout(resolve, 300));
      
      return {
        accessToken: 'mock-refreshed-access-token',
        refreshToken: 'mock-refreshed-refresh-token',
        expiresAt: new Date(Date.now() + 3600 * 1000) // 1 hour from now
      };
    },
    
    revokeAuth: async (userId: string, authData: any) => {
      console.log(`[${id}] Revoking auth for user ${userId}`);
      
      // Simulate revoking authentication
      await new Promise(resolve => setTimeout(resolve, 200));
      
      return true;
    },
    
    callApi: async (userId: string, endpoint: string, method: string, data?: any) => {
      console.log(`[${id}] Calling API endpoint ${endpoint} with method ${method} for user ${userId}`);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Return mock data based on endpoint
      switch (endpoint) {
        case 'health':
          return { status: 'healthy' };
        case 'user':
          return { id: userId, name: 'Mock User', email: '<EMAIL>' };
        case 'usage':
          return {
            timeSpentSeconds: 3600,
            apiCallsMade: 100,
            resourcesConsumed: 50,
            customMetric: 25
          };
        default:
          return { message: 'Mock API response' };
      }
    },
    
    getUsage: async (userId: string, startDate: Date, endDate: Date) => {
      console.log(`[${id}] Getting usage for user ${userId} from ${startDate.toISOString()} to ${endDate.toISOString()}`);
      
      // Simulate getting usage data
      await new Promise(resolve => setTimeout(resolve, 400));
      
      return {
        timeSpentSeconds: 7200,
        apiCallsMade: 200,
        resourcesConsumed: 100,
        customMetric: 50
      };
    },
    
    handleWebhook: async (event: string, payload: any) => {
      console.log(`[${id}] Handling webhook event ${event}`);
      
      // Simulate webhook handling
      await new Promise(resolve => setTimeout(resolve, 200));
      
      return { success: true, event, payload };
    }
  };
};

// Create adapters for all integrations
export const adapters: IntegrationAdapter[] = [
  createMockAdapter('chatgpt', 'openai'),
  createMockAdapter('dalle', 'openai'),
  createMockAdapter('midjourney', 'midjourney'),
  createMockAdapter('github-copilot', 'github'),
  createMockAdapter('descript', 'descript'),
  createMockAdapter('jasper', 'jasper'),
  createMockAdapter('runway', 'runway'),
  createMockAdapter('notion-ai', 'notion'),
  createMockAdapter('claude', 'anthropic'),
  createMockAdapter('stable-diffusion', 'stability-ai'),
  createMockAdapter('grammarly', 'grammarly'),
  createMockAdapter('synthesia', 'synthesia')
];
