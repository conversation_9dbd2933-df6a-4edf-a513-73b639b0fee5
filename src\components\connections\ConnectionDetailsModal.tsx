import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserConnection, getConnectionLogs } from '@/lib/connections';
import Modal from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ThemedBadge as Badge } from '@/components/ui/badge';
import { Loading } from '@/components/ui/loading';
import { 
  Loader2, 
  Info, 
  Clock, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  ExternalLink,
  Trash2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ConnectionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  tool: any;
  onDisconnect: () => void;
}

const ConnectionDetailsModal: React.FC<ConnectionDetailsModalProps> = ({
  isOpen,
  onClose,
  tool,
  onDisconnect
}) => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [connection, setConnection] = useState<any | null>(null);
  const [logs, setLogs] = useState<any[]>([]);

  useEffect(() => {
    const loadConnectionDetails = async () => {
      if (!user || !isOpen) return;
      
      setIsLoading(true);
      try {
        // Load connection details
        const connectionData = await getUserConnection(user.id, tool.id);
        setConnection(connectionData);
        
        // Load connection logs
        const logsData = await getConnectionLogs(user.id, tool.id);
        setLogs(logsData);
      } catch (error) {
        console.error('Error loading connection details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadConnectionDetails();
  }, [user, tool.id, isOpen]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <RefreshCw className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <Info className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get event type icon
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'create':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'update':
        return <RefreshCw className="h-4 w-4 text-blue-500" />;
      case 'delete':
        return <Trash2 className="h-4 w-4 text-red-500" />;
      case 'status_update':
        return <Info className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${tool.name} Connection Details`}
      size="lg"
      className="firenest-card"
      contentClassName="bg-dark-900"
    >
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loading size="md" text="Loading connection details..." />
        </div>
      ) : !connection ? (
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Connection Found</h3>
          <p className="text-white/70 mb-4">
            You don't have an active connection to {tool.name}
          </p>
          <Button
            onClick={onClose}
            className="bg-fiery hover:bg-fiery-600"
          >
            Close
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Connection Summary */}
          <div className="flex items-start gap-4">
            <div className="h-12 w-12 rounded-full bg-dark-800 flex items-center justify-center">
              {getStatusIcon(connection.status)}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-medium text-white">{tool.name}</h3>
                <Badge className={
                  connection.status === 'connected' ? "bg-green-500/20 text-green-400" :
                  connection.status === 'pending' ? "bg-yellow-500/20 text-yellow-400" :
                  connection.status === 'failed' ? "bg-red-500/20 text-red-400" :
                  "bg-gray-500/20 text-gray-400"
                }>
                  {connection.status.charAt(0).toUpperCase() + connection.status.slice(1)}
                </Badge>
              </div>
              <p className="text-white/70">{tool.description}</p>
            </div>
          </div>

          <Separator className="bg-white/10" />

          {/* Connection Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white/70">Authentication Method</h4>
              <div className="flex items-center gap-2">
                <Badge className="bg-white/10 text-white/70">
                  {connection.authMethod === 'oauth' ? 'OAuth' :
                   connection.authMethod === 'api_key' ? 'API Key' :
                   connection.authMethod === 'credentials' ? 'Login' :
                   connection.authMethod === 'ip_based' ? 'IP Based' :
                   'Unknown'}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white/70">Connection Status</h4>
              <div className="flex items-center gap-2">
                {getStatusIcon(connection.status)}
                <span className="text-white">
                  {connection.status === 'connected' ? 'Connected and ready to use' :
                   connection.status === 'pending' ? 'Connection in progress' :
                   connection.status === 'failed' ? 'Connection failed' :
                   connection.status === 'disconnected' ? 'Disconnected' :
                   'Unknown status'}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white/70">Connected Since</h4>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-white/50" />
                <span className="text-white">{formatDate(connection.createdAt)}</span>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-white/70">Last Used</h4>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-white/50" />
                <span className="text-white">
                  {connection.lastUsed ? formatRelativeTime(connection.lastUsed) : 'Never used'}
                </span>
              </div>
            </div>
          </div>

          <Separator className="bg-white/10" />

          {/* Connection History */}
          <div>
            <h4 className="text-sm font-medium text-white/70 mb-3">Connection History</h4>
            {logs.length === 0 ? (
              <p className="text-white/50 text-sm">No connection history available</p>
            ) : (
              <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2">
                {logs.slice(0, 10).map((log) => (
                  <div 
                    key={log.id} 
                    className="flex items-center gap-2 p-2 rounded-md bg-dark-800/50 border border-white/5"
                  >
                    {getEventIcon(log.eventType)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">
                          {log.eventType === 'create' ? 'Connection created' :
                           log.eventType === 'update' ? 'Connection updated' :
                           log.eventType === 'delete' ? 'Connection deleted' :
                           log.eventType === 'status_update' ? `Status changed to ${log.eventData?.status}` :
                           log.eventType}
                        </span>
                        <span className="text-xs text-white/50">
                          {formatRelativeTime(log.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                {logs.length > 10 && (
                  <p className="text-center text-xs text-white/50 pt-2">
                    Showing 10 of {logs.length} events
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-2">
            {connection.status === 'connected' && (
              <Button
                variant="outline"
                onClick={onDisconnect}
                className="border-red-500/30 text-red-400 hover:bg-red-500/10"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Disconnect
              </Button>
            )}
            <Button
              variant="outline"
              onClick={onClose}
              className="border-white/10 hover:bg-white/5"
            >
              Close
            </Button>
            {tool.website && (
              <Button
                className="bg-fiery hover:bg-fiery-600"
                onClick={() => window.open(tool.website, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Visit Website
              </Button>
            )}
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ConnectionDetailsModal;
