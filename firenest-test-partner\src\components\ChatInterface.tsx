import { useState, useEffect, useRef } from 'react';
import { generateChatResponse, Message } from '../lib/openrouter-api';
import { useFirenestAuth, reportUsageToFirenest } from '../lib/firenest-integration';

const ChatInterface = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: 'Hello! I\'m <PERSON><PERSON><PERSON>, your AI-powered content assistant. How can I help you today? You can ask me to write content, generate ideas, or help with editing.'
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [usageCount, setUsageCount] = useState(0);
  const [modelName, setModelName] = useState<string>('Phi 4 Reasoning Plus');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { userId, sessionId, token } = useFirenestAuth();

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Report usage to Firenest
  const reportUsage = async (action: string, quantity: number) => {
    if (userId && sessionId && token) {
      const success = await reportUsageToFirenest(
        userId,
        sessionId,
        token,
        action,
        quantity
      );

      if (success && action === 'chat_message') {
        setUsageCount(prev => prev + quantity);
      }

      return success;
    }
    return false;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim() || isLoading) return;

    // Add user message
    const userMessage: Message = { role: 'user', content: input };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Generate response using the default model (Microsoft Phi 4 Reasoning Plus)
      const response = await generateChatResponse([...messages, userMessage]);

      // Add assistant message
      const assistantMessage: Message = { role: 'assistant', content: response.content };
      setMessages(prev => [...prev, assistantMessage]);

      // Report usage to Firenest
      await reportUsage('chat_message', 1);

      // Log usage information
      if (response.usage) {
        console.log('Usage information:', response.usage);
      }
    } catch (error) {
      console.error('Error in chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-64px)] bg-dark-900">
      {/* Header with usage indicator */}
      <div className="bg-dark-800 border-b border-dark-600 p-3 flex justify-between items-center">
        <div className="text-light-600 text-sm flex items-center">
          <span className="font-medium text-primary">AtlasAI</span> - AI Content Assistant
          <span className="ml-2 px-2 py-1 bg-dark-700 rounded-md text-xs text-light-400">
            Using: {modelName}
          </span>
        </div>
        <div className="text-light-600 text-sm flex items-center">
          <span className="mr-2">Usage:</span>
          <span className="bg-dark-700 text-primary px-2 py-1 rounded-md font-medium">
            {usageCount} credits
          </span>
        </div>
      </div>

      {/* Chat messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[70%] rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-primary text-white'
                  : 'bg-dark-700 text-light border border-dark-600'
              }`}
            >
              {message.content}
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-[70%] rounded-lg p-3 bg-dark-700 text-light border border-dark-600">
              <div className="flex space-x-2">
                <div className="w-2 h-2 rounded-full bg-primary animate-bounce"></div>
                <div className="w-2 h-2 rounded-full bg-primary animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 rounded-full bg-primary animate-bounce" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input form */}
      <form onSubmit={handleSubmit} className="p-4 border-t border-dark-600 bg-dark-800">
        <div className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            className="input flex-1 bg-dark-700 border-dark-600 text-light"
            disabled={isLoading}
          />
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isLoading || !input.trim()}
          >
            Send
          </button>
        </div>
        <div className="mt-2 text-center text-light-600 text-xs">
          Powered by Firenest - Each message costs 1 credit
        </div>
      </form>
    </div>
  );
};

export default ChatInterface;