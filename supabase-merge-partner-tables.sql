-- Merge partners and partner_accounts tables
-- This script adds missing columns from partners to partner_accounts,
-- migrates data, and updates references

-- Step 1: Add missing columns to partner_accounts
ALTER TABLE partner_accounts
ADD COLUMN IF NOT EXISTS client_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS client_secret TEXT,
ADD COLUMN IF NOT EXISTS redirect_uris TEXT[],
ADD COLUMN IF NOT EXISTS website_url TEXT,
ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT TRUE;

-- Step 2: Update existing columns to match
-- Map website to website_url if website_url is NULL
UPDATE partner_accounts
SET website_url = website
WHERE website_url IS NULL AND website IS NOT NULL;

-- Step 3: Migrate data from partners to partner_accounts
-- This will insert partners that don't exist in partner_accounts
-- and update existing ones with OAuth information
DO $$
DECLARE
    partner_record RECORD;
BEGIN
    -- Check if partners table exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'partners'
    ) THEN
        -- Loop through each record in partners table
        FOR partner_record IN SELECT * FROM partners LOOP
            -- Check if a partner_account with the same ID exists
            IF EXISTS (SELECT 1 FROM partner_accounts WHERE id = partner_record.id) THEN
                -- Update existing partner_account with OAuth information
                UPDATE partner_accounts
                SET 
                    client_id = partner_record.client_id,
                    client_secret = partner_record.client_secret,
                    redirect_uris = partner_record.redirect_uris,
                    website_url = COALESCE(partner_record.website_url, website_url),
                    logo_url = COALESCE(partner_record.logo_url, logo_url),
                    description = COALESCE(partner_record.description, description),
                    active = partner_record.active,
                    updated_at = NOW()
                WHERE id = partner_record.id;
            ELSE
                -- Check if a partner_account with the same client_id exists (via api_key)
                IF EXISTS (SELECT 1 FROM partner_accounts WHERE api_key = partner_record.client_id) THEN
                    -- Update existing partner_account with OAuth information
                    UPDATE partner_accounts
                    SET 
                        client_id = partner_record.client_id,
                        client_secret = partner_record.client_secret,
                        redirect_uris = partner_record.redirect_uris,
                        website_url = COALESCE(partner_record.website_url, website_url),
                        logo_url = COALESCE(partner_record.logo_url, logo_url),
                        description = COALESCE(partner_record.description, description),
                        active = partner_record.active,
                        updated_at = NOW()
                    WHERE api_key = partner_record.client_id;
                ELSE
                    -- Insert new partner_account
                    INSERT INTO partner_accounts (
                        id,
                        name,
                        email,
                        company,
                        website,
                        website_url,
                        logo_url,
                        description,
                        status,
                        api_key,
                        client_id,
                        client_secret,
                        redirect_uris,
                        active,
                        created_at,
                        updated_at
                    ) VALUES (
                        partner_record.id,
                        partner_record.name,
                        'migrated-' || partner_record.client_id || '@firenest.com', -- Generate a placeholder email
                        partner_record.name, -- Use name as company
                        partner_record.website_url,
                        partner_record.website_url,
                        partner_record.logo_url,
                        partner_record.description,
                        CASE WHEN partner_record.active THEN 'active' ELSE 'suspended' END,
                        partner_record.client_id, -- Use client_id as api_key for compatibility
                        partner_record.client_id,
                        partner_record.client_secret,
                        partner_record.redirect_uris,
                        partner_record.active,
                        partner_record.created_at,
                        partner_record.updated_at
                    );
                END IF;
            END IF;
        END LOOP;
    END IF;
END $$;

-- Step 4: Update foreign key references
-- First, check if any tables reference the partners table
DO $$
DECLARE
    fk_record RECORD;
BEGIN
    FOR fk_record IN 
        SELECT
            tc.table_schema, 
            tc.constraint_name, 
            tc.table_name, 
            kcu.column_name, 
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM 
            information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'partners'
    LOOP
        -- For each foreign key reference, create a new one to partner_accounts
        -- and drop the old one
        RAISE NOTICE 'Found foreign key: %.%.% references partners.%', 
            fk_record.table_schema, fk_record.table_name, fk_record.column_name, fk_record.foreign_column_name;
            
        -- This would be where we'd update the references, but we'll handle this
        -- case by case in the application code instead since it's safer
    END LOOP;
END $$;

-- Step 5: Create a view for backward compatibility
CREATE OR REPLACE VIEW partners_view AS
SELECT
    id,
    name,
    description,
    website_url,
    logo_url,
    client_id,
    client_secret,
    redirect_uris,
    created_at,
    updated_at,
    active
FROM
    partner_accounts
WHERE
    client_id IS NOT NULL;

-- Step 6: Create a function to handle inserts to the view
CREATE OR REPLACE FUNCTION insert_into_partners_view()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO partner_accounts (
        id,
        name,
        email,
        company,
        website_url,
        logo_url,
        description,
        status,
        api_key,
        client_id,
        client_secret,
        redirect_uris,
        active,
        created_at,
        updated_at
    ) VALUES (
        COALESCE(NEW.id, uuid_generate_v4()),
        NEW.name,
        'migrated-' || NEW.client_id || '@firenest.com', -- Generate a placeholder email
        NEW.name, -- Use name as company
        NEW.website_url,
        NEW.logo_url,
        NEW.description,
        CASE WHEN NEW.active THEN 'active' ELSE 'suspended' END,
        NEW.client_id, -- Use client_id as api_key for compatibility
        NEW.client_id,
        NEW.client_secret,
        NEW.redirect_uris,
        NEW.active,
        COALESCE(NEW.created_at, NOW()),
        COALESCE(NEW.updated_at, NOW())
    )
    RETURNING
        id,
        name,
        description,
        website_url,
        logo_url,
        client_id,
        client_secret,
        redirect_uris,
        created_at,
        updated_at,
        active
    INTO NEW;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger for the view
DROP TRIGGER IF EXISTS insert_partners_view_trigger ON partners_view;
CREATE TRIGGER insert_partners_view_trigger
INSTEAD OF INSERT ON partners_view
FOR EACH ROW
EXECUTE FUNCTION insert_into_partners_view();

-- Step 7: Create a function to handle updates to the view
CREATE OR REPLACE FUNCTION update_partners_view()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE partner_accounts
    SET
        name = NEW.name,
        website_url = NEW.website_url,
        logo_url = NEW.logo_url,
        description = NEW.description,
        client_id = NEW.client_id,
        client_secret = NEW.client_secret,
        redirect_uris = NEW.redirect_uris,
        active = NEW.active,
        updated_at = COALESCE(NEW.updated_at, NOW())
    WHERE id = OLD.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger for updates to the view
DROP TRIGGER IF EXISTS update_partners_view_trigger ON partners_view;
CREATE TRIGGER update_partners_view_trigger
INSTEAD OF UPDATE ON partners_view
FOR EACH ROW
EXECUTE FUNCTION update_partners_view();

-- Note: We're not dropping the partners table yet to ensure backward compatibility
-- This can be done in a future migration after all code has been updated
