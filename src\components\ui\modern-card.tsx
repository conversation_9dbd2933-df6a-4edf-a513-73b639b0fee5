import React from 'react';
import { cn } from '@/lib/utils';

type IconColor = 'red' | 'green' | 'blue' | 'purple';
type BadgeColor = 'red' | 'green' | 'blue' | 'purple';

interface ModernCardProps {
  title: string;
  value: string | React.ReactNode;
  icon: React.ReactNode;
  iconColor?: IconColor;
  description?: string;
  badge?: string;
  badgeColor?: BadgeColor;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  footer?: React.ReactNode;
  className?: string;
  isLoading?: boolean;
}

export function ModernCard({
  title,
  value,
  icon,
  iconColor = 'red',
  description,
  badge,
  badgeColor = 'red',
  trend,
  footer,
  className,
  isLoading = false,
}: ModernCardProps) {
  // Map iconColor to gradient color
  const getGradientColor = () => {
    switch (iconColor) {
      case 'red':
        return 'from-fiery/20';
      case 'blue':
        return 'from-blue-500/20';
      case 'green':
        return 'from-green-500/20';
      case 'purple':
        return 'from-purple-500/20';
      default:
        return 'from-fiery/20';
    }
  };

  return (
    <div className={cn("firenest-card relative overflow-hidden", className)}>
      {/* Gradient effect from top right */}
      <div className={cn(
        "absolute top-0 right-0 w-32 h-32 bg-gradient-to-br",
        getGradientColor(),
        "to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"
      )}></div>

      <div className="flex items-center mb-1">
        <div className={cn("firenest-card-icon", `firenest-card-icon-${iconColor}`)}>
          {React.cloneElement(icon as React.ReactElement, { className: "h-3 w-3 text-white" })}
        </div>
        <span className="firenest-card-title">{title}</span>
      </div>
      <div className="mt-2">
        <div className="firenest-card-value">
          {isLoading ? (
            <div className="h-8 w-16 bg-white/5 animate-pulse rounded"></div>
          ) : (
            value
          )}
        </div>
        <div className="flex items-center mt-1">
          {description && (
            <div className="firenest-card-info">
              {description}
            </div>
          )}
          {trend && (
            <div className={cn("flex items-center text-xs", trend.isPositive ? "text-green-400" : "text-red-400")}>
              {trend.isPositive ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3 mr-1">
                  <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                  <polyline points="17 6 23 6 23 12"></polyline>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3 mr-1">
                  <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                  <polyline points="17 18 23 18 23 12"></polyline>
                </svg>
              )}
              <span>{trend.value}</span>
            </div>
          )}
          {badge && (
            <div className="ml-auto">
              <span className={cn("firenest-card-badge", `firenest-card-badge-${badgeColor}`)}>
                {badge}
              </span>
            </div>
          )}
        </div>
      </div>
      {footer && (
        <div className="mt-3 pt-3 border-t border-white/5">
          {footer}
        </div>
      )}
    </div>
  );
}

export default ModernCard;
