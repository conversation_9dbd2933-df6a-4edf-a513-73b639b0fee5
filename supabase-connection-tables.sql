-- Create user_connections table for tracking tool connections
CREATE TABLE IF NOT EXISTS user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  auth_method TEXT NOT NULL,
  status TEXT NOT NULL,
  connection_data JSONB,
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tool_id)
);

-- Create connection_logs table for tracking connection events
CREATE TABLE IF NOT EXISTS connection_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  event_type TEXT NOT NULL,
  event_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE user_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE connection_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for user_connections table
CREATE POLICY "Users can view their own connections" 
  ON user_connections FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connections" 
  ON user_connections FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own connections" 
  ON user_connections FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own connections" 
  ON user_connections FOR DELETE 
  USING (auth.uid() = user_id);

-- Create policies for connection_logs table
CREATE POLICY "Users can view their own connection logs" 
  ON connection_logs FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connection logs" 
  ON connection_logs FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS user_connections_user_id_idx ON user_connections(user_id);
CREATE INDEX IF NOT EXISTS user_connections_tool_id_idx ON user_connections(tool_id);
CREATE INDEX IF NOT EXISTS connection_logs_user_id_idx ON connection_logs(user_id);
CREATE INDEX IF NOT EXISTS connection_logs_tool_id_idx ON connection_logs(tool_id);
