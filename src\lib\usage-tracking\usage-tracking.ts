/**
 * Usage Tracking Service
 * 
 * This service tracks usage of third-party services and calculates credit consumption.
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  UsageEvent, 
  UsageSummary, 
  ServiceUsageConfig, 
  UserUsageLimits,
  UsageAlert,
  UsageReport,
  UsageEventType
} from './types';
import { serviceUsageConfigs } from './service-configs';
import { authBridge } from '../auth-bridge';

class UsageTrackingService {
  private usageEvents: UsageEvent[] = [];
  private serviceConfigs: Map<string, ServiceUsageConfig> = new Map();
  private userLimits: Map<string, UserUsageLimits> = new Map();
  private usageAlerts: UsageAlert[] = [];
  private eventListeners: Map<string, ((event: UsageEvent) => void)[]> = new Map();
  
  constructor() {
    // Load service configurations
    serviceUsageConfigs.forEach(config => {
      this.serviceConfigs.set(config.serviceId, config);
    });
    
    // Load usage events from storage
    this.loadUsageEvents();
    
    // Set up interval to periodically save usage events
    setInterval(() => this.saveUsageEvents(), 60000); // Every minute
    
    // Set up listener for auth bridge usage updates
    authBridge.onUsageUpdate(event => {
      this.trackActivity(
        event.userId,
        event.serviceId,
        event.sessionId,
        {
          timeSpentSeconds: event.metrics.timeSpentSeconds,
          apiCallsMade: event.metrics.apiCallsMade,
          resourcesConsumed: event.metrics.resourcesConsumed,
          customMetric: event.metrics.customMetric
        }
      );
    });
  }
  
  /**
   * Track a session start event
   */
  public trackSessionStart(userId: string, serviceId: string, sessionId: string): UsageEvent {
    return this.trackEvent(userId, serviceId, sessionId, 'session_start', {});
  }
  
  /**
   * Track a session end event
   */
  public trackSessionEnd(userId: string, serviceId: string, sessionId: string, metrics: any): UsageEvent {
    return this.trackEvent(userId, serviceId, sessionId, 'session_end', metrics);
  }
  
  /**
   * Track an activity event
   */
  public trackActivity(userId: string, serviceId: string, sessionId: string, metrics: any): UsageEvent {
    return this.trackEvent(userId, serviceId, sessionId, 'activity', metrics);
  }
  
  /**
   * Track an API call event
   */
  public trackApiCall(userId: string, serviceId: string, sessionId: string, count: number = 1, details?: any): UsageEvent {
    return this.trackEvent(userId, serviceId, sessionId, 'api_call', {
      apiCallsMade: count,
      details
    });
  }
  
  /**
   * Track a resource consumption event
   */
  public trackResourceConsumption(userId: string, serviceId: string, sessionId: string, count: number = 1, details?: any): UsageEvent {
    return this.trackEvent(userId, serviceId, sessionId, 'resource_consumption', {
      resourcesConsumed: count,
      details
    });
  }
  
  /**
   * Get usage summary for a user and service
   */
  public getUsageSummary(userId: string, serviceId: string, period: 'day' | 'week' | 'month' | 'all' = 'all'): UsageSummary {
    const now = new Date();
    let startDate: Date;
    
    // Determine start date based on period
    switch (period) {
      case 'day':
        startDate = new Date(now);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - now.getDay());
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'all':
      default:
        startDate = new Date(0); // Beginning of time
        break;
    }
    
    // Filter events by user, service, and time period
    const relevantEvents = this.usageEvents.filter(event => 
      event.userId === userId && 
      event.serviceId === serviceId && 
      event.timestamp >= startDate && 
      event.timestamp <= now
    );
    
    // Calculate metrics
    let timeSpentSeconds = 0;
    let apiCallsMade = 0;
    let resourcesConsumed = 0;
    let customMetric = 0;
    
    relevantEvents.forEach(event => {
      if (event.data.timeSpentSeconds) {
        timeSpentSeconds += event.data.timeSpentSeconds;
      }
      
      if (event.data.apiCallsMade) {
        apiCallsMade += event.data.apiCallsMade;
      }
      
      if (event.data.resourcesConsumed) {
        resourcesConsumed += event.data.resourcesConsumed;
      }
      
      if (event.data.customMetric) {
        customMetric += event.data.customMetric;
      }
    });
    
    // Calculate credits used
    const creditsUsed = this.calculateCredits(serviceId, {
      timeSpentSeconds,
      apiCallsMade,
      resourcesConsumed,
      customMetric
    });
    
    return {
      userId,
      serviceId,
      period,
      startDate,
      endDate: now,
      metrics: {
        timeSpentSeconds,
        apiCallsMade,
        resourcesConsumed,
        customMetric,
        creditsUsed
      }
    };
  }
  
  /**
   * Get usage summaries for all services used by a user
   */
  public getUserUsageSummaries(userId: string, period: 'day' | 'week' | 'month' | 'all' = 'all'): UsageSummary[] {
    // Get unique service IDs used by the user
    const serviceIds = new Set<string>();
    
    this.usageEvents.forEach(event => {
      if (event.userId === userId) {
        serviceIds.add(event.serviceId);
      }
    });
    
    // Get usage summary for each service
    const summaries: UsageSummary[] = [];
    
    serviceIds.forEach(serviceId => {
      summaries.push(this.getUsageSummary(userId, serviceId, period));
    });
    
    return summaries;
  }
  
  /**
   * Get total credits used by a user
   */
  public getTotalCreditsUsed(userId: string, period: 'day' | 'week' | 'month' | 'all' = 'all'): number {
    const summaries = this.getUserUsageSummaries(userId, period);
    
    return summaries.reduce((total, summary) => {
      return total + summary.metrics.creditsUsed;
    }, 0);
  }
  
  /**
   * Set usage limits for a user
   */
  public setUserLimits(userId: string, limits: UserUsageLimits): void {
    this.userLimits.set(userId, limits);
    this.saveUserLimits();
  }
  
  /**
   * Get usage limits for a user
   */
  public getUserLimits(userId: string): UserUsageLimits | undefined {
    return this.userLimits.get(userId);
  }
  
  /**
   * Check if a user has exceeded their usage limits
   */
  public checkUserLimits(userId: string): UsageAlert[] {
    const limits = this.userLimits.get(userId);
    
    if (!limits) {
      return [];
    }
    
    const alerts: UsageAlert[] = [];
    
    // Check daily limit
    if (limits.dailyLimit) {
      const dailyUsage = this.getTotalCreditsUsed(userId, 'day');
      
      if (dailyUsage >= limits.dailyLimit) {
        alerts.push(this.createAlert(userId, undefined, 'limit_reached', limits.dailyLimit, dailyUsage, 'Daily credit limit reached'));
      } else if (dailyUsage >= limits.dailyLimit * 0.8) {
        alerts.push(this.createAlert(userId, undefined, 'limit_approaching', limits.dailyLimit, dailyUsage, 'Approaching daily credit limit'));
      }
    }
    
    // Check weekly limit
    if (limits.weeklyLimit) {
      const weeklyUsage = this.getTotalCreditsUsed(userId, 'week');
      
      if (weeklyUsage >= limits.weeklyLimit) {
        alerts.push(this.createAlert(userId, undefined, 'limit_reached', limits.weeklyLimit, weeklyUsage, 'Weekly credit limit reached'));
      } else if (weeklyUsage >= limits.weeklyLimit * 0.8) {
        alerts.push(this.createAlert(userId, undefined, 'limit_approaching', limits.weeklyLimit, weeklyUsage, 'Approaching weekly credit limit'));
      }
    }
    
    // Check monthly limit
    if (limits.monthlyLimit) {
      const monthlyUsage = this.getTotalCreditsUsed(userId, 'month');
      
      if (monthlyUsage >= limits.monthlyLimit) {
        alerts.push(this.createAlert(userId, undefined, 'limit_reached', limits.monthlyLimit, monthlyUsage, 'Monthly credit limit reached'));
      } else if (monthlyUsage >= limits.monthlyLimit * 0.8) {
        alerts.push(this.createAlert(userId, undefined, 'limit_approaching', limits.monthlyLimit, monthlyUsage, 'Approaching monthly credit limit'));
      }
    }
    
    // Check service-specific limits
    if (limits.serviceLimits) {
      limits.serviceLimits.forEach(serviceLimit => {
        const serviceId = serviceLimit.serviceId;
        
        // Check daily limit
        if (serviceLimit.dailyLimit) {
          const dailyUsage = this.getUsageSummary(userId, serviceId, 'day').metrics.creditsUsed;
          
          if (dailyUsage >= serviceLimit.dailyLimit) {
            alerts.push(this.createAlert(userId, serviceId, 'limit_reached', serviceLimit.dailyLimit, dailyUsage, `Daily credit limit reached for ${serviceId}`));
          } else if (dailyUsage >= serviceLimit.dailyLimit * 0.8) {
            alerts.push(this.createAlert(userId, serviceId, 'limit_approaching', serviceLimit.dailyLimit, dailyUsage, `Approaching daily credit limit for ${serviceId}`));
          }
        }
        
        // Check weekly limit
        if (serviceLimit.weeklyLimit) {
          const weeklyUsage = this.getUsageSummary(userId, serviceId, 'week').metrics.creditsUsed;
          
          if (weeklyUsage >= serviceLimit.weeklyLimit) {
            alerts.push(this.createAlert(userId, serviceId, 'limit_reached', serviceLimit.weeklyLimit, weeklyUsage, `Weekly credit limit reached for ${serviceId}`));
          } else if (weeklyUsage >= serviceLimit.weeklyLimit * 0.8) {
            alerts.push(this.createAlert(userId, serviceId, 'limit_approaching', serviceLimit.weeklyLimit, weeklyUsage, `Approaching weekly credit limit for ${serviceId}`));
          }
        }
        
        // Check monthly limit
        if (serviceLimit.monthlyLimit) {
          const monthlyUsage = this.getUsageSummary(userId, serviceId, 'month').metrics.creditsUsed;
          
          if (monthlyUsage >= serviceLimit.monthlyLimit) {
            alerts.push(this.createAlert(userId, serviceId, 'limit_reached', serviceLimit.monthlyLimit, monthlyUsage, `Monthly credit limit reached for ${serviceId}`));
          } else if (monthlyUsage >= serviceLimit.monthlyLimit * 0.8) {
            alerts.push(this.createAlert(userId, serviceId, 'limit_approaching', serviceLimit.monthlyLimit, monthlyUsage, `Approaching monthly credit limit for ${serviceId}`));
          }
        }
      });
    }
    
    return alerts;
  }
  
  /**
   * Get usage alerts for a user
   */
  public getUserAlerts(userId: string): UsageAlert[] {
    return this.usageAlerts.filter(alert => alert.userId === userId);
  }
  
  /**
   * Acknowledge a usage alert
   */
  public acknowledgeAlert(alertId: string): boolean {
    const alertIndex = this.usageAlerts.findIndex(alert => alert.id === alertId);
    
    if (alertIndex === -1) {
      return false;
    }
    
    this.usageAlerts[alertIndex].acknowledged = true;
    return true;
  }
  
  /**
   * Generate a usage report for a user
   */
  public generateUsageReport(userId: string, period: 'day' | 'week' | 'month' = 'month'): UsageReport {
    const summaries = this.getUserUsageSummaries(userId, period);
    const totalCreditsUsed = summaries.reduce((total, summary) => total + summary.metrics.creditsUsed, 0);
    
    // Get service names
    const services = summaries.map(summary => {
      const serviceConfig = this.serviceConfigs.get(summary.serviceId);
      const serviceName = serviceConfig ? serviceConfig.unitName : summary.serviceId;
      
      return {
        serviceId: summary.serviceId,
        serviceName,
        timeSpentSeconds: summary.metrics.timeSpentSeconds,
        apiCallsMade: summary.metrics.apiCallsMade,
        resourcesConsumed: summary.metrics.resourcesConsumed,
        customMetric: summary.metrics.customMetric,
        creditsUsed: summary.metrics.creditsUsed
      };
    });
    
    // Calculate comparison with previous period
    let comparisonWithPrevious: { percentageChange: number; previousTotal: number } | undefined;
    
    const now = new Date();
    let previousStartDate: Date;
    let previousEndDate: Date;
    
    switch (period) {
      case 'day':
        previousStartDate = new Date(now);
        previousStartDate.setDate(previousStartDate.getDate() - 1);
        previousStartDate.setHours(0, 0, 0, 0);
        
        previousEndDate = new Date(previousStartDate);
        previousEndDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        previousStartDate = new Date(now);
        previousStartDate.setDate(previousStartDate.getDate() - now.getDay() - 7);
        previousStartDate.setHours(0, 0, 0, 0);
        
        previousEndDate = new Date(previousStartDate);
        previousEndDate.setDate(previousEndDate.getDate() + 6);
        previousEndDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        previousEndDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
        break;
    }
    
    // Calculate previous total
    const previousEvents = this.usageEvents.filter(event => 
      event.userId === userId && 
      event.timestamp >= previousStartDate && 
      event.timestamp <= previousEndDate
    );
    
    if (previousEvents.length > 0) {
      const previousTotal = this.calculateTotalCreditsFromEvents(previousEvents);
      
      if (previousTotal > 0) {
        const percentageChange = ((totalCreditsUsed - previousTotal) / previousTotal) * 100;
        
        comparisonWithPrevious = {
          percentageChange,
          previousTotal
        };
      }
    }
    
    return {
      userId,
      period,
      date: new Date(),
      services,
      totalCreditsUsed,
      comparisonWithPrevious
    };
  }
  
  /**
   * Register an event listener
   */
  public addEventListener(eventType: UsageEventType | 'all', callback: (event: UsageEvent) => void): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    
    this.eventListeners.get(eventType)!.push(callback);
    
    // Return a function to remove the listener
    return () => {
      const listeners = this.eventListeners.get(eventType);
      
      if (listeners) {
        this.eventListeners.set(
          eventType,
          listeners.filter(listener => listener !== callback)
        );
      }
    };
  }
  
  // Private methods
  
  private trackEvent(
    userId: string,
    serviceId: string,
    sessionId: string,
    type: UsageEventType,
    data: any
  ): UsageEvent {
    const event: UsageEvent = {
      id: uuidv4(),
      userId,
      serviceId,
      sessionId,
      timestamp: new Date(),
      type,
      data
    };
    
    // Add to events array
    this.usageEvents.push(event);
    
    // Save events
    this.saveUsageEvents();
    
    // Emit event
    this.emitEvent(event);
    
    // Check limits
    this.checkUserLimits(userId);
    
    return event;
  }
  
  private calculateCredits(serviceId: string, metrics: any): number {
    const serviceConfig = this.serviceConfigs.get(serviceId);
    
    if (!serviceConfig) {
      return 0;
    }
    
    let units = 0;
    
    switch (serviceConfig.metricType) {
      case 'time':
        // Convert seconds to minutes and round up
        units = Math.ceil(metrics.timeSpentSeconds / 60);
        break;
      case 'api_calls':
        units = metrics.apiCallsMade;
        break;
      case 'resources':
        units = metrics.resourcesConsumed;
        break;
      case 'custom':
        units = metrics.customMetric || 0;
        break;
    }
    
    // Apply minimum usage if specified
    if (serviceConfig.minimumUsage && units < serviceConfig.minimumUsage) {
      units = serviceConfig.minimumUsage;
    }
    
    // Calculate credits
    return units * serviceConfig.costPerUnit;
  }
  
  private calculateTotalCreditsFromEvents(events: UsageEvent[]): number {
    // Group events by service
    const serviceMetrics: Map<string, any> = new Map();
    
    events.forEach(event => {
      if (!serviceMetrics.has(event.serviceId)) {
        serviceMetrics.set(event.serviceId, {
          timeSpentSeconds: 0,
          apiCallsMade: 0,
          resourcesConsumed: 0,
          customMetric: 0
        });
      }
      
      const metrics = serviceMetrics.get(event.serviceId);
      
      if (event.data.timeSpentSeconds) {
        metrics.timeSpentSeconds += event.data.timeSpentSeconds;
      }
      
      if (event.data.apiCallsMade) {
        metrics.apiCallsMade += event.data.apiCallsMade;
      }
      
      if (event.data.resourcesConsumed) {
        metrics.resourcesConsumed += event.data.resourcesConsumed;
      }
      
      if (event.data.customMetric) {
        metrics.customMetric += event.data.customMetric;
      }
      
      serviceMetrics.set(event.serviceId, metrics);
    });
    
    // Calculate credits for each service
    let totalCredits = 0;
    
    serviceMetrics.forEach((metrics, serviceId) => {
      totalCredits += this.calculateCredits(serviceId, metrics);
    });
    
    return totalCredits;
  }
  
  private createAlert(
    userId: string,
    serviceId: string | undefined,
    type: 'limit_approaching' | 'limit_reached' | 'unusual_activity',
    threshold: number,
    currentUsage: number,
    message: string
  ): UsageAlert {
    const alert: UsageAlert = {
      id: uuidv4(),
      userId,
      serviceId,
      timestamp: new Date(),
      type,
      threshold,
      currentUsage,
      message,
      acknowledged: false
    };
    
    // Add to alerts array
    this.usageAlerts.push(alert);
    
    return alert;
  }
  
  private emitEvent(event: UsageEvent): void {
    // Emit to type-specific listeners
    const typeListeners = this.eventListeners.get(event.type);
    
    if (typeListeners) {
      typeListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
    
    // Emit to 'all' listeners
    const allListeners = this.eventListeners.get('all');
    
    if (allListeners) {
      allListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }
  
  private saveUsageEvents(): void {
    try {
      localStorage.setItem('firenest-usage-events', JSON.stringify(this.usageEvents));
    } catch (error) {
      console.error('Error saving usage events:', error);
    }
  }
  
  private loadUsageEvents(): void {
    try {
      const eventsJson = localStorage.getItem('firenest-usage-events');
      
      if (eventsJson) {
        const events = JSON.parse(eventsJson) as UsageEvent[];
        
        // Convert string dates back to Date objects
        events.forEach(event => {
          event.timestamp = new Date(event.timestamp);
        });
        
        this.usageEvents = events;
      }
    } catch (error) {
      console.error('Error loading usage events:', error);
    }
  }
  
  private saveUserLimits(): void {
    try {
      const limitsArray = Array.from(this.userLimits.entries());
      localStorage.setItem('firenest-user-limits', JSON.stringify(limitsArray));
    } catch (error) {
      console.error('Error saving user limits:', error);
    }
  }
  
  private loadUserLimits(): void {
    try {
      const limitsJson = localStorage.getItem('firenest-user-limits');
      
      if (limitsJson) {
        const limitsArray = JSON.parse(limitsJson) as [string, UserUsageLimits][];
        
        limitsArray.forEach(([userId, limits]) => {
          this.userLimits.set(userId, limits);
        });
      }
    } catch (error) {
      console.error('Error loading user limits:', error);
    }
  }
}

// Create and export a singleton instance
export const usageTracking = new UsageTrackingService();
