<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Firenest Sandbox - Pricing Intelligence Platform</title>
    <meta name="description" content="Advanced pricing intelligence and simulation platform for enterprise decision-making" />
    
    <!-- Preconnect to improve performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Inter font for modern UI -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon and app icons -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#1e40af" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Firenest Sandbox - Pricing Intelligence Platform" />
    <meta property="og:description" content="Advanced pricing intelligence and simulation platform for enterprise decision-making" />
    <meta property="og:image" content="/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Firenest Sandbox - Pricing Intelligence Platform" />
    <meta property="twitter:description" content="Advanced pricing intelligence and simulation platform for enterprise decision-making" />
    <meta property="twitter:image" content="/og-image.png" />
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    
    <!-- Prevent zoom on mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
