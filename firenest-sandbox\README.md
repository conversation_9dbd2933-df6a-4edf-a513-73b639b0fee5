# Firenest Sandbox - Pricing Intelligence Platform

A standalone AWS-based infrastructure for secure pricing model simulation and analysis.

## Overview

The Firenest Sandbox is a completely separate infrastructure from the main Firenest application, designed to handle sensitive customer data for pricing intelligence and simulation. It implements absolute data isolation, zero-trust architecture, and SOC 2 compliance measures.

## Architecture

### Infrastructure Components
- **AWS VPC** with private and public subnets
- **AWS RDS PostgreSQL** for secure data storage
- **AWS S3** with server-side encryption for file storage
- **AWS SQS** for asynchronous job processing
- **AWS ECS on Fargate** for containerized services
- **AWS CloudWatch** for monitoring and logging

### Security Features
- Zero-trust architecture with authentication on every request
- Absolute data isolation between customers
- Immutable audit trails for all actions
- Server-side encryption for all data at rest
- Pre-signed URLs for secure file uploads

## Development Phases

### Phase 0: Foundation & Secure Scaffolding ✅
- Infrastructure as Code (Terraform)
- Identity & Access Management
- Database schema setup
- API & Frontend shell

### Phase 1: Secure Data Ingestion
- Data upload initiation API
- Frontend upload component
- Background validation jobs

### Phase 2: Pricing Model Builder
- Pricing model schema
- CRUD APIs for models
- Frontend model builder UI

### Phase 3: Simulation Engine Backend
- Simulation initiation
- Background simulation worker
- Results storage

### Phase 4: Results Visualization & Reporting
- Results API
- Interactive dashboard
- Export capabilities

## Getting Started

### Prerequisites
- AWS CLI configured with appropriate permissions
- Terraform >= 1.0
- Node.js >= 18.0
- PostgreSQL client (for local development)
- Docker (optional, for containerized deployment)

### 1. Infrastructure Setup

```bash
cd infrastructure
terraform init
terraform plan
terraform apply
```

This will create:
- VPC with public/private subnets
- RDS PostgreSQL instance with encryption
- S3 bucket with server-side encryption
- SQS queues for background processing
- Security groups and IAM roles

### 2. Database Setup

```bash
# Connect to your RDS instance and run the schema
psql -h <rds-endpoint> -U sandbox_admin -d firenest_sandbox -f database/schema.sql
```

### 3. Backend API Setup

```bash
cd backend
npm install

# Copy environment template
cp .env.example .env

# Update .env with your AWS and database credentials
# Start the API server
npm run dev

# In another terminal, start the validation worker
npm run dev:worker
```

### 4. Frontend Setup

```bash
cd frontend
npm install

# Start the development server
npm run dev
```

The application will be available at `http://localhost:3000`

### Environment Configuration

Create a `.env` file in the backend directory with the following variables:

```bash
# Database Configuration
DB_HOST=your-rds-endpoint : aws-0-ap-south-1.pooler.supabase.com
DB_PORT=5432 : 6543
DB_NAME=firenest_sandbox : postgres
DB_USERNAME=sandbox_admin : postgres
DB_PASSWORD=your-secure-password : thqkwadinfbnopoyxitj
DB_SSL=true

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_BUCKET_NAME=your-s3-bucket
SQS_QUEUE_URL=your-sqs-queue-url
SQS_DLQ_URL=your-dlq-url

# Authentication
JWT_SECRET=your-32-character-secret-key
AUTH_PROVIDER_TYPE=auth0
AUTH_PROVIDER_DOMAIN=your-auth0-domain
AUTH_PROVIDER_CLIENT_ID=your-client-id
AUTH_PROVIDER_CLIENT_SECRET=your-client-secret

# Security
ENCRYPTION_KEY=your-32-character-encryption-key
HASH_SALT=12

# Application
NODE_ENV=development
PORT=3001
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Logging
LOG_LEVEL=info
CLOUDWATCH_LOG_GROUP=/aws/ecs/firenest-sandbox
```

Create a `.env` file in the frontend directory:

```bash
VITE_API_URL=http://localhost:3001/api/v1
```

## Phase 1: Secure Data Ingestion ✅

### Features Implemented

#### 🔒 **Enterprise Security**
- **Zero-trust architecture** with JWT authentication on every request
- **Row Level Security (RLS)** for absolute data isolation between customers
- **End-to-end encryption** with S3 server-side encryption and HTTPS
- **Immutable audit trails** for all user actions and data access
- **SOC 2 compliance** measures throughout the platform

#### 📁 **File Upload System**
- **Drag-and-drop interface** with real-time progress tracking
- **Pre-signed S3 URLs** for secure direct uploads
- **File type validation** (CSV, JSON, XLSX) with size limits
- **Multi-file upload** support with individual progress tracking
- **Automatic retry** mechanisms for failed uploads

#### 🔍 **Background Validation Engine**
- **Asynchronous processing** using SQS queues for scalability
- **Comprehensive data validation** with business rule checks
- **Data quality analysis** including completeness, consistency, and accuracy metrics
- **Detailed error reporting** with row-level validation feedback
- **Real-time status updates** with WebSocket-like polling

#### 📊 **Data Quality Insights**
- **Interactive validation reports** with drill-down capabilities
- **Data quality scoring** across multiple dimensions
- **Performance metrics** including processing time and file statistics
- **Actionable recommendations** for data improvement
- **Visual progress indicators** throughout the validation process

#### 🎨 **User Experience Excellence**
- **Firenest design system** integration with consistent styling
- **Responsive design** optimized for all screen sizes
- **Real-time feedback** with toast notifications and progress bars
- **Intuitive navigation** with clear status indicators
- **Professional loading states** and error handling

### Business Value Delivered

#### **Customer Confidence**
- **Transparent processing** with detailed status updates builds trust
- **Professional UI/UX** demonstrates platform reliability
- **Security-first approach** addresses enterprise compliance needs
- **Real-time feedback** keeps users engaged during processing

#### **Operational Excellence**
- **Scalable architecture** handles large datasets without blocking UI
- **Comprehensive error handling** reduces support burden
- **Audit trails** support compliance and troubleshooting
- **Performance monitoring** enables proactive optimization

#### **Competitive Advantages**
- **Zero-trust security** as a key differentiator
- **SOC 2 compliance** enables enterprise sales
- **Real-time processing** improves user experience
- **Data quality insights** add analytical value beyond basic upload

## Security Compliance

This system is designed to meet SOC 2 Type II compliance requirements:
- **CC6.1** Infrastructure Security
- **CC6.2** Access Control
- **CC6.3** Authentication
- **CC6.6** Logical Access Security
- **CC6.7** Data Encryption in Transit
- **CC6.8** Secure Software Development
- **CC7.1** System Operations
- **CC7.2** Change Management
- **A1.2** Data Integrity
- **CC3.2** Audit Trails

## Data Isolation

Each customer's data is completely isolated through:
- Workspace-based data segregation
- Row-level security policies
- Encrypted storage with customer-specific keys
- Audit trails for all data access

## Phase 2: Pricing Model Builder ✅

### Features Implemented

#### **🎨 Visual Model Builder Interface**
- **Drag-and-drop component palette** with intuitive pricing building blocks
- **Interactive model canvas** with visual component arrangement and reordering
- **Real-time component editor** with validation and configuration options
- **Live model preview** with sample calculations and revenue scenarios
- **Professional component library** including base fees, usage rates, tiers, and caps

#### **💼 Component-Based Architecture**
- **Base Fee Components** - Fixed monthly/yearly charges with currency support
- **Per-Unit Rate Components** - Usage-based pricing with custom metrics
- **Tiered Rate Components** - Progressive pricing with unlimited tier support
- **Minimum/Maximum Fee Components** - Revenue protection and customer caps
- **Flexible configuration** with real-time validation and error handling

#### **📊 Business Intelligence Features**
- **Sample calculation engine** with detailed breakdown visualization
- **Revenue scenario modeling** across different usage patterns
- **Cost-per-unit analysis** with annual revenue projections
- **Model comparison tools** for A/B testing pricing strategies
- **Professional preview interface** for stakeholder presentations

#### **🔧 Enterprise Model Management**
- **Project-based organization** with workspace-level access controls
- **Model versioning** and duplication for iterative testing
- **Comprehensive model dashboard** with cross-project analytics
- **Advanced filtering** and search across all pricing models
- **Audit trails** for model changes and business decisions

### Business Value Delivered

#### **Revenue Optimization**
- **Visual pricing strategy creation** reduces time-to-market for new pricing
- **Scenario modeling** enables data-driven pricing decisions
- **Component reusability** accelerates pricing experimentation
- **Real-time calculations** provide immediate feedback on revenue impact

#### **Stakeholder Alignment**
- **Professional model previews** facilitate executive buy-in
- **Clear component breakdown** improves cross-team understanding
- **Revenue projections** support business planning and forecasting
- **Visual interface** reduces technical barriers for business users

#### **Competitive Advantages**
- **No-code pricing creation** democratizes pricing strategy development
- **Enterprise-grade model management** scales with organizational growth
- **Real-time validation** prevents pricing configuration errors
- **Comprehensive analytics** provide insights beyond basic modeling

## Phase 3: Simulation Engine Backend ✅

### Features Implemented

#### **🚀 High-Performance Simulation Processing**
- **Scalable SQS-based job queue** for enterprise-grade background processing
- **Multi-model simulation engine** processing customer data through multiple pricing models simultaneously
- **Real-time progress tracking** with detailed status updates and percentage completion
- **Streaming data processing** handling large customer datasets without memory constraints
- **Automatic retry mechanisms** and comprehensive error handling for production reliability

#### **📊 Advanced Analytics & Revenue Intelligence**
- **Comprehensive revenue analysis** with total revenue, customer count, and per-customer metrics
- **Statistical insights** including median, quartiles, min/max revenue calculations
- **Customer segmentation analysis** showing revenue distribution across different customer types
- **Model comparison tools** enabling A/B testing of pricing strategies with detailed breakdowns
- **Revenue optimization recommendations** based on simulation results and data patterns

#### **💼 Business-Critical Simulation Features**
- **Accurate revenue projections** using real customer data and validated pricing models
- **Scenario comparison dashboard** for strategic pricing decision making
- **Sensitivity analysis** testing pricing impact across different usage patterns
- **Component-level breakdown** showing how each pricing component contributes to total revenue
- **Professional results visualization** suitable for executive presentations and board meetings

#### **⚡ Enterprise Performance & Scalability**
- **Concurrent simulation processing** handling multiple simulations without resource conflicts
- **Efficient background workers** using AWS SQS for reliable job processing
- **Database optimization** with proper indexing and query performance tuning
- **Memory-efficient processing** streaming large datasets without loading everything into memory
- **Horizontal scaling capability** supporting increased load through additional worker instances

#### **📈 Results Visualization & Reporting**
- **Interactive analytics dashboard** with multiple view modes (overview, models, customers, insights)
- **Real-time progress monitoring** with detailed model-by-model processing status
- **Professional result presentation** with charts, metrics, and actionable insights
- **Export functionality** for simulation results and comprehensive reports
- **Stakeholder-friendly summaries** highlighting key findings and recommendations

### Business Value Delivered

#### **Strategic Decision Making**
- **Data-driven pricing decisions** based on actual customer data and revenue projections
- **Risk mitigation** through comprehensive scenario testing before implementing pricing changes
- **Revenue optimization** identifying the highest-performing pricing models for maximum profitability
- **Competitive advantage** through sophisticated pricing intelligence and market analysis

#### **Operational Excellence**
- **Automated processing** reducing manual effort and human error in pricing analysis
- **Scalable infrastructure** supporting business growth without performance degradation
- **Real-time insights** enabling rapid response to market changes and opportunities
- **Audit trails** providing complete transparency for compliance and decision tracking

#### **Customer Success**
- **Professional presentation** of complex pricing analysis in easily digestible formats
- **Actionable recommendations** providing clear next steps for pricing optimization
- **Confidence building** through transparent methodology and detailed result breakdowns
- **Time-to-insight** dramatically reduced from weeks to hours for pricing analysis

### **🔄 Platform Completion**

With Phase 3 complete, Firenest Sandbox now provides a **complete end-to-end pricing intelligence platform**:

1. **Phase 1: Secure Data Ingestion** - Enterprise-grade data upload and validation
2. **Phase 2: Pricing Model Builder** - Visual pricing strategy creation and management
3. **Phase 3: Simulation Engine** - High-performance revenue analysis and optimization

The platform is now ready for **production deployment** and **customer onboarding**, providing:
- **Complete pricing workflow** from data upload to strategic recommendations
- **Enterprise security** with SOC 2 compliance and zero-trust architecture
- **Scalable performance** supporting large datasets and concurrent users
- **Professional user experience** suitable for executive and technical users alike

## Support

For technical support or questions, please refer to the developer guide in the `docs/` directory.
