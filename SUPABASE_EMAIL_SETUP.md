# Supabase Email Verification Setup

This document provides instructions for setting up email verification in Supabase for the Firenest application.

## Prerequisites

1. Access to the Supabase project dashboard
2. Admin privileges to modify authentication settings

## Steps to Configure Email Verification

### 1. Set Up SMTP Credentials

Supabase needs SMTP credentials to send emails. Follow these steps:

1. Log in to your Supabase dashboard
2. Navigate to **Authentication** > **<PERSON>ail Templates**
3. Click on **SMTP Settings**
4. Enter your SMTP credentials:
   - **Host**: Your SMTP server (e.g., smtp.gmail.com)
   - **Port**: SMTP port (e.g., 587 for TLS)
   - **Username**: Your email address
   - **Password**: Your email password or app password
   - **Sender Name**: Firenest
   - **Sender Email**: <EMAIL> (or your preferred sender email)

### 2. Configure Email Templates

1. In the Supabase dashboard, go to **Authentication** > **Email Templates**
2. Select the **Confirmation** template
3. Replace the default template with the custom HTML template from `email-templates/confirmation.html`
4. Update the subject line to: "Verify your email for Firenest"
5. Save the changes

### 3. Configure Authentication Settings

1. Go to **Authentication** > **Settings**
2. Under **Email Auth**, ensure the following settings:
   - **Enable Email Signup**: Enabled
   - **Enable Email Confirmations**: Enabled
   - **Secure Email Change**: Enabled
   - **Confirmation URL Custom Domain**: Your application domain (e.g., https://firenest.io)
   - **Redirect URLs**: Add your application URLs (e.g., https://firenest.io/verify-email-confirm)

### 4. Test the Email Verification Flow

1. Create a test user account through your application
2. Check if the verification email is received
3. Click the verification link and ensure it redirects to the verification confirmation page
4. Verify that the user can log in after email confirmation

## Troubleshooting

If emails are not being sent:

1. **Check SMTP Settings**: Verify that your SMTP credentials are correct
2. **Check Spam Folder**: Verification emails might be filtered as spam
3. **Check Supabase Logs**: Go to **Database** > **Logs** to check for any errors
4. **Test SMTP Connection**: Use the "Test" button in the SMTP settings to verify the connection
5. **Check Rate Limits**: Some email providers have sending limits

## Using a Third-Party Email Service (Alternative)

If Supabase's built-in email doesn't work for you, consider using:

1. **SendGrid**: Create an account, set up SMTP credentials, and use them in Supabase
2. **Mailgun**: Similar to SendGrid, provides reliable email delivery
3. **Amazon SES**: AWS's email service, good for high volume

## Important Notes

- The redirect URL in the code (`emailRedirectTo`) must match the allowed redirect URLs in Supabase settings
- Email templates use Go's template syntax for variables (e.g., `{{ .ConfirmationURL }}`)
- For production, use a dedicated email address rather than a personal one
- Consider implementing a custom email verification solution if Supabase's doesn't meet your needs
