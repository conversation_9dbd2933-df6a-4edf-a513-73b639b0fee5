import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { notify } from '@/components/ui/notification-system';
import DebugConsole from '@/components/error/DebugConsole';
import { logError, ErrorSeverity, ErrorCategory } from '@/lib/error-utils';

interface DebugContextType {
  isDebugMode: boolean;
  toggleDebugMode: () => void;
  openDebugConsole: () => void;
  logDebugInfo: (message: string, data?: any) => void;
  logDebugWarning: (message: string, data?: any) => void;
  logDebugError: (message: string, error?: any) => void;
}

const DebugContext = createContext<DebugContextType>({
  isDebugMode: false,
  toggleDebugMode: () => {},
  openDebugConsole: () => {},
  logDebugInfo: () => {},
  logDebugWarning: () => {},
  logDebugError: () => {},
});

export const useDebug = () => useContext(DebugContext);

interface DebugProviderProps {
  children: React.ReactNode;
}

export const DebugProvider: React.FC<DebugProviderProps> = ({ children }) => {
  const [isDebugMode, setIsDebugMode] = useState(false);
  const [isDebugConsoleOpen, setIsDebugConsoleOpen] = useState(false);

  // Initialize debug mode from localStorage
  useEffect(() => {
    const storedDebugMode = localStorage.getItem('firenest-debug-mode');
    if (storedDebugMode === 'true') {
      setIsDebugMode(true);
    }

    // Set up keyboard shortcut for debug console (Ctrl+Shift+D)
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        toggleDebugMode();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Save debug mode to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('firenest-debug-mode', isDebugMode.toString());

    if (isDebugMode) {
      console.log('%cFirenest Debug Mode Enabled', 'color: #FF4500; font-size: 14px; font-weight: bold;');
      notify.info('Debug mode enabled', {
        title: 'Developer Mode',
        description: 'Press Ctrl+Shift+D to open debug console',
        duration: 4000
      });
    }
  }, [isDebugMode]);

  const toggleDebugMode = useCallback(() => {
    setIsDebugMode(prev => !prev);
    if (!isDebugMode) {
      setIsDebugConsoleOpen(true);
    }
  }, [isDebugMode]);

  const openDebugConsole = useCallback(() => {
    setIsDebugConsoleOpen(true);
  }, []);

  const closeDebugConsole = useCallback(() => {
    setIsDebugConsoleOpen(false);
  }, []);

  const logDebugInfo = useCallback((message: string, data?: any) => {
    if (isDebugMode) {
      console.log(`%c[DEBUG INFO] ${message}`, 'color: #1E90FF', data || '');
      logError(
        { message },
        null,
        ErrorSeverity.INFO,
        ErrorCategory.UNKNOWN,
        { additionalData: data }
      );
    }
  }, [isDebugMode]);

  const logDebugWarning = useCallback((message: string, data?: any) => {
    if (isDebugMode) {
      console.warn(`%c[DEBUG WARNING] ${message}`, 'color: #FFA500', data || '');
      logError(
        { message },
        null,
        ErrorSeverity.WARNING,
        ErrorCategory.UNKNOWN,
        { additionalData: data }
      );
    }
  }, [isDebugMode]);

  const logDebugError = useCallback((message: string, error?: any) => {
    if (isDebugMode) {
      console.error(`%c[DEBUG ERROR] ${message}`, 'color: #FF4500', error || '');
      logError(
        error || { message },
        null,
        ErrorSeverity.ERROR,
        ErrorCategory.UNKNOWN
      );
    }
  }, [isDebugMode]);

  return (
    <DebugContext.Provider
      value={{
        isDebugMode,
        toggleDebugMode,
        openDebugConsole,
        logDebugInfo,
        logDebugWarning,
        logDebugError,
      }}
    >
      {children}
      {isDebugConsoleOpen && <DebugConsole isOpen={isDebugConsoleOpen} onClose={closeDebugConsole} />}
    </DebugContext.Provider>
  );
};

export default DebugProvider;
