/**
 * Database Configuration
 * PostgreSQL connection with connection pooling and security
 * SOC 2 Alignment: CC6.6 (Logical Access Security)
 */

import { Pool, PoolClient } from 'pg';
import { config } from './environment';
import { logger } from '@/utils/logger';

let pool: Pool;

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl: boolean | { rejectUnauthorized: boolean };
  max: number;
  idleTimeoutMillis: number;
  connectionTimeoutMillis: number;
}

export function createDatabaseConfig(): DatabaseConfig {
  return {
    host: config.database.host,
    port: config.database.port,
    database: config.database.name,
    user: config.database.username,
    password: config.database.password,
    ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
    max: config.database.maxConnections,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: config.database.connectionTimeout,
  };
}

export async function connectDatabase(): Promise<void> {
  try {
    const dbConfig = createDatabaseConfig();
    
    pool = new Pool(dbConfig);
    
    // Test the connection
    const client = await pool.connect();
    
    // Set up session configuration for RLS
    await client.query('SET row_security = on');
    
    client.release();
    
    logger.info('Database connection pool created successfully');
    
    // Handle pool errors
    pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
    
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }
}

export function getPool(): Pool {
  if (!pool) {
    throw new Error('Database pool not initialized. Call connectDatabase() first.');
  }
  return pool;
}

export async function getClient(): Promise<PoolClient> {
  if (!pool) {
    throw new Error('Database pool not initialized. Call connectDatabase() first.');
  }
  return await pool.connect();
}

export async function query(text: string, params?: any[]): Promise<any> {
  const client = await getClient();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getClient();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Set user context for Row Level Security
export async function setUserContext(client: PoolClient, authProviderId: string): Promise<void> {
  await client.query('SELECT set_config($1, $2, true)', ['app.current_user_auth_id', authProviderId]);
}

// Helper function to execute queries with user context
export async function queryWithUserContext(
  authProviderId: string,
  text: string,
  params?: any[]
): Promise<any> {
  const client = await getClient();
  try {
    await setUserContext(client, authProviderId);
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// Helper function for transactions with user context
export async function transactionWithUserContext<T>(
  authProviderId: string,
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getClient();
  try {
    await client.query('BEGIN');
    await setUserContext(client, authProviderId);
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    logger.info('Database connection pool closed');
  }
}

// Database health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const result = await query('SELECT 1 as health_check');
    return result.rows[0]?.health_check === 1;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}
