import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { Flame, Shield, AlertTriangle, CheckCircle, XCircle, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface PartnerInfo {
  id: string;
  name: string;
  company?: string;
  logoUrl?: string;
  website?: string;
  scopes?: string[];
}

interface AuthRequest {
  client_id: string;
  redirect_uri: string;
  state: string;
  scope: string;
  partner_id: string;
  partner_name: string;
}

export default function GoogleStyleAuthorize() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partnerInfo, setPartnerInfo] = useState<PartnerInfo | null>(null);
  const [authRequest, setAuthRequest] = useState<AuthRequest | null>(null);
  const [scopes, setScopes] = useState<string[]>([]);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    const handleAuthorization = async () => {
      try {
        // Get the request parameters from the URL
        const { request } = router.query;

        if (!request) {
          setError('Missing request parameters');
          setLoading(false);
          return;
        }

        // Parse the request parameters
        const parsedRequest = JSON.parse(request as string);
        setAuthRequest(parsedRequest);

        // Parse scopes
        if (parsedRequest.scope) {
          setScopes(parsedRequest.scope.split(' '));
        }

        // Fetch partner information
        try {
          const { data: partner, error: partnerError } = await supabase
            .from('partners')
            .select('id, name, company, logo_url, website')
            .eq('id', parsedRequest.partner_id)
            .single();

          if (partnerError || !partner) {
            throw new Error('Partner not found');
          }

          setPartnerInfo({
            id: partner.id,
            name: partner.name,
            company: partner.company || '',
            logoUrl: partner.logo_url,
            website: partner.website
          });
        } catch (error) {
          console.error('Error fetching partner info:', error);
          // If we can't get partner info, at least use the name from the request
          setPartnerInfo({
            id: parsedRequest.partner_id,
            name: parsedRequest.partner_name
          });
        }

        // Check if the user is authenticated
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          // Redirect to login page with return URL
          const returnUrl = encodeURIComponent(window.location.href);
          router.push(`/login-next?returnUrl=${returnUrl}`);
          return;
        }

        setLoading(false);
      } catch (error) {
        console.error('Error handling authorization:', error);
        setError('Error handling authorization request');
        setLoading(false);
      }
    };

    if (router.isReady) {
      handleAuthorization();
    }
  }, [router.isReady, router.query]);

  const handleAuthorize = async () => {
    if (!authRequest || !user) return;

    setLoading(true);

    try {
      // Generate an authorization code
      const code = uuidv4();

      // Store the authorization code in the database
      const { error: insertError } = await supabase
        .from('auth_codes')
        .insert({
          code,
          user_id: user.id,
          client_id: authRequest.client_id, // Add client_id which was missing
          partner_id: authRequest.partner_id,
          redirect_uri: authRequest.redirect_uri,
          scope: authRequest.scope || 'read write',
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes expiration
          used: false
        });

      if (insertError) {
        console.error('Error storing authorization code:', insertError);
        setError('Error storing authorization code');
        setLoading(false);
        return;
      }

      // Redirect to the redirect URI with the authorization code
      const redirectUrl = new URL(authRequest.redirect_uri);
      redirectUrl.searchParams.append('code', code);
      redirectUrl.searchParams.append('state', authRequest.state || '');

      window.location.href = redirectUrl.toString();
    } catch (error) {
      console.error('Error authorizing application:', error);
      setError('Error authorizing application');
      setLoading(false);
    }
  };

  const handleDeny = () => {
    if (!authRequest) return;

    // Construct the redirect URL with an error
    const redirectUrl = new URL(authRequest.redirect_uri);
    redirectUrl.searchParams.append('error', 'access_denied');
    redirectUrl.searchParams.append('state', authRequest.state);

    // Redirect the user back to the client application
    window.location.href = redirectUrl.toString();
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-dark-900 p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
              <Flame className="h-6 w-6 text-fiery" />
            </div>
          </div>
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-dark-700">
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-fiery mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                Loading...
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Please wait while we process your request.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-dark-900 p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
              <Flame className="h-6 w-6 text-fiery" />
            </div>
          </div>
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-dark-700">
            <div className="p-6 text-center">
              <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                Authorization Error
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {error}
              </p>
              <Button
                onClick={() => router.push('/')}
                className="bg-fiery hover:bg-fiery-600 text-white"
              >
                Return to Home
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Not logged in state
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-dark-900 p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
              <Flame className="h-6 w-6 text-fiery" />
            </div>
          </div>
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-dark-700">
            <div className="p-6 text-center">
              <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-4">
                <Lock className="h-6 w-6 text-blue-500" />
              </div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                Authentication Required
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                You need to be logged in to authorize this application.
              </p>
              <Button
                onClick={() => {
                  const returnUrl = encodeURIComponent(window.location.href);
                  router.push(`/login-next?returnUrl=${returnUrl}`);
                }}
                className="bg-fiery hover:bg-fiery-600 text-white"
              >
                Sign In
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main authorization page
  return (
    <div className="min-h-screen flex items-center justify-center bg-white dark:bg-dark-900 p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
            <Flame className="h-6 w-6 text-fiery" />
          </div>
        </div>
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-dark-700">
          <div className="border-b border-gray-200 dark:border-dark-700 p-6">
            <h1 className="text-xl font-semibold text-gray-800 dark:text-white">
              {partnerInfo?.name || 'Application'} wants access to your Firenest account
            </h1>
          </div>

          <div className="p-6">
            <div className="flex items-center mb-6">
              {partnerInfo?.logoUrl ? (
                <img
                  src={partnerInfo.logoUrl}
                  alt={partnerInfo.name}
                  className="w-10 h-10 rounded mr-3"
                />
              ) : (
                <div className="w-10 h-10 rounded bg-gray-200 dark:bg-dark-700 flex items-center justify-center mr-3">
                  <Shield className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                </div>
              )}
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {user.email}
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500">
                  {partnerInfo?.company && `${partnerInfo.company} • `}
                  {partnerInfo?.website && (
                    <a
                      href={partnerInfo.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-fiery hover:underline"
                    >
                      {new URL(partnerInfo.website).hostname}
                    </a>
                  )}
                </p>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                This will allow {partnerInfo?.name || 'the application'} to:
              </h3>
              <ul className="space-y-2">
                {scopes.length > 0 ? (
                  scopes.map((scope, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-300">
                        {getScopeDescription(scope)}
                      </span>
                    </li>
                  ))
                ) : (
                  <>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-300">
                        Access your basic profile information
                      </span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600 dark:text-gray-300">
                        Track usage of connected services
                      </span>
                    </li>
                  </>
                )}
              </ul>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-100 dark:border-yellow-800/30 rounded-lg p-3 mb-6">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-yellow-800 dark:text-yellow-400">
                    Make sure you trust {partnerInfo?.name || 'this application'}
                  </p>
                  <p className="text-xs text-yellow-700 dark:text-yellow-500 mt-1">
                    You can revoke access at any time from your Firenest account settings.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-dark-700 p-4 flex justify-between">
            <Button
              variant="outline"
              onClick={handleDeny}
              className="border-gray-300 dark:border-dark-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAuthorize}
              className="bg-fiery hover:bg-fiery-600 text-white"
            >
              Continue
            </Button>
          </div>
        </div>
        <div className="text-center mt-4">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Secured by <span className="font-medium">Firenest</span>
          </p>
        </div>
      </div>
    </div>
  );
}

// Helper function to get a user-friendly description of a scope
function getScopeDescription(scope: string): string {
  const scopeDescriptions: Record<string, string> = {
    'read': 'Read your basic profile information',
    'write': 'Make changes to your account settings',
    'profile': 'Access your profile information',
    'email': 'View your email address',
    'openid': 'Verify your identity',
    'offline_access': 'Access your data when you're not using the application',
  };

  return scopeDescriptions[scope] || `Access to ${scope}`;
}
