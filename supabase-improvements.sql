-- Firenest Database Improvements
-- This script implements recommended improvements for the connection management system

-- 1. Create a function for automatic timestamp updates
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Fix INSERT policies by adding WITH CHECK clauses

-- tool_launches table
DROP POLICY IF EXISTS "Users can insert their own tool launches" ON tool_launches;
CREATE POLICY "Users can insert their own tool launches" 
  ON tool_launches FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- usage_sessions table
DROP POLICY IF EXISTS "Users can insert their own usage sessions" ON usage_sessions;
CREATE POLICY "Users can insert their own usage sessions" 
  ON usage_sessions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- usage_events table
DROP POLICY IF EXISTS "Users can insert their own usage events" ON usage_events;
CREATE POLICY "Users can insert their own usage events" 
  ON usage_events FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- 3. Add DELETE policies for all tables

-- tool_launches table
CREATE POLICY "Users can delete their own tool launches" 
  ON tool_launches FOR DELETE 
  USING (auth.uid() = user_id);

-- usage_sessions table
CREATE POLICY "Users can delete their own usage sessions" 
  ON usage_sessions FOR DELETE 
  USING (auth.uid() = user_id);

-- usage_events table
CREATE POLICY "Users can delete their own usage events" 
  ON usage_events FOR DELETE 
  USING (auth.uid() = user_id);

-- user_connections table
CREATE POLICY "Users can delete their own connections" 
  ON user_connections FOR DELETE 
  USING (auth.uid() = user_id);

-- connection_logs table
CREATE POLICY "Users can delete their own connection logs" 
  ON connection_logs FOR DELETE 
  USING (auth.uid() = user_id);

-- 4. Add triggers for automatic timestamp updates

-- tool_launches table
CREATE TRIGGER set_timestamp_tool_launches
BEFORE UPDATE ON tool_launches
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- usage_sessions table
CREATE TRIGGER set_timestamp_usage_sessions
BEFORE UPDATE ON usage_sessions
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- user_connections table
CREATE TRIGGER set_timestamp_user_connections
BEFORE UPDATE ON user_connections
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 5. Modify foreign keys to include CASCADE options
-- Note: This requires dropping and recreating the foreign keys

-- First, we need to drop the existing foreign keys
ALTER TABLE IF EXISTS tool_launches
  DROP CONSTRAINT IF EXISTS tool_launches_user_id_fkey;

ALTER TABLE IF EXISTS usage_sessions
  DROP CONSTRAINT IF EXISTS usage_sessions_user_id_fkey,
  DROP CONSTRAINT IF EXISTS usage_sessions_launch_id_fkey;

ALTER TABLE IF EXISTS usage_events
  DROP CONSTRAINT IF EXISTS usage_events_session_id_fkey,
  DROP CONSTRAINT IF EXISTS usage_events_user_id_fkey;

-- Then recreate them with CASCADE options
ALTER TABLE tool_launches
  ADD CONSTRAINT tool_launches_user_id_fkey
  FOREIGN KEY (user_id)
  REFERENCES users(id)
  ON DELETE CASCADE;

ALTER TABLE usage_sessions
  ADD CONSTRAINT usage_sessions_user_id_fkey
  FOREIGN KEY (user_id)
  REFERENCES users(id)
  ON DELETE CASCADE,
  ADD CONSTRAINT usage_sessions_launch_id_fkey
  FOREIGN KEY (launch_id)
  REFERENCES tool_launches(id)
  ON DELETE CASCADE;

ALTER TABLE usage_events
  ADD CONSTRAINT usage_events_session_id_fkey
  FOREIGN KEY (session_id)
  REFERENCES usage_sessions(id)
  ON DELETE CASCADE,
  ADD CONSTRAINT usage_events_user_id_fkey
  FOREIGN KEY (user_id)
  REFERENCES users(id)
  ON DELETE CASCADE;

-- 6. Add additional indexes for better performance

-- Add index for launched_at in tool_launches
CREATE INDEX IF NOT EXISTS tool_launches_launched_at_idx ON tool_launches(launched_at);

-- Add index for start_time and end_time in usage_sessions
CREATE INDEX IF NOT EXISTS usage_sessions_start_time_idx ON usage_sessions(start_time);
CREATE INDEX IF NOT EXISTS usage_sessions_end_time_idx ON usage_sessions(end_time);

-- Add index for created_at in usage_events
CREATE INDEX IF NOT EXISTS usage_events_created_at_idx ON usage_events(created_at);

-- 7. Add a function to get usage statistics for a user

CREATE OR REPLACE FUNCTION get_user_usage_stats(
  user_id UUID,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Set default date range if not provided
  start_date := COALESCE(start_date, NOW() - INTERVAL '30 days');
  end_date := COALESCE(end_date, NOW());
  
  -- Get usage statistics
  WITH tool_stats AS (
    SELECT
      t.tool_id,
      COUNT(t.id) AS launch_count,
      COUNT(DISTINCT DATE(t.launched_at)) AS days_used,
      SUM(COALESCE(s.actual_credits_used, 0)) AS total_credits_used,
      SUM(COALESCE(s.duration_seconds, 0)) AS total_duration_seconds,
      MAX(t.launched_at) AS last_used
    FROM
      tool_launches t
    LEFT JOIN
      usage_sessions s ON t.id = s.launch_id
    WHERE
      t.user_id = get_user_usage_stats.user_id
      AND t.launched_at BETWEEN start_date AND end_date
    GROUP BY
      t.tool_id
  )
  SELECT
    jsonb_build_object(
      'total_launches', COALESCE((SELECT SUM(launch_count) FROM tool_stats), 0),
      'total_credits_used', COALESCE((SELECT SUM(total_credits_used) FROM tool_stats), 0),
      'total_duration_hours', COALESCE((SELECT ROUND(SUM(total_duration_seconds) / 3600.0, 2) FROM tool_stats), 0),
      'active_days', COALESCE((SELECT COUNT(DISTINCT DATE(launched_at)) FROM tool_launches WHERE user_id = get_user_usage_stats.user_id AND launched_at BETWEEN start_date AND end_date), 0),
      'tools_used', COALESCE((SELECT COUNT(*) FROM tool_stats), 0),
      'tool_breakdown', COALESCE(
        (SELECT 
          jsonb_object_agg(
            tool_id, 
            jsonb_build_object(
              'launches', launch_count,
              'credits_used', total_credits_used,
              'duration_hours', ROUND(total_duration_seconds / 3600.0, 2),
              'days_used', days_used,
              'last_used', last_used
            )
          )
        FROM tool_stats),
        '{}'::jsonb
      )
    ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_usage_stats TO authenticated;

-- 8. Add a function to get detailed session history for a user

CREATE OR REPLACE FUNCTION get_user_session_history(
  user_id UUID,
  limit_count INTEGER DEFAULT 50,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  session_id UUID,
  tool_id TEXT,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  status TEXT,
  credits_used INTEGER,
  launch_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.id AS session_id,
    s.tool_id,
    s.start_time,
    s.end_time,
    s.duration_seconds,
    s.status,
    COALESCE(s.actual_credits_used, s.estimated_credits) AS credits_used,
    s.launch_id
  FROM
    usage_sessions s
  WHERE
    s.user_id = get_user_session_history.user_id
  ORDER BY
    s.start_time DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_session_history TO authenticated;

-- 9. Add a function to get session details including events

CREATE OR REPLACE FUNCTION get_session_details(
  session_id UUID
)
RETURNS JSONB AS $$
DECLARE
  session_data JSONB;
  events_data JSONB;
  result JSONB;
BEGIN
  -- Get session data
  SELECT
    jsonb_build_object(
      'id', s.id,
      'user_id', s.user_id,
      'tool_id', s.tool_id,
      'launch_id', s.launch_id,
      'start_time', s.start_time,
      'end_time', s.end_time,
      'duration_seconds', s.duration_seconds,
      'status', s.status,
      'metrics', s.metrics,
      'estimated_credits', s.estimated_credits,
      'actual_credits_used', s.actual_credits_used,
      'created_at', s.created_at,
      'updated_at', s.updated_at
    ) INTO session_data
  FROM
    usage_sessions s
  WHERE
    s.id = get_session_details.session_id;
  
  -- Get events data
  SELECT
    jsonb_agg(
      jsonb_build_object(
        'id', e.id,
        'event_type', e.event_type,
        'event_data', e.event_data,
        'credits_used', e.credits_used,
        'created_at', e.created_at
      )
      ORDER BY e.created_at
    ) INTO events_data
  FROM
    usage_events e
  WHERE
    e.session_id = get_session_details.session_id;
  
  -- Combine data
  SELECT
    jsonb_build_object(
      'session', session_data,
      'events', COALESCE(events_data, '[]'::jsonb)
    ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_session_details TO authenticated;

-- 10. Add a function to get connection history for a user

CREATE OR REPLACE FUNCTION get_user_connection_history(
  user_id UUID
)
RETURNS TABLE (
  tool_id TEXT,
  auth_method TEXT,
  status TEXT,
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  connection_id UUID,
  event_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.tool_id,
    c.auth_method,
    c.status,
    c.last_used,
    c.created_at,
    c.updated_at,
    c.id AS connection_id,
    COUNT(l.id) AS event_count
  FROM
    user_connections c
  LEFT JOIN
    connection_logs l ON c.user_id = l.user_id AND c.tool_id = l.tool_id
  WHERE
    c.user_id = get_user_connection_history.user_id
  GROUP BY
    c.id
  ORDER BY
    c.last_used DESC NULLS LAST,
    c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_connection_history TO authenticated;
