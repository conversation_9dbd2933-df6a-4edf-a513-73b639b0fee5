<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - Firenest Test Partner</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }
    .firenest-button {
      background: linear-gradient(135deg, #ff4500 0%, #ff7e00 100%);
      transition: all 0.3s ease;
    }
    .firenest-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(255, 69, 0, 0.2), 0 4px 6px -2px rgba(255, 69, 0, 0.1);
    }
    .feature-card {
      transition: all 0.3s ease;
    }
    .feature-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .loading {
      border-top-color: #ff4500;
      animation: spinner 0.6s linear infinite;
    }
    @keyframes spinner {
      to {transform: rotate(360deg);}
    }
  </style>
</head>
<body class="gradient-bg min-h-screen text-white">
  <div class="container mx-auto px-4 py-8">
    <header class="mb-8">
      <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold">Test Partner Dashboard</h1>
        <div class="flex items-center">
          <span id="user-name" class="mr-4 text-gray-300">Loading...</span>
          <a href="/logout" class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">Logout</a>
        </div>
      </div>
    </header>

    <main>
      <div class="bg-gray-800 rounded-lg shadow-xl p-6 mb-8">
        <h2 class="text-2xl font-bold mb-4">Welcome to Your Dashboard</h2>
        <p class="text-gray-300 mb-4">
          You're now authenticated with Firenest. You can access premium features below.
        </p>
        <div id="user-profile" class="bg-gray-700 rounded-lg p-4 text-gray-300">
          <div class="flex items-center justify-center h-12">
            <div class="loading h-6 w-6 rounded-full border-2 border-gray-300"></div>
          </div>
        </div>
      </div>

      <h3 class="text-xl font-bold mb-4">Premium Features</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Feature 1 -->
        <div class="feature-card bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div class="p-6">
            <h4 class="text-lg font-bold mb-2">Advanced Data Export</h4>
            <p class="text-gray-300 mb-4">
              Export your data in multiple formats with advanced filtering options.
            </p>
            <button 
              class="firenest-button text-white font-bold py-2 px-4 rounded-lg w-full"
              onclick="useFeature('data-export-pro')"
            >
              Use Feature
            </button>
          </div>
          <div class="feature-result hidden bg-gray-700 p-4 border-t border-gray-600"></div>
        </div>
        
        <!-- Feature 2 -->
        <div class="feature-card bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div class="p-6">
            <h4 class="text-lg font-bold mb-2">AI Content Generation</h4>
            <p class="text-gray-300 mb-4">
              Generate high-quality content using advanced AI models.
            </p>
            <button 
              class="firenest-button text-white font-bold py-2 px-4 rounded-lg w-full"
              onclick="useFeature('ai-content-gen')"
            >
              Use Feature
            </button>
          </div>
          <div class="feature-result hidden bg-gray-700 p-4 border-t border-gray-600"></div>
        </div>
        
        <!-- Feature 3 -->
        <div class="feature-card bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div class="p-6">
            <h4 class="text-lg font-bold mb-2">Custom Report Builder</h4>
            <p class="text-gray-300 mb-4">
              Create custom reports with advanced analytics and visualizations.
            </p>
            <button 
              class="firenest-button text-white font-bold py-2 px-4 rounded-lg w-full"
              onclick="useFeature('custom-reports')"
            >
              Use Feature
            </button>
          </div>
          <div class="feature-result hidden bg-gray-700 p-4 border-t border-gray-600"></div>
        </div>
        
        <!-- Feature 4 -->
        <div class="feature-card bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div class="p-6">
            <h4 class="text-lg font-bold mb-2">Priority API Access</h4>
            <p class="text-gray-300 mb-4">
              Get priority access to our API with higher rate limits.
            </p>
            <button 
              class="firenest-button text-white font-bold py-2 px-4 rounded-lg w-full"
              onclick="useFeature('priority-api')"
            >
              Use Feature
            </button>
          </div>
          <div class="feature-result hidden bg-gray-700 p-4 border-t border-gray-600"></div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // Fetch user profile on page load
    document.addEventListener('DOMContentLoaded', async () => {
      try {
        const response = await fetch('/api/profile');
        const data = await response.json();
        
        if (data.isAuthenticated) {
          // Update user name
          document.getElementById('user-name').textContent = data.name || data.email || 'User';
          
          // Update user profile
          const profileHtml = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p class="text-gray-400 text-sm">User ID</p>
                <p>${data.userId}</p>
              </div>
              <div>
                <p class="text-gray-400 text-sm">Email</p>
                <p>${data.email || 'Not provided'}</p>
              </div>
            </div>
          `;
          document.getElementById('user-profile').innerHTML = profileHtml;
        } else {
          // Redirect to login if not authenticated
          window.location.href = '/';
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
        document.getElementById('user-profile').innerHTML = `
          <div class="text-red-400">
            Error loading profile. Please <a href="/logout" class="underline">log out</a> and try again.
          </div>
        `;
      }
    });
    
    // Use a premium feature
    async function useFeature(featureId) {
      const featureCard = document.querySelector(`[onclick="useFeature('${featureId}')"]`).closest('.feature-card');
      const resultDiv = featureCard.querySelector('.feature-result');
      const button = featureCard.querySelector('button');
      
      // Show loading state
      button.disabled = true;
      button.innerHTML = `
        <div class="flex items-center justify-center">
          <div class="loading h-4 w-4 rounded-full border-2 border-white mr-2"></div>
          Processing...
        </div>
      `;
      
      try {
        const response = await fetch(`/api/feature/${featureId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        const data = await response.json();
        
        if (response.ok) {
          // Show success result
          resultDiv.innerHTML = `
            <div class="text-green-400 mb-2">✓ Feature used successfully</div>
            <div class="text-sm">${data.result}</div>
          `;
          resultDiv.classList.remove('hidden');
        } else if (response.status === 402) {
          // Payment required
          resultDiv.innerHTML = `
            <div class="text-yellow-400 mb-2">⚠ Insufficient credits</div>
            <div class="text-sm">
              This feature requires ${data.estimatedCost} credits. 
              <a href="#" class="underline">Add credits</a> to continue.
            </div>
          `;
          resultDiv.classList.remove('hidden');
        } else {
          // Show error
          resultDiv.innerHTML = `
            <div class="text-red-400 mb-2">✗ Error</div>
            <div class="text-sm">${data.error || 'An error occurred'}</div>
          `;
          resultDiv.classList.remove('hidden');
        }
      } catch (error) {
        console.error('Error using feature:', error);
        resultDiv.innerHTML = `
          <div class="text-red-400 mb-2">✗ Error</div>
          <div class="text-sm">Failed to use feature. Please try again.</div>
        `;
        resultDiv.classList.remove('hidden');
      } finally {
        // Reset button
        button.disabled = false;
        button.innerHTML = 'Use Feature';
      }
    }
  </script>
</body>
</html>
