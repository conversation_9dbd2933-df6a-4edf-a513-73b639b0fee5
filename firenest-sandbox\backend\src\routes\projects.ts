/**
 * Project Routes
 * Sandbox project management within workspaces
 * SOC 2 Alignment: CC6.2 (Access Control)
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { queryWithUserContext, transactionWithUserContext } from '@/config/database';
import { logger } from '@/utils/logger';
import { validate, schemas } from '@/utils/validation';
import { asyncHand<PERSON>, NotFoundError, ConflictError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';

const router = Router();

// Get all projects for the authenticated user
router.get('/',
  validate(schemas.pagination, 'query'),
  validate(schemas.projectFilters, 'query'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { page, limit, sortBy, sortOrder, workspaceId, status, search } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    const queryParams = [req.user.id, limit, offset];
    let paramIndex = 4;

    if (workspaceId) {
      whereClause += ` AND sp.workspace_id = $${paramIndex++}`;
      queryParams.push(workspaceId);
    }

    if (status) {
      whereClause += ` AND sp.status = $${paramIndex++}`;
      queryParams.push(status);
    }

    if (search) {
      whereClause += ` AND (sp.name ILIKE $${paramIndex++} OR sp.description ILIKE $${paramIndex++})`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.id, sp.name, sp.description, sp.status, sp.metadata, sp.created_at, sp.updated_at,
              w.id as workspace_id, w.name as workspace_name,
              (SELECT COUNT(*) FROM data_uploads du WHERE du.project_id = sp.id) as upload_count,
              (SELECT COUNT(*) FROM pricing_models pm WHERE pm.project_id = sp.id) as model_count,
              (SELECT COUNT(*) FROM simulations s WHERE s.project_id = sp.id) as simulation_count
       FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE (w.owner_id = $1 OR w.id IN (
         SELECT workspace_id FROM workspace_members WHERE user_id = $1
       )) ${whereClause}
       ORDER BY ${sortBy || 'sp.created_at'} ${sortOrder}
       LIMIT $2 OFFSET $3`,
      queryParams
    );

    const countParams = [req.user.id];
    let countParamIndex = 2;
    let countWhereClause = '';

    if (workspaceId) {
      countWhereClause += ` AND sp.workspace_id = $${countParamIndex++}`;
      countParams.push(workspaceId);
    }

    if (status) {
      countWhereClause += ` AND sp.status = $${countParamIndex++}`;
      countParams.push(status);
    }

    if (search) {
      countWhereClause += ` AND (sp.name ILIKE $${countParamIndex++} OR sp.description ILIKE $${countParamIndex++})`;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const countResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT COUNT(*) as total
       FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE (w.owner_id = $1 OR w.id IN (
         SELECT workspace_id FROM workspace_members WHERE user_id = $1
       )) ${countWhereClause}`,
      countParams
    );

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  })
);

// Get project by ID
router.get('/:projectId',
  validate(Joi.object({ projectId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { projectId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.id, sp.name, sp.description, sp.status, sp.metadata, sp.created_at, sp.updated_at,
              w.id as workspace_id, w.name as workspace_name,
              w.owner_id = $2 as is_workspace_owner
       FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1`,
      [projectId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Project not found');
    }

    // Get project uploads
    const uploadsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT id, original_filename, file_type, status, file_size, created_at
       FROM data_uploads
       WHERE project_id = $1
       ORDER BY created_at DESC`,
      [projectId]
    );

    // Get project models
    const modelsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT id, name, description, model_type, created_at
       FROM pricing_models
       WHERE project_id = $1
       ORDER BY created_at DESC`,
      [projectId]
    );

    // Get project simulations
    const simulationsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT id, name, status, started_at, completed_at, created_at
       FROM simulations
       WHERE project_id = $1
       ORDER BY created_at DESC`,
      [projectId]
    );

    const project = result.rows[0];
    project.uploads = uploadsResult.rows;
    project.models = modelsResult.rows;
    project.simulations = simulationsResult.rows;

    res.json({
      success: true,
      data: project
    });
  })
);

// Create new project
router.post('/',
  validate(schemas.createProject),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { name, description, workspaceId } = req.body;

    // Verify workspace access
    const workspaceCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT 1 FROM workspaces w
       WHERE w.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [workspaceId, req.user.id]
    );

    if (workspaceCheck.rows.length === 0) {
      throw new NotFoundError('Workspace not found or access denied');
    }

    const result = await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        const projectResult = await client.query(
          `INSERT INTO sandbox_projects (id, name, description, workspace_id, status, metadata)
           VALUES ($1, $2, $3, $4, $5, $6)
           RETURNING id, name, description, status, metadata, created_at, updated_at`,
          [uuidv4(), name, description, workspaceId, 'CREATED', JSON.stringify({})]
        );

        return projectResult.rows[0];
      }
    );

    logger.info('Project created', {
      projectId: result.id,
      userId: req.user.id,
      workspaceId,
      name
    });

    res.status(201).json({
      success: true,
      data: result
    });
  })
);

// Update project
router.put('/:projectId',
  validate(Joi.object({ projectId: schemas.uuid }), 'params'),
  validate(schemas.updateProject),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { projectId } = req.params;
    const { name, description, status } = req.body;

    // Check project access
    const accessCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.name as current_name FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (accessCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex++}`);
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex++}`);
      updateValues.push(description);
    }
    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`);
      updateValues.push(status);
    }

    updateFields.push(`updated_at = NOW()`);
    updateValues.push(projectId);

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `UPDATE sandbox_projects 
       SET ${updateFields.join(', ')}
       WHERE id = $${paramIndex}
       RETURNING id, name, description, status, metadata, created_at, updated_at`,
      updateValues
    );

    logger.info('Project updated', {
      projectId,
      userId: req.user.id,
      changes: { name, description, status }
    });

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Delete project
router.delete('/:projectId',
  validate(Joi.object({ projectId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { projectId } = req.params;

    // Check project access and get project info
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.name, sp.status FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const project = projectCheck.rows[0];

    // Check if project has running simulations
    if (project.status === 'SIMULATING') {
      throw new ConflictError('Cannot delete project with running simulations');
    }

    await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        // Delete in correct order due to foreign key constraints
        await client.query('DELETE FROM simulation_results WHERE simulation_id IN (SELECT id FROM simulations WHERE project_id = $1)', [projectId]);
        await client.query('DELETE FROM simulation_model_runs WHERE simulation_id IN (SELECT id FROM simulations WHERE project_id = $1)', [projectId]);
        await client.query('DELETE FROM simulations WHERE project_id = $1', [projectId]);
        await client.query('DELETE FROM model_components WHERE model_id IN (SELECT id FROM pricing_models WHERE project_id = $1)', [projectId]);
        await client.query('DELETE FROM pricing_models WHERE project_id = $1', [projectId]);
        await client.query('DELETE FROM data_uploads WHERE project_id = $1', [projectId]);
        await client.query('DELETE FROM sandbox_projects WHERE id = $1', [projectId]);
      }
    );

    logger.info('Project deleted', {
      projectId,
      userId: req.user.id,
      projectName: project.name
    });

    res.json({
      success: true,
      message: 'Project deleted successfully'
    });
  })
);

export default router;
