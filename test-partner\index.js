/**
 * Firenest Test Partner Application
 * 
 * This is a simple Express application that demonstrates how to integrate with Firenest
 * using the Firenest SDK.
 */

// Load environment variables
require('dotenv').config();

const express = require('express');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const jwt = require('jsonwebtoken');
const path = require('path');

// Create Express app
const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(session({
  secret: process.env.SESSION_SECRET || 'firenest-test-partner-session-secret',
  resave: false,
  saveUninitialized: true,
  cookie: { secure: false } // Set to true in production with HTTPS
}));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Firenest configuration
const firenestConfig = {
  clientId: process.env.FIRENEST_CLIENT_ID,
  clientSecret: process.env.FIRENEST_CLIENT_SECRET,
  apiUrl: process.env.FIRENEST_API_URL,
  redirectUri: process.env.FIRENEST_REDIRECT_URI
};

// Home page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Dashboard page (protected)
app.get('/dashboard', (req, res) => {
  if (!req.session.firenestUserId) {
    return res.redirect('/');
  }
  
  res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
});

// User profile endpoint
app.get('/api/profile', (req, res) => {
  if (!req.session.firenestUserId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  
  res.json({
    userId: req.session.firenestUserId,
    name: req.session.userName,
    email: req.session.userEmail,
    isAuthenticated: true
  });
});

// Logout endpoint
app.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/');
});

// Firenest login endpoint
app.get('/login/firenest', (req, res) => {
  // Generate a state parameter for security
  const state = uuidv4();
  req.session.firenestAuthState = state;
  
  // Generate the authorization URL
  const authUrl = new URL(`${firenestConfig.apiUrl}/api/v1/auth/authorize`);
  authUrl.searchParams.append('response_type', 'code');
  authUrl.searchParams.append('client_id', firenestConfig.clientId);
  authUrl.searchParams.append('redirect_uri', firenestConfig.redirectUri);
  authUrl.searchParams.append('state', state);
  authUrl.searchParams.append('scope', 'openid profile email');
  
  // Redirect the user to the authorization URL
  res.redirect(authUrl.toString());
});

// Firenest callback endpoint
app.get('/auth/callback', async (req, res) => {
  const { code, state, error } = req.query;
  
  // Check for errors
  if (error) {
    console.error('Authorization error:', error);
    return res.redirect('/?error=auth_error');
  }
  
  // Verify state parameter
  if (state !== req.session.firenestAuthState) {
    console.error('State mismatch');
    return res.redirect('/?error=state_mismatch');
  }
  
  // Exchange the code for tokens
  try {
    const tokenResponse = await axios.post(`${firenestConfig.apiUrl}/api/v1/auth/token`, {
      grant_type: 'authorization_code',
      code,
      client_id: firenestConfig.clientId,
      client_secret: firenestConfig.clientSecret,
      redirect_uri: firenestConfig.redirectUri
    });
    
    const { access_token, id_token, refresh_token, user_id } = tokenResponse.data;
    
    // Decode the ID token
    const decodedToken = jwt.decode(id_token);
    
    // Store user information in session
    req.session.firenestUserId = user_id;
    req.session.firenestAccessToken = access_token;
    req.session.firenestIdToken = id_token;
    req.session.firenestRefreshToken = refresh_token;
    req.session.userName = decodedToken.name;
    req.session.userEmail = decodedToken.email;
    
    // Redirect to dashboard
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Error exchanging code for tokens:', error.response?.data || error.message);
    res.redirect('/?error=token_error');
  }
});

// Feature usage endpoint
app.post('/api/feature/:featureId', async (req, res) => {
  const { featureId } = req.params;
  
  if (!req.session.firenestUserId || !req.session.firenestAccessToken) {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  
  try {
    // Check if the user has access to the feature
    const accessResponse = await axios.post(
      `${firenestConfig.apiUrl}/api/v1/authorize`,
      {
        userId: req.session.firenestUserId,
        featureId
      },
      {
        auth: {
          username: firenestConfig.clientId,
          password: firenestConfig.clientSecret
        }
      }
    );
    
    if (!accessResponse.data.allowed) {
      return res.status(402).json({
        error: 'Payment required',
        reason: accessResponse.data.reason,
        estimatedCost: accessResponse.data.estimatedCost
      });
    }
    
    // Simulate feature execution
    const featureResult = {
      featureId,
      result: `Feature ${featureId} executed successfully at ${new Date().toISOString()}`,
      timestamp: new Date().toISOString()
    };
    
    // Report usage
    await axios.post(
      `${firenestConfig.apiUrl}/api/v1/usage/track`,
      {
        userId: req.session.firenestUserId,
        featureId,
        units: 1,
        metadata: {
          source: 'test-partner',
          details: `Feature ${featureId} usage`
        }
      },
      {
        auth: {
          username: firenestConfig.clientId,
          password: firenestConfig.clientSecret
        }
      }
    );
    
    res.json({
      success: true,
      ...featureResult
    });
  } catch (error) {
    console.error('Error using feature:', error.response?.data || error.message);
    res.status(500).json({ error: 'Error using feature' });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Firenest Test Partner running at http://localhost:${port}`);
});
