-- OAuth Authorization Tables

-- Table for storing authorization codes
CREATE TABLE IF NOT EXISTS auth_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT NOT NULL UNIQUE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  client_id TEXT NOT NULL,
  partner_id UUID NOT NULL REFERENCES partner_accounts(id) ON DELETE CASCADE,
  redirect_uri TEXT NOT NULL,
  scope TEXT,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing access tokens
CREATE TABLE IF NOT EXISTS access_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  access_token TEXT NOT NULL UNIQUE,
  refresh_token TEXT NOT NULL UNIQUE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  client_id TEXT NOT NULL,
  partner_id UUID NOT NULL REFERENCES partner_accounts(id) ON DELETE CASCADE,
  scope TEXT,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for auth_codes table
ALTER TABLE auth_codes ENABLE ROW LEVEL SECURITY;

-- Partners can view their own auth codes
CREATE POLICY "Partners can view their own auth codes"
ON auth_codes
FOR SELECT
TO authenticated
USING (
  partner_id = auth.uid() OR
  user_id = auth.uid()
);

-- Partners can insert auth codes for their clients
CREATE POLICY "Partners can insert auth codes"
ON auth_codes
FOR INSERT
TO authenticated
WITH CHECK (
  partner_id = auth.uid()
);

-- Users can view their own auth codes
CREATE POLICY "Users can view their own auth codes"
ON auth_codes
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
);

-- Add RLS policies for access_tokens table
ALTER TABLE access_tokens ENABLE ROW LEVEL SECURITY;

-- Partners can view tokens for their clients
CREATE POLICY "Partners can view their tokens"
ON access_tokens
FOR SELECT
TO authenticated
USING (
  partner_id = auth.uid()
);

-- Users can view their own tokens
CREATE POLICY "Users can view their own tokens"
ON access_tokens
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
);

-- Partners can update tokens for their clients
CREATE POLICY "Partners can update their tokens"
ON access_tokens
FOR UPDATE
TO authenticated
USING (
  partner_id = auth.uid()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_auth_codes_user_id ON auth_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_codes_client_id ON auth_codes(client_id);
CREATE INDEX IF NOT EXISTS idx_auth_codes_partner_id ON auth_codes(partner_id);
CREATE INDEX IF NOT EXISTS idx_access_tokens_user_id ON access_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_access_tokens_client_id ON access_tokens(client_id);
CREATE INDEX IF NOT EXISTS idx_access_tokens_partner_id ON access_tokens(partner_id);
CREATE INDEX IF NOT EXISTS idx_access_tokens_refresh_token ON access_tokens(refresh_token);
