/**
 * OAuth Utilities
 *
 * This file contains utility functions for OAuth authorization flows.
 */

import { supabase } from '@/lib/supabase';
import { verifyAccessToken as verifyToken } from './token-utils';

/**
 * Verify an access token
 *
 * @param accessToken The access token to verify
 * @returns The user ID and partner ID if the token is valid, null otherwise
 */
export async function verifyAccessToken(accessToken: string): Promise<{ userId: string, partnerId: string } | null> {
  try {
    const result = await verifyToken(accessToken, supabase);

    if (!result.valid || !result.userId || !result.partnerId) {
      return null;
    }

    return {
      userId: result.userId,
      partnerId: result.partnerId
    };
  } catch (error) {
    console.error('Error verifying access token:', error);
    return null;
  }
}

/**
 * Revoke an access token
 *
 * @param accessToken The access token to revoke
 * @returns True if the token was revoked, false otherwise
 */
export async function revokeAccessToken(accessToken: string): Promise<boolean> {
  try {
    // Delete the token from the database
    const { error } = await supabase
      .from('access_tokens')
      .delete()
      .eq('access_token', accessToken);

    if (error) {
      console.error('Error revoking access token:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error revoking access token:', error);
    return false;
  }
}

/**
 * Get all active tokens for a user
 *
 * @param userId The user ID
 * @returns An array of active tokens
 */
export async function getUserTokens(userId: string): Promise<any[]> {
  try {
    // Get all active tokens for the user
    const { data, error } = await supabase
      .from('access_tokens')
      .select('id, client_id, partner_id, scope, created_at, expires_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting user tokens:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error getting user tokens:', error);
    return [];
  }
}

/**
 * Revoke all tokens for a user and partner
 *
 * @param userId The user ID
 * @param partnerId The partner ID
 * @returns True if the tokens were revoked, false otherwise
 */
export async function revokeUserPartnerTokens(userId: string, partnerId: string): Promise<boolean> {
  try {
    // Delete all tokens for the user and partner
    const { error } = await supabase
      .from('access_tokens')
      .delete()
      .eq('user_id', userId)
      .eq('partner_id', partnerId);

    if (error) {
      console.error('Error revoking user partner tokens:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error revoking user partner tokens:', error);
    return false;
  }
}

/**
 * Get partner information by client ID
 *
 * @param clientId The client ID
 * @returns The partner information if found, null otherwise
 */
export async function getPartnerByClientId(clientId: string): Promise<any | null> {
  try {
    if (!clientId) {
      console.error('Invalid client ID provided to getPartnerByClientId');
      return null;
    }

    console.log('Getting partner by client ID:', clientId);

    // Query the database to find the partner tool with this client ID
    const { data: integrationConfigs, error: configError } = await supabase
      .from('integration_configs')
      .select('tool_id, config_data, updated_at')
      .eq('config_data->clientId', clientId);

    if (configError) {
      console.error('Error getting partner by client ID:', configError);
      return null;
    }

    if (!integrationConfigs || integrationConfigs.length === 0) {
      console.log('No integration config found for client ID:', clientId);
      return null;
    }

    // If we have multiple results, use the most recently updated one
    let integrationConfig;
    if (integrationConfigs.length > 1) {
      console.warn(`Multiple integration configs found for client ID: ${clientId}. Using the most recent one.`);

      // Sort by updated_at in descending order to get the most recent one
      integrationConfigs.sort((a, b) => {
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      });

      integrationConfig = integrationConfigs[0];
    } else {
      integrationConfig = integrationConfigs[0];
    }

    const toolId = integrationConfig.tool_id;
    console.log('Found tool ID:', toolId);

    // Get the tool details
    const { data: tool, error: toolError } = await supabase
      .from('partner_tools')
      .select('partner_id, name')
      .eq('id', toolId)
      .maybeSingle();

    if (toolError) {
      console.error('Error getting tool details:', toolError);
      return null;
    }

    if (!tool) {
      console.log('No tool found for tool ID:', toolId);
      return null;
    }

    // Get the partner details
    const { data: partner, error: partnerError } = await supabase
      .from('partner_accounts')
      .select('id, name, company, logo_url, website')
      .eq('id', tool.partner_id)
      .maybeSingle();

    if (partnerError) {
      console.error('Error getting partner details:', partnerError);
      return null;
    }

    if (!partner) {
      console.log('No partner found for partner ID:', tool.partner_id);
      return null;
    }

    console.log('Found partner:', partner.name, 'for tool:', tool.name);

    return {
      id: partner.id,
      name: tool.name,
      company: partner.company,
      logoUrl: partner.logo_url,
      website: partner.website
    };
  } catch (error) {
    console.error('Error getting partner by client ID:', error);
    return null;
  }
}
