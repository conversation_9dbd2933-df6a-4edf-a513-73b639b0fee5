/**
 * Integration Framework Types
 * 
 * This file defines the core types for the integration framework,
 * which enables seamless integration with third-party services.
 */

// Integration status
export type IntegrationStatus = 'live' | 'beta' | 'coming_soon';

// Integration category
export type IntegrationCategory = 
  'ai_writing' | 
  'image_generation' | 
  'video_creation' | 
  'code_assistant' | 
  'data_analysis' | 
  'audio_tools' | 
  'productivity' | 
  'marketing' | 
  'design';

// Integration feature
export interface IntegrationFeature {
  id: string;
  name: string;
  description: string;
  isAvailable: boolean;
}

// Integration partner
export interface IntegrationPartner {
  id: string;
  name: string;
  description: string;
  website: string;
  logo: string;
  status: IntegrationStatus;
}

// Integration metadata
export interface IntegrationMetadata {
  id: string;
  partnerId: string;
  name: string;
  description: string;
  longDescription: string;
  category: IntegrationCategory;
  tags: string[];
  features: IntegrationFeature[];
  usageExamples: string[];
  pricing: {
    metricType: 'time' | 'api_calls' | 'resources' | 'custom';
    unitName: string;
    costPerUnit: number;
    minimumUsage?: number;
  };
  rating: number;
  reviewCount: number;
  popular: boolean;
  new: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Integration configuration
export interface IntegrationConfig {
  id: string;
  partnerId: string;
  authMethod: 'oauth' | 'api_key' | 'credentials' | 'ip_based';
  
  // OAuth specific configuration
  oauth?: {
    clientId: string;
    clientSecret: string;
    authorizationUrl: string;
    tokenUrl: string;
    redirectUrl: string;
    scope: string[];
    responseType: 'code' | 'token';
  };
  
  // API Key specific configuration
  apiKey?: {
    headerName: string;
    queryParamName?: string;
    isBearer?: boolean;
  };
  
  // Credentials specific configuration
  credentials?: {
    loginUrl: string;
    usernameField: string;
    passwordField: string;
    cookiesToCapture: string[];
  };
  
  // IP based specific configuration
  ipBased?: {
    allowedIps: string[];
    ipHeaderName?: string;
  };
  
  // Service endpoints
  endpoints: {
    baseUrl: string;
    login?: string;
    logout?: string;
    userInfo?: string;
    usage?: string;
    [key: string]: string | undefined;
  };
  
  // Webhook configuration
  webhooks?: {
    enabled: boolean;
    secret?: string;
    events: string[];
    url: string;
  };
  
  // Rate limiting
  rateLimit?: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
}

// Integration adapter
export interface IntegrationAdapter {
  id: string;
  partnerId: string;
  version: string;
  
  // Authentication methods
  authenticate: (userId: string, credentials: any) => Promise<any>;
  refreshAuth: (userId: string, authData: any) => Promise<any>;
  revokeAuth: (userId: string, authData: any) => Promise<boolean>;
  
  // API methods
  callApi: (userId: string, endpoint: string, method: string, data?: any) => Promise<any>;
  
  // Usage tracking methods
  getUsage: (userId: string, startDate: Date, endDate: Date) => Promise<any>;
  
  // Webhook handling
  handleWebhook?: (event: string, payload: any) => Promise<any>;
}

// Integration test result
export interface IntegrationTestResult {
  id: string;
  partnerId: string;
  timestamp: Date;
  success: boolean;
  tests: {
    name: string;
    success: boolean;
    error?: string;
    duration: number;
  }[];
  overallDuration: number;
}

// Integration health status
export interface IntegrationHealthStatus {
  id: string;
  partnerId: string;
  timestamp: Date;
  status: 'healthy' | 'degraded' | 'down';
  latency: number;
  uptime: number;
  incidents: {
    timestamp: Date;
    message: string;
    resolved: boolean;
  }[];
}
