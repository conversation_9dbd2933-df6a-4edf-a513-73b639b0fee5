import React, { useState } from 'react';
import { notify } from '@/components/ui/notification-system';
import { submitContactForm } from '@/lib/supabase';

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.email || !formData.message) {
      notify.error("Please fill out all required fields", {
        title: "Missing Information",
        position: "top-center",
        duration: 4000
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await submitContactForm({
        name: formData.name,
        email: formData.email,
        company: formData.company,
        message: formData.message
      });

      if (result.success) {
        setFormData({
          name: '',
          email: '',
          company: '',
          message: '',
        });
        notify.success("Message sent successfully! We'll get back to you soon.", {
          title: "Message Sent",
          position: "top-center",
          duration: 4000
        });
      } else {
        notify.error("Something went wrong. Please try again.", {
          title: "Submission Failed",
          position: "top-center",
          duration: 5000
        });
      }
    } catch (error) {
      console.error("Error submitting contact form:", error);
      notify.error("Something went wrong. Please try again.", {
        title: "Error",
        position: "top-center",
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="glass-card p-6 md:p-8 max-w-2xl mx-auto relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
      <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

      <div className="flex items-center gap-4 mb-6">
        <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-fiery/20 to-fiery/5 flex items-center justify-center text-3xl">
          ✉️
        </div>
        <h3 className="text-2xl font-bold">Send Us a Message</h3>
      </div>

      <p className="text-white/70 mb-6">Have questions about Firenest? We're here to help! Fill out the form below and our team will get back to you as soon as possible.</p>

      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-white/80 mb-2">Your Name</label>
            <div className="relative">
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-fiery/50 focus:border-transparent text-white transition-all duration-300 hover:bg-white/10"
                placeholder="John Doe"
              />
            </div>
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-white/80 mb-2">Email Address</label>
            <div className="relative">
              <input
                type="email"
                id="email"
                name="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-fiery/50 focus:border-transparent text-white transition-all duration-300 hover:bg-white/10"
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="company" className="block text-sm font-medium text-white/80 mb-2">Company Name</label>
          <div className="relative">
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleChange}
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-fiery/50 focus:border-transparent text-white transition-all duration-300 hover:bg-white/10"
              placeholder="Your Company Ltd."
            />
          </div>
        </div>

        <div>
          <label htmlFor="message" className="block text-sm font-medium text-white/80 mb-2">Your Message</label>
          <div className="relative">
            <textarea
              id="message"
              name="message"
              required
              rows={5}
              value={formData.message}
              onChange={handleChange}
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-fiery/50 focus:border-transparent text-white resize-none transition-all duration-300 hover:bg-white/10"
              placeholder="How can we help you?"
            ></textarea>
          </div>
        </div>

        <div className="flex justify-center mt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="fire-button px-8 py-3 disabled:opacity-70 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 active:scale-95"
          >
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
            ) : 'Send Message'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ContactForm;
