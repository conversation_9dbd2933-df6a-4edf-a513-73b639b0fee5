# Firenest Supabase Integration

This document explains how to set up Supabase for the Firenest application to handle form submissions and user data.

## Setup Instructions

### 1. Create a Supabase Account and Project

1. Go to [Supabase](https://supabase.com/) and sign up for an account
2. Create a new project
3. Once your project is created, go to the project dashboard

### 2. Get Your API Keys

1. In your Supabase project dashboard, go to Project Settings > API
2. Copy the `URL` and `anon` key
3. Create a `.env.local` file in the root of your project and add these values:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 3. Create Required Tables

You need to create three tables in your Supabase database:

#### Early Access Requests Table

```sql
CREATE TABLE early_access_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  name TEXT,
  company TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index on email to prevent duplicates
CREATE UNIQUE INDEX early_access_email_idx ON early_access_requests (email);
```

#### Contact Messages Table

```sql
CREATE TABLE contact_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  company TEXT,
  phone TEXT,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Newsletter Subscribers Table

```sql
CREATE TABLE newsletter_subscribers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index on email to prevent duplicates
CREATE UNIQUE INDEX newsletter_email_idx ON newsletter_subscribers (email);
```

### 4. Set Up Row-Level Security (RLS)

For security, enable Row-Level Security on all tables and create appropriate policies:

```sql
-- Enable RLS
ALTER TABLE early_access_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;

-- Create policies for inserting data (public can insert, only authenticated can read)
CREATE POLICY "Allow public inserts to early_access_requests" 
  ON early_access_requests FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow public inserts to contact_messages" 
  ON contact_messages FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow public inserts to newsletter_subscribers" 
  ON newsletter_subscribers FOR INSERT 
  WITH CHECK (true);

-- Create policies for reading data (only authenticated users with specific roles)
CREATE POLICY "Allow authenticated reads to early_access_requests" 
  ON early_access_requests FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated reads to contact_messages" 
  ON contact_messages FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated reads to newsletter_subscribers" 
  ON newsletter_subscribers FOR SELECT 
  USING (auth.role() = 'authenticated');
```

## Testing Your Setup

After completing the setup:

1. Restart your development server
2. Try submitting the contact form or early access form
3. Check your Supabase dashboard to verify that data is being stored correctly

## Troubleshooting

- If forms aren't submitting correctly, check the browser console for errors
- Verify your Supabase URL and anon key are correctly set in the `.env.local` file
- Make sure your tables are created with the correct structure
- Check that RLS policies are properly configured

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript/introduction)
- [Row-Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
