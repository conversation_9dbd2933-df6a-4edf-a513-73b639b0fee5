/**
 * Simulation Progress Tracker
 * Real-time progress monitoring with detailed status updates
 */

import React, { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  BarChart3,
  Loader2,
  ArrowRight
} from 'lucide-react'
import { simulationsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate } from '@/lib/utils'

interface SimulationProgressProps {
  simulationId: string
  onComplete?: () => void
  onCancel?: () => void
  showActions?: boolean
}

export function SimulationProgress({ 
  simulationId, 
  onComplete, 
  onCancel,
  showActions = true 
}: SimulationProgressProps) {
  const { data: simulation, isLoading, refetch } = useQuery({
    queryKey: ['simulation-status', simulationId],
    queryFn: () => simulationsApi.getStatus(simulationId),
    refetchInterval: (data) => {
      // Refetch every 2 seconds if simulation is running
      const status = data?.data?.data?.status
      return status === 'QUEUED' || status === 'RUNNING' ? 2000 : false
    },
    refetchIntervalInBackground: true
  })

  const simulationData = simulation?.data?.data

  // Call onComplete when simulation finishes
  useEffect(() => {
    if (simulationData?.status === 'COMPLETE' && onComplete) {
      onComplete()
    }
  }, [simulationData?.status, onComplete])

  if (isLoading) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  if (!simulationData) {
    return (
      <div className="firenest-card">
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Simulation Not Found</h3>
          <p className="text-gray-400">The simulation could not be loaded.</p>
        </div>
      </div>
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'QUEUED':
        return Clock
      case 'RUNNING':
        return Loader2
      case 'COMPLETE':
        return CheckCircle
      case 'FAILED':
        return XCircle
      case 'CANCELLED':
        return XCircle
      default:
        return Clock
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'QUEUED':
        return 'text-yellow-400'
      case 'RUNNING':
        return 'text-blue-400'
      case 'COMPLETE':
        return 'text-green-400'
      case 'FAILED':
        return 'text-red-400'
      case 'CANCELLED':
        return 'text-gray-400'
      default:
        return 'text-gray-400'
    }
  }

  const StatusIcon = getStatusIcon(simulationData.status)
  const statusColor = getStatusColor(simulationData.status)
  const isRunning = simulationData.status === 'QUEUED' || simulationData.status === 'RUNNING'

  return (
    <div className="space-y-6">
      {/* Main Status Card */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className={`w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center`}>
              <StatusIcon className={`w-6 h-6 ${statusColor} ${simulationData.status === 'RUNNING' ? 'animate-spin' : ''}`} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {simulationData.name || 'Simulation'}
              </h2>
              <div className="flex items-center space-x-3">
                <Badge 
                  variant={simulationData.status === 'COMPLETE' ? 'success' : 
                          simulationData.status === 'FAILED' ? 'destructive' : 'secondary'}
                >
                  {simulationData.status}
                </Badge>
                <span className="text-sm text-gray-400">
                  {simulationData.project_name}
                </span>
              </div>
            </div>
          </div>

          {showActions && (
            <div className="flex space-x-3">
              {isRunning && (
                <Button variant="outline" size="sm" onClick={() => {
                  // TODO: Implement cancel functionality
                }}>
                  Cancel
                </Button>
              )}
              {onCancel && (
                <Button variant="outline" onClick={onCancel}>
                  Close
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {isRunning && (
          <div className="mb-6">
            <div className="flex justify-between text-sm mb-2">
              <span className="text-gray-400">Progress</span>
              <span className="text-white">{simulationData.progress_percentage}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-fiery h-2 rounded-full transition-all duration-300"
                style={{ width: `${simulationData.progress_percentage}%` }}
              />
            </div>
          </div>
        )}

        {/* Status Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="firenest-nested-card text-center">
            <div className="text-lg font-semibold text-white">
              {simulationData.started_at ? formatDate(simulationData.started_at) : 'Not started'}
            </div>
            <div className="text-xs text-gray-400">Started</div>
          </div>

          <div className="firenest-nested-card text-center">
            <div className="text-lg font-semibold text-white">
              {simulationData.completed_at ? formatDate(simulationData.completed_at) : 
               isRunning ? 'In progress...' : 'Not completed'}
            </div>
            <div className="text-xs text-gray-400">Completed</div>
          </div>

          <div className="firenest-nested-card text-center">
            <div className="text-lg font-semibold text-white">
              {simulationData.model_runs?.length || 0}
            </div>
            <div className="text-xs text-gray-400">Models</div>
          </div>
        </div>

        {/* Error Message */}
        {simulationData.error_message && (
          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg mb-6">
            <div className="flex items-center space-x-3">
              <XCircle className="w-5 h-5 text-red-400" />
              <div>
                <h4 className="text-red-400 font-medium">Simulation Failed</h4>
                <p className="text-sm text-gray-300">{simulationData.error_message}</p>
              </div>
            </div>
          </div>
        )}

        {/* Next Steps */}
        {simulationData.nextSteps && simulationData.nextSteps.length > 0 && (
          <div>
            <h4 className="text-white font-medium mb-3">Next Steps</h4>
            <div className="space-y-2">
              {simulationData.nextSteps.map((step: string, index: number) => (
                <div key={index} className="flex items-center space-x-3 text-sm">
                  <div className="w-6 h-6 bg-fiery/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-fiery text-xs font-bold">{index + 1}</span>
                  </div>
                  <span className="text-gray-300">{step}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Model Progress */}
      {simulationData.model_runs && simulationData.model_runs.length > 0 && (
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Model Processing Status</h3>
          
          <div className="space-y-4">
            {simulationData.model_runs.map((modelRun: any) => (
              <ModelRunProgress key={modelRun.model_id} modelRun={modelRun} />
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {simulationData.status === 'COMPLETE' && (
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Simulation Complete</h3>
          <p className="text-gray-400 mb-6">
            Your simulation has finished processing. View the results to analyze revenue impact and optimization opportunities.
          </p>
          
          <div className="flex space-x-4">
            <Button variant="fiery" onClick={onComplete}>
              <BarChart3 className="w-4 h-4 mr-2" />
              View Results
            </Button>
            <Button variant="outline">
              Export Report
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

interface ModelRunProgressProps {
  modelRun: any
}

function ModelRunProgress({ modelRun }: ModelRunProgressProps) {
  const getProgressPercentage = () => {
    if (!modelRun.total_records || modelRun.total_records === 0) return 0
    return Math.round((modelRun.records_processed / modelRun.total_records) * 100)
  }

  const StatusIcon = getStatusIcon(modelRun.status)
  const statusColor = getStatusColor(modelRun.status)
  const progressPercentage = getProgressPercentage()

  return (
    <div className="firenest-nested-card">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <StatusIcon className={`w-4 h-4 ${statusColor} ${modelRun.status === 'RUNNING' ? 'animate-spin' : ''}`} />
          <div>
            <h4 className="text-white font-medium">{modelRun.model_name}</h4>
            <Badge variant="secondary" className="text-xs">
              {modelRun.status}
            </Badge>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-white">
            {modelRun.records_processed || 0} / {modelRun.total_records || 0}
          </div>
          <div className="text-xs text-gray-400">Records processed</div>
        </div>
      </div>

      {modelRun.status === 'RUNNING' && (
        <div className="mb-3">
          <div className="flex justify-between text-xs mb-1">
            <span className="text-gray-400">Progress</span>
            <span className="text-white">{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1">
            <div 
              className="bg-blue-400 h-1 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      )}

      {modelRun.error_message && (
        <div className="text-xs text-red-400 mt-2">
          Error: {modelRun.error_message}
        </div>
      )}
    </div>
  )
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'QUEUED':
      return Clock
    case 'RUNNING':
      return Loader2
    case 'COMPLETE':
      return CheckCircle
    case 'FAILED':
      return XCircle
    case 'CANCELLED':
      return XCircle
    default:
      return Clock
  }
}

function getStatusColor(status: string) {
  switch (status) {
    case 'QUEUED':
      return 'text-yellow-400'
    case 'RUNNING':
      return 'text-blue-400'
    case 'COMPLETE':
      return 'text-green-400'
    case 'FAILED':
      return 'text-red-400'
    case 'CANCELLED':
      return 'text-gray-400'
    default:
      return 'text-gray-400'
  }
}
