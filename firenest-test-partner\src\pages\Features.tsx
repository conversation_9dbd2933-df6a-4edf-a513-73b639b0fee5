import Header from '../components/Header';
import Footer from '../components/Footer';
import Features from '../components/Features';
import CTA from '../components/CTA';
import LoginSignup from '../components/LoginSignup';

const FeaturesPage = () => {
  const featureSections = [
    {
      title: "AI-Powered Content Creation",
      description: "Create high-quality content in seconds with our advanced AI models.",
      image: "https://images.unsplash.com/photo-1593642532744-d377ab507dc8",
      features: [
        "Generate blog posts, social media content, and marketing copy",
        "Customize tone, style, and format to match your brand voice",
        "Edit and refine AI-generated content with our intuitive editor",
        "Create content in multiple languages with our multilingual support"
      ],
      imagePosition: "right"
    },
    {
      title: "Advanced SEO Tools",
      description: "Optimize your content for search engines and drive more organic traffic.",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f",
      features: [
        "Keyword research and suggestions to target the right terms",
        "Real-time SEO analysis and recommendations as you write",
        "Readability scoring to ensure your content is easy to understand",
        "Meta tag generation for improved search engine visibility"
      ],
      imagePosition: "left"
    },
    {
      title: "Team Collaboration",
      description: "Work together seamlessly with your team to create and manage content.",
      image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c",
      features: [
        "Real-time collaboration with multiple team members",
        "Comment and feedback system for content review",
        "Approval workflows to streamline the content creation process",
        "Role-based access control for team management"
      ],
      imagePosition: "right"
    },
    {
      title: "Analytics & Reporting",
      description: "Track content performance and measure the impact of your content strategy.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71",
      features: [
        "Comprehensive analytics dashboard to track content performance",
        "Custom reports to measure ROI and content effectiveness",
        "Audience insights to understand reader engagement",
        "Content performance tracking across multiple channels"
      ],
      imagePosition: "left"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-dark-900">
      <Header />

      <main className="flex-1">
        <section className="py-20 md:py-32 bg-dark-900 relative overflow-hidden">
          {/* Background gradient */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute -top-24 -left-24 w-96 h-96 bg-primary rounded-full filter blur-3xl opacity-20"></div>
            <div className="absolute -bottom-24 -right-24 w-96 h-96 bg-secondary rounded-full filter blur-3xl opacity-20"></div>
          </div>

          <div className="container relative z-10">
            <div className="flex flex-col lg:flex-row items-center gap-12">
              {/* Left side - Content */}
              <div className="w-full lg:w-1/2 text-left">
                <h1 className="text-4xl md:text-5xl font-bold mb-6">
                  Powerful <span className="gradient-text">AI Features</span> for Content Teams
                </h1>
                <p className="text-xl text-light-600 mb-8">
                  Discover how AtlasAI can transform your content creation process with our comprehensive suite of AI-powered tools.
                </p>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <svg className="w-6 h-6 text-primary flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="ml-3 text-light">Generate high-quality content in seconds</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-6 h-6 text-primary flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="ml-3 text-light">Optimize for SEO and readability</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-6 h-6 text-primary flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="ml-3 text-light">Collaborate with your team in real-time</span>
                  </li>
                </ul>
              </div>

              {/* Right side - Login/Signup Form */}
              <div className="w-full lg:w-1/2">
                <LoginSignup />
              </div>
            </div>
          </div>
        </section>

        <Features />

        {/* Detailed Feature Sections */}
        <section className="bg-dark-800 py-24">
          <div className="container">
            {featureSections.map((section, index) => (
              <div
                key={index}
                className={`flex flex-col ${section.imagePosition === 'right' ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center mb-24 last:mb-0`}
              >
                <div className="w-full lg:w-1/2 mb-10 lg:mb-0">
                  <h2 className="text-3xl font-bold mb-6 text-white">{section.title}</h2>
                  <p className="text-light-600 text-lg mb-8">{section.description}</p>

                  <ul className="space-y-4">
                    {section.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <svg className="w-6 h-6 text-primary flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="ml-3 text-light">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className={`w-full lg:w-1/2 ${section.imagePosition === 'right' ? 'lg:pl-12' : 'lg:pr-12'}`}>
                  <div className="rounded-xl overflow-hidden shadow-xl border border-dark-600">
                    <img
                      src={`${section.image}?auto=format&fit=crop&w=800&q=80`}
                      alt={section.title}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        <CTA />
      </main>

      <Footer />
    </div>
  );
};

export default FeaturesPage;
