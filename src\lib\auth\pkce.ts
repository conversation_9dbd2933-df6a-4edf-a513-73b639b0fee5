/**
 * PKCE (Proof Key for Code Exchange) Utilities
 * 
 * This module provides functions for implementing PKCE in the OAuth 2.0 authorization flow.
 * PKCE is an extension to the Authorization Code flow to prevent CSRF and authorization code injection attacks.
 * 
 * References:
 * - RFC 7636: https://tools.ietf.org/html/rfc7636
 */

import crypto from 'crypto';

/**
 * Generates a random code verifier for PKCE.
 * The code verifier is a high-entropy cryptographic random string using the
 * unreserved characters [A-Z], [a-z], [0-9], "-", ".", "_", "~".
 * 
 * @returns {string} A random code verifier string
 */
export function generateCodeVerifier(): string {
  // Generate a random string of 43-128 characters (we use 64)
  const buffer = crypto.randomBytes(48);
  return buffer.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
    .substring(0, 64);
}

/**
 * Generates a code challenge from a code verifier using the S256 method.
 * The code challenge is derived from the code verifier by using the SHA-256
 * hash algorithm on the code verifier, then base64url-encoding the result.
 * 
 * @param {string} codeVerifier - The code verifier to generate a challenge from
 * @returns {string} The code challenge
 */
export function generateCodeChallenge(codeVerifier: string): string {
  const hash = crypto.createHash('sha256').update(codeVerifier).digest();
  return hash.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Verifies that a code challenge matches a code verifier.
 * 
 * @param {string} codeVerifier - The code verifier
 * @param {string} codeChallenge - The code challenge to verify
 * @param {string} method - The challenge method ('plain' or 'S256')
 * @returns {boolean} True if the challenge is valid for the verifier
 */
export function verifyCodeChallenge(
  codeVerifier: string,
  codeChallenge: string,
  method: 'plain' | 'S256' = 'S256'
): boolean {
  if (method === 'plain') {
    return codeVerifier === codeChallenge;
  } else if (method === 'S256') {
    const calculatedChallenge = generateCodeChallenge(codeVerifier);
    return calculatedChallenge === codeChallenge;
  }
  return false;
}

/**
 * Stores a code verifier in session storage for later retrieval.
 * 
 * @param {string} codeVerifier - The code verifier to store
 * @param {string} state - The state parameter used in the authorization request
 */
export function storeCodeVerifier(codeVerifier: string, state: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem(`pkce_verifier_${state}`, codeVerifier);
  }
}

/**
 * Retrieves a stored code verifier from session storage.
 * 
 * @param {string} state - The state parameter used in the authorization request
 * @returns {string|null} The stored code verifier or null if not found
 */
export function retrieveCodeVerifier(state: string): string | null {
  if (typeof window !== 'undefined') {
    const verifier = sessionStorage.getItem(`pkce_verifier_${state}`);
    // Clean up after retrieval
    sessionStorage.removeItem(`pkce_verifier_${state}`);
    return verifier;
  }
  return null;
}

/**
 * Generates PKCE parameters for an authorization request.
 * 
 * @returns {Object} An object containing the code_verifier, code_challenge, and code_challenge_method
 */
export function generatePkceParameters() {
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = generateCodeChallenge(codeVerifier);
  
  return {
    codeVerifier,
    codeChallenge,
    codeChallengeMethod: 'S256' as const
  };
}
