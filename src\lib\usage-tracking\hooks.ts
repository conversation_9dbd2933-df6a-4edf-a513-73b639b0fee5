/**
 * Usage Tracking Hooks
 * 
 * This file provides React hooks for using the usage tracking system.
 */

import { useState, useEffect, useCallback } from 'react';
import { usageTracking } from './usage-tracking';
import { 
  UsageEvent, 
  UsageSummary, 
  UsageAlert,
  UsageReport,
  UsageEventType
} from './types';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook for tracking usage events
 */
export function useUsageEvents(serviceId?: string) {
  const { user } = useAuth();
  const [events, setEvents] = useState<UsageEvent[]>([]);
  
  useEffect(() => {
    if (!user) {
      setEvents([]);
      return;
    }
    
    // Set up event listener
    const unsubscribe = usageTracking.addEventListener('all', (event: UsageEvent) => {
      if (event.userId === user.id && (!serviceId || event.serviceId === serviceId)) {
        setEvents(prevEvents => [...prevEvents, event]);
      }
    });
    
    return () => {
      unsubscribe();
    };
  }, [user, serviceId]);
  
  const trackEvent = useCallback((
    serviceId: string,
    sessionId: string,
    type: UsageEventType,
    data: any
  ) => {
    if (!user) {
      return null;
    }
    
    switch (type) {
      case 'session_start':
        return usageTracking.trackSessionStart(user.id, serviceId, sessionId);
      case 'session_end':
        return usageTracking.trackSessionEnd(user.id, serviceId, sessionId, data);
      case 'activity':
        return usageTracking.trackActivity(user.id, serviceId, sessionId, data);
      case 'api_call':
        return usageTracking.trackApiCall(user.id, serviceId, sessionId, data.count, data.details);
      case 'resource_consumption':
        return usageTracking.trackResourceConsumption(user.id, serviceId, sessionId, data.count, data.details);
      default:
        return null;
    }
  }, [user]);
  
  return {
    events,
    trackEvent
  };
}

/**
 * Hook for accessing usage summaries
 */
export function useUsageSummaries(period: 'day' | 'week' | 'month' | 'all' = 'all') {
  const { user } = useAuth();
  const [summaries, setSummaries] = useState<UsageSummary[]>([]);
  const [totalCredits, setTotalCredits] = useState(0);
  
  const refreshSummaries = useCallback(() => {
    if (!user) {
      setSummaries([]);
      setTotalCredits(0);
      return;
    }
    
    const userSummaries = usageTracking.getUserUsageSummaries(user.id, period);
    setSummaries(userSummaries);
    
    const total = usageTracking.getTotalCreditsUsed(user.id, period);
    setTotalCredits(total);
  }, [user, period]);
  
  useEffect(() => {
    refreshSummaries();
    
    // Set up event listener
    const unsubscribe = usageTracking.addEventListener('all', () => {
      refreshSummaries();
    });
    
    return () => {
      unsubscribe();
    };
  }, [user, period, refreshSummaries]);
  
  return {
    summaries,
    totalCredits,
    refreshSummaries
  };
}

/**
 * Hook for accessing usage alerts
 */
export function useUsageAlerts() {
  const { user } = useAuth();
  const [alerts, setAlerts] = useState<UsageAlert[]>([]);
  
  const refreshAlerts = useCallback(() => {
    if (!user) {
      setAlerts([]);
      return;
    }
    
    const userAlerts = usageTracking.getUserAlerts(user.id);
    setAlerts(userAlerts);
  }, [user]);
  
  useEffect(() => {
    refreshAlerts();
    
    // Set up event listener
    const unsubscribe = usageTracking.addEventListener('all', () => {
      refreshAlerts();
    });
    
    return () => {
      unsubscribe();
    };
  }, [user, refreshAlerts]);
  
  const acknowledgeAlert = useCallback((alertId: string) => {
    const success = usageTracking.acknowledgeAlert(alertId);
    
    if (success) {
      refreshAlerts();
    }
    
    return success;
  }, [refreshAlerts]);
  
  return {
    alerts,
    refreshAlerts,
    acknowledgeAlert
  };
}

/**
 * Hook for generating usage reports
 */
export function useUsageReports() {
  const { user } = useAuth();
  const [report, setReport] = useState<UsageReport | null>(null);
  
  const generateReport = useCallback((period: 'day' | 'week' | 'month' = 'month') => {
    if (!user) {
      setReport(null);
      return null;
    }
    
    const userReport = usageTracking.generateUsageReport(user.id, period);
    setReport(userReport);
    return userReport;
  }, [user]);
  
  return {
    report,
    generateReport
  };
}
