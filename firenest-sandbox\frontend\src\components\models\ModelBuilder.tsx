/**
 * Pricing Model Builder
 * Phase 2: Visual pricing model creation interface
 * Drag-and-drop component-based model building
 */

import React, { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  Plus, 
  Calculator, 
  DollarSign, 
  TrendingUp, 
  Layers,
  Save,
  Eye,
  Settings
} from 'lucide-react'
import { modelsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { ComponentPalette } from './ComponentPalette'
import { ModelCanvas } from './ModelCanvas'
import { ComponentEditor } from './ComponentEditor'
import { ModelPreview } from './ModelPreview'
import toast from 'react-hot-toast'

interface ModelBuilderProps {
  projectId: string
  model?: any
  onSave?: (model: any) => void
  onCancel?: () => void
}

export interface ModelComponent {
  id: string
  type: 'BASE_FEE' | 'TIERED_RATE' | 'PER_UNIT_RATE' | 'MINIMUM_FEE' | 'MAXIMUM_FEE'
  config: any
  sortOrder: number
  isNew?: boolean
}

const MODEL_TYPES = [
  {
    value: 'USAGE_BASED',
    label: 'Usage-Based',
    description: 'Charge based on actual usage metrics',
    icon: TrendingUp,
    color: 'text-blue-400'
  },
  {
    value: 'TIERED',
    label: 'Tiered Pricing',
    description: 'Different rates for usage tiers',
    icon: Layers,
    color: 'text-purple-400'
  },
  {
    value: 'HYBRID',
    label: 'Hybrid Model',
    description: 'Combination of base fee and usage',
    icon: Calculator,
    color: 'text-green-400'
  },
  {
    value: 'SUBSCRIPTION',
    label: 'Subscription',
    description: 'Fixed recurring pricing',
    icon: DollarSign,
    color: 'text-fiery'
  }
]

export function ModelBuilder({ projectId, model, onSave, onCancel }: ModelBuilderProps) {
  const [modelName, setModelName] = useState(model?.name || '')
  const [modelDescription, setModelDescription] = useState(model?.description || '')
  const [modelType, setModelType] = useState(model?.model_type || 'USAGE_BASED')
  const [components, setComponents] = useState<ModelComponent[]>(
    model?.components?.map((comp: any, index: number) => ({
      id: comp.id,
      type: comp.component_type,
      config: comp.config,
      sortOrder: comp.sort_order || index
    })) || []
  )
  const [selectedComponent, setSelectedComponent] = useState<ModelComponent | null>(null)
  const [activeTab, setActiveTab] = useState<'build' | 'preview'>('build')
  const [isEditing, setIsEditing] = useState(false)

  const queryClient = useQueryClient()

  const createModelMutation = useMutation({
    mutationFn: (data: any) => modelsApi.create(data),
    onSuccess: (response) => {
      toast.success('Pricing model created successfully!')
      queryClient.invalidateQueries({ queryKey: ['models', projectId] })
      onSave?.(response.data.data)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create model')
    }
  })

  const updateModelMutation = useMutation({
    mutationFn: (data: any) => modelsApi.update(model.id, data),
    onSuccess: (response) => {
      toast.success('Pricing model updated successfully!')
      queryClient.invalidateQueries({ queryKey: ['models', model.id] })
      onSave?.(response.data.data)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update model')
    }
  })

  const handleSaveModel = async () => {
    if (!modelName.trim()) {
      toast.error('Please enter a model name')
      return
    }

    const modelData = {
      name: modelName,
      description: modelDescription,
      modelType,
      projectId
    }

    if (model) {
      updateModelMutation.mutate(modelData)
    } else {
      createModelMutation.mutate(modelData)
    }
  }

  const handleAddComponent = (componentType: ModelComponent['type']) => {
    const newComponent: ModelComponent = {
      id: `temp-${Date.now()}`,
      type: componentType,
      config: getDefaultConfig(componentType),
      sortOrder: components.length,
      isNew: true
    }
    
    setComponents([...components, newComponent])
    setSelectedComponent(newComponent)
    setIsEditing(true)
  }

  const handleUpdateComponent = (updatedComponent: ModelComponent) => {
    setComponents(components.map(comp => 
      comp.id === updatedComponent.id ? updatedComponent : comp
    ))
    setSelectedComponent(updatedComponent)
  }

  const handleDeleteComponent = (componentId: string) => {
    setComponents(components.filter(comp => comp.id !== componentId))
    if (selectedComponent?.id === componentId) {
      setSelectedComponent(null)
    }
  }

  const handleReorderComponents = (newComponents: ModelComponent[]) => {
    setComponents(newComponents.map((comp, index) => ({
      ...comp,
      sortOrder: index
    })))
  }

  const getDefaultConfig = (type: ModelComponent['type']) => {
    switch (type) {
      case 'BASE_FEE':
        return {
          amount: 0,
          currency: 'USD',
          period: 'monthly'
        }
      case 'PER_UNIT_RATE':
        return {
          metricName: '',
          unitRate: 0,
          currency: 'USD'
        }
      case 'TIERED_RATE':
        return {
          metricName: '',
          tiers: [
            { upTo: 1000, unitRate: 0.10 },
            { upTo: 'infinity', unitRate: 0.05 }
          ],
          currency: 'USD'
        }
      case 'MINIMUM_FEE':
      case 'MAXIMUM_FEE':
        return {
          amount: 0,
          currency: 'USD',
          period: 'monthly'
        }
      default:
        return {}
    }
  }

  const selectedModelType = MODEL_TYPES.find(type => type.value === modelType)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Calculator className="w-8 h-8 text-fiery" />
            <div>
              <h2 className="text-2xl font-bold text-white">
                {model ? 'Edit Pricing Model' : 'Create Pricing Model'}
              </h2>
              <p className="text-gray-400">
                Build flexible pricing strategies with visual components
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              onClick={handleSaveModel}
              disabled={createModelMutation.isPending || updateModelMutation.isPending}
              variant="fiery"
            >
              <Save className="w-4 h-4 mr-2" />
              {model ? 'Update Model' : 'Create Model'}
            </Button>
          </div>
        </div>

        {/* Model Basic Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="form-label mb-2">Model Name</label>
            <input
              type="text"
              value={modelName}
              onChange={(e) => setModelName(e.target.value)}
              placeholder="e.g., Usage-Based Pricing v2"
              className="form-input"
            />
          </div>
          
          <div>
            <label className="form-label mb-2">Model Type</label>
            <select
              value={modelType}
              onChange={(e) => setModelType(e.target.value)}
              className="form-input"
            >
              {MODEL_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-4">
          <label className="form-label mb-2">Description</label>
          <textarea
            value={modelDescription}
            onChange={(e) => setModelDescription(e.target.value)}
            placeholder="Describe your pricing strategy..."
            rows={3}
            className="form-input"
          />
        </div>

        {/* Model Type Info */}
        {selectedModelType && (
          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center space-x-3">
              <selectedModelType.icon className={`w-5 h-5 ${selectedModelType.color}`} />
              <div>
                <h4 className="font-medium text-white">{selectedModelType.label}</h4>
                <p className="text-sm text-gray-400">{selectedModelType.description}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="firenest-card">
        <div className="flex space-x-1 p-1 bg-muted rounded-lg">
          <button
            onClick={() => setActiveTab('build')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'build'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Settings className="w-4 h-4 mr-2" />
            Build Model
          </button>
          <button
            onClick={() => setActiveTab('preview')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'preview'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'build' ? (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Component Palette */}
          <div className="lg:col-span-1">
            <ComponentPalette onAddComponent={handleAddComponent} />
          </div>

          {/* Model Canvas */}
          <div className="lg:col-span-2">
            <ModelCanvas
              components={components}
              selectedComponent={selectedComponent}
              onSelectComponent={setSelectedComponent}
              onDeleteComponent={handleDeleteComponent}
              onReorderComponents={handleReorderComponents}
            />
          </div>

          {/* Component Editor */}
          <div className="lg:col-span-1">
            <ComponentEditor
              component={selectedComponent}
              isEditing={isEditing}
              onUpdateComponent={handleUpdateComponent}
              onStartEditing={() => setIsEditing(true)}
              onStopEditing={() => setIsEditing(false)}
            />
          </div>
        </div>
      ) : (
        <ModelPreview
          modelName={modelName}
          modelType={modelType}
          components={components}
        />
      )}
    </div>
  )
}
