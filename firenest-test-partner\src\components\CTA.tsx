const CTA = () => {
  return (
    <section className="section bg-dark-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary rounded-full filter blur-3xl opacity-10"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary rounded-full filter blur-3xl opacity-10"></div>
      </div>

      <div className="container relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
            Ready to Transform Your <span className="gradient-text">Content Strategy</span>?
          </h2>

          <p className="text-xl text-light-600 mb-10 max-w-3xl mx-auto">
            Join thousands of teams already using AtlasAI to create better content in less time. Start your 14-day free trial today.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a href="/features" className="btn btn-primary text-lg px-8 py-4">
              Create Account
            </a>
            <a href="#" className="btn btn-outline text-lg px-8 py-4">
              Schedule Demo
            </a>
          </div>

          <p className="text-light-600 text-sm mt-6">
            No credit card required. Cancel anytime.
          </p>
        </div>
      </div>
    </section>
  );
};

export default CTA;
