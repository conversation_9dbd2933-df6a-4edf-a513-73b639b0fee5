// Script to revert the policy changes made to the access_tokens table
import { supabase } from '@/lib/supabase';
import fs from 'fs';
import path from 'path';

async function revertPolicyChanges() {
  try {
    console.log('Reading SQL revert script...');
    const sqlFilePath = path.join(process.cwd(), 'src', 'lib', 'auth', 'revert-policy-changes.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('Reverting policy changes...');
    const { error } = await supabase.rpc('exec_sql', { sql: sqlContent });
    
    if (error) {
      console.error('Error reverting policy changes:', error);
      
      // If the exec_sql function doesn't exist, we can't revert the changes using RPC
      if (error.message.includes('function') && error.message.includes('does not exist')) {
        console.error('The exec_sql function does not exist. You may need to revert the changes manually.');
        console.log('SQL to execute manually:');
        console.log(sqlContent);
      }
      
      return {
        success: false,
        error: error.message
      };
    }
    
    console.log('Policy changes reverted successfully!');
    
    // Check if the policies were actually removed
    const { data, error: checkError } = await supabase.rpc('get_table_policies', { table_name: 'access_tokens' });
    
    if (checkError) {
      console.warn('Error checking policies after reversion:', checkError);
      // Continue anyway, as the reversion might have succeeded
    } else {
      // Check if any INSERT policies still exist
      const insertPolicies = data.filter(policy => policy.operation === 'INSERT');
      
      if (insertPolicies.length > 0) {
        console.warn('Some INSERT policies still exist after reversion:', insertPolicies);
        return {
          success: false,
          warning: 'Some INSERT policies still exist',
          policies: insertPolicies
        };
      }
      
      console.log('Verified that all INSERT policies have been removed.');
    }
    
    return {
      success: true,
      message: 'Policy changes reverted successfully'
    };
  } catch (error) {
    console.error('Exception reverting policy changes:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  revertPolicyChanges()
    .then(result => {
      console.log('Reversion result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

export default revertPolicyChanges;
