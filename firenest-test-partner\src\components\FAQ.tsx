import { useState } from 'react';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const toggleFaq = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqs = [
    {
      question: "How does AtlasAI's content generation work?",
      answer: "AtlasAI uses advanced natural language processing models to generate high-quality content based on your inputs. Simply provide a topic, keywords, and tone preferences, and our AI will create content tailored to your needs. You can then edit and refine the content as needed."
    },
    {
      question: "Can I try AtlasAI before subscribing?",
      answer: "Yes! All plans include a 14-day free trial with full access to features. No credit card is required to start your trial. You can upgrade to a paid plan at any time during or after your trial period."
    },
    {
      question: "How does billing work with Firenest?",
      answer: "AtlasAI uses Firenest for authentication and billing. When you sign up, you'll create a Firenest account that manages your subscription and payments. Firenest provides a unified billing system for multiple AI tools, making it easier to manage all your subscriptions in one place."
    },
    {
      question: "Can I upgrade or downgrade my plan later?",
      answer: "Absolutely! You can upgrade your plan at any time, and the changes will take effect immediately. If you downgrade, the changes will apply at the start of your next billing cycle. Your Firenest account makes this process seamless."
    },
    {
      question: "Is there a limit to how much content I can generate?",
      answer: "Each plan has different content generation limits. The Starter plan includes 10,000 words per month, Professional includes 50,000 words per month, and Enterprise offers unlimited content generation. If you need more content, you can upgrade your plan or purchase additional words."
    },
    {
      question: "How does the team collaboration feature work?",
      answer: "Our collaboration features allow multiple team members to work on content together. You can assign tasks, leave comments, request approvals, and track changes. Each plan includes a different number of user seats, and you can add more seats as needed."
    },
    {
      question: "What integrations does AtlasAI support?",
      answer: "AtlasAI integrates with popular platforms like WordPress, Shopify, HubSpot, Google Analytics, and more. The number of available integrations depends on your plan. Enterprise customers can request custom integrations to fit their specific needs."
    },
    {
      question: "Is my data secure with AtlasAI?",
      answer: "Yes, we take data security very seriously. All data is encrypted in transit and at rest. We follow industry best practices for security and compliance. Enterprise plans include additional security features like SSO, role-based access control, and custom data retention policies."
    }
  ];

  return (
    <section className="section bg-dark-800">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Frequently Asked <span className="gradient-text">Questions</span>
          </h2>
          <p className="text-light-600 text-lg">
            Have questions about AtlasAI? Find answers to common questions below.
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <div 
              key={index} 
              className="border-b border-dark-600 last:border-b-0"
            >
              <button
                onClick={() => toggleFaq(index)}
                className="w-full py-6 flex justify-between items-center text-left focus:outline-none"
              >
                <h3 className="text-lg font-medium text-white">{faq.question}</h3>
                <svg 
                  className={`w-6 h-6 text-primary transition-transform ${openIndex === index ? 'transform rotate-180' : ''}`} 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <div 
                className={`overflow-hidden transition-all duration-300 ${
                  openIndex === index ? 'max-h-96 pb-6' : 'max-h-0'
                }`}
              >
                <p className="text-light-600">{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-light-600 mb-6">
            Still have questions? We're here to help.
          </p>
          <a href="#" className="btn btn-outline">
            Contact Support
          </a>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
