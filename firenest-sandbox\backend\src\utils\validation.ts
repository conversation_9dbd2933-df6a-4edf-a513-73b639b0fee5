/**
 * Validation Utilities
 * Input validation and environment validation
 */

import Jo<PERSON> from 'joi';
import { ValidationError } from '@/middleware/errorHandler';
import { config } from '@/config/environment';

// Environment validation
export function validateEnvironment(): void {
  const requiredEnvVars = [
    'DB_PASSWORD',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'S3_BUCKET_NAME',
    'SQS_QUEUE_URL',
    'JWT_SECRET'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Validate JWT secret length
  if (config.auth.jwtSecret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }
}

// Common validation schemas
export const schemas = {
  uuid: Joi.string().uuid().required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  name: Joi.string().min(1).max(255).required(),
  description: Joi.string().max(1000).optional(),
  
  // Workspace schemas
  createWorkspace: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().max(1000).optional(),
    settings: Joi.object().optional()
  }),

  updateWorkspace: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().max(1000).optional(),
    settings: Joi.object().optional()
  }),

  // Project schemas
  createProject: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().max(1000).optional(),
    workspaceId: Joi.string().uuid().required()
  }),

  updateProject: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().max(1000).optional(),
    status: Joi.string().valid('CREATED', 'UPLOADING', 'VALIDATING', 'READY', 'SIMULATING', 'COMPLETE', 'ERROR').optional()
  }),

  // Pricing model schemas
  createPricingModel: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().max(1000).optional(),
    modelType: Joi.string().valid('USAGE_BASED', 'HYBRID', 'SUBSCRIPTION', 'TIERED').required(),
    projectId: Joi.string().uuid().required()
  }),

  updatePricingModel: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().max(1000).optional(),
    modelType: Joi.string().valid('USAGE_BASED', 'HYBRID', 'SUBSCRIPTION', 'TIERED').optional()
  }),

  // Model component schemas
  createModelComponent: Joi.object({
    componentType: Joi.string().valid('BASE_FEE', 'TIERED_RATE', 'PER_UNIT_RATE', 'MINIMUM_FEE', 'MAXIMUM_FEE').required(),
    config: Joi.object().required(),
    sortOrder: Joi.number().integer().min(0).optional()
  }),

  // Base fee component config
  baseFeeConfig: Joi.object({
    amount: Joi.number().positive().required(),
    currency: Joi.string().length(3).required(),
    period: Joi.string().valid('monthly', 'yearly', 'one-time').required()
  }),

  // Per unit rate component config
  perUnitRateConfig: Joi.object({
    metricName: Joi.string().min(1).max(100).required(),
    unitRate: Joi.number().positive().required(),
    currency: Joi.string().length(3).optional().default('USD')
  }),

  // Tiered rate component config
  tieredRateConfig: Joi.object({
    metricName: Joi.string().min(1).max(100).required(),
    tiers: Joi.array().items(
      Joi.object({
        upTo: Joi.alternatives().try(
          Joi.number().positive(),
          Joi.string().valid('infinity')
        ).required(),
        unitRate: Joi.number().min(0).required()
      })
    ).min(1).required(),
    currency: Joi.string().length(3).optional().default('USD')
  }),

  // File upload schemas
  uploadInitiate: Joi.object({
    fileName: Joi.string().min(1).max(255).required(),
    fileSize: Joi.number().positive().max(100 * 1024 * 1024).required(), // 100MB max
    fileType: Joi.string().valid('customer_usage_data', 'billing_data', 'customer_metadata').required(),
    projectId: Joi.string().uuid().required()
  }),

  uploadFinalize: Joi.object({
    key: Joi.string().min(1).required(),
    fileName: Joi.string().min(1).max(255).required(),
    fileType: Joi.string().valid('customer_usage_data', 'billing_data', 'customer_metadata').required()
  }),

  // Simulation schemas
  createSimulation: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    modelIds: Joi.array().items(Joi.string().uuid()).min(1).max(10).required(),
    projectId: Joi.string().uuid().required(),
    scenarioConfig: Joi.object({
      includeCustomerSegmentation: Joi.boolean().optional().default(true),
      calculateSensitivityAnalysis: Joi.boolean().optional().default(true),
      projectionPeriod: Joi.number().integer().min(1).max(60).optional().default(12)
    }).optional()
  }),

  // Pagination schemas
  pagination: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc')
  }),

  // Query filters
  projectFilters: Joi.object({
    workspaceId: Joi.string().uuid().optional(),
    status: Joi.string().valid('CREATED', 'UPLOADING', 'VALIDATING', 'READY', 'SIMULATING', 'COMPLETE', 'ERROR').optional(),
    search: Joi.string().max(255).optional()
  }),

  simulationFilters: Joi.object({
    projectId: Joi.string().uuid().optional(),
    status: Joi.string().valid('QUEUED', 'RUNNING', 'COMPLETE', 'FAILED').optional(),
    search: Joi.string().max(255).optional()
  })
};

// Validation middleware factory
export function validate(schema: Joi.Schema, property: 'body' | 'query' | 'params' = 'body') {
  return (req: any, _res: any, next: any) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      throw new ValidationError('Validation failed', details);
    }

    // Replace the property with the validated and sanitized value
    req[property] = value;
    next();
  };
}

// Custom validation functions
export function validateModelComponentConfig(componentType: string, config: any): void {
  let schema: Joi.ObjectSchema;

  switch (componentType) {
    case 'BASE_FEE':
      schema = schemas.baseFeeConfig;
      break;
    case 'PER_UNIT_RATE':
      schema = schemas.perUnitRateConfig;
      break;
    case 'TIERED_RATE':
      schema = schemas.tieredRateConfig;
      break;
    default:
      throw new ValidationError(`Invalid component type: ${componentType}`);
  }

  const { error } = schema.validate(config);
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
    throw new ValidationError('Component configuration validation failed', details);
  }
}

// File validation
export function validateFileType(filename: string, allowedTypes: string[]): boolean {
  const extension = filename.split('.').pop()?.toLowerCase();
  return allowedTypes.includes(extension || '');
}

export function validateFileSize(size: number, maxSize: number): boolean {
  return size <= maxSize;
}

// CSV validation helpers
export function validateCSVHeaders(headers: string[], requiredHeaders: string[]): void {
  const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
  if (missingHeaders.length > 0) {
    throw new ValidationError(`Missing required CSV headers: ${missingHeaders.join(', ')}`);
  }
}

// Sanitization helpers
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function sanitizeFilename(filename: string): string {
  return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
}

// UUID validation
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}
