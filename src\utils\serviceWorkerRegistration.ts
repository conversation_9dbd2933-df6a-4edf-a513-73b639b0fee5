// This optional code is used to register a service worker.
// register() is not called by default.

// This lets the app load faster on subsequent visits in production, and gives
// it offline capabilities. However, it also means that developers (and users)
// will only see deployed updates on subsequent visits to a page, after all the
// existing tabs have been closed, since previously cached resources are updated
// in the background.

const isLocalhost = Boolean(
  window.location.hostname === 'localhost' ||
    // [::1] is the IPv6 localhost address.
    window.location.hostname === '[::1]' ||
    // *********/8 are considered localhost for IPv4.
    window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)
);

type Config = {
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
};

// Track if the service worker has been registered
let isServiceWorkerRegistered = false;

export function register(config?: Config): void {
  try {
    // Skip registration if already registered
    if (isServiceWorkerRegistered) {
      console.log('Service worker already registered, skipping registration');
      return;
    }

    // Only register in production and if browser supports service workers
    if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {
      // The URL constructor is available in all browsers that support SW.
      const publicUrl = new URL(process.env.PUBLIC_URL || '', window.location.href);
      if (publicUrl.origin !== window.location.origin) {
        // Our service worker won't work if PUBLIC_URL is on a different origin
        console.log('Skipping service worker registration: PUBLIC_URL is on a different origin');
        return;
      }

      // Register when the window loads
      window.addEventListener('load', () => {
        try {
          const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;
          console.log('Registering service worker from:', swUrl);

          if (isLocalhost) {
            // This is running on localhost. Let's check if a service worker still exists or not.
            checkValidServiceWorker(swUrl, config);

            // Add some additional logging to localhost
            navigator.serviceWorker.ready.then(() => {
              console.log('Service worker ready on localhost');
            });
          } else {
            // Is not localhost. Just register service worker
            registerValidSW(swUrl, config);
          }
        } catch (error) {
          console.error('Error in service worker registration load handler:', error);
        }
      });
    } else {
      console.log('Service worker not registered: not in production or browser does not support service workers');
    }
  } catch (error) {
    console.error('Error in service worker registration:', error);
  }
}

function registerValidSW(swUrl: string, config?: Config): void {
  navigator.serviceWorker
    .register(swUrl)
    .then((registration) => {
      // Mark service worker as registered
      isServiceWorkerRegistered = true;
      // Check for updates every hour
      setInterval(() => {
        registration.update();
      }, 60 * 60 * 1000);

      registration.onupdatefound = () => {
        const installingWorker = registration.installing;
        if (installingWorker == null) {
          return;
        }
        installingWorker.onstatechange = () => {
          if (installingWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              // At this point, the updated precached content has been fetched,
              // but the previous service worker will still serve the older
              // content until all client tabs are closed.
              console.log(
                'New content is available and will be used when all ' +
                  'tabs for this page are closed. See https://cra.link/PWA.'
              );

              // Execute callback
              if (config && config.onUpdate) {
                config.onUpdate(registration);
              }
            } else {
              // At this point, everything has been precached.
              // It's the perfect time to display a
              // "Content is cached for offline use." message.
              console.log('Content is cached for offline use.');

              // Execute callback
              if (config && config.onSuccess) {
                config.onSuccess(registration);
              }
            }
          }
        };
      };
    })
    .catch((error) => {
      console.error('Error during service worker registration:', error);
    });
}

function checkValidServiceWorker(swUrl: string, config?: Config): void {
  try {
    // Check if the service worker can be found. If it can't reload the page.
    fetch(swUrl, {
      headers: { 'Service-Worker': 'script' },
    })
      .then((response) => {
        // Ensure service worker exists, and that we really are getting a JS file.
        const contentType = response.headers.get('content-type');
        if (
          response.status === 404 ||
          (contentType != null && contentType.indexOf('javascript') === -1)
        ) {
          console.log('No service worker found or invalid content type. Unregistering any existing service worker.');
          // No service worker found. Probably a different app. Reload the page.
          if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(registrations => {
              for (let registration of registrations) {
                registration.unregister();
                console.log('Service worker unregistered');
              }
            });
          }
        } else {
          // Service worker found. Proceed as normal.
          registerValidSW(swUrl, config);
        }
      })
      .catch((error) => {
        console.log('Error checking service worker:', error);
        console.log('No internet connection found or service worker error. App is running without service worker.');
      });
  } catch (error) {
    console.error('Error in checkValidServiceWorker:', error);
  }
}

export function unregister(): void {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error(error.message);
      });
  }
}

// Function to check if there's a new service worker and update it
export function checkForUpdates(): void {
  if ('serviceWorker' in navigator && isServiceWorkerRegistered) {
    navigator.serviceWorker.ready.then((registration) => {
      registration.update();
    });
  }
}

// Function to check if service worker is active
export function isServiceWorkerActive(): boolean {
  return isServiceWorkerRegistered && 'serviceWorker' in navigator && !!navigator.serviceWorker.controller;
}

// Function to send a message to the service worker
export function sendMessageToSW(message: any): void {
  if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage(message);
  }
}
