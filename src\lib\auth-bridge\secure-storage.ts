/**
 * Secure Storage Utility
 *
 * This utility provides a way to store sensitive data like authentication tokens.
 * In a production environment, this would use encryption and secure storage mechanisms.
 * For this implementation, we're using localStorage with a simple prefix.
 */

// Simple base64 encoding/decoding for minimal obfuscation
const encode = (str: string): string => {
  try {
    return btoa(encodeURIComponent(str));
  } catch (e) {
    return str; // Fallback if encoding fails
  }
};

const decode = (str: string): string => {
  try {
    return decodeURIComponent(atob(str));
  } catch (e) {
    return str; // Fallback if decoding fails
  }
};

class SecureStorage {
  /**
   * Set an item in secure storage
   */
  public setItem(key: string, value: string): void {
    try {
      // Simple encoding for minimal obfuscation
      const encodedValue = encode(value);

      // Store in localStorage with a prefix
      localStorage.setItem(`firenest_secure_${key}`, encodedValue);
    } catch (error) {
      console.error('Error setting secure item:', error);
      throw error;
    }
  }

  /**
   * Get an item from secure storage
   */
  public getItem(key: string): string | null {
    try {
      // Get from localStorage
      const encodedValue = localStorage.getItem(`firenest_secure_${key}`);

      if (!encodedValue) {
        return null;
      }

      // Decode the value
      return decode(encodedValue);
    } catch (error) {
      console.error('Error getting secure item:', error);
      return null;
    }
  }

  /**
   * Remove an item from secure storage
   */
  public removeItem(key: string): void {
    try {
      localStorage.removeItem(`firenest_secure_${key}`);
    } catch (error) {
      console.error('Error removing secure item:', error);
      throw error;
    }
  }

  /**
   * Clear all items from secure storage
   */
  public clear(): void {
    try {
      // Remove only items with our prefix
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('firenest_secure_')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error clearing secure storage:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const secureStorage = new SecureStorage();
