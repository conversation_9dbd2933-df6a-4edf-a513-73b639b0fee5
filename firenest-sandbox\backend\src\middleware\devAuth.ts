/**
 * Development Authentication Middleware
 * Provides authentication bypass for development and testing
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        sub: string;
        email: string;
        name: string;
        role: string;
        permissions?: string[];
        auth0Id?: string;
      };
    }
  }
}

// Development user profiles
const DEV_USERS = {
  admin: {
    sub: 'dev-admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
    permissions: ['read:all', 'write:all', 'admin:users', 'admin:system'],
    auth0Id: 'dev-admin-123'
  },
  user: {
    sub: 'dev-user-456',
    email: '<EMAIL>',
    name: 'Demo User',
    role: 'user',
    permissions: ['read:profile', 'write:profile', 'read:projects', 'write:projects', 'read:simulations', 'write:simulations'],
    auth0Id: 'dev-user-456'
  },
  viewer: {
    sub: 'dev-viewer-789',
    email: '<EMAIL>',
    name: 'Viewer User',
    role: 'viewer',
    permissions: ['read:profile', 'read:projects', 'read:simulations'],
    auth0Id: 'dev-viewer-789'
  }
};

/**
 * Development authentication middleware
 * Bypasses Auth0 in development mode and provides mock authentication
 */
export function devAuthMiddleware(req: Request, res: Response, next: NextFunction) {
  // Only use in development
  if (config.environment !== 'development') {
    return next();
  }

  const authHeader = req.headers.authorization;
  
  // If no auth header, provide default user
  if (!authHeader) {
    req.user = DEV_USERS.user;
    logger.debug('Dev auth: No auth header, using default user', { user: req.user.email });
    return next();
  }

  // Extract token
  const token = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

  try {
    // Check for special dev tokens
    if (token === 'dev-admin') {
      req.user = DEV_USERS.admin;
      logger.debug('Dev auth: Admin token used', { user: req.user.email });
      return next();
    }

    if (token === 'dev-viewer') {
      req.user = DEV_USERS.viewer;
      logger.debug('Dev auth: Viewer token used', { user: req.user.email });
      return next();
    }

    if (token === 'dev-user') {
      req.user = DEV_USERS.user;
      logger.debug('Dev auth: User token used', { user: req.user.email });
      return next();
    }

    // Try to decode JWT token (for frontend compatibility)
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.sub) {
        // Use decoded token data if valid
        req.user = {
          sub: decoded.sub,
          email: decoded.email || '<EMAIL>',
          name: decoded.name || 'Demo User',
          role: decoded.role || 'user',
          permissions: decoded.permissions || DEV_USERS.user.permissions,
          auth0Id: decoded.sub
        };
        logger.debug('Dev auth: JWT token decoded', { user: req.user.email });
        return next();
      }
    } catch (jwtError) {
      // JWT decode failed, continue with default
      logger.debug('Dev auth: JWT decode failed, using default user');
    }

    // Default to regular user
    req.user = DEV_USERS.user;
    logger.debug('Dev auth: Using default user', { user: req.user.email });
    next();

  } catch (error) {
    logger.error('Dev auth error:', error);
    req.user = DEV_USERS.user;
    next();
  }
}

/**
 * Create a development JWT token for testing
 */
export function createDevToken(userType: 'admin' | 'user' | 'viewer' = 'user'): string {
  const user = DEV_USERS[userType];
  const payload = {
    ...user,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };

  return jwt.sign(payload, config.auth.jwtSecret);
}

/**
 * Development login endpoint helper
 */
export function handleDevLogin(userType: 'admin' | 'user' | 'viewer' = 'user') {
  const user = DEV_USERS[userType];
  const token = createDevToken(userType);
  
  return {
    success: true,
    token,
    user: {
      id: user.sub,
      email: user.email,
      name: user.name,
      role: user.role,
      permissions: user.permissions
    },
    expiresIn: '24h',
    provider: 'development'
  };
}

/**
 * Middleware to check if user has required role
 */
export function requireRole(roles: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

/**
 * Middleware to check if user has required permission
 */
export function requirePermission(permission: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = req.user.permissions || [];
    if (!userPermissions.includes(permission) && !userPermissions.includes('admin:system')) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}
