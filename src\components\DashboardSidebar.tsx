import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Flame,
  Home,
  CreditCard,
  Settings,
  HelpCircle,
  LogOut,
  Layers
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface DashboardSidebarProps {
  open: boolean;
}

const DashboardSidebar = ({ open }: DashboardSidebarProps) => {
  const location = useLocation();
  const { logout } = useAuth();

  const navItems = [
    { icon: Home, label: 'Dashboard', path: '/dashboard' },
    { icon: Layers, label: 'Workbench', path: '/dashboard/workbench' },
    { icon: CreditCard, label: 'Credits', path: '/dashboard/credits' },
    { icon: Settings, label: 'Settings', path: '/dashboard/settings' },
    { icon: HelpCircle, label: 'Help', path: '/dashboard/help' },
  ];

  const handleLogout = async () => {
    await logout();
  };

  return (
    <aside
      className={cn(
        "bg-sidebar-background border-r border-white/10 transition-all duration-300 z-20",
        open ? "w-64" : "w-20"
      )}
    >
      <div className="h-full flex flex-col">
        <div className="flex items-center gap-2 p-4 h-16 border-b border-white/10">
          <div className="flex items-center justify-center bg-gradient-to-br from-fiery to-fiery-600 rounded-md w-10 h-10">
            <Flame className="text-white h-6 w-6" />
          </div>
          {open && (
            <h1 className="text-xl font-bold">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-fiery via-fiery to-cool-500 bg-size-200">
                <span className="text-fiery">Fir</span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
                <span className="text-cool">est</span>
              </span>
            </h1>
          )}
        </div>

        <nav className="flex-1 py-6 px-3 space-y-1 overflow-y-auto hide-scrollbar">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                location.pathname === item.path
                  ? "bg-white/10 text-white"
                  : "text-white/70 hover:bg-white/5 hover:text-white"
              )}
            >
              <item.icon className="h-5 w-5" />
              {open && <span>{item.label}</span>}
            </Link>
          ))}
        </nav>

        <div className="p-3 border-t border-white/10">
          <button
            onClick={handleLogout}
            className="flex items-center gap-3 px-3 py-2 w-full rounded-md text-white/70 hover:bg-white/5 hover:text-white transition-colors"
          >
            <LogOut className="h-5 w-5" />
            {open && <span>Logout</span>}
          </button>
        </div>
      </div>
    </aside>
  );
};

export default DashboardSidebar;
