/**
 * Models Dashboard
 * Overview of all pricing models across projects
 */

import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { 
  Calculator, 
  Plus, 
  Search, 
  Filter,
  TrendingUp,
  Layers,
  DollarSign,
  BarChart3,
  ArrowRight
} from 'lucide-react'
import { projectsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate } from '@/lib/utils'

export function ModelsDashboard() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [modelTypeFilter, setModelTypeFilter] = useState('')

  const { data: projects, isLoading } = useQuery({
    queryKey: ['projects'],
    queryFn: () => projectsApi.list({ sortBy: 'updated_at', sortOrder: 'desc' })
  })

  if (isLoading) {
    return (
      <div className="main-content">
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  const projectsList = projects?.data?.data || []
  const projectsWithModels = projectsList.filter((project: any) => project.model_count > 0)

  return (
    <div className="main-content">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white">Pricing Models</h1>
          <p className="text-gray-400 mt-1">
            Manage and analyze your pricing strategies across all projects
          </p>
        </div>
        <Button onClick={() => navigate('/projects')} variant="fiery">
          <Plus className="w-4 h-4 mr-2" />
          Create Model
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Models</p>
              <p className="metric-value">
                {projectsList.reduce((sum: number, project: any) => sum + (project.model_count || 0), 0)}
              </p>
            </div>
            <Calculator className="w-8 h-8 text-fiery" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Active Projects</p>
              <p className="metric-value">{projectsWithModels.length}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Simulations Run</p>
              <p className="metric-value">
                {projectsList.reduce((sum: number, project: any) => sum + (project.simulation_count || 0), 0)}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Avg Models/Project</p>
              <p className="metric-value">
                {projectsWithModels.length > 0 
                  ? (projectsList.reduce((sum: number, project: any) => sum + (project.model_count || 0), 0) / projectsWithModels.length).toFixed(1)
                  : '0'
                }
              </p>
            </div>
            <Layers className="w-8 h-8 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="firenest-card mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 w-full sm:w-64"
              />
            </div>
            
            {/* Model Type Filter */}
            <select
              value={modelTypeFilter}
              onChange={(e) => setModelTypeFilter(e.target.value)}
              className="form-input w-full sm:w-auto"
            >
              <option value="">All Model Types</option>
              <option value="USAGE_BASED">Usage-Based</option>
              <option value="TIERED">Tiered</option>
              <option value="HYBRID">Hybrid</option>
              <option value="SUBSCRIPTION">Subscription</option>
            </select>
          </div>
          
          <div className="text-sm text-gray-400">
            {projectsWithModels.length} projects with models
          </div>
        </div>
      </div>

      {/* Projects with Models */}
      {projectsWithModels.length === 0 ? (
        <div className="firenest-card">
          <div className="text-center py-12">
            <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No pricing models yet</h3>
            <p className="text-gray-400 mb-6">
              Create your first pricing model to start analyzing revenue scenarios
            </p>
            <Button onClick={() => navigate('/projects')} variant="fiery">
              <Plus className="w-4 h-4 mr-2" />
              Get Started
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {projectsWithModels
            .filter((project: any) => 
              !searchTerm || project.name.toLowerCase().includes(searchTerm.toLowerCase())
            )
            .map((project: any) => (
              <ProjectModelCard key={project.id} project={project} />
            ))}
        </div>
      )}

      {/* Model Type Distribution */}
      {projectsWithModels.length > 0 && (
        <div className="firenest-card mt-8">
          <h3 className="text-lg font-semibold text-white mb-6">Model Type Distribution</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { type: 'USAGE_BASED', label: 'Usage-Based', icon: TrendingUp, color: 'text-blue-400' },
              { type: 'TIERED', label: 'Tiered', icon: Layers, color: 'text-purple-400' },
              { type: 'HYBRID', label: 'Hybrid', icon: Calculator, color: 'text-green-400' },
              { type: 'SUBSCRIPTION', label: 'Subscription', icon: DollarSign, color: 'text-fiery' }
            ].map(modelType => (
              <div key={modelType.type} className="firenest-nested-card text-center">
                <modelType.icon className={`w-6 h-6 ${modelType.color} mx-auto mb-2`} />
                <div className="text-lg font-semibold text-white">
                  {/* This would need actual model type data from API */}
                  0
                </div>
                <div className="text-xs text-gray-400">{modelType.label}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

interface ProjectModelCardProps {
  project: any
}

function ProjectModelCard({ project }: ProjectModelCardProps) {
  const navigate = useNavigate()

  return (
    <div className="firenest-card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-fiery/20 rounded-lg flex items-center justify-center">
            <Calculator className="w-6 h-6 text-fiery" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{project.name}</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span>Workspace: {project.workspace_name}</span>
              <span>•</span>
              <Badge variant="secondary">{project.status}</Badge>
            </div>
          </div>
        </div>
        
        <Button
          variant="outline"
          onClick={() => navigate(`/projects/${project.id}/models`)}
        >
          View Models
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>

      {project.description && (
        <p className="text-gray-400 text-sm mb-4">{project.description}</p>
      )}

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{project.model_count}</div>
          <div className="text-xs text-gray-400">Models</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{project.upload_count}</div>
          <div className="text-xs text-gray-400">Data Files</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{project.simulation_count}</div>
          <div className="text-xs text-gray-400">Simulations</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-white">
            {formatDate(project.updated_at).split(',')[0]}
          </div>
          <div className="text-xs text-gray-400">Last Updated</div>
        </div>
      </div>

      <div className="flex justify-between items-center pt-4 border-t border-white/10">
        <div className="text-xs text-gray-400">
          Created {formatDate(project.created_at)}
        </div>
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/projects/${project.id}/upload`)}
          >
            Upload Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/projects/${project.id}/models`)}
          >
            Manage Models
          </Button>
        </div>
      </div>
    </div>
  )
}
