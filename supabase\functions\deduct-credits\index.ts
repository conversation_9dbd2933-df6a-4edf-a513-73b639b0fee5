// /supabase/functions/deduct-credits/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  // This function should ideally be callable ONLY by other trusted functions (like usage processing)
  // or require specific service role authentication, not directly by users.
  // For now, adding basic CORS and structure. Revisit security based on caller.
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { userId, amount, toolId, description } = await req.json()

    if (!userId || !amount || typeof amount !== 'number' || amount <= 0 || !toolId) {
       return new Response(JSON.stringify({ error: 'Missing or invalid parameters' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Use a database function/transaction for atomicity and balance check
    const { data, error } = await supabaseAdmin.rpc('deduct_user_credits', {
      p_user_id: userId,
      p_amount: amount,
      p_tool_id: toolId, // Pass toolId for logging context
      p_transaction_description: description || `Usage deduction for tool ${toolId}`,
      p_transaction_type: 'usage'
    })

    if (error) {
        // Check for specific error message from the function (e.g., insufficient funds)
        if (error.message.includes('Insufficient credits')) {
             return new Response(JSON.stringify({ error: 'Insufficient credits' }), {
                status: 402, // Payment Required / Insufficient Funds
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            })
        }
        throw error // Re-throw other errors
    }


    return new Response(JSON.stringify({ success: true, new_balance: data }), { // Assuming rpc returns new balance
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Deduct Credits Error:', error)
    return new Response(JSON.stringify({ error: error.message || 'Failed to deduct credits' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})

/*
-- Corresponding Supabase SQL Function (add in Supabase SQL Editor)
CREATE OR REPLACE FUNCTION deduct_user_credits(p_user_id uuid, p_amount numeric, p_tool_id uuid, p_transaction_description text, p_transaction_type text)
RETURNS numeric -- Returns the new available balance
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_available numeric;
  v_new_available numeric;
BEGIN
  -- Lock the row and check balance
  SELECT available INTO v_current_available
  FROM public.user_credits
  WHERE user_id = p_user_id
  FOR UPDATE; -- Lock the row

  IF v_current_available IS NULL THEN
    RAISE EXCEPTION 'User credits record not found for user %', p_user_id;
  END IF;

  IF v_current_available < p_amount THEN
    RAISE EXCEPTION 'Insufficient credits: User % has %, needs %', p_user_id, v_current_available, p_amount;
  END IF;

  v_new_available := v_current_available - p_amount;

  -- Update credits
  UPDATE public.user_credits
  SET
    available = v_new_available,
    used = used + p_amount
  WHERE user_id = p_user_id;

  -- Log the transaction
  INSERT INTO public.credit_transactions (user_id, amount, type, description, tool_id, ending_balance)
  VALUES (p_user_id, -p_amount, p_transaction_type, p_transaction_description, p_tool_id, v_new_available); -- Log negative amount for deduction

  RETURN v_new_available;
END;
$$;
*/
