import { BarChart4, Zap, CreditCard, Settings, BarChart3, GitBranch, Sparkles, History, ChevronRight, Image, MessageSquare, Brain, CodeSquare, Video, Music, Bot, Lightbulb, Workflow, Bot as BotIcon, Gem, Speech, File, TrendingUp, Layers, Search, Pen, Wand2, Mic, Globe, Code } from "lucide-react";

const DashboardPreview = () => {
  return (
    <div className="relative w-full h-full animate-float group">
      {/* Enhanced Glow Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-fiery/30 via-fiery/20 to-cool-500/30 rounded-lg blur-xl opacity-80 animate-pulse-slow group-hover:opacity-100 transition-opacity duration-700"></div>

      {/* Corner accents */}
      <div className="absolute -top-1 -left-1 w-20 h-20 bg-gradient-to-br from-fiery/40 to-transparent rounded-tl-lg blur-md"></div>
      <div className="absolute -bottom-1 -right-1 w-20 h-20 bg-gradient-to-tl from-cool-500/40 to-transparent rounded-br-lg blur-md"></div>

      <div className="relative bg-gradient-to-br from-dark-800/95 via-dark-900 to-dark-800/95 rounded-lg border border-white/10 overflow-hidden shadow-2xl backdrop-blur-sm">
        {/* Animated border effect */}
        <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700 pointer-events-none overflow-hidden">
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-fiery/50 to-transparent animate-shine"></div>
          <div className="absolute inset-y-0 right-0 w-px bg-gradient-to-b from-transparent via-cool-500/50 to-transparent animate-shine delay-300"></div>
          <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-fiery/50 to-transparent animate-shine delay-500"></div>
          <div className="absolute inset-y-0 left-0 w-px bg-gradient-to-b from-transparent via-cool-500/50 to-transparent animate-shine delay-700"></div>
        </div>

        {/* Header - More visually striking */}
        <div className="bg-gradient-to-r from-fiery/20 via-fiery/10 to-cool-500/10 py-3 px-4 flex items-center border-b border-white/10">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-fiery via-fiery-600 to-cool-500 flex items-center justify-center mr-3 shadow-lg shadow-fiery/20 animate-pulse-slow">
            <div className="w-6 h-6 rounded-full bg-white/90 flex items-center justify-center">
              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-fiery to-fiery-500 animate-pulse"></div>
            </div>
          </div>
          <div className="text-left">
            <h3 className="text-md font-bold text-transparent bg-clip-text bg-gradient-to-r from-white via-white to-white/80">AI Credit Centre</h3>
            <p className="text-white/70 text-xs">One dashboard for all your AI needs</p>
          </div>
          <div className="ml-auto bg-white/5 border border-white/10 rounded-full px-2 py-1 flex items-center hover:bg-white/10 hover:border-fiery/30 transition-all duration-300 group-hover:shadow-sm group-hover:shadow-fiery/20">
            <Zap className="h-3 w-3 text-fiery mr-1 animate-pulse-slow" />
            <span className="text-[9px] text-transparent bg-clip-text bg-gradient-to-r from-white to-white/80">Save 70% on AI costs</span>
          </div>
        </div>

        <div className="p-3">
          {/* Credit Balance Card - Enhanced */}
          <div className="firenest-card px-3 py-2 mb-3 group-hover:shadow-sm group-hover:shadow-fiery/10">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-baseline">
                  <div className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-white via-white to-white/90">4,250</div>
                  <div className="text-white/60 text-xs ml-1">credits</div>
                </div>
                <div className="text-[9px] flex items-center text-white/50">
                  <TrendingUp className="h-2 w-2 mr-0.5 text-fiery" />
                  Est. savings: <span className="text-fiery ml-0.5">₹8,500</span>
                </div>
              </div>
              <div className="flex flex-col items-end">
                <span className="text-xs px-2 py-0.5 bg-gradient-to-r from-fiery/30 to-fiery/10 text-white rounded-full mb-1 border border-fiery/20">Active</span>
                <button className="text-[9px] bg-gradient-to-r from-white/10 to-white/5 hover:from-fiery/20 hover:to-fiery/5 text-white px-3 py-0.5 rounded-full flex items-center gap-1 transition-all duration-300 border border-white/10 hover:border-fiery/30 group-hover:shadow-sm">
                  <CreditCard className="h-2.5 w-2.5" /> Top Up
                </button>
              </div>
            </div>
          </div>

          {/* AI Tools Section - More visually exciting */}
          <div className="mb-3 relative">
            <div className="flex justify-between items-center mb-2">
              <h4 className="flex items-center text-xs font-medium text-transparent bg-clip-text bg-gradient-to-r from-white to-white/80">
                <Layers className="h-3 w-3 mr-1 text-fiery" /> Latest AI Tools
              </h4>
              <button className="text-fiery text-xs flex items-center hover:text-white transition-colors duration-300">
                View All <ChevronRight className="h-3 w-3 ml-1" />
              </button>
            </div>

            <div className="grid grid-cols-5 gap-2.5">
              {/* First row of tools */}
              <div className="firenest-card p-1.5 flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center mb-1 shadow-sm">
                  <Bot className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Meta AI</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">4 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mb-1 shadow-sm">
                  <Lightbulb className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Jasper</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">3 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center mb-1 shadow-sm">
                  <Workflow className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Zapier AI</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">3 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-pink-500 flex items-center justify-center mb-1 shadow-sm">
                  <Speech className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Pi Voice</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">2 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-indigo-500 flex items-center justify-center mb-1 shadow-sm">
                  <Search className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Arc Search</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">2 credits</span>
              </div>

              {/* Second row of tools */}
              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-rose-500 flex items-center justify-center mb-1 shadow-sm">
                  <Pen className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Anywrite</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">3 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-violet-500 flex items-center justify-center mb-1 shadow-sm">
                  <Wand2 className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Stability AI</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">4 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-emerald-500 flex items-center justify-center mb-1 shadow-sm">
                  <Mic className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Whisper</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">2 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center mb-1 shadow-sm">
                  <Globe className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">DeepL</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">1 credit</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center mb-1 shadow-sm">
                  <Code className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">AutoGPT</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">5 credits</span>
              </div>

              {/* Regular tools */}
              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mb-1 shadow-sm">
                  <MessageSquare className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">ChatGPT</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">2 credits</span>
              </div>

              {/* Regular tools */}
              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center mb-1 shadow-sm">
                  <Brain className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Claude</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">3 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center mb-1 shadow-sm">
                  <Image className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Midjourney</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">5 credits</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center mb-1 shadow-sm">
                  <CodeSquare className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Copilot</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">1 credit</span>
              </div>

              <div className="bg-white/8 border border-white/10 p-1.5 rounded-md flex flex-col items-center transition-all duration-300 hover:bg-fiery/5 hover:border-fiery/30 cursor-pointer hover:scale-105 hover:shadow-sm">
                <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center mb-1 shadow-sm">
                  <Video className="h-3 w-3 text-white" />
                </div>
                <span className="text-white text-[9px] font-medium">Runway</span>
                <span className="text-white/50 text-[7px] bg-white/10 rounded-full px-1.5">6 credits</span>
              </div>
            </div>
          </div>

          {/* Usage Metrics - Enhanced */}
          <div className="firenest-card p-2 mb-3 group-hover:shadow-sm">
            <div className="flex justify-between items-center mb-1">
              <h4 className="text-xs font-medium text-transparent bg-clip-text bg-gradient-to-r from-white via-white to-white/80">Usage This Month</h4>
              <div className="flex items-center gap-1">
                <span className="text-white/60 text-[8px]">May 2025</span>
                <Settings className="h-2.5 w-2.5 text-white/60" />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <BarChart3 className="h-3 w-3 text-fiery" />
              <div className="flex-1">
                <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-cool-500 via-cool-300 to-fiery rounded-full animate-pulse-slow" style={{ width: "35%" }}></div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-2 mt-1">
              <div className="text-center">
                <div className="text-[8px] text-white/60">Used</div>
                <div className="text-[10px] font-medium text-white">875</div>
              </div>
              <div className="text-center">
                <div className="text-[8px] text-white/60">Remaining</div>
                <div className="text-[10px] font-medium text-white">3,375</div>
              </div>
              <div className="text-center">
                <div className="text-[8px] text-white/60">Efficiency</div>
                <div className="text-[10px] font-medium text-fiery">95%</div>
              </div>
            </div>
          </div>

          {/* Recent Activity - Enhanced */}
          <div className="firenest-card p-2 group-hover:shadow-sm">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-xs font-medium text-transparent bg-clip-text bg-gradient-to-r from-white via-white to-white/80">Recent Activity</h4>
              <History className="h-2.5 w-2.5 text-white/60" />
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-between text-[9px] py-0.5 border-b border-white/5">
                <span className="text-white/70 flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-green-400 to-green-600 mr-1"></span>
                  ChatGPT API - 4K Context
                </span>
                <span className="text-fiery">-2 cr</span>
              </div>
              <div className="flex items-center justify-between text-[9px] py-0.5 border-b border-white/5">
                <span className="text-white/70 flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 mr-1"></span>
                  Claude AI - Document Analysis
                </span>
                <span className="text-fiery">-3 cr</span>
              </div>
              <div className="flex items-center justify-between text-[9px] py-0.5">
                <span className="text-white/70 flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-purple-400 to-purple-600 mr-1"></span>
                  Midjourney - Image Generation
                </span>
                <span className="text-fiery">-5 cr</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPreview;
