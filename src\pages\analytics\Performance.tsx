import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Activity, Clock, Zap, AlertCircle, CheckCircle, XCircle, BarChart3, TrendingUp, TrendingDown, Shield, Server, Cpu } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

/**
 * Performance Analytics page with professional design
 * Inspired by industry leaders like Datadog, New Relic, and Grafana
 */
const Performance = () => {
  const [timeRange, setTimeRange] = useState('day');
  const [activeTab, setActiveTab] = useState('overview');

  // Enhanced mock data for performance metrics
  const performanceData = [
    {
      tool: 'ChatGPT',
      avgResponseTime: 1.2,
      reliability: 98,
      errorRate: 2,
      successRate: 98,
      availability: 99.9,
      costPerRequest: 0.35,
      totalRequests: 145,
      p95ResponseTime: 1.8,
      p99ResponseTime: 2.3
    },
    {
      tool: 'Midjourney',
      avgResponseTime: 3.5,
      reliability: 95,
      errorRate: 5,
      successRate: 95,
      availability: 99.5,
      costPerRequest: 1.2,
      totalRequests: 78,
      p95ResponseTime: 4.2,
      p99ResponseTime: 5.1
    },
    {
      tool: 'Claude',
      avgResponseTime: 1.5,
      reliability: 97,
      errorRate: 3,
      successRate: 97,
      availability: 99.7,
      costPerRequest: 0.45,
      totalRequests: 92,
      p95ResponseTime: 2.1,
      p99ResponseTime: 2.8
    },
    {
      tool: 'DALL-E',
      avgResponseTime: 4.2,
      reliability: 94,
      errorRate: 6,
      successRate: 94,
      availability: 99.3,
      costPerRequest: 0.95,
      totalRequests: 56,
      p95ResponseTime: 5.3,
      p99ResponseTime: 6.7
    },
  ];

  // Time series data for response time trends
  const responseTimeTrend = [
    { time: '00:00', ChatGPT: 1.1, Midjourney: 3.2, Claude: 1.4, 'DALL-E': 4.0 },
    { time: '04:00', ChatGPT: 1.0, Midjourney: 3.3, Claude: 1.3, 'DALL-E': 3.9 },
    { time: '08:00', ChatGPT: 1.3, Midjourney: 3.7, Claude: 1.6, 'DALL-E': 4.4 },
    { time: '12:00', ChatGPT: 1.5, Midjourney: 4.0, Claude: 1.8, 'DALL-E': 4.8 },
    { time: '16:00', ChatGPT: 1.4, Midjourney: 3.8, Claude: 1.7, 'DALL-E': 4.5 },
    { time: '20:00', ChatGPT: 1.2, Midjourney: 3.4, Claude: 1.5, 'DALL-E': 4.1 },
  ];

  // Error types distribution
  const errorDistribution = [
    { name: 'Timeout', value: 45, color: '#FF4405' },
    { name: 'Rate Limit', value: 25, color: '#F59E0B' },
    { name: 'API Error', value: 20, color: '#3B82F6' },
    { name: 'Network', value: 10, color: '#8B5CF6' },
  ];

  const COLORS = errorDistribution.map(item => item.color);

  // System health metrics
  const systemHealth = {
    uptime: '99.95%',
    avgLoadTime: '2.6s',
    cpuUtilization: 42,
    memoryUtilization: 38,
    networkLatency: '68ms',
    databaseQueryTime: '120ms',
    activeConnections: 28,
    queuedRequests: 3
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Performance Analytics</h1>
          <p className="text-white/70">Monitor the performance and reliability of your AI tools</p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px] bg-dark-800 border-white/10">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent className="bg-dark-800 border-white/10">
              <SelectItem value="hour">Last Hour</SelectItem>
              <SelectItem value="day">Last 24 Hours</SelectItem>
              <SelectItem value="week">Last 7 Days</SelectItem>
              <SelectItem value="month">Last 30 Days</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
            <Activity className="h-4 w-4 mr-2" />
            Live View
          </Button>
        </div>
      </div>

      {/* Summary metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-fiery/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <Clock className="h-4 w-4 mr-2 text-fiery" />
              Average Response Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {performanceData.reduce((sum, tool) => sum + tool.avgResponseTime * tool.totalRequests, 0) /
               performanceData.reduce((sum, tool) => sum + tool.totalRequests, 0)
              }s
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-white/70">
                across all tools
              </div>
              <Badge variant="outline" className="bg-green-500/10 text-green-400 border-green-500/20 text-xs">
                Good
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <CheckCircle className="h-4 w-4 mr-2 text-blue-400" />
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {Math.round(performanceData.reduce((sum, tool) => sum + tool.successRate * tool.totalRequests, 0) /
               performanceData.reduce((sum, tool) => sum + tool.totalRequests, 0)
              )}%
            </div>
            <div className="mt-2">
              <div className="flex justify-between text-xs text-white/70 mb-1">
                <span>Successful requests</span>
              </div>
              <Progress
                value={Math.round(performanceData.reduce((sum, tool) => sum + tool.successRate * tool.totalRequests, 0) /
                performanceData.reduce((sum, tool) => sum + tool.totalRequests, 0))}
                className="h-1.5 bg-white/10"
                indicatorClassName="bg-blue-400"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-amber-500/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <AlertCircle className="h-4 w-4 mr-2 text-amber-400" />
              Error Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {Math.round(performanceData.reduce((sum, tool) => sum + tool.errorRate * tool.totalRequests, 0) /
               performanceData.reduce((sum, tool) => sum + tool.totalRequests, 0)
              )}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-white/70">
                failed requests
              </div>
              <Badge variant="outline" className="bg-amber-500/10 text-amber-400 border-amber-500/20 text-xs">
                Warning
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-dark-800 border-white/10 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center text-white/70">
              <Zap className="h-4 w-4 mr-2 text-purple-400" />
              Fastest Tool
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {performanceData.reduce((fastest, tool) =>
                tool.avgResponseTime < fastest.avgResponseTime ? tool : fastest
              , performanceData[0]).tool}
            </div>
            <div className="text-xs text-white/70 mt-2">
              {performanceData.reduce((fastest, tool) =>
                tool.avgResponseTime < fastest.avgResponseTime ? tool : fastest
              , performanceData[0]).avgResponseTime}s average response
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main content tabs */}
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="bg-dark-800 border border-white/10">
          <TabsTrigger value="overview" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="response-time" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Response Time
          </TabsTrigger>
          <TabsTrigger value="errors" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Errors
          </TabsTrigger>
          <TabsTrigger value="system" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            System Health
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Response time trends chart */}
            <Card className="bg-dark-800 border-white/10 lg:col-span-2">
              <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle className="text-lg text-white">
                    Response Time Trends
                  </CardTitle>
                  <CardDescription>
                    Average response time by tool over time
                  </CardDescription>
                </div>
                <div className="flex items-center gap-3 mt-2 sm:mt-0">
                  {performanceData.slice(0, 3).map((tool, index) => (
                    <div key={index} className="flex items-center gap-1.5">
                      <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                      <span className="text-xs text-white/70">{tool.tool}</span>
                    </div>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart
                    data={responseTimeTrend}
                    margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                    <XAxis dataKey="time" stroke="#888" />
                    <YAxis stroke="#888" />
                    <Tooltip
                      contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                      labelStyle={{ color: '#F9FAFB' }}
                      itemStyle={{ color: '#F9FAFB' }}
                    />
                    <Legend />
                    {performanceData.slice(0, 4).map((tool, index) => (
                      <Line
                        key={index}
                        type="monotone"
                        dataKey={tool.tool}
                        stroke={COLORS[index % COLORS.length]}
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Error distribution */}
            <Card className="bg-dark-800 border-white/10">
              <CardHeader>
                <CardTitle className="text-lg text-white">
                  Error Distribution
                </CardTitle>
                <CardDescription>
                  Breakdown of error types
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={errorDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {errorDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                      labelStyle={{ color: '#F9FAFB' }}
                      itemStyle={{ color: '#F9FAFB' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Tool performance comparison */}
          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                Tool Performance Comparison
              </CardTitle>
              <CardDescription>
                Response times and reliability by tool
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performanceData.map((tool, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-sm mr-2" style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                        <span className="text-sm font-medium text-white">{tool.tool}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center">
                          <Clock className="h-3.5 w-3.5 mr-1 text-white/50" />
                          <span className="text-sm text-white/70">{tool.avgResponseTime}s</span>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="h-3.5 w-3.5 mr-1 text-white/50" />
                          <span className="text-sm text-white/70">{tool.successRate}%</span>
                        </div>
                      </div>
                    </div>
                    <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                      <div
                        className="h-full rounded-full"
                        style={{
                          width: `${100 - (tool.avgResponseTime / 5) * 100}%`,
                          backgroundColor: COLORS[index % COLORS.length]
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between items-center text-xs text-white/50">
                      <span>Response Time</span>
                      <div className="flex items-center gap-2">
                        <span>P95: {tool.p95ResponseTime}s</span>
                        <span>P99: {tool.p99ResponseTime}s</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Response Time Tab */}
        <TabsContent value="response-time" className="space-y-6">
          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                Response Time Analysis
              </CardTitle>
              <CardDescription>
                Detailed breakdown of response times by tool
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={performanceData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  layout="vertical"
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                  <XAxis type="number" stroke="#888" />
                  <YAxis dataKey="tool" type="category" stroke="#888" />
                  <Tooltip
                    contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                    labelStyle={{ color: '#F9FAFB' }}
                    itemStyle={{ color: '#F9FAFB' }}
                  />
                  <Legend />
                  <Bar dataKey="avgResponseTime" name="Average" fill="#FF4405" />
                  <Bar dataKey="p95ResponseTime" name="P95" fill="#F59E0B" />
                  <Bar dataKey="p99ResponseTime" name="P99" fill="#3B82F6" />
                </BarChart>
              </ResponsiveContainer>

              <div className="mt-6 space-y-4">
                <h3 className="text-sm font-medium text-white">Response Time Percentiles</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {performanceData.map((tool, index) => (
                    <div key={index} className="bg-dark-700 rounded-lg p-4 border border-white/5">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <div className="h-3 w-3 rounded-sm mr-2" style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                          <span className="font-medium text-white">{tool.tool}</span>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-xs text-white/70 mb-1">
                            <span>Average</span>
                            <span>{tool.avgResponseTime}s</span>
                          </div>
                          <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-fiery rounded-full"
                              style={{ width: `${(tool.avgResponseTime / 7) * 100}%` }}
                            ></div>
                          </div>
                        </div>

                        <div>
                          <div className="flex justify-between text-xs text-white/70 mb-1">
                            <span>P95 (95th percentile)</span>
                            <span>{tool.p95ResponseTime}s</span>
                          </div>
                          <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-amber-500 rounded-full"
                              style={{ width: `${(tool.p95ResponseTime / 7) * 100}%` }}
                            ></div>
                          </div>
                        </div>

                        <div>
                          <div className="flex justify-between text-xs text-white/70 mb-1">
                            <span>P99 (99th percentile)</span>
                            <span>{tool.p99ResponseTime}s</span>
                          </div>
                          <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-blue-500 rounded-full"
                              style={{ width: `${(tool.p99ResponseTime / 7) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Errors Tab */}
        <TabsContent value="errors" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <Card className="bg-dark-800 border-white/10 lg:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg text-white">
                  Error Rate by Tool
                </CardTitle>
                <CardDescription>
                  Percentage of failed requests by tool
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={performanceData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                    <XAxis dataKey="tool" stroke="#888" />
                    <YAxis stroke="#888" />
                    <Tooltip
                      contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                      labelStyle={{ color: '#F9FAFB' }}
                      itemStyle={{ color: '#F9FAFB' }}
                    />
                    <Bar dataKey="errorRate" name="Error Rate (%)" fill="#FF4405" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-dark-800 border-white/10">
              <CardHeader>
                <CardTitle className="text-lg text-white">
                  Error Types
                </CardTitle>
                <CardDescription>
                  Distribution of error categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={errorDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {errorDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #374151', borderRadius: '6px' }}
                      labelStyle={{ color: '#F9FAFB' }}
                      itemStyle={{ color: '#F9FAFB' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                Error Analysis
              </CardTitle>
              <CardDescription>
                Key insights about error patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-sm text-white/70">
                <li className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                  </div>
                  <span>Timeout errors are the most common at <span className="text-white font-medium">45%</span> of all errors</span>
                </li>
                <li className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                  </div>
                  <span>Image generation tools (Midjourney, DALL-E) have <span className="text-white font-medium">higher error rates</span> compared to text-based tools</span>
                </li>
                <li className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                  </div>
                  <span>Rate limit errors increase during <span className="text-white font-medium">peak usage hours</span> (12-4 PM)</span>
                </li>
                <li className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                  </div>
                  <span>Network errors account for only <span className="text-white font-medium">10%</span> of all failures</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Health Tab */}
        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-dark-800 border-white/10">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center text-white/70">
                  <Shield className="h-4 w-4 mr-2 text-green-400" />
                  System Uptime
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  {systemHealth.uptime}
                </div>
                <div className="text-xs text-white/70 mt-1">
                  Last 30 days
                </div>
              </CardContent>
            </Card>

            <Card className="bg-dark-800 border-white/10">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center text-white/70">
                  <Cpu className="h-4 w-4 mr-2 text-blue-400" />
                  CPU Utilization
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  {systemHealth.cpuUtilization}%
                </div>
                <div className="mt-2">
                  <div className="flex justify-between text-xs text-white/70 mb-1">
                    <span>Current usage</span>
                  </div>
                  <Progress
                    value={systemHealth.cpuUtilization}
                    className="h-1.5 bg-white/10"
                    indicatorClassName={systemHealth.cpuUtilization > 80 ? "bg-red-400" : systemHealth.cpuUtilization > 60 ? "bg-amber-400" : "bg-blue-400"}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-dark-800 border-white/10">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center text-white/70">
                  <Server className="h-4 w-4 mr-2 text-purple-400" />
                  Memory Utilization
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  {systemHealth.memoryUtilization}%
                </div>
                <div className="mt-2">
                  <div className="flex justify-between text-xs text-white/70 mb-1">
                    <span>Current usage</span>
                  </div>
                  <Progress
                    value={systemHealth.memoryUtilization}
                    className="h-1.5 bg-white/10"
                    indicatorClassName={systemHealth.memoryUtilization > 80 ? "bg-red-400" : systemHealth.memoryUtilization > 60 ? "bg-amber-400" : "bg-purple-400"}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-dark-800 border-white/10">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center text-white/70">
                  <Activity className="h-4 w-4 mr-2 text-teal-400" />
                  Network Latency
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  {systemHealth.networkLatency}
                </div>
                <div className="text-xs text-white/70 mt-1">
                  average response time
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-dark-800 border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-white">
                System Metrics
              </CardTitle>
              <CardDescription>
                Detailed system performance indicators
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-white">Request Processing</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-md bg-fiery/20 flex items-center justify-center mr-3">
                          <Activity className="h-4 w-4 text-fiery" />
                        </div>
                        <span className="text-sm text-white">Active Connections</span>
                      </div>
                      <span className="text-sm font-medium text-white">{systemHealth.activeConnections}</span>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-md bg-blue-500/20 flex items-center justify-center mr-3">
                          <Clock className="h-4 w-4 text-blue-400" />
                        </div>
                        <span className="text-sm text-white">Queued Requests</span>
                      </div>
                      <span className="text-sm font-medium text-white">{systemHealth.queuedRequests}</span>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-md bg-purple-500/20 flex items-center justify-center mr-3">
                          <Server className="h-4 w-4 text-purple-400" />
                        </div>
                        <span className="text-sm text-white">Database Query Time</span>
                      </div>
                      <span className="text-sm font-medium text-white">{systemHealth.databaseQueryTime}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-white">Performance Insights</h3>
                  <ul className="space-y-3 text-sm text-white/70">
                    <li className="flex items-start">
                      <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                      </div>
                      <span>System is operating at <span className="text-white font-medium">optimal capacity</span> with no performance bottlenecks</span>
                    </li>
                    <li className="flex items-start">
                      <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                      </div>
                      <span>Database query times are <span className="text-white font-medium">within expected ranges</span> for current load</span>
                    </li>
                    <li className="flex items-start">
                      <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                      </div>
                      <span>Network latency is <span className="text-white font-medium">stable</span> with no significant fluctuations</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Performance Insights Card */}
      <Card className="bg-dark-800 border-white/10">
        <CardHeader>
          <CardTitle className="text-lg text-white">
            Performance Insights
          </CardTitle>
          <CardDescription>
            Key observations about your tools
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3 text-sm text-white/70">
            <li className="flex items-start">
              <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
              </div>
              <span>ChatGPT has the fastest average response time at <span className="text-white font-medium">1.2 seconds</span></span>
            </li>
            <li className="flex items-start">
              <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
              </div>
              <span>Image generation tools (Midjourney, DALL-E) have <span className="text-white font-medium">longer response times</span> but are still within acceptable ranges</span>
            </li>
            <li className="flex items-start">
              <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
              </div>
              <span>All tools maintain a <span className="text-white font-medium">reliability above 90%</span>, with ChatGPT leading at 98%</span>
            </li>
            <li className="flex items-start">
              <div className="h-5 w-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5 mr-2">
                <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
              </div>
              <span>Peak usage times show <span className="text-white font-medium">slightly increased response times</span> but minimal impact on reliability</span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default Performance;
