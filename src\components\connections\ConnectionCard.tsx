import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ThemedBadge as Badge } from '@/components/ui/badge';
import { Loader2, ExternalLink, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { trackToolLaunch } from '@/lib/connections';
import { notify } from '@/components/ui/notification-system';
import LaunchConfirmationModal from './LaunchConfirmationModal';

interface ConnectionCardProps {
  tool: any;
  statusIcon: React.ReactNode;
  authMethodIcon: React.ReactNode;
  onConnect: () => void;
  onDisconnect: () => void;
  onViewDetails: () => void;
  isConnecting: boolean;
}

const ConnectionCard: React.FC<ConnectionCardProps> = ({
  tool,
  statusIcon,
  authMethodIcon,
  onConnect,
  onDisconnect,
  onViewDetails,
  isConnecting
}) => {
  const { user } = useAuth();
  const [isLaunching, setIsLaunching] = useState(false);
  const [showLaunchConfirmation, setShowLaunchConfirmation] = useState(false);

  // Handle tool launch
  const handleLaunch = async () => {
    if (!user) {
      notify.error('You must be logged in to launch tools');
      return;
    }

    if (!tool.website) {
      notify.error('This tool does not have a website configured');
      return;
    }

    setIsLaunching(true);
    try {
      // Track the tool launch
      const success = await trackToolLaunch(user.id, tool.id);

      if (success) {
        // Show launch confirmation
        notify.success(`Launching ${tool.name}. Usage will be tracked automatically.`);

        // Open the tool website in a new tab
        window.open(tool.website, '_blank');

        // Show the launch confirmation modal
        setShowLaunchConfirmation(true);
      } else {
        notify.error(`Failed to track launch for ${tool.name}`);
      }
    } catch (error) {
      console.error('Error launching tool:', error);
      notify.error('An error occurred while launching the tool');
    } finally {
      setIsLaunching(false);
    }
  };

  // Determine the icon component to use
  const IconComponent = React.useMemo(() => {
    const iconName = tool.icon || 'Zap';
    // This is a simple approach - in a real app, you might want to import icons dynamically
    const icons: Record<string, React.ReactNode> = {
      Bot: <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-blue-400"><path d="M12 8V4H8"/><rect width="16" height="12" x="4" y="8" rx="2"/><path d="M2 14h2"/><path d="M20 14h2"/><path d="M15 13v2"/><path d="M9 13v2"/></svg></div>,
      Image: <div className="h-10 w-10 rounded-full bg-purple-500/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-purple-400"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg></div>,
      Code: <div className="h-10 w-10 rounded-full bg-green-500/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-green-400"><polyline points="16 18 22 12 16 6"/><polyline points="8 6 2 12 8 18"/></svg></div>,
      Video: <div className="h-10 w-10 rounded-full bg-red-500/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-red-400"><path d="m22 8-6 4 6 4V8Z"/><rect width="14" height="12" x="2" y="6" rx="2" ry="2"/></svg></div>,
      MessageSquare: <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-blue-400"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg></div>,
      Headphones: <div className="h-10 w-10 rounded-full bg-yellow-500/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-yellow-400"><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"/></svg></div>
    };

    return icons[iconName] || <div className="h-10 w-10 rounded-full bg-fiery/20 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-fiery"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/></svg></div>;
  }, [tool.icon]);

  return (
    <Card className={cn(
      "firenest-card border border-white/10 overflow-hidden transition-all duration-300",
      tool.isConnected && "border-green-500/30"
    )}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {IconComponent}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white truncate">{tool.name}</h3>
              <div className="flex items-center gap-2">
                {statusIcon}
                {authMethodIcon}
              </div>
            </div>
            <p className="text-sm text-white/70 line-clamp-2 mt-1">{tool.description}</p>
          </div>
        </div>

        <div className="mt-3 flex flex-wrap gap-1">
          {tool.tags && tool.tags.slice(0, 3).map((tag: string, index: number) => (
            <Badge key={index} className="bg-white/10 hover:bg-white/20 text-white/70">
              {tag}
            </Badge>
          ))}
          {tool.tags && tool.tags.length > 3 && (
            <Badge className="bg-white/10 hover:bg-white/20 text-white/70">
              +{tool.tags.length - 3}
            </Badge>
          )}
        </div>

        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xs text-white/50">Status:</span>
            <Badge className={cn(
              "text-xs",
              tool.isConnected ? "bg-green-500/20 text-green-400" :
              tool.connection?.status === 'pending' ? "bg-yellow-500/20 text-yellow-400" :
              tool.connection?.status === 'failed' ? "bg-red-500/20 text-red-400" :
              "bg-gray-500/20 text-gray-400"
            )}>
              {tool.isConnected ? 'Connected' :
               tool.connection?.status === 'pending' ? 'Pending' :
               tool.connection?.status === 'failed' ? 'Failed' :
               'Not Connected'}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-white/50">Auth:</span>
            <Badge className="bg-white/10 text-white/70 text-xs">
              {tool.authMethod === 'oauth' ? 'OAuth' :
               tool.authMethod === 'api_key' ? 'API Key' :
               tool.authMethod === 'credentials' ? 'Login' :
               tool.authMethod === 'ip_based' ? 'IP Based' :
               'Unknown'}
            </Badge>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex gap-2">
        {tool.isConnected ? (
          <>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 border-white/10 hover:bg-white/5"
              onClick={onDisconnect}
            >
              Disconnect
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 border-white/10 hover:bg-white/5"
              onClick={onViewDetails}
            >
              <Info className="h-4 w-4 mr-1" />
              Details
            </Button>
            <Button
              variant="default"
              size="sm"
              className="flex-1 bg-fiery hover:bg-fiery-600"
              onClick={handleLaunch}
              disabled={isLaunching}
            >
              {isLaunching ? (
                <>
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  Launching...
                </>
              ) : (
                <>
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Launch
                </>
              )}
            </Button>
          </>
        ) : (
          <Button
            variant="default"
            size="sm"
            className="flex-1 bg-fiery hover:bg-fiery-600"
            onClick={onConnect}
            disabled={isConnecting}
          >
            {isConnecting ? (
              <>
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                Connecting...
              </>
            ) : (
              <>Connect</>
            )}
          </Button>
        )}
      </CardFooter>

      {/* Launch Confirmation Modal */}
      <LaunchConfirmationModal
        isOpen={showLaunchConfirmation}
        onClose={() => setShowLaunchConfirmation(false)}
        tool={tool}
      />
    </Card>
  );
};

export default ConnectionCard;
