/**
 * Login Page
 * Authentication entry point with identity provider integration
 */

import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Shield, Zap, Database, Lock } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { But<PERSON> } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import toast from 'react-hot-toast'

export function LoginPage() {
  const navigate = useNavigate()
  const { login } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)

  const handleLogin = async () => {
    setIsLoading(true)
    try {
      // In a real implementation, this would integrate with Auth0, Okta, etc.
      // For demo purposes, we'll create a mock JWT token
      const mockToken = btoa(JSON.stringify({
        sub: 'demo-user-123',
        email: '<EMAIL>',
        role: 'user',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
      }))

      await login(mockToken, 'demo')
      navigate('/')
    } catch (error) {
      toast.error('<PERSON><PERSON> failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-dark flex">
      {/* Left Side - Content */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="max-w-md w-full space-y-8">
          <div>
            <div className="flex items-center space-x-2 mb-8">
              <div className="w-10 h-10 bg-fiery rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">FS</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Firenest Sandbox</h1>
                <p className="text-sm text-gray-400">Pricing Intelligence Platform</p>
              </div>
            </div>
            
            <h2 className="text-3xl font-bold text-white">
              Welcome back
            </h2>
            <p className="mt-2 text-gray-400">
              Sign in to access your pricing intelligence workspace
            </p>
          </div>

          <div className="space-y-6">
            <Button
              onClick={handleLogin}
              disabled={isLoading}
              className="w-full h-12 text-base"
              variant="fiery"
            >
              {isLoading ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <Shield className="w-5 h-5 mr-2" />
              )}
              {isLoading ? 'Signing in...' : 'Sign in with SSO'}
            </Button>

            <div className="text-center">
              <p className="text-xs text-gray-400">
                By signing in, you agree to our Terms of Service and Privacy Policy
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Features */}
      <div className="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center bg-black/20 backdrop-blur">
        <div className="max-w-md space-y-8 p-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">
              Enterprise-Grade Security
            </h3>
            <p className="text-gray-300">
              Built for sensitive pricing data with zero-trust architecture and SOC 2 compliance
            </p>
          </div>

          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">Zero-Trust Security</h4>
                <p className="text-sm text-gray-400">
                  Every request authenticated with immutable audit trails
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Database className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">Data Isolation</h4>
                <p className="text-sm text-gray-400">
                  Absolute separation between customer data with RLS
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Lock className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">End-to-End Encryption</h4>
                <p className="text-sm text-gray-400">
                  Data encrypted in transit and at rest with AES-256
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Zap className="w-5 h-5 text-fiery" />
              </div>
              <div>
                <h4 className="text-white font-medium">Real-Time Processing</h4>
                <p className="text-sm text-gray-400">
                  Instant validation and simulation results at scale
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-white/10 pt-6">
            <div className="text-center">
              <p className="text-xs text-gray-400">
                Trusted by pricing teams at leading SaaS companies
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
