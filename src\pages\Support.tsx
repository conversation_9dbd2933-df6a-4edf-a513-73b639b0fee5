import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { BookOpen, FileText, HelpCircle, Mail, MessageSquare, Search } from 'lucide-react';

/**
 * Support page with professional design
 */
const Support = () => {
  // Mock data for FAQs
  const faqs = [
    {
      question: 'How do I add credits to my account?',
      answer: 'You can add credits to your account by navigating to the Credits page and selecting your preferred payment method. We accept credit cards, PayPal, and bank transfers.'
    },
    {
      question: 'Can I share my account with team members?',
      answer: 'Yes, you can invite team members to your account from the Team section. Each team member will have their own login credentials and you can set different permission levels.'
    },
    {
      question: 'How are credits calculated for different AI tools?',
      answer: 'Different AI tools consume credits at different rates based on their computational requirements. You can view the specific credit consumption rates for each tool in the Workbench section.'
    },
    {
      question: 'Is there a way to set credit limits for my team?',
      answer: 'Yes, as an admin you can set credit limits for individual team members or for the entire team from the Team Settings page.'
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white">Help & Support</h1>
        <p className="text-white/70">Get help with Firenest</p>
      </div>

      <Tabs defaultValue="help" className="space-y-6">
        <TabsList className="firenest-card">
          <TabsTrigger value="help" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Help Center
          </TabsTrigger>
          <TabsTrigger value="contact" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Contact Support
          </TabsTrigger>
          <TabsTrigger value="docs" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Documentation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="help" className="space-y-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/50" />
            <Input
              type="text"
              placeholder="Search for help..."
              className="pl-10 py-6 firenest-card text-white text-lg"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="firenest-card hover:bg-dark-700 transition-colors cursor-pointer">
              <CardHeader className="pb-2">
                <CardTitle className="text-md font-medium flex items-center text-white">
                  <BookOpen className="h-5 w-5 mr-2 text-fiery" />
                  Getting Started
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70">
                  Learn the basics of Firenest and how to set up your account
                </p>
              </CardContent>
            </Card>

            <Card className="firenest-card hover:bg-dark-700 transition-colors cursor-pointer">
              <CardHeader className="pb-2">
                <CardTitle className="text-md font-medium flex items-center text-white">
                  <FileText className="h-5 w-5 mr-2 text-fiery" />
                  Billing & Credits
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70">
                  Understand how billing works and manage your credits
                </p>
              </CardContent>
            </Card>

            <Card className="firenest-card hover:bg-dark-700 transition-colors cursor-pointer">
              <CardHeader className="pb-2">
                <CardTitle className="text-md font-medium flex items-center text-white">
                  <MessageSquare className="h-5 w-5 mr-2 text-fiery" />
                  AI Tools
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70">
                  Learn about the different AI tools available on Firenest
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-lg text-white">Frequently Asked Questions</CardTitle>
              <CardDescription>
                Common questions about using Firenest
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {faqs.map((faq, index) => (
                  <div key={index} className="p-4 rounded-md bg-white/5">
                    <h3 className="text-md font-medium text-white flex items-start">
                      <HelpCircle className="h-5 w-5 mr-2 text-fiery shrink-0 mt-0.5" />
                      <span>{faq.question}</span>
                    </h3>
                    <p className="mt-2 text-sm text-white/70 ml-7">
                      {faq.answer}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-6">
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-lg text-white">Contact Support</CardTitle>
              <CardDescription>
                Get help from our support team
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm text-white/70">Name</label>
                    <Input
                      id="name"
                      placeholder="Your name"
                      className="firenest-card text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm text-white/70">Email</label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Your email"
                      className="firenest-card text-white"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="subject" className="text-sm text-white/70">Subject</label>
                  <Input
                    id="subject"
                    placeholder="What's your issue about?"
                    className="firenest-card text-white"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm text-white/70">Message</label>
                  <Textarea
                    id="message"
                    placeholder="Describe your issue in detail"
                    className="firenest-card text-white min-h-32"
                  />
                </div>
                <Button className="bg-fiery hover:bg-fiery/90 text-white">
                  <Mail className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="firenest-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-md font-medium text-white">Email Support</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70 mb-4">
                  You can also reach our support team directly via email
                </p>
                <Button variant="outline" className="border-white/10 text-white hover:bg-white/5">
                  <Mail className="h-4 w-4 mr-2" />
                  <EMAIL>
                </Button>
              </CardContent>
            </Card>

            <Card className="firenest-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-md font-medium text-white">Response Time</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70">
                  We typically respond to all support requests within 24 hours during business days. For urgent issues, please indicate this in your subject line.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="docs" className="space-y-6">
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-lg text-white">Documentation</CardTitle>
              <CardDescription>
                Comprehensive guides and API documentation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 rounded-md bg-white/5 flex flex-col">
                  <h3 className="text-md font-medium text-white mb-2">User Guides</h3>
                  <p className="text-sm text-white/70 mb-4">
                    Step-by-step guides for using Firenest features
                  </p>
                  <Button className="mt-auto bg-fiery hover:bg-fiery/90 text-white">
                    View User Guides
                  </Button>
                </div>

                <div className="p-4 rounded-md bg-white/5 flex flex-col">
                  <h3 className="text-md font-medium text-white mb-2">API Documentation</h3>
                  <p className="text-sm text-white/70 mb-4">
                    Technical documentation for developers
                  </p>
                  <Button className="mt-auto bg-fiery hover:bg-fiery/90 text-white">
                    View API Docs
                  </Button>
                </div>

                <div className="p-4 rounded-md bg-white/5 flex flex-col">
                  <h3 className="text-md font-medium text-white mb-2">Tutorials</h3>
                  <p className="text-sm text-white/70 mb-4">
                    Learn how to get the most out of Firenest
                  </p>
                  <Button className="mt-auto bg-fiery hover:bg-fiery/90 text-white">
                    View Tutorials
                  </Button>
                </div>

                <div className="p-4 rounded-md bg-white/5 flex flex-col">
                  <h3 className="text-md font-medium text-white mb-2">Integration Guides</h3>
                  <p className="text-sm text-white/70 mb-4">
                    Connect Firenest with your existing tools
                  </p>
                  <Button className="mt-auto bg-fiery hover:bg-fiery/90 text-white">
                    View Integrations
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Support;
