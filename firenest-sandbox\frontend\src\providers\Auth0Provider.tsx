/**
 * Auth0 Provider Configuration
 * SOC 2 Alignment: CC6.1 (Logical Access), CC6.2 (Access Control)
 */

import React from 'react'
import { Auth0Provider as Auth0ProviderBase } from '@auth0/auth0-react'
import { useNavigate } from 'react-router-dom'

interface Auth0ProviderProps {
  children: React.ReactNode
}

export function Auth0Provider({ children }: Auth0ProviderProps) {
  const navigate = useNavigate()

  const domain = import.meta.env.VITE_AUTH0_DOMAIN || 'dev-22a3bgq8rrdgtwy3.au.auth0.com'
  const clientId = import.meta.env.VITE_AUTH0_CLIENT_ID || 'Jgi7Idu1z8y2tT6vSLxWVmZfmMNarvS3'
  const redirectUri = window.location.origin

  const onRedirectCallback = (appState?: any) => {
    navigate(appState?.returnTo || window.location.pathname)
  }

  if (!domain || !clientId) {
    console.error('Auth0 configuration missing. Please check your environment variables.')
    return <div>Auth0 configuration error</div>
  }

  return (
    <Auth0ProviderBase
      domain={domain}
      clientId={clientId}
      authorizationParams={{
        redirect_uri: redirectUri,
        audience: `https://${domain}/api/v2/`,
        scope: 'openid profile email read:current_user update:current_user_metadata'
      }}
      onRedirectCallback={onRedirectCallback}
      useRefreshTokens={true}
      cacheLocation="localstorage"
    >
      {children}
    </Auth0ProviderBase>
  )
}

export default Auth0Provider
