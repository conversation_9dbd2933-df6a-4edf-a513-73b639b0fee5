import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Home,
  Settings,
  Key,
  Code,
  BarChart2,
  Zap,
  FileText,
  Bell,
  HelpCircle,
  Webhook,
  Shield,
  Database,
  Users
} from 'lucide-react';

// Define the navigation steps and their corresponding routes
export const navigationSteps = [
  {
    id: 'setup',
    label: 'Setup',
    icon: Zap,
    path: '/partner/tools',
    description: 'Configure your SaaS integration',
    tabValue: 'setup',
    setupStep: 1
  },
  {
    id: 'details',
    label: 'Tool Details',
    icon: FileText,
    path: '/partner/tools',
    description: 'Provide comprehensive information about your AI tool',
    tabValue: 'details',
    setupStep: 1
  },
  {
    id: 'authentication',
    label: 'Authentication',
    icon: Key,
    path: '/partner/tools',
    description: 'Configure authentication methods',
    tabValue: 'authentication',
    setupStep: 2
  },
  {
    id: 'integration',
    label: 'Integration',
    icon: Code,
    path: '/partner/tools',
    description: 'Connect your tool with Firenest\'s platform',
    tabValue: 'integration',
    setupStep: 3
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: BarChart2,
    path: '/partner/tools',
    description: 'View usage analytics and reports',
    tabValue: 'analytics',
    setupStep: 4
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    path: '/partner/tools',
    description: 'Manage tool settings',
    tabValue: 'settings',
    setupStep: 4
  },
  {
    id: 'webhooks',
    label: 'Webhooks',
    icon: Webhook,
    path: '/partner/webhooks',
    description: 'Set up webhook notifications'
  },
  {
    id: 'documentation',
    label: 'Documentation',
    icon: FileText,
    path: '/partner/documentation',
    description: 'View documentation and guides'
  }
];

interface IntegratedNavigationProps {
  currentStep?: string;
  setupStep?: number;
  activeTab?: string;
  toolId?: string;
  onSetupStepChange?: (step: number) => void;
  onTabChange?: (tab: string) => void;
  className?: string;
}

const IntegratedNavigation: React.FC<IntegratedNavigationProps> = ({
  currentStep,
  setupStep = 1,
  activeTab = 'setup',
  toolId,
  onSetupStepChange,
  onTabChange,
  className
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeItem, setActiveItem] = useState<string>('');
  // Determine the active item based on the current location and active tab
  useEffect(() => {
    const path = location.pathname;

    if (path.includes('/partner/tools/')) {
      // If we're on a tool detail page, find the matching item based on the active tab
      const matchingItem = navigationSteps.find(item => item.tabValue === activeTab);
      if (matchingItem) {
        setActiveItem(matchingItem.id);
      } else {
        setActiveItem('setup');
      }
    } else {
      // For other pages, find the matching navigation item based on path
      const matchingItem = navigationSteps.find(item => item.path === path);
      if (matchingItem) {
        setActiveItem(matchingItem.id);
      }
    }
  }, [location.pathname, activeTab]);

  // Handle navigation item click
  const handleItemClick = (item: typeof navigationSteps[0]) => {
    if (item.path.includes('/partner/tools') && toolId) {
      // If this is a tool-related item and we have a toolId, navigate to the tool page with the appropriate tab
      if (item.tabValue) {
        if (onTabChange) {
          onTabChange(item.tabValue);
        }
        navigate(`/partner/tools/${toolId}?tab=${item.tabValue}`, { replace: true });

        // If this item has a setupStep, update it
        if (item.setupStep && onSetupStepChange) {
          onSetupStepChange(item.setupStep);
        }
      }
    } else {
      // Otherwise, navigate to the item's path
      navigate(item.path);
    }
  };

  return (
    <div
      className={cn(
        "bg-dark-900 border-b border-white/10 transition-all duration-300 py-3",
        className
      )}
    >
      <div className="container mx-auto px-4">
        {/* Navigation with Back Button and Menu Items */}
        <div className="flex justify-between items-center mb-4">
          {/* Back Button */}
          <Button
            variant="ghost"
            size="sm"
            className="text-white/70 hover:text-white hover:bg-white/5 mr-4"
            onClick={() => navigate('/partner/dashboard')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
            Back to Dashboard
          </Button>

          {/* Main Navigation */}
          <div className="flex-1 flex justify-center">
            <div className="flex items-center space-x-6 overflow-x-auto hide-scrollbar">
              {navigationSteps
                .filter(item => !['docs', 'support'].includes(item.id)) // Filter out docs and support
                .map((item) => (
                  <TooltipProvider key={item.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={activeItem === item.id ? "default" : "ghost"}
                          size="sm"
                          className={cn(
                            "flex items-center gap-2 whitespace-nowrap transition-all duration-300 px-4 py-2 rounded-md",
                            activeItem === item.id
                              ? "bg-fiery text-white hover:bg-fiery/90"
                              : "text-white/70 hover:text-white hover:bg-white/5",
                          )}
                          onClick={() => handleItemClick(item)}
                        >
                          <item.icon className="w-5 h-5 flex-shrink-0" />
                          <span className="transition-all duration-300">{item.label}</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>{item.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
            </div>
          </div>
        </div>

        {/* Wizard progress bar has been moved to the main content */}

        {/* Footer with Docs and Support - moved to the bottom of the page */}
      </div>
    </div>
  );
};

export default IntegratedNavigation;
