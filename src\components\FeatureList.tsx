
import { Flame, Zap, BarChart3, IndianRupee } from "lucide-react";

const features = [
  {
    title: "AI tools without subscription fees",
    description: "Pay only for what you use with our flexible credit-based system.",
    icon: <Zap className="w-6 h-6 text-fiery" />
  },
  {
    title: "Tailored for India's startup ecosystem",
    description: "Built specifically for the needs and challenges of Indian entrepreneurs.",
    icon: <IndianRupee className="w-6 h-6 text-fiery" />
  },
  {
    title: "Credit-based pricing for startups",
    description: "More affordable than subscriptions, perfect for growing businesses.",
    icon: <BarChart3 className="w-6 h-6 text-fiery" />
  },
  {
    title: "Supported by Startup India",
    description: "Backed by Startup India's seed fund for maximum impact.",
    icon: <Flame className="w-6 h-6 text-fiery" />
  }
];

const FeatureList = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
      {features.map((feature, index) => (
        <div 
          key={index}
          className="flex gap-4 p-6 rounded-lg bg-white/5 border border-white/10 animate-fade-in"
          style={{ animationDelay: `${index * 150}ms` }}
        >
          <div className="shrink-0 mt-1">
            {feature.icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
            <p className="text-white/70">{feature.description}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FeatureList;
