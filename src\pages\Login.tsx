import { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import AuthLayout from '@/components/AuthLayout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Mail, KeyRound } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InlineLoading } from '@/components/ui/loading';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSendingMagicLink, setIsSendingMagicLink] = useState(false);
  const [error, setError] = useState<string | JSX.Element>('');
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const { login, signInWithMagicLink, user, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if user is already logged in
  useEffect(() => {
    console.log('Login page - user:', user ? 'exists' : 'null', 'isLoading:', isLoading);
    if (user && !isLoading) {
      navigate('/dashboard');
    }
  }, [user, isLoading, navigate]);

  // Check for message in location state (e.g., from signup page)
  useEffect(() => {
    if (location.state?.message) {
      notify.info(location.state.message, {
        title: 'Information',
        duration: 4000
      });
      // Clear the message after showing it
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location, navigate]);

  const handleMagicLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email) {
      setError('Email is required');
      return;
    }

    setIsSendingMagicLink(true);

    try {
      console.log('Attempting to send magic link to:', email);
      const result = await signInWithMagicLink(email);

      if (result.success) {
        setMagicLinkSent(true);
        // Success message is handled by the signInWithMagicLink function
      } else {
        console.error('Magic link failed:', result.error);
        const errorMessage = result.error?.message || 'Failed to send magic link';
        setError(errorMessage);
      }
    } catch (err) {
      console.error('Unexpected error sending magic link:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSendingMagicLink(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email || !password) {
      setError('Email and password are required');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Attempting to login with:', email);

      // Add a timeout to prevent infinite loading
      const loginPromise = login(email, password);
      const timeoutPromise = new Promise<{success: false, error: {message: string}}>((resolve) => {
        setTimeout(() => {
          resolve({
            success: false,
            error: { message: 'Login timed out. Please try again.' }
          });
        }, 10000); // 10 second timeout
      });

      const result = await Promise.race([loginPromise, timeoutPromise]) as {success: boolean, error?: {message: string}};

      if (result.success) {
        console.log('Login successful, redirecting to dashboard');
        // The redirect will happen automatically via the useEffect when user state updates
      } else {
        console.error('Login failed:', result.error);

        // Handle specific error cases
        const errorMessage = result.error?.message || '';
        if (errorMessage.includes('Invalid login credentials')) {
          setError('Invalid email or password. Please check your credentials and try again.');
        } else if (errorMessage.includes('Email not confirmed')) {
          // Redirect to the email verification page
          navigate('/verify-email', { state: { email } });
          return;
        } else if (errorMessage.includes('timed out')) {
          setError('Login process timed out. This could be due to network issues or database problems. Please try again.');
        } else {
          setError(errorMessage || 'Login failed. Please check your credentials.');
        }
      }
    } catch (err) {
      console.error('Unexpected error during login:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AuthLayout
      title="Welcome Back"
      subtitle="Log in to access your Firenest dashboard"
    >
      <Tabs defaultValue="password" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="password" className="flex items-center gap-2">
            <KeyRound className="h-4 w-4" />
            Password
          </TabsTrigger>
          <TabsTrigger value="magic-link" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Magic Link
          </TabsTrigger>
        </TabsList>

        <TabsContent value="password">
          <form onSubmit={handlePasswordSubmit} className="space-y-4">
        {error && (
          <div className="firenest-card bg-red-500/20 border-red-500/50 text-red-200 px-4 py-3 mb-4">
            {error}
          </div>
        )}

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-white/80 mb-2">
            Email Address
          </label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            required
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="password" className="block text-sm font-medium text-white/80">
              Password
            </label>
            <Link to="/forgot-password" className="text-xs text-fiery hover:text-fiery-400 transition-colors">
              Forgot password?
            </Link>
          </div>
          <Input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="••••••••"
            required
            className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
          />
        </div>

        <Button
          type="submit"
          disabled={isSubmitting}
          className="w-full pop-button"
        >
          {isSubmitting ? (
            <>
              <InlineLoading className="mr-2 h-4 w-4" />
              Logging in...
            </>
          ) : (
            'Log In'
          )}
        </Button>

        </form>
        </TabsContent>

        <TabsContent value="magic-link">
          {magicLinkSent ? (
            <div className="firenest-card bg-green-500/20 border-green-500/50 text-green-200 px-4 py-6 text-center">
              <Mail className="h-12 w-12 mx-auto mb-4 text-green-400" />
              <h3 className="text-lg font-medium mb-2">Magic Link Sent!</h3>
              <p>We've sent a login link to <strong>{email}</strong></p>
              <p className="mt-4 text-sm">Check your email and click the link to sign in.</p>
              <Button
                type="button"
                variant="outline"
                className="mt-4 border-white/20 hover:bg-white/5"
                onClick={() => setMagicLinkSent(false)}
              >
                Send another link
              </Button>
            </div>
          ) : (
            <form onSubmit={handleMagicLinkSubmit} className="space-y-4">
              <div>
                <label htmlFor="magic-email" className="block text-sm font-medium text-white/80 mb-2">
                  Email Address
                </label>
                <Input
                  id="magic-email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                  className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                />
              </div>

              <Button
                type="submit"
                disabled={isSendingMagicLink}
                className="w-full pop-button"
              >
                {isSendingMagicLink ? (
                  <>
                    <InlineLoading className="mr-2 h-4 w-4" />
                    Sending link...
                  </>
                ) : (
                  'Send Magic Link'
                )}
              </Button>

              <div className="firenest-card bg-blue-500/20 border-blue-500/50 text-blue-200 px-4 py-3 text-sm">
                <p>We'll send a secure login link to your email. No password needed!</p>
              </div>
            </form>
          )}
        </TabsContent>
      </Tabs>

      <div className="text-center mt-6">
        <p className="text-white/70 text-sm">
          Don't have an account?{' '}
          <Link to="/signup" className="text-fiery hover:text-fiery-400 transition-colors">
            Sign up
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
};

export default Login;
