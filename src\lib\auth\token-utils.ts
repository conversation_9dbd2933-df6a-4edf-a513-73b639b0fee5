import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a secure token with optional prefix
 * 
 * @param type The type of token ('access' or 'refresh')
 * @returns A secure token string
 */
export function generateToken(type: 'access' | 'refresh'): string {
  // Generate a random string for the token using UUID v4
  const token = uuidv4();
  
  // Add a prefix to distinguish between access and refresh tokens
  return type === 'access' ? `at_${token}` : `rt_${token}`;
}

/**
 * Verify an access token
 * 
 * @param accessToken The access token to verify
 * @returns The user ID and partner ID if the token is valid, null otherwise
 */
export async function verifyAccessToken(accessToken: string, supabase: any): Promise<{ valid: boolean; userId?: string; partnerId?: string }> {
  try {
    if (!accessToken) {
      return { valid: false };
    }

    // Verify token in database
    const { data, error } = await supabase
      .from('access_tokens')
      .select('user_id, partner_id, expires_at')
      .eq('access_token', accessToken)
      .maybeSingle();

    if (error || !data) {
      console.error('Error verifying access token:', error);
      return { valid: false };
    }

    // Check if token has expired
    const expiresAt = new Date(data.expires_at);
    if (expiresAt < new Date()) {
      console.log('Token has expired:', { expiresAt, now: new Date() });
      return { valid: false };
    }

    return { 
      valid: true,
      userId: data.user_id,
      partnerId: data.partner_id
    };
  } catch (error) {
    console.error('Error in verifyAccessToken:', error);
    return { valid: false };
  }
}

/**
 * Store an access token in the database
 * 
 * @param tokenData The token data to store
 * @param supabase The Supabase client
 * @returns The result of the database operation
 */
export async function storeAccessToken(
  tokenData: {
    access_token: string;
    refresh_token: string;
    user_id: string;
    partner_id: string;
    client_id: string;
    expires_at: string;
    scope?: string;
  },
  supabase: any
) {
  try {
    const { data, error } = await supabase
      .from('access_tokens')
      .insert(tokenData)
      .select('id')
      .single();

    if (error) {
      console.error('Error storing access token:', error);
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error in storeAccessToken:', error);
    return { data: null, error };
  }
}

/**
 * Refresh an access token
 * 
 * @param refreshToken The refresh token
 * @param clientId The client ID
 * @param supabase The Supabase client
 * @returns The new tokens if successful, null otherwise
 */
export async function refreshAccessToken(
  refreshToken: string,
  clientId: string,
  supabase: any
) {
  try {
    // Verify the refresh token
    const { data: tokenData, error: tokenError } = await supabase
      .from('access_tokens')
      .select('*')
      .eq('refresh_token', refreshToken)
      .eq('client_id', clientId)
      .maybeSingle();

    if (tokenError || !tokenData) {
      console.error('Error verifying refresh token:', tokenError);
      return { success: false, error: tokenError || new Error('Invalid refresh token') };
    }

    // Generate new tokens
    const access_token = generateToken('access');
    const new_refresh_token = generateToken('refresh');

    // Update the tokens in the database
    const { error: updateError } = await supabase
      .from('access_tokens')
      .update({
        access_token,
        refresh_token: new_refresh_token,
        expires_at: new Date(Date.now() + 3600 * 1000).toISOString() // 1 hour expiration
      })
      .eq('refresh_token', refreshToken);

    if (updateError) {
      console.error('Error updating tokens:', updateError);
      return { success: false, error: updateError };
    }

    return {
      success: true,
      access_token,
      refresh_token: new_refresh_token,
      expires_in: 3600,
      token_type: 'Bearer',
      scope: tokenData.scope
    };
  } catch (error) {
    console.error('Error in refreshAccessToken:', error);
    return { success: false, error };
  }
}
