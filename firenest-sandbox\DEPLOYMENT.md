# Firenest Sandbox - Production Deployment Guide

## Overview

This guide covers the complete deployment of Firenest Sandbox, a production-ready pricing intelligence platform with enterprise-grade security, scalability, and performance.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Workers       │
│   (React SPA)   │────│   (Node.js)     │────│   (Background)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CloudFront    │    │   PostgreSQL    │    │   AWS SQS       │
│   (CDN)         │    │   (RDS)         │    │   (Job Queue)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   AWS S3        │
                       │   (File Storage)│
                       └─────────────────┘
```

## Prerequisites

- AWS Account with appropriate permissions
- Domain name for production deployment
- SSL certificate (AWS Certificate Manager recommended)
- Node.js 18+ for local development
- Docker for containerized deployment
- Terraform for infrastructure management

## Phase 1: Infrastructure Setup

### 1. AWS Infrastructure

```bash
cd infrastructure
terraform init
terraform plan -var="environment=production"
terraform apply -var="environment=production"
```

This creates:
- **VPC** with public/private subnets across multiple AZs
- **RDS PostgreSQL** with encryption, automated backups, and Multi-AZ
- **S3 Bucket** with server-side encryption and versioning
- **SQS Queues** for background job processing with DLQ
- **Security Groups** with least-privilege access
- **IAM Roles** for service-to-service authentication

### 2. Database Setup

```bash
# Connect to RDS instance
psql -h <rds-endpoint> -U sandbox_admin -d firenest_sandbox

# Run schema creation
\i database/schema.sql

# Verify tables and policies
\dt
\dp
```

### 3. Environment Configuration

Create production environment files:

**Backend (.env.production)**
```bash
# Database
DB_HOST=<rds-endpoint>
DB_PORT=5432
DB_NAME=firenest_sandbox
DB_USERNAME=sandbox_admin
DB_PASSWORD=<secure-password>
DB_SSL=true
DB_MAX_CONNECTIONS=20

# AWS Services
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=<access-key>
AWS_SECRET_ACCESS_KEY=<secret-key>
S3_BUCKET_NAME=<production-bucket>
SQS_QUEUE_URL=<sqs-queue-url>
SQS_DLQ_URL=<dlq-url>

# Security
JWT_SECRET=<32-character-secret>
ENCRYPTION_KEY=<32-character-key>
HASH_SALT=12

# Authentication (Auth0/Cognito)
AUTH_PROVIDER_TYPE=auth0
AUTH_PROVIDER_DOMAIN=<auth0-domain>
AUTH_PROVIDER_CLIENT_ID=<client-id>
AUTH_PROVIDER_CLIENT_SECRET=<client-secret>

# Application
NODE_ENV=production
PORT=3001
CORS_ALLOWED_ORIGINS=https://sandbox.firenest.com

# Monitoring
LOG_LEVEL=info
CLOUDWATCH_LOG_GROUP=/aws/ecs/firenest-sandbox
SENTRY_DSN=<sentry-dsn>
```

**Frontend (.env.production)**
```bash
VITE_API_URL=https://api.sandbox.firenest.com/api/v1
VITE_AUTH_DOMAIN=<auth0-domain>
VITE_AUTH_CLIENT_ID=<client-id>
VITE_ENVIRONMENT=production
```

## Phase 2: Application Deployment

### Option A: ECS Deployment (Recommended)

1. **Build and Push Docker Images**

```bash
# Backend API
cd backend
docker build -t firenest-sandbox-api:latest .
docker tag firenest-sandbox-api:latest <ecr-repo>/firenest-sandbox-api:latest
docker push <ecr-repo>/firenest-sandbox-api:latest

# Background Workers
docker build -f Dockerfile.worker -t firenest-sandbox-worker:latest .
docker tag firenest-sandbox-worker:latest <ecr-repo>/firenest-sandbox-worker:latest
docker push <ecr-repo>/firenest-sandbox-worker:latest

# Frontend
cd ../frontend
docker build -t firenest-sandbox-frontend:latest .
docker tag firenest-sandbox-frontend:latest <ecr-repo>/firenest-sandbox-frontend:latest
docker push <ecr-repo>/firenest-sandbox-frontend:latest
```

2. **Deploy ECS Services**

```bash
# Deploy using Terraform ECS module
cd infrastructure/ecs
terraform init
terraform apply -var="api_image=<ecr-repo>/firenest-sandbox-api:latest" \
                -var="worker_image=<ecr-repo>/firenest-sandbox-worker:latest" \
                -var="frontend_image=<ecr-repo>/firenest-sandbox-frontend:latest"
```

### Option B: EC2 Deployment

1. **Setup Application Server**

```bash
# Install Node.js and PM2
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install -g pm2

# Clone and setup application
git clone <repository-url> /opt/firenest-sandbox
cd /opt/firenest-sandbox

# Backend setup
cd backend
npm ci --production
npm run build
cp .env.production .env

# Frontend setup
cd ../frontend
npm ci
npm run build

# Setup PM2 ecosystem
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

2. **Configure Nginx**

```nginx
server {
    listen 80;
    server_name sandbox.firenest.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name sandbox.firenest.com;

    ssl_certificate /etc/ssl/certs/firenest.crt;
    ssl_certificate_key /etc/ssl/private/firenest.key;

    # Frontend
    location / {
        root /opt/firenest-sandbox/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    }

    # API
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Phase 3: Monitoring & Observability

### 1. Application Monitoring

```bash
# Install monitoring agents
npm install @sentry/node @sentry/tracing

# Setup CloudWatch logs
aws logs create-log-group --log-group-name /aws/ecs/firenest-sandbox

# Configure health checks
curl -f http://localhost:3001/health || exit 1
```

### 2. Database Monitoring

```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Monitor connections
SELECT count(*) FROM pg_stat_activity;

-- Monitor slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

### 3. Performance Metrics

Key metrics to monitor:
- **API Response Time**: < 200ms for 95th percentile
- **Database Connections**: < 80% of max connections
- **Queue Depth**: SQS messages should process within 30 seconds
- **Error Rate**: < 0.1% for production traffic
- **Memory Usage**: < 80% of allocated memory
- **CPU Usage**: < 70% average utilization

## Phase 4: Security Hardening

### 1. Network Security

```bash
# Configure security groups
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxx \
  --protocol tcp \
  --port 443 \
  --cidr 0.0.0.0/0

# Setup WAF rules
aws wafv2 create-web-acl \
  --name firenest-sandbox-waf \
  --scope CLOUDFRONT
```

### 2. Data Encryption

- **At Rest**: RDS encryption, S3 server-side encryption
- **In Transit**: TLS 1.3 for all communications
- **Application**: JWT tokens, bcrypt password hashing

### 3. Access Control

```bash
# Setup IAM roles with least privilege
aws iam create-role --role-name FirenestSandboxAPI
aws iam attach-role-policy --role-name FirenestSandboxAPI --policy-arn arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess

# Configure RLS policies (already in schema.sql)
# Enable audit logging
```

## Phase 5: Backup & Disaster Recovery

### 1. Database Backups

```bash
# Automated RDS backups (configured in Terraform)
# Point-in-time recovery enabled
# Cross-region backup replication

# Manual backup
pg_dump -h <rds-endpoint> -U sandbox_admin firenest_sandbox > backup.sql
```

### 2. Application Backups

```bash
# S3 versioning enabled
# Cross-region replication configured
# Lifecycle policies for cost optimization
```

### 3. Disaster Recovery Plan

- **RTO**: 4 hours (Recovery Time Objective)
- **RPO**: 1 hour (Recovery Point Objective)
- **Multi-AZ deployment** for high availability
- **Cross-region backups** for disaster recovery
- **Automated failover** procedures documented

## Phase 6: Performance Optimization

### 1. Database Optimization

```sql
-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM simulations WHERE project_id = 'xxx';

-- Update statistics
ANALYZE;

-- Vacuum regularly
VACUUM ANALYZE;
```

### 2. Application Optimization

```bash
# Enable compression
gzip on;
gzip_types text/plain application/json application/javascript text/css;

# Setup CDN caching
aws cloudfront create-distribution
```

### 3. Scaling Configuration

- **Auto Scaling Groups** for EC2 instances
- **RDS Read Replicas** for read-heavy workloads
- **ElastiCache** for session storage and caching
- **SQS scaling** based on queue depth

## Production Checklist

- [ ] Infrastructure deployed and tested
- [ ] Database schema applied with RLS policies
- [ ] Application deployed with health checks
- [ ] SSL certificates configured
- [ ] Monitoring and alerting setup
- [ ] Backup procedures tested
- [ ] Security scanning completed
- [ ] Performance testing passed
- [ ] Documentation updated
- [ ] Team training completed

## Support & Maintenance

### Regular Maintenance Tasks

- **Weekly**: Review performance metrics and error logs
- **Monthly**: Security updates and dependency patches
- **Quarterly**: Disaster recovery testing
- **Annually**: Security audit and penetration testing

### Troubleshooting

Common issues and solutions documented in `/docs/troubleshooting.md`

For technical support: [<EMAIL>](mailto:<EMAIL>)
