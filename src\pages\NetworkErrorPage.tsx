import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Wifi, RefreshCw, Home, ChevronDown, ChevronUp, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { notify } from '@/components/ui/notification-system';

interface NetworkErrorPageProps {
  title?: string;
  message?: string;
  statusCode?: number;
  onRetry?: () => void;
}

const NetworkErrorPage: React.FC<NetworkErrorPageProps> = ({
  title = 'Connection Issue',
  message = 'We\'re having trouble connecting to our servers',
  statusCode,
  onRetry
}) => {
  const navigate = useNavigate();
  const [showDetails, setShowDetails] = useState(false);
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const [diagnosticResults, setDiagnosticResults] = useState<string[]>([]);

  const handleRefresh = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const runNetworkDiagnostics = async () => {
    setIsRunningDiagnostics(true);
    setDiagnosticResults([]);

    // Add initial diagnostic message
    setDiagnosticResults(prev => [...prev, 'Starting network diagnostics...']);

    // Check if online
    const isOnline = navigator.onLine;
    await new Promise(resolve => setTimeout(resolve, 500));
    setDiagnosticResults(prev => [...prev, `Internet connection: ${isOnline ? 'Online ✓' : 'Offline ✗'}`]);

    // Check DNS resolution (simulated)
    await new Promise(resolve => setTimeout(resolve, 700));
    setDiagnosticResults(prev => [...prev, 'DNS resolution: Checking...']);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setDiagnosticResults(prev => [...prev, 'DNS resolution: Working ✓']);

    // Check API endpoint
    await new Promise(resolve => setTimeout(resolve, 800));
    setDiagnosticResults(prev => [...prev, 'API endpoint: Checking...']);

    try {
      // Simulate API check
      await new Promise(resolve => setTimeout(resolve, 1500));

      if (statusCode && statusCode >= 500) {
        setDiagnosticResults(prev => [...prev, `API endpoint: Error (${statusCode}) ✗`]);
        setDiagnosticResults(prev => [...prev, 'Our servers appear to be experiencing issues. Please try again later.']);
      } else if (statusCode && statusCode === 403) {
        setDiagnosticResults(prev => [...prev, `API endpoint: Forbidden (${statusCode}) ✗`]);
        setDiagnosticResults(prev => [...prev, 'You may not have permission to access this resource.']);
      } else if (statusCode && statusCode === 401) {
        setDiagnosticResults(prev => [...prev, `API endpoint: Unauthorized (${statusCode}) ✗`]);
        setDiagnosticResults(prev => [...prev, 'Your session may have expired. Try logging in again.']);
      } else if (!navigator.onLine) {
        setDiagnosticResults(prev => [...prev, 'API endpoint: Unreachable (No internet) ✗']);
      } else {
        // Random success/failure for demo purposes
        const success = Math.random() > 0.5;
        setDiagnosticResults(prev => [...prev, `API endpoint: ${success ? 'Reachable ✓' : 'Unreachable ✗'}`]);

        if (!success) {
          setDiagnosticResults(prev => [...prev, 'There may be a temporary issue with our API. Please try again later.']);
        }
      }
    } catch (error) {
      setDiagnosticResults(prev => [...prev, 'API endpoint: Error during check ✗']);
    }

    // Final diagnosis
    await new Promise(resolve => setTimeout(resolve, 1000));
    setDiagnosticResults(prev => [...prev, 'Diagnostics complete.']);

    setIsRunningDiagnostics(false);
    notify.info('Network diagnostics completed', {
      title: 'Diagnostics',
      duration: 4000
    });
  };

  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />

      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>

      <main className="flex-grow flex items-center justify-center p-6">
        <div className="w-full max-w-2xl">
          <div className="glass-card p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

            <div className="flex flex-col items-center mb-8">
              <div className="w-20 h-20 bg-cool-500/10 rounded-full flex items-center justify-center mb-4">
                <Wifi className="h-10 w-10 text-cool-500 animate-pulse-slow" />
              </div>
              <h2 className="text-2xl font-bold mb-2">{title}</h2>
              <p className="text-white/70 text-center">{message}</p>
              {statusCode && (
                <div className="mt-2 px-3 py-1 bg-dark-800/50 rounded-full text-white/60 text-sm">
                  Status Code: {statusCode}
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <Button
                onClick={handleRefresh}
                className="flex-1 bg-fiery hover:bg-fiery-600 text-white"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>

              <Button
                onClick={handleGoHome}
                variant="outline"
                className="flex-1 border-white/20"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
            </div>

            <div className="border-t border-white/10 pt-4">
              <button
                onClick={toggleDetails}
                className="flex items-center justify-between w-full text-white/70 hover:text-white transition-colors py-2"
              >
                <span className="font-medium">Network Diagnostics</span>
                {showDetails ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </button>

              {showDetails && (
                <div className="mt-4 space-y-4">
                  <p className="text-sm text-white/70">
                    Run a network diagnostic test to help identify the source of the connection issue.
                  </p>

                  <Button
                    onClick={runNetworkDiagnostics}
                    disabled={isRunningDiagnostics}
                    className="w-full bg-cool-500 hover:bg-cool-600 text-white"
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    {isRunningDiagnostics ? 'Running Diagnostics...' : 'Run Network Diagnostics'}
                  </Button>

                  {diagnosticResults.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-white/80 mb-2">Diagnostic Results</h3>
                      <div className="bg-dark-900/70 border border-white/10 rounded-md p-3 text-sm text-white/70">
                        {diagnosticResults.map((result, index) => (
                          <div key={index} className="py-1 border-b border-white/5 last:border-0">
                            {result}
                          </div>
                        ))}
                        {isRunningDiagnostics && (
                          <div className="py-1 flex items-center">
                            <div className="animate-spin mr-2 h-3 w-3 border-2 border-white/20 border-t-white/80 rounded-full"></div>
                            Running...
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="text-xs text-white/50 mt-4">
                    <p>Common solutions:</p>
                    <ul className="list-disc pl-5 mt-1 space-y-1">
                      <li>Check your internet connection</li>
                      <li>Clear your browser cache</li>
                      <li>Try using a different browser</li>
                      <li>Disable any VPN or proxy services</li>
                      <li>Contact your network administrator if on a corporate network</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <footer className="py-4 px-6 text-center text-white/50 text-sm">
        <p>
          <Link to="/" className="text-fiery hover:text-fiery-400 hover:underline">
            Firenest
          </Link>{' '}
          &copy; {new Date().getFullYear()} All rights reserved.
        </p>
      </footer>
    </div>
  );
};

export default NetworkErrorPage;
