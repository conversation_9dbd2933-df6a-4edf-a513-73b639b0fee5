import React, { useEffect, useState, useRef } from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Loading } from '@/components/ui/loading';
import { PartnerProvider } from '@/contexts/PartnerContext';
import { getPartnerAccount, createPartnerAccount } from '@/lib/partner-portal/api';
import { usePageVisibility } from '@/hooks/usePageVisibility';
import { notify } from '@/components/ui/notification-system';
import PartnerErrorBoundary from '@/components/partner/PartnerErrorBoundary';

const PartnerProtectedRoute: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasPartnerAccount, setHasPartnerAccount] = useState(false);
  const [sessionCheckCount, setSessionCheckCount] = useState(0);

  // Track page visibility to prevent unnecessary auth checks when switching tabs
  const isVisible = usePageVisibility();
  const lastVisibleTime = useRef<number>(Date.now());
  const sessionCheckedOnce = useRef<boolean>(false);
  const partnerDataRef = useRef<{userId: string, partnerId: string} | null>(null);

  // Function to check if we have cached partner data
  const getCachedPartnerData = () => {
    try {
      // Try localStorage first for persistence across tabs
      const cachedData = localStorage.getItem('firenest_partner_data') ||
                         sessionStorage.getItem('firenest_partner_data');

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        const cacheTime = parsedData.timestamp || 0;
        // Use cached data if it's less than 60 minutes old
        if (Date.now() - cacheTime < 60 * 60 * 1000) {
          console.log('Using cached partner data from storage');
          return parsedData;
        }
      }
    } catch (error) {
      console.error('Error reading cached partner data:', error);
    }
    return null;
  };

  // Function to save partner data to both session and local storage for persistence
  const savePartnerData = (userId: string, partnerId: string) => {
    try {
      const dataToCache = {
        userId,
        partnerId,
        timestamp: Date.now()
      };

      // Save to both storage types for maximum persistence
      const dataString = JSON.stringify(dataToCache);
      localStorage.setItem('firenest_partner_data', dataString);
      sessionStorage.setItem('firenest_partner_data', dataString);

      partnerDataRef.current = { userId, partnerId };
      console.log('Partner data cached successfully');
    } catch (error) {
      console.error('Error saving partner data to cache:', error);
    }
  };

  // Update last visible time when page becomes visible
  useEffect(() => {
    if (isVisible) {
      lastVisibleTime.current = Date.now();
    }
  }, [isVisible]);

  useEffect(() => {
    // Skip session check if page is not visible and we've already checked once
    if (!isVisible && sessionCheckedOnce.current) {
      return;
    }

    const checkAuth = async () => {
      setIsLoading(true);

      try {
        console.log(`Checking authentication status (attempt ${sessionCheckCount + 1})...`);

        // Try to use cached data first if available
        const cachedData = getCachedPartnerData();
        if (cachedData && cachedData.userId && cachedData.partnerId) {
          console.log('Using cached partner data:', cachedData);
          setIsAuthenticated(true);
          setHasPartnerAccount(true);
          setIsLoading(false);
          sessionCheckedOnce.current = true;
          return;
        }

        // Add a timeout to the session check
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise<{data: {session: null}}>((resolve) => {
          setTimeout(() => {
            console.warn('Session check timed out');
            resolve({ data: { session: null } });
          }, 5000); // 5 second timeout
        });

        const { data: { session } } = await Promise.race([sessionPromise, timeoutPromise]);

        if (session?.user) {
          console.log('User is authenticated:', session.user.id);
          setIsAuthenticated(true);

          // Check if user has a partner account
          console.log('Checking for partner account');

          try {
            // Use a direct approach without race to avoid the race condition
            const partnerAccount = await getPartnerAccount(session.user.id);

            if (partnerAccount) {
              console.log('Partner account found:', partnerAccount.id);
              setHasPartnerAccount(true);
              // Cache the partner data for future use
              savePartnerData(session.user.id, partnerAccount.id);
            } else {
              console.log('No partner account found for user:', session.user.id);
              setHasPartnerAccount(false);
            }
          } catch (partnerError) {
            console.error('Error fetching partner account:', partnerError);
            setHasPartnerAccount(false);
          }
        } else {
          console.log('No active session found');
          setIsAuthenticated(false);
          setHasPartnerAccount(false);
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        setIsAuthenticated(false);
        setHasPartnerAccount(false);
      } finally {
        setIsLoading(false);
        sessionCheckedOnce.current = true;
      }
    };

    checkAuth();

    // Set up a timer to re-check the session if loading takes too long
    const sessionCheckTimer = setTimeout(() => {
      if (isLoading) {
        console.log('Session check is taking too long, forcing a re-check...');
        setSessionCheckCount(prev => prev + 1);
      }
    }, 8000); // 8 seconds timeout

    // Listen for auth state changes
    console.log('Setting up auth state change listener');
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);

      // Debounce auth state changes to prevent excessive processing
      // Only process if the page is visible or this is a critical event
      if (!isVisible && !['SIGNED_IN', 'SIGNED_OUT', 'USER_DELETED'].includes(event)) {
        console.log('Page not visible, skipping non-critical auth state change');
        return;
      }

      // Skip processing for non-critical events if we've already checked once
      if (sessionCheckedOnce.current && !['SIGNED_IN', 'SIGNED_OUT', 'USER_DELETED'].includes(event)) {
        console.log('Session already checked, skipping non-critical auth state change:', event);
        return;
      }

      setIsLoading(true);

      try {
        if (session?.user) {
          console.log('User is authenticated after state change:', session.user.id);
          setIsAuthenticated(true);

          // Try to use cached data first if available
          const cachedData = getCachedPartnerData();
          if (cachedData && cachedData.userId === session.user.id && cachedData.partnerId) {
            console.log('Using cached partner data during auth state change:', cachedData);
            setHasPartnerAccount(true);
            setIsLoading(false);
            return;
          }

          // Check if user has a partner account with a longer timeout
          console.log('Checking for partner account after state change');

          try {
            // Use a direct approach without race to avoid the race condition
            const partnerAccount = await getPartnerAccount(session.user.id);

            if (partnerAccount) {
              console.log('Partner account found after state change:', partnerAccount.id);
              setHasPartnerAccount(true);
              // Cache the partner data for future use
              savePartnerData(session.user.id, partnerAccount.id);
            } else {
              console.log('No partner account found after state change for user:', session.user.id);
              setHasPartnerAccount(false);
            }
          } catch (partnerError) {
            console.error('Error fetching partner account during auth state change:', partnerError);
            // Check if we have cached data as a fallback
            const cachedData = getCachedPartnerData();
            if (cachedData && cachedData.userId === session.user.id) {
              console.log('Using cached partner data as fallback after error:', cachedData);
              setHasPartnerAccount(true);
            } else {
              setHasPartnerAccount(false);
            }
          }
        } else {
          console.log('No active session after state change');
          setIsAuthenticated(false);
          setHasPartnerAccount(false);
        }
      } catch (error) {
        console.error('Error in auth state change:', error);
        setIsAuthenticated(false);
        setHasPartnerAccount(false);
      } finally {
        setIsLoading(false);
      }
    });

    return () => {
      clearTimeout(sessionCheckTimer);
      console.log('Unsubscribing from auth state changes');
      subscription.unsubscribe();
    };
  }, [isVisible, sessionCheckCount, isLoading]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-950 flex flex-col items-center justify-center">
        <div className="mb-6">
          <Loading size="lg" className="text-fiery" />
        </div>
        <h2 className="text-xl font-medium text-white mb-2">Loading Partner Portal</h2>
        <p className="text-white/60 max-w-md text-center">
          Please wait while we prepare your partner dashboard...
        </p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/partner" replace />;
  }

  if (!hasPartnerAccount) {
    // User is authenticated but doesn't have a partner account
    console.log('User is authenticated but does not have a partner account');

    // Get the current user to access metadata
    supabase.auth.getUser().then(({ data }) => {
      if (data?.user) {
        const { name, company } = data.user.user_metadata || {};

        // If we have enough metadata, try to create a partner account
        if (name && company && data.user.email) {
          console.log('Attempting to create partner account with existing metadata');

          createPartnerAccount(
            data.user.id,
            name,
            data.user.email,
            company
          ).then((partnerAccount) => {
            if (partnerAccount) {
              console.log('Partner account created successfully');
              notify.success('Partner account created successfully');
              window.location.reload(); // Reload to trigger re-evaluation
              return;
            }
          }).catch((error: Error) => {
            console.error('Failed to create partner account:', error);
          });
        }
      }

      // If we couldn't create a partner account, sign out and redirect
      notify.error('No partner account found. Please complete registration.');
      supabase.auth.signOut().then(() => {
        console.log('User signed out due to missing partner account');
      });
    });

    return <Navigate to="/partner" replace />;
  }

  return (
    <PartnerErrorBoundary>
      <PartnerProvider>
        <Outlet />
      </PartnerProvider>
    </PartnerErrorBoundary>
  );
};

export default PartnerProtectedRoute;
