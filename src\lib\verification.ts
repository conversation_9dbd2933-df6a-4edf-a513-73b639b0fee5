import { supabase } from './supabase';

/**
 * Generates a random 6-digit verification code
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Stores a verification code in the database
 * In a real implementation, this would store the code in a secure database table
 * For demo purposes, we're using localStorage
 */
export function storeVerificationCode(email: string, code: string): void {
  try {
    // In a real implementation, you would store this in a database with an expiration
    // For demo purposes, we're using localStorage
    const verificationData = {
      email,
      code,
      expiresAt: Date.now() + 30 * 60 * 1000, // 30 minutes from now
    };
    
    localStorage.setItem(`verification_${email}`, JSON.stringify(verificationData));
    console.log(`Verification code stored for ${email}: ${code}`);
  } catch (error) {
    console.error('Error storing verification code:', error);
  }
}

/**
 * Verifies a code against the stored code for an email
 * In a real implementation, this would check against a database
 * For demo purposes, we're using localStorage
 */
export function verifyCode(email: string, code: string): boolean {
  try {
    const storedDataString = localStorage.getItem(`verification_${email}`);
    if (!storedDataString) {
      console.log(`No verification code found for ${email}`);
      return false;
    }
    
    const storedData = JSON.parse(storedDataString);
    
    // Check if the code has expired
    if (storedData.expiresAt < Date.now()) {
      console.log(`Verification code for ${email} has expired`);
      localStorage.removeItem(`verification_${email}`);
      return false;
    }
    
    // Check if the code matches
    const isValid = storedData.code === code;
    console.log(`Verification code for ${email} is ${isValid ? 'valid' : 'invalid'}`);
    
    // If valid, remove the code to prevent reuse
    if (isValid) {
      localStorage.removeItem(`verification_${email}`);
    }
    
    return isValid;
  } catch (error) {
    console.error('Error verifying code:', error);
    return false;
  }
}

/**
 * Manually marks an email as verified in Supabase
 * Note: This is a placeholder function. In a real implementation,
 * you would need server-side code with admin privileges to update the user's status.
 */
export async function markEmailAsVerified(email: string): Promise<{ success: boolean; error?: any }> {
  try {
    // In a real implementation, this would be a server-side function
    // that uses Supabase admin API to update the user's email_confirmed_at
    
    // For demo purposes, we'll just try to sign in the user
    // and return success if it works
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password: 'placeholder-password', // This will fail, but that's expected
    });
    
    if (error) {
      // If we get an "Invalid login credentials" error, it means the email exists
      // but the password is wrong, which is what we expect
      if (error.message.includes('Invalid login credentials')) {
        console.log(`Email ${email} exists and can be manually verified`);
        return { success: true };
      }
      
      // If we get an "Email not confirmed" error, we need to handle it differently
      if (error.message.includes('Email not confirmed')) {
        console.log(`Email ${email} exists but is not verified`);
        // In a real implementation, this is where you would use admin API
        // to update the user's email_confirmed_at field
        return { 
          success: false, 
          error: { message: 'Cannot manually verify email without admin privileges' } 
        };
      }
      
      return { success: false, error };
    }
    
    // If no error, the user was signed in (unlikely with a placeholder password)
    return { success: true };
  } catch (error) {
    console.error('Error marking email as verified:', error);
    return { success: false, error };
  }
}
