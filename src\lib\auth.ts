import { supabase } from './supabase';
import { notify } from '@/components/ui/notification-system';
import {
  mockSignIn,
  mockSignUp,
  mockSignOut,
  mockGetSession,
  mockGetUserProfile,
  mockGetUserCredits,
  mockGetCreditTransactions,
  mockUpdateUserProfile
} from './mockAuth';

// Flag to use mock auth functions instead of real Supabase
// Set this to false when you have set up the Supabase tables
const USE_MOCK_AUTH = false;

// Types
export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatarUrl?: string;
}

export interface UserProfile {
  id: string;
  userId: string;
  company?: string;
  jobTitle?: string;
  bio?: string;
  website?: string;
  phone?: string;
  country?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserCredits {
  totalCredits: number;
  usedCredits: number;
  availableCredits: number;
}

export interface CreditTransaction {
  id: string;
  userId: string;
  amount: number;
  description: string;
  transactionType: string; // 'purchase', 'usage', 'refund', 'bonus'
  createdAt: string;
}

export interface AuthSession {
  user: AuthUser | null;
  session: any;
  emailVerificationNeeded?: boolean;
  unverifiedEmail?: string;
}

// Define a common result type for auth functions
type AuthResult = {
  success: boolean;
  data?: any;
  error?: any;
  message?: string;
};

// Sign up with email and password
export async function signUp(email: string, password: string, name?: string): Promise<AuthResult & { data?: any; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock signup');
      return await mockSignUp(email, password, name);
    }

    console.log('Using real Supabase signup with:', { email, hasPassword: !!password, name });

    // Add email verification redirect URL
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
        },
        emailRedirectTo: `${window.location.origin}/verify-email-confirm`,
      },
    });

    // Log the redirect URL for debugging
    console.log('Email redirect URL:', `${window.location.origin}/verify-email-confirm`);

    if (error) {
      console.error('Supabase signup error:', error);
      throw error;
    }

    console.log('Supabase signup successful:', data);

    // Check if email confirmation is required
    if (data?.user && !data.session) {
      return {
        success: true,
        data,
        message: 'Please check your email to confirm your account.'
      };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error signing up:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Signup Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Sign in with email and password
export async function signIn(email: string, password: string): Promise<AuthResult & { data?: any; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock signin');
      return await mockSignIn(email, password);
    }

    console.log('Using real Supabase signin with:', { email, hasPassword: !!password });

    // First try to sign in with password
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // If successful, return the data
      if (!error) {
        console.log('Supabase password signin successful:', data);

        // Check if email is confirmed
        if (data.user && !data.user.email_confirmed_at) {
          console.warn('User email is not verified:', data.user.email);
          return {
            success: false,
            error: { message: 'Email not confirmed. Please check your inbox for a verification link.' },
            data: { user: data.user, session: data.session }
          };
        }

        return { success: true, data };
      }

      // If the error is not about invalid credentials, throw it
      if (!error.message.includes('Invalid login credentials')) {
        console.error('Supabase signin error:', error);
        throw error;
      }

      console.warn('Password login failed, trying alternative methods...');
    } catch (passwordError) {
      console.error('Error during password login:', passwordError);
      // Continue to try other methods
    }

    // If password login failed, we'll check if the user exists without sending a magic link
    // We'll use the admin API to check if the user exists
    try {
      // Instead of sending a magic link, we'll just return a generic error message
      // This avoids sending unwanted emails during password login attempts
      console.log('Password login failed, returning generic error without sending magic link');

      // Return a generic error message that doesn't reveal if the user exists
      return {
        success: false,
        error: { message: 'Invalid login credentials. Please check your email and password.' }
      };

      // Note: We've removed the code that was sending magic links during password login attempts
    } catch (otpError) {
      console.error('Error during OTP check:', otpError);
    }

    // If we get here, the user doesn't exist or there's another issue
    return {
      success: false,
      error: { message: 'Invalid login credentials. Please check your email and password.' }
    };
  } catch (error) {
    console.error('Error signing in:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Login Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Sign in with magic link
export async function signInWithMagicLink(email: string): Promise<AuthResult & { data?: any; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock magic link signin');
      return await mockSignIn(email, 'magic-link');
    }

    console.log('Using real Supabase magic link signin with:', { email });

    // Get the current origin with protocol (http/https)
    const origin = window.location.origin;
    const redirectUrl = `${origin}/verify-email-confirm`;

    console.log('Magic link redirect URL:', redirectUrl);

    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: redirectUrl,
        shouldCreateUser: true // Create user if they don't exist
      }
    });

    if (error) {
      console.error('Supabase magic link signin error:', error);
      throw error;
    }

    console.log('Supabase magic link sent successfully');

    return {
      success: true,
      data,
      message: 'Magic link sent! Please check your email to complete the login process.'
    };
  } catch (error) {
    console.error('Error sending magic link:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Magic Link Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Sign out
export async function signOut(): Promise<AuthResult> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock signout');
      return await mockSignOut();
    }

    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Logout Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Get current session
export async function getSession(): Promise<AuthSession> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock getSession');
      return await mockGetSession();
    }

    console.log('Using real Supabase getSession');
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Supabase getSession error:', error);
      throw error;
    }

    if (data.session?.user) {
      console.log('Supabase session found:', {
        id: data.session.user.id,
        email: data.session.user.email,
        metadata: data.session.user.user_metadata,
        email_confirmed_at: data.session.user.email_confirmed_at
      });

      // Check if email is verified
      if (!data.session.user.email_confirmed_at) {
        console.warn('Session found but email is not verified:', data.session.user.email);
        return {
          user: null,
          session: data.session,
          emailVerificationNeeded: true,
          unverifiedEmail: data.session.user.email
        };
      }

      const user = {
        id: data.session.user.id,
        email: data.session.user.email || '',
        name: data.session.user.user_metadata?.name,
        avatarUrl: data.session.user.user_metadata?.avatar_url,
      };

      return { user, session: data.session };
    } else {
      console.log('No Supabase session found');
      return { user: null, session: null };
    }
  } catch (error) {
    console.error('Error getting session:', error);
    return { user: null, session: null };
  }
}

// Check if email is verified
export async function checkEmailVerification(email: string): Promise<{ verified: boolean; error?: any }> {
  try {
    // We'll only use the password method to check verification status
    // This avoids sending magic links when we're just checking verification status
    console.log('Checking email verification status for:', email);

    // Try to sign in with a dummy password
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password: 'dummy-password-for-verification-check-' + Math.random(),
    });

    if (error) {
      // Check the specific error message
      if (error.message.includes('Invalid login credentials') && !error.message.includes('Email not confirmed')) {
        // This likely means the email exists and is verified, but password is wrong
        console.log('Email verification check: Email is verified');
        return { verified: true };
      } else if (error.message.includes('Email not confirmed')) {
        // Email explicitly not verified
        console.log('Email verification check: Email is NOT verified');
        return { verified: false, error };
      } else {
        // Ambiguous case - we can't be sure
        console.log('Email verification check: Ambiguous result, assuming not verified');
        return { verified: false, error };
      }
    } else {
      // This shouldn't happen with a random password
      console.warn('Unexpected successful login with dummy password');
      return { verified: true };
    }
  } catch (error) {
    console.error('Error checking email verification:', error);
    return { verified: false, error };
  }
}

// Reset password
export async function resetPassword(email: string) {
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error resetting password:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Password Reset Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Get user profile
export async function getUserProfile(userId: string): Promise<{ success: boolean; data?: UserProfile; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock getUserProfile');
      return await mockGetUserProfile(userId);
    }

    console.log('Getting user profile for userId:', userId);

    // First check if the table exists
    const { error: tableError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);

    if (tableError) {
      console.error('Error checking if user_profiles table exists:', tableError);
      // Return a default profile if table doesn't exist
      return {
        success: false,
        error: tableError,
        data: {
          id: userId,
          userId: userId,
          createdAt: new Date().toISOString()
        }
      };
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      // Return a default profile if user profile doesn't exist
      return {
        success: false,
        error,
        data: {
          id: userId,
          userId: userId,
          createdAt: new Date().toISOString()
        }
      };
    }

    console.log('User profile data:', data);
    return {
      success: true,
      data: {
        id: data.id,
        userId: data.user_id,
        company: data.company,
        jobTitle: data.job_title,
        bio: data.bio,
        website: data.website,
        phone: data.phone,
        country: data.country,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      }
    };
  } catch (error) {
    console.error('Error getting user profile:', error);
    // Return a default profile in case of error
    return {
      success: false,
      error,
      data: {
        id: userId,
        userId: userId,
        createdAt: new Date().toISOString()
      }
    };
  }
}

// Update user profile
export async function updateUserProfile(
  userId: string,
  profileData: Partial<UserProfile>
): Promise<{ success: boolean; data?: any; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock updateUserProfile');
      return await mockUpdateUserProfile(userId, profileData);
    }

    // Convert camelCase to snake_case for database
    const dbProfileData: Record<string, any> = {
      company: profileData.company,
      job_title: profileData.jobTitle,
      bio: profileData.bio,
      website: profileData.website,
      phone: profileData.phone,
      country: profileData.country,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('user_profiles')
      .update(dbProfileData)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error updating user profile:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Profile Update Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Get user credits
export async function getUserCredits(userId: string): Promise<{ success: boolean; data?: UserCredits; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock getUserCredits');
      return await mockGetUserCredits(userId);
    }

    console.log('Getting user credits for userId:', userId);

    // First check if the table exists
    const { error: tableError } = await supabase
      .from('user_credits')
      .select('count')
      .limit(1);

    if (tableError) {
      console.error('Error checking if user_credits table exists:', tableError);
      // Return default credits if table doesn't exist
      return {
        success: false,
        error: tableError,
        data: {
          totalCredits: 500,
          usedCredits: 0,
          availableCredits: 500
        }
      };
    }

    const { data, error } = await supabase
      .from('user_credits')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching user credits:', error);
      // Return default credits if user credits don't exist
      return {
        success: false,
        error,
        data: {
          totalCredits: 500,
          usedCredits: 0,
          availableCredits: 500
        }
      };
    }

    console.log('User credits data:', data);
    return {
      success: true,
      data: {
        totalCredits: data.total_credits,
        usedCredits: data.used_credits,
        availableCredits: data.total_credits - data.used_credits
      }
    };
  } catch (error) {
    console.error('Error getting user credits:', error);
    // Return default credits in case of error
    return {
      success: false,
      error,
      data: {
        totalCredits: 500,
        usedCredits: 0,
        availableCredits: 500
      }
    };
  }
}

// Get credit transactions
export async function getCreditTransactions(
  userId: string,
  limit: number = 10
): Promise<{ success: boolean; data?: CreditTransaction[]; error?: any }> {
  try {
    if (USE_MOCK_AUTH) {
      console.log('Using mock getCreditTransactions');
      return await mockGetCreditTransactions(userId, limit);
    }

    console.log('Getting credit transactions for userId:', userId);

    // First check if the table exists
    const { error: tableError } = await supabase
      .from('credit_transactions')
      .select('count')
      .limit(1);

    if (tableError) {
      console.error('Error checking if credit_transactions table exists:', tableError);
      // Return empty array if table doesn't exist
      return { success: false, error: tableError, data: [] };
    }

    const { data, error } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching credit transactions:', error);
      return { success: false, error, data: [] };
    }

    if (!data || data.length === 0) {
      console.log('No transactions found for user');
      return { success: true, data: [] };
    }

    console.log('Credit transactions data:', data);
    const transactions: CreditTransaction[] = data.map(transaction => ({
      id: transaction.id,
      userId: transaction.user_id,
      amount: transaction.amount,
      description: transaction.description,
      transactionType: transaction.transaction_type,
      createdAt: transaction.created_at
    }));

    return { success: true, data: transactions };
  } catch (error) {
    console.error('Error getting credit transactions:', error);
    return { success: false, error, data: [] };
  }
}
