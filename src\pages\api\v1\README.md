# Firenest API Documentation

This document provides an overview of the Firenest API, which allows partners to integrate with Firenest for authentication, usage tracking, and session management.

## API Endpoints

### Authentication

#### Authorization Endpoint

```
GET /api/v1/auth/authorize
```

Initiates the OAuth 2.0 authorization flow. This endpoint validates the request and redirects the user to the authorization page.

**Query Parameters:**
- `response_type` (required): Must be `code`
- `client_id` (required): The client ID of the partner
- `redirect_uri` (required): The URI to redirect to after authorization
- `state` (optional): A random string to prevent CSRF attacks
- `scope` (optional): The scope of the authorization request

**Example:**
```
GET /api/v1/auth/authorize?response_type=code&client_id=your-client-id&redirect_uri=https://your-app.com/callback&state=random-state
```

#### Token Exchange Endpoint

```
POST /api/v1/auth/token
```

Exchanges an authorization code for an access token. This endpoint implements the OAuth 2.0 authorization code flow.

**Request Body:**
```json
{
  "grant_type": "authorization_code",
  "code": "authorization-code",
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "redirect_uri": "https://your-app.com/callback"
}
```

**Response:**
```json
{
  "success": true,
  "access_token": "access-token",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "refresh-token",
  "user_id": "user-id",
  "session_id": "session-id",
  "user_info": {
    "id": "user-id",
    "email": "<EMAIL>",
    "display_name": "User Name"
  }
}
```

### Usage Tracking

#### Track Usage Endpoint

```
POST /api/v1/usage/track
```

Allows partners to report usage of their tools by Firenest users. It handles tracking different types of usage metrics and deducts credits accordingly.

**Request Headers:**
- `Authorization`: Bearer token obtained from the token exchange endpoint

**Request Body:**
```json
{
  "userId": "user-id",
  "sessionId": "session-id",
  "action": "chat_message",
  "quantity": 1,
  "timestamp": "2023-05-05T10:00:00Z",
  "details": {
    "toolName": "YourTool",
    "actionType": "resource_consumption",
    "resourceType": "message"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "eventId": "event-id",
    "sessionId": "session-id",
    "creditsDeducted": 1,
    "timestamp": "2023-05-05T10:00:01Z"
  }
}
```

### Session Management

#### Create Session Endpoint

```
POST /api/v1/session
```

Creates a new session for a user.

**Request Headers:**
- `Authorization`: Bearer token obtained from the token exchange endpoint

**Request Body:**
```json
{
  "metadata": {
    "toolName": "YourTool",
    "toolVersion": "1.0.0",
    "userAgent": "Mozilla/5.0 ..."
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session-id",
    "startTime": "2023-05-05T10:00:00Z",
    "userId": "user-id",
    "partnerId": "partner-id",
    "creditBalance": 100
  }
}
```

#### Get Session Endpoint

```
GET /api/v1/session?sessionId=session-id
```

Retrieves information about a session.

**Request Headers:**
- `Authorization`: Bearer token obtained from the token exchange endpoint

**Query Parameters:**
- `sessionId` (required): The ID of the session to retrieve

**Response:**
```json
{
  "success": true,
  "data": {
    "session": {
      "id": "session-id",
      "startTime": "2023-05-05T10:00:00Z",
      "endTime": null,
      "status": "active",
      "metrics": {},
      "estimatedCredits": 0,
      "actualCreditsUsed": null
    },
    "events": [
      {
        "id": "event-id",
        "event_type": "session_start",
        "event_data": {
          "action": "session_start",
          "quantity": 1,
          "timestamp": "2023-05-05T10:00:00Z",
          "details": {}
        },
        "credits_used": 0,
        "created_at": "2023-05-05T10:00:00Z"
      }
    ],
    "user": {
      "id": "user-id",
      "creditBalance": 100
    }
  }
}
```

#### Update Session Endpoint

```
PUT /api/v1/session?sessionId=session-id
```

Updates an existing session.

**Request Headers:**
- `Authorization`: Bearer token obtained from the token exchange endpoint

**Query Parameters:**
- `sessionId` (required): The ID of the session to update

**Request Body:**
```json
{
  "status": "completed",
  "metrics": {
    "totalMessages": 10,
    "totalTokens": 1000
  },
  "endTime": "2023-05-05T11:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session-id",
    "status": "completed",
    "metrics": {
      "totalMessages": 10,
      "totalTokens": 1000
    },
    "endTime": "2023-05-05T11:00:00Z",
    "actualCreditsUsed": 10
  }
}
```

## Usage Event Types

The following event types are supported for usage tracking:

- `session_start`: Indicates the start of a session
- `session_end`: Indicates the end of a session
- `session_heartbeat`: Indicates that the session is still active
- `api_call`: Indicates an API call
- `resource_consumption`: Indicates consumption of a resource
- `chat_message`: Indicates a chat message
- `custom`: Indicates a custom event type

## Error Handling

All API endpoints return a consistent error format:

```json
{
  "success": false,
  "error": "error_type",
  "message": "Error message"
}
```

The following error types are supported:

- `unauthorized`: The request is not authorized
- `invalid_request`: The request is invalid
- `not_found`: The requested resource was not found
- `server_error`: An internal server error occurred
- `rate_limited`: The request was rate limited
- `insufficient_credits`: The user does not have sufficient credits

## Security Considerations

- All API endpoints require authentication
- Access tokens expire after 1 hour
- Refresh tokens can be used to obtain new access tokens
- All API requests are logged for security and debugging purposes
- Row Level Security is implemented in the database to ensure data isolation

## Integration Steps

1. Register your application with Firenest to obtain client credentials
2. Implement the OAuth 2.0 authorization code flow
3. Use the access token to make API requests
4. Track usage using the usage tracking endpoint
5. Manage sessions using the session management endpoints

## Best Practices

- Always validate user input
- Handle errors gracefully
- Implement proper error logging
- Use HTTPS for all API requests
- Store client secrets securely
- Implement proper token management
- Implement proper session management
- Implement proper error handling
- Implement proper logging
