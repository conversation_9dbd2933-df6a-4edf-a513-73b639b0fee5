import { supabase } from './supabase';
import { notify } from '@/components/ui/notification-system';
import { authBridge } from './auth-bridge/auth-bridge';
import { serviceConfigs } from './auth-bridge/service-configs';
import { integrations } from './integration-framework/integrations';
import { AuthMethod } from './auth-bridge/types';

// Types
export interface UserConnection {
  id: string;
  userId: string;
  toolId: string;
  authMethod: AuthMethod;
  status: ConnectionStatus;
  connectionData?: any;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export type ConnectionStatus = 'connected' | 'disconnected' | 'expired' | 'pending' | 'failed';

export interface ConnectionLog {
  id: string;
  userId: string;
  toolId: string;
  eventType: string;
  eventData?: any;
  createdAt: string;
}

// Get all connections for a user
export async function getUserConnections(userId: string): Promise<UserConnection[]> {
  try {
    const { data, error } = await supabase
      .from('user_connections')
      .select('*')
      .eq('user_id', userId);

    if (error) throw error;

    return data.map(connection => ({
      id: connection.id,
      userId: connection.user_id,
      toolId: connection.tool_id,
      authMethod: connection.auth_method as AuthMethod,
      status: connection.status as ConnectionStatus,
      connectionData: connection.connection_data,
      lastUsed: connection.last_used,
      createdAt: connection.created_at,
      updatedAt: connection.updated_at
    }));
  } catch (error) {
    console.error('Error getting user connections:', error);
    notify.error('Failed to load your connections');
    return [];
  }
}

// Get a specific connection
export async function getUserConnection(userId: string, toolId: string): Promise<UserConnection | null> {
  try {
    const { data, error } = await supabase
      .from('user_connections')
      .select('*')
      .eq('user_id', userId)
      .eq('tool_id', toolId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No connection found
        return null;
      }
      throw error;
    }

    return {
      id: data.id,
      userId: data.user_id,
      toolId: data.tool_id,
      authMethod: data.auth_method as AuthMethod,
      status: data.status as ConnectionStatus,
      connectionData: data.connection_data,
      lastUsed: data.last_used,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  } catch (error) {
    console.error('Error getting user connection:', error);
    notify.error('Failed to load connection details');
    return null;
  }
}

// Create or update a connection
export async function saveUserConnection(
  userId: string,
  toolId: string,
  authMethod: AuthMethod,
  status: ConnectionStatus,
  connectionData?: any
): Promise<UserConnection | null> {
  try {
    // Check if connection already exists
    const existingConnection = await getUserConnection(userId, toolId);

    // Log the connection event
    await logConnectionEvent(userId, toolId, existingConnection ? 'update' : 'create', {
      authMethod,
      status,
      previousStatus: existingConnection?.status
    });

    // Upsert the connection
    const { data, error } = await supabase
      .from('user_connections')
      .upsert({
        user_id: userId,
        tool_id: toolId,
        auth_method: authMethod,
        status,
        connection_data: connectionData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      userId: data.user_id,
      toolId: data.tool_id,
      authMethod: data.auth_method as AuthMethod,
      status: data.status as ConnectionStatus,
      connectionData: data.connection_data,
      lastUsed: data.last_used,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  } catch (error) {
    console.error('Error saving user connection:', error);
    notify.error('Failed to save connection');
    return null;
  }
}

// Update connection status
export async function updateConnectionStatus(
  userId: string,
  toolId: string,
  status: ConnectionStatus
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('user_connections')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('tool_id', toolId);

    if (error) throw error;

    // Log the status update
    await logConnectionEvent(userId, toolId, 'status_update', { status });

    return true;
  } catch (error) {
    console.error('Error updating connection status:', error);
    notify.error('Failed to update connection status');
    return false;
  }
}

// Delete a connection
export async function deleteUserConnection(userId: string, toolId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('user_connections')
      .delete()
      .eq('user_id', userId)
      .eq('tool_id', toolId);

    if (error) throw error;

    // Log the deletion
    await logConnectionEvent(userId, toolId, 'delete');

    return true;
  } catch (error) {
    console.error('Error deleting user connection:', error);
    notify.error('Failed to delete connection');
    return false;
  }
}

// Update last used timestamp
export async function updateConnectionLastUsed(userId: string, toolId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('user_connections')
      .update({
        last_used: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('tool_id', toolId);

    if (error) throw error;

    return true;
  } catch (error) {
    console.error('Error updating last used timestamp:', error);
    // Don't show notification for this non-critical operation
    return false;
  }
}

// Log connection events
export async function logConnectionEvent(
  userId: string,
  toolId: string,
  eventType: string,
  eventData?: any
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('connection_logs')
      .insert({
        user_id: userId,
        tool_id: toolId,
        event_type: eventType,
        event_data: eventData
      });

    if (error) throw error;

    return true;
  } catch (error) {
    console.error('Error logging connection event:', error);
    // Don't show notification for this background operation
    return false;
  }
}

// Get connection logs for a user
export async function getConnectionLogs(userId: string, toolId?: string): Promise<ConnectionLog[]> {
  try {
    let query = supabase
      .from('connection_logs')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (toolId) {
      query = query.eq('tool_id', toolId);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data.map(log => ({
      id: log.id,
      userId: log.user_id,
      toolId: log.tool_id,
      eventType: log.event_type,
      eventData: log.event_data,
      createdAt: log.created_at
    }));
  } catch (error) {
    console.error('Error getting connection logs:', error);
    notify.error('Failed to load connection history');
    return [];
  }
}

// Initialize a new connection
export async function initializeConnection(userId: string, toolId: string): Promise<{
  success: boolean;
  redirectUrl?: string;
  error?: string;
}> {
  try {
    // Get the tool configuration
    const toolConfig = serviceConfigs.find(config => config.id === toolId);

    if (!toolConfig) {
      throw new Error(`Tool configuration not found for ${toolId}`);
    }

    // Initialize the connection in pending state
    await saveUserConnection(userId, toolId, toolConfig.authMethod, 'pending');

    // Initialize authentication
    const result = await authBridge.initializeAuth(toolId, userId);

    if (!result.success) {
      // Update connection status to failed
      await updateConnectionStatus(userId, toolId, 'failed');

      return {
        success: false,
        error: result.error?.message || 'Failed to initialize connection'
      };
    }

    // For OAuth, return the redirect URL
    if (result.redirectUrl) {
      return {
        success: true,
        redirectUrl: result.redirectUrl
      };
    }

    // For other auth methods, update the connection status
    await updateConnectionStatus(userId, toolId, 'connected');

    return {
      success: true
    };
  } catch (error) {
    console.error('Error initializing connection:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

    // Update connection status to failed
    await updateConnectionStatus(userId, toolId, 'failed');

    return {
      success: false,
      error: errorMessage
    };
  }
}

// Complete OAuth connection
export async function completeOAuthConnection(
  userId: string,
  toolId: string,
  code: string,
  state: string
): Promise<boolean> {
  try {
    // Complete the OAuth flow
    const result = await authBridge.completeAuth(toolId, userId, { code, state });

    if (!result.success) {
      // Update connection status to failed
      await updateConnectionStatus(userId, toolId, 'failed');

      notify.error(result.error?.message || 'Failed to complete authentication');
      return false;
    }

    // Update connection status to connected
    await saveUserConnection(
      userId,
      toolId,
      'oauth',
      'connected',
      {
        tokenInfo: {
          hasToken: true,
          expiresAt: result.session?.authData?.expiresAt
        }
      }
    );

    notify.success(`Successfully connected to ${getToolName(toolId)}`);
    return true;
  } catch (error) {
    console.error('Error completing OAuth connection:', error);

    // Update connection status to failed
    await updateConnectionStatus(userId, toolId, 'failed');

    notify.error('Failed to complete authentication');
    return false;
  }
}

// Save API key connection
export async function saveApiKeyConnection(
  userId: string,
  toolId: string,
  apiKey: string
): Promise<boolean> {
  try {
    // Validate the API key (in a real app, you would verify with the service)
    if (!apiKey || apiKey.trim().length < 10) {
      notify.error('Please enter a valid API key');
      return false;
    }

    // Complete the API key authentication
    const result = await authBridge.completeAuth(toolId, userId, { apiKey });

    if (!result.success) {
      // Update connection status to failed
      await updateConnectionStatus(userId, toolId, 'failed');

      notify.error(result.error?.message || 'Failed to validate API key');
      return false;
    }

    // Save the connection with masked API key
    const maskedKey = maskApiKey(apiKey);
    await saveUserConnection(
      userId,
      toolId,
      'api_key',
      'connected',
      {
        maskedApiKey: maskedKey
      }
    );

    notify.success(`Successfully connected to ${getToolName(toolId)}`);
    return true;
  } catch (error) {
    console.error('Error saving API key connection:', error);

    // Update connection status to failed
    await updateConnectionStatus(userId, toolId, 'failed');

    notify.error('Failed to save API key');
    return false;
  }
}

// Save credentials connection
export async function saveCredentialsConnection(
  userId: string,
  toolId: string,
  username: string,
  password: string
): Promise<boolean> {
  try {
    // Validate credentials
    if (!username || !password) {
      notify.error('Please enter both username and password');
      return false;
    }

    // Complete the credentials authentication
    const result = await authBridge.completeAuth(toolId, userId, { username, password });

    if (!result.success) {
      // Update connection status to failed
      await updateConnectionStatus(userId, toolId, 'failed');

      notify.error(result.error?.message || 'Failed to validate credentials');
      return false;
    }

    // Save the connection with masked password
    await saveUserConnection(
      userId,
      toolId,
      'credentials',
      'connected',
      {
        username,
        hasPassword: true
      }
    );

    notify.success(`Successfully connected to ${getToolName(toolId)}`);
    return true;
  } catch (error) {
    console.error('Error saving credentials connection:', error);

    // Update connection status to failed
    await updateConnectionStatus(userId, toolId, 'failed');

    notify.error('Failed to save credentials');
    return false;
  }
}

// Helper function to get tool name
export function getToolName(toolId: string): string {
  const integration = integrations.find(i => i.id === toolId);
  const serviceConfig = serviceConfigs.find(s => s.id === toolId);

  return integration?.name || serviceConfig?.name || toolId;
}

// Helper function to mask API key
function maskApiKey(apiKey: string): string {
  if (!apiKey || apiKey.length < 8) return '••••••••';

  const firstFour = apiKey.substring(0, 4);
  const lastFour = apiKey.substring(apiKey.length - 4);

  return `${firstFour}${'•'.repeat(apiKey.length - 8)}${lastFour}`;
}

// Track tool launch and usage
export async function trackToolLaunch(userId: string, toolId: string): Promise<boolean> {
  try {
    // 1. Record the launch event
    const { data: launchData, error: launchError } = await supabase
      .from('tool_launches')
      .insert({
        user_id: userId,
        tool_id: toolId,
        launched_at: new Date().toISOString(),
        status: 'active'
      })
      .select('id')
      .single();

    if (launchError) {
      console.error('Error recording tool launch:', launchError);
      return false;
    }

    const launchId = launchData?.id;

    // 2. Update the user connection's last used timestamp
    const { error: connectionError } = await supabase
      .from('user_connections')
      .update({
        last_used: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('tool_id', toolId);

    if (connectionError) {
      console.error('Error updating connection last used:', connectionError);
    }

    // 3. Start a usage session
    const { error: sessionError } = await supabase
      .from('usage_sessions')
      .insert({
        user_id: userId,
        tool_id: toolId,
        launch_id: launchId,
        start_time: new Date().toISOString(),
        status: 'active',
        estimated_credits: 0
      });

    if (sessionError) {
      console.error('Error starting usage session:', sessionError);
    }

    // 4. Deduct initial credits (if applicable)
    // This would typically be handled by a server-side function
    // For now, we'll just simulate it

    return true;
  } catch (error) {
    console.error('Error tracking tool launch:', error);
    return false;
  }
}

// Get tools with connection status
export async function getToolsWithConnectionStatus(userId: string): Promise<any[]> {
  try {
    // Get all tools from integrations
    const tools = integrations.map(integration => ({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      longDescription: integration.longDescription,
      category: integration.category,
      tags: integration.tags,
      features: integration.features,
      pricing: integration.pricing,
      rating: integration.rating,
      reviewCount: integration.reviewCount,
      popular: integration.popular,
      new: integration.new,
      createdAt: integration.createdAt,
      updatedAt: integration.updatedAt,
      // Get auth method from service configs
      authMethod: serviceConfigs.find(config => config.id === integration.id)?.authMethod || 'oauth',
      // Default connection status
      isConnected: false,
      connection: null
    }));

    // Get user connections
    const connections = await getUserConnections(userId);

    // Update tools with connection status
    return tools.map(tool => {
      const connection = connections.find(conn => conn.toolId === tool.id);

      if (connection) {
        return {
          ...tool,
          isConnected: connection.status === 'connected',
          connection: {
            id: connection.id,
            status: connection.status,
            authMethod: connection.authMethod,
            lastUsed: connection.lastUsed,
            createdAt: connection.createdAt
          }
        };
      }

      return tool;
    });
  } catch (error) {
    console.error('Error getting tools with connection status:', error);
    notify.error('Failed to load tools with connection status');
    return [];
  }
}


