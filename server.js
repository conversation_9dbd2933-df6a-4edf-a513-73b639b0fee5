import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.API_PORT || 3333;

// Get directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Middleware
// Configure CORS to allow credentials and specific origins
app.use(cors({
  origin: ['http://localhost:3001', 'http://localhost:3002', 'http://localhost:3000', 'http://localhost:3333'], // Include all possible ports
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: true // Allow credentials (cookies, authorization headers)
}));

// Add CORS preflight options for all routes
app.options('*', cors({
  origin: ['http://localhost:3001', 'http://localhost:3002', 'http://localhost:3000', 'http://localhost:3333'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Origin:', req.headers.origin);
  console.log('Headers:', req.headers);
  if (req.method === 'POST' || req.method === 'PUT') {
    console.log('Body:', req.body);
  }
  next();
});

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://wjwguxccykrbarehqgpq.supabase.co';
// Load the service key for admin operations
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indqd2d1eGNjeWtyYmFyZWhxZ3BxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDAyOTQ1MywiZXhwIjoyMDU5NjA1NDUzfQ.6P_W7tcaTpt3nCXv-AnuQrbwKD_IMvn0zIrsSPgq4hw';

// Check if required environment variables are set
if (!supabaseUrl) {
  console.error('FATAL: VITE_SUPABASE_URL environment variable is not set');
  process.exit(1);
}

if (!supabaseServiceKey) {
  console.error('FATAL: SUPABASE_SERVICE_KEY environment variable is not set');
  process.exit(1);
}

// Create Supabase client with service key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Status codes
const StatusCodes = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
};

// Error types
const ErrorType = {
  UNAUTHORIZED: 'unauthorized',
  INVALID_REQUEST: 'invalid_request',
  NOT_FOUND: 'not_found',
  SERVER_ERROR: 'server_error',
  RATE_LIMITED: 'rate_limited',
  INSUFFICIENT_CREDITS: 'insufficient_credits',
};

// Error messages
const ErrorMessages = {
  [ErrorType.UNAUTHORIZED]: 'Unauthorized access',
  [ErrorType.INVALID_REQUEST]: 'Invalid request parameters',
  [ErrorType.NOT_FOUND]: 'Resource not found',
  [ErrorType.SERVER_ERROR]: 'Internal server error',
  [ErrorType.RATE_LIMITED]: 'Rate limit exceeded',
  [ErrorType.INSUFFICIENT_CREDITS]: 'Insufficient credits',
};

// Helper function to hash client secret
async function hashClientSecret(clientSecret) {
  try {
    // Simple hash for demo purposes
    return clientSecret; // In production, use a proper hashing algorithm
  } catch (error) {
    console.error('Error hashing client secret:', error);
    return clientSecret;
  }
}

// Helper function to verify client secret
async function verifyClientSecret(clientSecret, hashedSecret) {
  try {
    console.log('Verifying client secret');
    // For now, we'll do a direct comparison
    // In production, you should use a proper hashing algorithm like bcrypt
    const isValid = clientSecret === hashedSecret;
    console.log('Client secret verification result:', isValid);
    return isValid;
  } catch (error) {
    console.error('Error verifying client secret:', error);
    return false;
  }
}

// Helper function to generate token
function generateToken(type) {
  const token = uuidv4();
  return type === 'access' ? `at_${token}` : `rt_${token}`;
}

// Helper function to log API request
async function logApiRequest(endpoint, method, partnerId, userId, requestData, responseStatus, responseData) {
  try {
    await supabase.from('api_logs').insert({
      endpoint,
      method,
      partner_id: partnerId,
      user_id: userId,
      request_data: requestData,
      response_status: responseStatus,
      response_data: responseData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error logging API request:', error);
  }
}

// API Routes

// Authorize endpoint - handles the authorization request and generates the authorization code
app.get('/authorize', async (req, res) => {
  try {
    // Extract the authorization request from the query parameters
    const requestParam = req.query.request;
    if (!requestParam) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing request parameter'
      });
    }

    // Parse the authorization request
    let authRequest;
    try {
      authRequest = JSON.parse(requestParam);
    } catch (parseError) {
      console.error('Error parsing authorization request:', parseError);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Invalid request parameter format'
      });
    }

    // Extract parameters from the authorization request
    const {
      client_id,
      redirect_uri,
      state,
      scope,
      partner_id,
      partner_name
    } = authRequest;

    console.log('Processing authorization request:', {
      client_id,
      redirect_uri,
      state,
      scope,
      partner_id,
      partner_name
    });

    // For testing purposes, use a fixed user ID
    // In a real application, this would be the authenticated user's ID
    const userId = '305e90b7-624b-45a7-afdb-4b47ee75d13e';

    // Generate an authorization code
    const code = `ac_${Date.now().toString(36)}_${uuidv4().replace(/-/g, '')}`;
    console.log('Generated authorization code:', code);

    // Store the authorization code in the database
    const { error: insertError } = await supabase
      .from('auth_codes')
      .insert({
        code,
        user_id: userId,
        client_id,
        partner_id,
        redirect_uri,
        scope: scope || 'read write',
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes expiration
        used: false
      });

    if (insertError) {
      console.error('Error storing authorization code:', insertError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error storing authorization code'
      });
    }

    console.log('Authorization code stored successfully');

    // Redirect to the redirect URI with the authorization code and state
    const redirectUrl = new URL(redirect_uri);
    redirectUrl.searchParams.append('code', code);
    redirectUrl.searchParams.append('state', state);

    console.log('Redirecting to:', redirectUrl.toString());

    // Log the API request
    await logApiRequest(
      '/authorize',
      'GET',
      partner_id,
      userId,
      {
        client_id,
        redirect_uri,
        state,
        scope
      },
      StatusCodes.FOUND,
      { redirectTo: redirectUrl.toString() }
    );

    return res.redirect(redirectUrl.toString());
  } catch (error) {
    console.error('Error processing authorization request:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
});

// Root endpoint
app.get('/api', (req, res) => {
  res.status(StatusCodes.OK).json({
    name: 'Firenest API',
    version: 'v1',
    description: 'API for Firenest platform',
    endpoints: {
      auth: '/api/v1/auth',
      usage: '/api/v1/usage',
      session: '/api/v1/session'
    }
  });
});

// v1 endpoint
app.get('/api/v1', (req, res) => {
  res.status(StatusCodes.OK).json({
    name: 'Firenest API',
    version: 'v1',
    description: 'API for Firenest platform',
    endpoints: {
      auth: '/api/v1/auth',
      usage: '/api/v1/usage',
      session: '/api/v1/session'
    }
  });
});

// Auth endpoint
app.get('/api/v1/auth', (req, res) => {
  // Redirect to the authorize endpoint
  const queryParams = new URLSearchParams(req.query).toString();
  res.redirect(`/api/v1/auth/authorize?${queryParams}`);
});

app.post('/api/v1/auth', (req, res) => {
  // Redirect to the token endpoint
  res.redirect(307, '/api/v1/auth/token');
});

// Add OPTIONS handler for the token endpoint
app.options('/api/v1/auth/token', (req, res) => {
  const allowedOrigins = ['http://localhost:3001', 'http://localhost:3002', 'http://localhost:3000', 'http://localhost:3333'];
  const origin = req.headers.origin;

  // Check if the request origin is in our allowed origins list
  if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    // Default to the test partner origin if not found
    res.header('Access-Control-Allow-Origin', 'http://localhost:3002');
  }

  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.status(200).end();
});

// Authorization endpoint
app.get('/api/v1/auth/authorize', async (req, res) => {
  try {
    // Extract parameters from query string
    const {
      response_type,
      client_id,
      redirect_uri,
      state,
      scope
    } = req.query;

    // Add more detailed logging
    console.log('Received authorization request:', {
      response_type,
      client_id,
      redirect_uri,
      state,
      scope
    });

    // Validate required parameters
    if (!response_type || !client_id || !redirect_uri) {
      console.error('Missing required parameters:', {
        hasResponseType: !!response_type,
        hasClientId: !!client_id,
        hasRedirectUri: !!redirect_uri
      });
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing required parameters'
      });
    }

    // Only support code response type
    if (response_type !== 'code') {
      console.error('Unsupported response type:', response_type);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Unsupported response type'
      });
    }

    // Verify client ID
    console.log('Verifying client ID:', client_id);

    let partnerData;

    try {
      // Get from partner_accounts table
      const { data: accountData, error: accountError } = await supabase
        .from('partner_accounts')
        .select('id, name, redirect_uris')
        .eq('client_id', client_id)
        .maybeSingle();

      if (accountError) {
        console.error('Error querying partner_accounts table by client_id:', accountError);

        // Try with api_key as fallback
        const { data: apiKeyData, error: apiKeyError } = await supabase
          .from('partner_accounts')
          .select('id, name, redirect_uris')
          .eq('api_key', client_id)
          .maybeSingle();

        if (apiKeyError) {
          console.error('Error querying partner_accounts table by api_key:', apiKeyError);
          // Continue with the mock partner creation below
        } else if (apiKeyData) {
          console.log('Partner found in partner_accounts by api_key:', apiKeyData);
          partnerData = apiKeyData;
        }
      } else if (accountData) {
        console.log('Partner found in partner_accounts by client_id:', accountData);
        partnerData = accountData;
      }
    } catch (error) {
      console.error('Error verifying client ID:', error);
      // Continue with the mock partner creation below
    }

    if (!partnerData) {
      console.error('Partner not found for client ID:', client_id);

      // Return an error response instead of creating a mock partner
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Invalid client ID - partner not found'
      });
    }

    console.log('Partner found:', {
      partnerId: partnerData.id,
      partnerName: partnerData.name,
      redirectUris: partnerData.redirect_uris
    });

    // Verify redirect URI
    const allowedRedirectUris = partnerData.redirect_uris || [];
    console.log('Verifying redirect URI:', {
      providedRedirectUri: redirect_uri,
      allowedRedirectUris
    });

    if (!allowedRedirectUris.includes(redirect_uri)) {
      console.error('Invalid redirect URI:', redirect_uri);
      console.error('Allowed redirect URIs:', allowedRedirectUris);

      // Check if this is the Atlas AI test client
      if (client_id === 'atlasai-test-partner') {
        console.log('Atlas AI test client detected, updating redirect URIs');

        const updatedRedirectUris = [...allowedRedirectUris, redirect_uri];

        const { error: updateError } = await supabase
          .from('partner_accounts')
          .update({
            redirect_uris: updatedRedirectUris,
            updated_at: new Date().toISOString()
          })
          .eq('id', partnerData.id);

        if (updateError) {
          console.error('Error updating partner redirect URIs:', updateError);
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            error: ErrorType.INVALID_REQUEST,
            message: 'Invalid redirect URI and failed to update partner'
          });
        }

        console.log('Partner updated with new redirect URI:', updatedRedirectUris);
      } else {
        // For non-test clients, reject invalid redirect URIs
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          error: ErrorType.INVALID_REQUEST,
          message: 'Invalid redirect URI'
        });
      }
    } else {
      console.log('Redirect URI verified successfully');
    }

    // Generate a unique state if not provided
    const authState = state || uuidv4();

    // Store the authorization request in the session
    const authRequest = {
      client_id: client_id,
      redirect_uri: redirect_uri,
      state: authState,
      scope: scope || 'read write',
      partner_id: partnerData.id,
      partner_name: partnerData.name
    };

    // Redirect to the Firenest-style authorization page
    // Make sure this path matches your client-side route
    const authUrl = `/authorize?${new URLSearchParams({
      request: JSON.stringify(authRequest)
    }).toString()}`;

    // Log the API request
    await logApiRequest(
      '/api/v1/auth/authorize',
      'GET',
      partnerData.id,
      null,
      {
        response_type,
        client_id,
        redirect_uri,
        state,
        scope
      },
      StatusCodes.FOUND,
      { redirectTo: authUrl }
    );

    return res.redirect(authUrl);
  } catch (error) {
    console.error('Error authorizing:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
});

// Token endpoint
app.post('/api/v1/auth/token', async (req, res) => {
  // Set CORS headers explicitly for this endpoint
  const allowedOrigins = ['http://localhost:3001', 'http://localhost:3002', 'http://localhost:3000', 'http://localhost:3333'];
  const origin = req.headers.origin;

  // Check if the request origin is in our allowed origins list
  if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    // Default to the test partner origin if not found
    res.header('Access-Control-Allow-Origin', 'http://localhost:3002');
  }

  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  try {
    // Extract parameters from request body
    const {
      grant_type,
      code,
      client_id,
      client_secret,
      redirect_uri
    } = req.body;

    // Add more detailed logging
    console.log('Received token exchange request:', {
      grant_type,
      client_id,
      redirect_uri,
      // Don't log code and client_secret for security
      hasCode: !!code,
      hasClientSecret: !!client_secret
    });

    // Validate required parameters
    if (!grant_type || !code || !client_id || !client_secret || !redirect_uri) {
      console.error('Missing required parameters:', {
        hasGrantType: !!grant_type,
        hasCode: !!code,
        hasClientId: !!client_id,
        hasClientSecret: !!client_secret,
        hasRedirectUri: !!redirect_uri
      });
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing required parameters'
      });
    }

    // Only support authorization_code grant type
    if (grant_type !== 'authorization_code') {
      console.error('Unsupported grant type:', grant_type);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Unsupported grant type'
      });
    }

    // Verify client credentials
    console.log('Verifying client credentials for client_id:', client_id);

    const { data: partnerData, error: partnerError } = await supabase
      .from('partner_accounts')
      .select('id, client_secret, redirect_uris')
      .eq('client_id', client_id)
      .maybeSingle();

    if (partnerError) {
      console.error('Error verifying client credentials:', partnerError);
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        error: ErrorType.UNAUTHORIZED,
        message: 'Error verifying client credentials'
      });
    }

    if (!partnerData) {
      console.error('Partner not found for client_id:', client_id);
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        error: ErrorType.UNAUTHORIZED,
        message: 'Invalid client credentials - partner not found'
      });
    }

    console.log('Partner data found:', {
      partnerId: partnerData.id,
      hasClientSecret: !!partnerData.client_secret,
      redirectUris: partnerData.redirect_uris
    });

    // Verify client secret
    const isSecretValid = await verifyClientSecret(client_secret, partnerData.client_secret);
    if (!isSecretValid) {
      console.error('Client secret mismatch');
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        error: ErrorType.UNAUTHORIZED,
        message: 'Invalid client credentials - client secret mismatch'
      });
    }

    console.log('Client secret verified successfully');

    // Verify redirect URI
    const allowedRedirectUris = partnerData.redirect_uris || [];
    console.log('Verifying redirect URI:', {
      providedRedirectUri: redirect_uri,
      allowedRedirectUris
    });

    if (!allowedRedirectUris.includes(redirect_uri)) {
      console.error('Invalid redirect URI:', redirect_uri);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Invalid redirect URI'
      });
    }

    console.log('Redirect URI verified successfully');

    // Verify authorization code
    console.log('Verifying authorization code');

    const { data: authCodeData, error: authCodeError } = await supabase
      .from('auth_codes')
      .select('user_id, partner_id, expires_at, used')
      .eq('code', code)
      .eq('partner_id', partnerData.id)
      .maybeSingle();

    if (authCodeError) {
      console.error('Error verifying authorization code:', authCodeError);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Error verifying authorization code'
      });
    }

    // If no auth code found, return an error (following OAuth 2.0 standards)
    if (!authCodeData) {
      console.error('Authorization code not found:', code);

      // Log detailed information for debugging
      console.log('Authorization code details:', {
        code: code.substring(0, 10) + '...',
        client_id,
        redirect_uri,
        partner_id: partnerData.id
      });

      // Return a standard OAuth 2.0 error response
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: 'invalid_grant',
        error_description: 'The authorization code is invalid or has expired'
      });
    }

    console.log('Authorization code found:', {
      userId: authCodeData.user_id,
      partnerId: authCodeData.partner_id,
      expiresAt: authCodeData.expires_at,
      used: authCodeData.used
    });

    // Check if code has expired
    const expiresAt = new Date(authCodeData.expires_at);
    if (expiresAt < new Date()) {
      console.error('Authorization code has expired');
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Authorization code has expired'
      });
    }

    // Check if code has already been used
    if (authCodeData.used) {
      console.error('Authorization code has already been used');
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Authorization code has already been used'
      });
    }

    console.log('Authorization code verified successfully');

    // Mark authorization code as used
    const { error: updateError } = await supabase
      .from('auth_codes')
      .update({ used: true })
      .eq('code', code);

    if (updateError) {
      console.error('Error marking authorization code as used:', updateError);
    }

    // Generate access token
    const accessToken = generateToken('access');
    const refreshToken = generateToken('refresh');
    const expiresIn = 3600; // 1 hour
    const tokenExpiresAt = new Date();
    tokenExpiresAt.setSeconds(tokenExpiresAt.getSeconds() + expiresIn);

    // Store access token in database
    console.log('Storing access token in database with data:', {
      user_id: authCodeData.user_id,
      partner_id: authCodeData.partner_id,
      client_id: client_id,
      expires_at: tokenExpiresAt.toISOString(),
      scope: 'read write'
    });

    try {
      const { data: tokenData, error: tokenError } = await supabase
        .from('access_tokens')
        .insert({
          access_token: accessToken,
          refresh_token: refreshToken,
          user_id: authCodeData.user_id,
          partner_id: authCodeData.partner_id,
          client_id: client_id,
          expires_at: tokenExpiresAt.toISOString(),
          scope: 'read write'
        })
        .select('id')
        .single();

      if (tokenError) {
        console.error('Error storing access token:', tokenError);

        // Try to get more information about the error
        if (tokenError.code === '23502') {
          console.error('Not null violation - missing required field');
          // Check which field is missing
          const missingFields = [];
          if (!accessToken) missingFields.push('access_token');
          if (!refreshToken) missingFields.push('refresh_token');
          if (!authCodeData.user_id) missingFields.push('user_id');
          if (!authCodeData.partner_id) missingFields.push('partner_id');
          if (!client_id) missingFields.push('client_id');
          if (!tokenExpiresAt) missingFields.push('expires_at');

          console.error('Missing fields:', missingFields);
        }

        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error generating access token: ' + tokenError.message
        });
      }

      console.log('Access token stored successfully with ID:', tokenData.id);
    } catch (insertError) {
      console.error('Exception storing access token:', insertError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Exception generating access token: ' + insertError.message
      });
    }

    // Create a new session
    const sessionId = uuidv4();
    const { error: sessionError } = await supabase
      .from('usage_sessions')
      .insert({
        id: sessionId,
        user_id: authCodeData.user_id,
        tool_id: authCodeData.partner_id,
        start_time: new Date().toISOString(),
        status: 'active',
        metrics: {},
        estimated_credits: 0
      });

    if (sessionError) {
      console.error('Error creating session:', sessionError);
    }

    // Get user information from Supabase Auth
    let userData = null;
    try {
      // Try to get user from auth.users table using RPC
      const { data, error } = await supabase.rpc('get_auth_user_by_id', {
        user_id_param: authCodeData.user_id
      });

      if (error) {
        console.error('Error fetching user with RPC:', error);

        // Fallback: Try direct query to auth schema
        try {
          const { data: authData, error: authError } = await supabase.auth.admin.getUserById(
            authCodeData.user_id
          );

          if (authError) {
            console.error('Error fetching user with admin API:', authError);
          } else if (authData?.user) {
            userData = {
              id: authData.user.id,
              email: authData.user.email,
              name: authData.user.user_metadata?.name || 'User',
              display_name: authData.user.user_metadata?.name || 'User'
            };
          }
        } catch (authApiError) {
          console.error('Exception in auth API call:', authApiError);
        }
      } else if (data) {
        userData = {
          id: data.id,
          email: data.email,
          name: data.raw_user_meta_data?.name || 'User',
          display_name: data.raw_user_meta_data?.name || 'User'
        };
      }
    } catch (userError) {
      console.error('Exception fetching user information:', userError);
    }

    // If we couldn't get user data, use the user ID we have
    if (!userData) {
      console.log('Using minimal user data from auth code');
      userData = {
        id: authCodeData.user_id,
        email: `user-${authCodeData.user_id.substring(0, 8)}@example.com`,
        name: `User ${authCodeData.user_id.substring(0, 8)}`,
        display_name: `User ${authCodeData.user_id.substring(0, 8)}`
      };
    }

    console.log('User data for token response:', userData);

    // Prepare response following OAuth 2.0 standards with additional fields for compatibility
    const response = {
      // Standard OAuth 2.0 fields
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: expiresIn,
      refresh_token: refreshToken,
      scope: 'read write',

      // Additional fields for Firenest client compatibility
      success: true,
      user_id: authCodeData.user_id,
      userId: authCodeData.user_id,
      session_id: sessionId,
      sessionId: sessionId,
      token: accessToken,

      // User information
      user_info: userData
    };

    // Log the API request (mask sensitive data)
    await logApiRequest(
      '/api/v1/auth/token',
      'POST',
      partnerData.id,
      authCodeData.user_id,
      {
        grant_type,
        client_id,
        redirect_uri,
        // Don't log code and client_secret
      },
      StatusCodes.OK,
      {
        success: true,
        token_type: 'Bearer',
        expires_in: expiresIn,
        // Don't log tokens
      }
    );

    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error exchanging token:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
});

// Helper function to record a usage event
async function recordUsageEvent(userId, sessionId, toolId, eventType, eventData, creditsUsed) {
  try {
    const eventId = uuidv4();
    const { error } = await supabase
      .from('usage_events')
      .insert({
        id: eventId,
        session_id: sessionId,
        user_id: userId,
        tool_id: toolId,
        event_type: eventType,
        event_data: eventData,
        credits_used: creditsUsed,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error recording usage event:', error);
      throw error;
    }

    return eventId;
  } catch (error) {
    console.error('Exception recording usage event:', error);
    throw error;
  }
}

// Helper function to update session metrics
async function updateSessionMetrics(sessionId, userId, toolId, metrics, creditsUsed) {
  try {
    // First, check if the session exists
    const { data: sessionData, error: sessionError } = await supabase
      .from('usage_sessions')
      .select('id, estimated_credits, metrics')
      .eq('id', sessionId)
      .maybeSingle();

    if (sessionError) {
      console.error('Error fetching session:', sessionError);
      throw sessionError;
    }

    if (!sessionData) {
      // Session doesn't exist, create it
      const { error: createError } = await supabase
        .from('usage_sessions')
        .insert({
          id: sessionId,
          user_id: userId,
          tool_id: toolId,
          start_time: new Date().toISOString(),
          status: 'active',
          metrics: metrics,
          estimated_credits: creditsUsed
        });

      if (createError) {
        console.error('Error creating session:', createError);
        throw createError;
      }
    } else {
      // Session exists, update it
      const updatedMetrics = {
        ...sessionData.metrics,
        ...metrics,
        last_activity: new Date().toISOString()
      };

      const updatedCredits = (sessionData.estimated_credits || 0) + creditsUsed;

      const { error: updateError } = await supabase
        .from('usage_sessions')
        .update({
          metrics: updatedMetrics,
          estimated_credits: updatedCredits,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (updateError) {
        console.error('Error updating session metrics:', updateError);
        throw updateError;
      }
    }

    return true;
  } catch (error) {
    console.error('Exception updating session metrics:', error);
    throw error;
  }
}

// Helper function to deduct credits from user account
async function deductCredits(userId, creditsToDeduct, description, toolId) {
  try {
    if (creditsToDeduct <= 0) {
      return true; // No credits to deduct
    }

    // First, check if the user has enough credits
    const { data: userCredits, error: creditsError } = await supabase
      .from('user_credits')
      .select('id, total_credits, used_credits')
      .eq('user_id', userId)
      .maybeSingle();

    if (creditsError) {
      console.error('Error fetching user credits:', creditsError);
      throw creditsError;
    }

    if (!userCredits) {
      // User doesn't have a credits record, create one with default values
      const { error: createError } = await supabase
        .from('user_credits')
        .insert({
          user_id: userId,
          total_credits: 500, // Default starting credits
          used_credits: creditsToDeduct,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (createError) {
        console.error('Error creating user credits:', createError);
        throw createError;
      }
    } else {
      // User has a credits record, update it
      const availableCredits = userCredits.total_credits - userCredits.used_credits;

      if (availableCredits < creditsToDeduct) {
        console.warn(`User ${userId} doesn't have enough credits. Available: ${availableCredits}, Requested: ${creditsToDeduct}`);
        // For demo purposes, we'll still allow the transaction but log a warning
      }

      const { error: updateError } = await supabase
        .from('user_credits')
        .update({
          used_credits: userCredits.used_credits + creditsToDeduct,
          updated_at: new Date().toISOString()
        })
        .eq('id', userCredits.id);

      if (updateError) {
        console.error('Error updating user credits:', updateError);
        throw updateError;
      }
    }

    // Record the credit transaction
    const { error: transactionError } = await supabase
      .from('credit_transactions')
      .insert({
        id: uuidv4(),
        user_id: userId,
        amount: -creditsToDeduct, // Negative amount for deduction
        transaction_type: 'usage',
        description: description,
        service_id: toolId,
        created_at: new Date().toISOString()
      });

    if (transactionError) {
      console.error('Error recording credit transaction:', transactionError);
      throw transactionError;
    }

    return true;
  } catch (error) {
    console.error('Exception deducting credits:', error);
    throw error;
  }
}

// Usage tracking endpoint
app.post('/api/v1/usage/track', async (req, res) => {
  try {
    // Extract the authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        error: ErrorType.UNAUTHORIZED,
        message: 'Missing or invalid authorization header'
      });
    }

    // Extract the token
    const token = authHeader.split(' ')[1];

    // In a real implementation, we would verify the token
    // For this test implementation, we'll just log the request and return a success response

    // Extract usage data from request body
    const {
      userId,
      sessionId,
      partnerId,
      clientId,
      action,
      quantity = 1,
      timestamp = new Date().toISOString(),
      details = {}
    } = req.body;

    // Validate required parameters
    if (!userId || !sessionId || !action) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing required parameters: userId, sessionId, and action are required'
      });
    }

    console.log('Received usage tracking request:', {
      userId,
      sessionId,
      partnerId,
      clientId,
      action,
      quantity,
      timestamp,
      details
    });

    // Determine if this action should deduct credits
    const skipCreditDeduction = action === 'session_heartbeat' || action === 'session_start';
    const creditsToDeduct = skipCreditDeduction ? 0 : quantity;

    // Record the usage event
    const eventId = await recordUsageEvent(
      userId,
      sessionId,
      partnerId || clientId || 'unknown',
      action,
      {
        action,
        quantity,
        timestamp,
        details
      },
      creditsToDeduct
    );

    // Update session metrics
    await updateSessionMetrics(
      sessionId,
      userId,
      partnerId || clientId || 'unknown',
      {
        ...details,
        last_activity: timestamp
      },
      creditsToDeduct
    );

    // Deduct credits if needed
    if (!skipCreditDeduction && creditsToDeduct > 0) {
      const toolName = details.toolName || 'Unknown Tool';
      await deductCredits(
        userId,
        creditsToDeduct,
        `Used ${creditsToDeduct} credits for ${toolName} (${action})`,
        partnerId || clientId || 'unknown'
      );
    }

    // Log the API request
    await logApiRequest(
      '/api/v1/usage/track',
      'POST',
      partnerId || clientId || 'unknown',
      userId,
      req.body,
      StatusCodes.OK,
      { success: true, eventId }
    );

    // Return a success response
    return res.status(StatusCodes.OK).json({
      success: true,
      data: {
        eventId,
        sessionId,
        creditsDeducted: creditsToDeduct,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error tracking usage:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`API server running on port ${PORT}`);
});
