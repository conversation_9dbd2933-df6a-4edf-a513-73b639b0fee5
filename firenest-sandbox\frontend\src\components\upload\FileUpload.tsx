/**
 * File Upload Component
 * Phase 1: Secure Data Ingestion
 * Drag-and-drop file upload with real-time progress tracking
 */

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Upload, FileText, AlertCircle, CheckCircle, X, RefreshCw } from 'lucide-react'
import toast from 'react-hot-toast'
import { uploadsApi, uploadFile } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Progress } from '@/components/ui/Progress'
import { Badge } from '@/components/ui/Badge'

interface FileUploadProps {
  projectId: string
  onUploadComplete?: (uploadId: string) => void
  maxFileSize?: number // in bytes
  acceptedFileTypes?: string[]
}

interface UploadingFile {
  file: File
  uploadId?: string
  progress: number
  status: 'uploading' | 'processing' | 'complete' | 'error'
  error?: string
}

const FILE_TYPE_OPTIONS = [
  { value: 'customer_usage_data', label: 'Customer Usage Data', description: 'Usage metrics and consumption data' },
  { value: 'billing_data', label: 'Billing Data', description: 'Historical billing and revenue information' },
  { value: 'customer_metadata', label: 'Customer Metadata', description: 'Customer profiles and plan information' }
]

export function FileUpload({ 
  projectId, 
  onUploadComplete,
  maxFileSize = 100 * 1024 * 1024, // 100MB default
  acceptedFileTypes = ['.csv', '.json', '.xlsx']
}: FileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])
  const [selectedFileType, setSelectedFileType] = useState<string>('')
  const queryClient = useQueryClient()

  const uploadMutation = useMutation({
    mutationFn: async ({ file, fileType }: { file: File; fileType: string }) => {
      // Step 1: Initiate upload
      const initiateResponse = await uploadsApi.initiate({
        fileName: file.name,
        fileSize: file.size,
        fileType,
        projectId
      })

      const { uploadId, uploadUrl, key } = initiateResponse.data.data

      // Step 2: Upload to S3
      await uploadFile(file, uploadUrl)

      // Step 3: Finalize upload
      const finalizeResponse = await uploadsApi.finalize({
        key,
        fileName: file.name,
        fileType
      })

      return { uploadId, ...finalizeResponse.data.data }
    },
    onSuccess: (data) => {
      toast.success('File uploaded successfully! Validation in progress...')
      onUploadComplete?.(data.uploadId)
      queryClient.invalidateQueries({ queryKey: ['uploads', projectId] })
      queryClient.invalidateQueries({ queryKey: ['projects', projectId] })
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Upload failed'
      toast.error(message)
    }
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (!selectedFileType) {
      toast.error('Please select a file type first')
      return
    }

    acceptedFiles.forEach((file) => {
      // Validate file size
      if (file.size > maxFileSize) {
        toast.error(`File ${file.name} is too large. Maximum size is ${Math.round(maxFileSize / 1024 / 1024)}MB`)
        return
      }

      // Validate file type
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (!acceptedFileTypes.includes(fileExtension)) {
        toast.error(`File type ${fileExtension} is not supported`)
        return
      }

      // Add to uploading files
      const uploadingFile: UploadingFile = {
        file,
        progress: 0,
        status: 'uploading'
      }

      setUploadingFiles(prev => [...prev, uploadingFile])

      // Start upload
      uploadMutation.mutate(
        { file, fileType: selectedFileType },
        {
          onSuccess: (data) => {
            setUploadingFiles(prev => 
              prev.map(f => 
                f.file === file 
                  ? { ...f, uploadId: data.uploadId, progress: 100, status: 'complete' }
                  : f
              )
            )
          },
          onError: (error: any) => {
            setUploadingFiles(prev => 
              prev.map(f => 
                f.file === file 
                  ? { ...f, progress: 0, status: 'error', error: error.message }
                  : f
              )
            )
          }
        }
      )
    })
  }, [selectedFileType, maxFileSize, acceptedFileTypes, uploadMutation, projectId, onUploadComplete])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxSize: maxFileSize,
    multiple: true
  })

  const removeUploadingFile = (file: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== file))
  }

  const retryUpload = (file: File) => {
    if (!selectedFileType) return

    setUploadingFiles(prev => 
      prev.map(f => 
        f.file === file 
          ? { ...f, progress: 0, status: 'uploading', error: undefined }
          : f
      )
    )

    uploadMutation.mutate({ file, fileType: selectedFileType })
  }

  return (
    <div className="space-y-6">
      {/* File Type Selection */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-4">Select Data Type</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {FILE_TYPE_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => setSelectedFileType(option.value)}
              className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                selectedFileType === option.value
                  ? 'border-fiery bg-fiery/10 text-white'
                  : 'border-white/20 hover:border-white/40 text-gray-300'
              }`}
            >
              <div className="font-medium">{option.label}</div>
              <div className="text-sm text-gray-400 mt-1">{option.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Upload Area */}
      <div className="firenest-card">
        <div
          {...getRootProps()}
          className={`upload-area ${isDragActive ? 'dragover' : ''} ${
            !selectedFileType ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
        >
          <input {...getInputProps()} disabled={!selectedFileType} />
          <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          
          {isDragActive ? (
            <p className="text-lg text-fiery font-medium">Drop files here...</p>
          ) : (
            <div className="text-center">
              <p className="text-lg font-medium text-white mb-2">
                {selectedFileType ? 'Drag & drop files here, or click to browse' : 'Select a data type first'}
              </p>
              <p className="text-sm text-muted-foreground">
                Supports CSV, JSON, and XLSX files up to {Math.round(maxFileSize / 1024 / 1024)}MB
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Uploading Files */}
      {uploadingFiles.length > 0 && (
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Upload Progress</h3>
          <div className="space-y-4">
            {uploadingFiles.map((uploadingFile, index) => (
              <UploadingFileItem
                key={index}
                uploadingFile={uploadingFile}
                onRemove={() => removeUploadingFile(uploadingFile.file)}
                onRetry={() => retryUpload(uploadingFile.file)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

interface UploadingFileItemProps {
  uploadingFile: UploadingFile
  onRemove: () => void
  onRetry: () => void
}

function UploadingFileItem({ uploadingFile, onRemove, onRetry }: UploadingFileItemProps) {
  const { file, progress, status, error } = uploadingFile

  const getStatusIcon = () => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-400" />
      case 'complete':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />
      default:
        return <FileText className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'uploading':
        return 'Uploading...'
      case 'processing':
        return 'Processing...'
      case 'complete':
        return 'Upload complete'
      case 'error':
        return error || 'Upload failed'
      default:
        return 'Pending'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return 'bg-blue-500'
      case 'complete':
        return 'bg-green-500'
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className="firenest-nested-card">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1">
          {getStatusIcon()}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-white truncate">{file.name}</p>
            <p className="text-xs text-gray-400">
              {(file.size / 1024 / 1024).toFixed(2)} MB • {getStatusText()}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant={status === 'error' ? 'destructive' : status === 'complete' ? 'success' : 'secondary'}>
            {status}
          </Badge>
          
          {status === 'error' && (
            <Button size="sm" variant="outline" onClick={onRetry}>
              <RefreshCw className="w-3 h-3" />
            </Button>
          )}
          
          <Button size="sm" variant="ghost" onClick={onRemove}>
            <X className="w-3 h-3" />
          </Button>
        </div>
      </div>
      
      {(status === 'uploading' || status === 'processing') && (
        <div className="mt-3">
          <Progress value={progress} className="h-2" />
        </div>
      )}
    </div>
  )
}
