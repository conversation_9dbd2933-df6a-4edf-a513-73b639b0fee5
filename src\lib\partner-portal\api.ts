/**
 * Partner Portal API
 *
 * This file contains functions for interacting with the partner portal API.
 */

import { supabase } from '@/lib/supabase';
import {
  PartnerAccount,
  PartnerTool,
  IntegrationConfig,
  CodeSnippet,
  PartnerStatus,
  ToolStatus,
  PartnerAuthMethod,
  OAuthConfig,
  WebhookConfig
} from './types';
import { notify } from '@/components/ui/notification-system';
import { generateClientId, generateClientSecret, hashClientSecret } from './oauth-utils';

// Partner Account Functions

/**
 * Get a partner account by ID
 */
export async function getPartnerAccount(partnerId: string): Promise<PartnerAccount | null> {
  if (!partnerId) {
    console.error('getPartnerAccount called with empty partnerId');
    return null;
  }

  try {
    console.log(`Fetching partner account for user ID: ${partnerId}`);

    const { data, error } = await supabase
      .from('partner_accounts')
      .select('*')
      .eq('id', partnerId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no record is found

    if (error) {
      console.error('Supabase error when fetching partner account:', error);
      throw error;
    }

    if (!data) {
      console.log(`No partner account found for user ID: ${partnerId}`);
      return null;
    }

    console.log(`Partner account found for user ID: ${partnerId}`, data);

    return {
      id: data.id,
      name: data.name,
      email: data.email,
      company: data.company,
      website: data.website,
      logoUrl: data.logo_url,
      description: data.description,
      status: data.status as PartnerStatus,
      apiKey: data.api_key,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  } catch (error: any) {
    // Don't show notification here, let the calling component handle it
    console.error('Error getting partner account:', error);

    if (error.code === 'PGRST116') {
      // This is the error code when no rows are returned with .single()
      console.log(`No partner account found for user ID: ${partnerId}`);
      return null;
    }

    // For other errors, log details but don't notify
    console.error('Error details:', JSON.stringify(error, null, 2));
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.details) {
      console.error('Error details:', error.details);
    }

    return null;
  }
}

/**
 * Create a new partner account
 */
export async function createPartnerAccount(
  id: string, // Auth user ID
  name: string,
  email: string,
  company: string,
  website?: string,
  description?: string
): Promise<PartnerAccount | null> {
  if (!id) {
    console.error('createPartnerAccount called with empty id');
    notify.error('Failed to create partner account: Missing user ID');
    return null;
  }

  if (!name || !email || !company) {
    console.error('createPartnerAccount called with missing required fields');
    notify.error('Failed to create partner account: Missing required fields');
    return null;
  }

  try {
    console.log(`Creating partner account for user ID: ${id}`);

    // First check if a partner account already exists for this user by ID
    const existingAccountById = await getPartnerAccount(id);

    if (existingAccountById) {
      console.log(`Partner account already exists for user ID: ${id}`);
      return existingAccountById;
    }

    // Also check if a partner account exists with the same email
    const { data: existingAccountByEmail, error: emailCheckError } = await supabase
      .from('partner_accounts')
      .select('*')
      .eq('email', email)
      .maybeSingle();

    if (emailCheckError) {
      console.error('Error checking for existing account by email:', emailCheckError);
    }

    if (existingAccountByEmail) {
      console.log(`Partner account already exists with email ${email}, updating ID to match auth user`);

      // Update the existing account to use the auth user ID
      const { data: updatedAccount, error: updateError } = await supabase
        .from('partner_accounts')
        .update({
          id, // Update to use the auth user ID
          updated_at: new Date().toISOString()
        })
        .eq('email', email)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating existing partner account:', updateError);
        throw updateError;
      }

      if (updatedAccount) {
        console.log('Successfully updated partner account ID to match auth user');
        return {
          id: updatedAccount.id,
          name: updatedAccount.name,
          email: updatedAccount.email,
          company: updatedAccount.company,
          website: updatedAccount.website,
          logoUrl: updatedAccount.logo_url,
          description: updatedAccount.description,
          status: updatedAccount.status as PartnerStatus,
          apiKey: updatedAccount.api_key,
          createdAt: updatedAccount.created_at,
          updatedAt: updatedAccount.updated_at
        };
      }
    }

    // Create a new partner account
    console.log('Creating new partner account with ID:', id);
    const { data, error } = await supabase
      .from('partner_accounts')
      .insert({
        id, // Use the provided ID (Auth user ID)
        name,
        email,
        company,
        website,
        description,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase error when creating partner account:', error);
      throw error;
    }

    if (!data) {
      console.error('No data returned when creating partner account');
      throw new Error('Failed to create partner account');
    }

    console.log(`Partner account created successfully:`, data);

    return {
      id: data.id,
      name: data.name,
      email: data.email,
      company: data.company,
      website: data.website,
      logoUrl: data.logo_url,
      description: data.description,
      status: data.status as PartnerStatus,
      apiKey: data.api_key,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  } catch (error: any) {
    console.error('Error creating partner account:', error);

    // Handle specific error cases
    if (error.code === '23505') {
      // This is the error code for unique constraint violation
      console.log(`Partner account already exists for user ID: ${id}`);

      // Try to fetch the existing account one more time
      const existingAccount = await getPartnerAccount(id);
      if (existingAccount) {
        return existingAccount;
      }

      // If we still can't find it by ID, check by email
      const { data: accountByEmail } = await supabase
        .from('partner_accounts')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (accountByEmail) {
        console.log(`Found partner account by email, updating ID to match auth user`);

        // Update the existing account to use the auth user ID
        const { data: updatedAccount, error: updateError } = await supabase
          .from('partner_accounts')
          .update({
            id, // Update to use the auth user ID
            updated_at: new Date().toISOString()
          })
          .eq('email', email)
          .select()
          .single();

        if (!updateError && updatedAccount) {
          return {
            id: updatedAccount.id,
            name: updatedAccount.name,
            email: updatedAccount.email,
            company: updatedAccount.company,
            website: updatedAccount.website,
            logoUrl: updatedAccount.logo_url,
            description: updatedAccount.description,
            status: updatedAccount.status as PartnerStatus,
            apiKey: updatedAccount.api_key,
            createdAt: updatedAccount.created_at,
            updatedAt: updatedAccount.updated_at
          };
        }
      }
    }

    // For other errors, log details and notify
    console.error('Error details:', JSON.stringify(error, null, 2));
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.details) {
      console.error('Error details:', error.details);
    }

    notify.error(`Failed to create partner account: ${error.message || 'Unknown error'}`);
    return null;
  }
}

/**
 * Update a partner account
 */
export async function updatePartnerAccount(
  partnerId: string,
  updates: Partial<PartnerAccount>
): Promise<PartnerAccount | null> {
  try {
    // Convert from camelCase to snake_case for Supabase
    const dbUpdates: any = {};
    if (updates.name) dbUpdates.name = updates.name;
    if (updates.email) dbUpdates.email = updates.email;
    if (updates.company) dbUpdates.company = updates.company;
    if (updates.website !== undefined) dbUpdates.website = updates.website;
    if (updates.logoUrl !== undefined) dbUpdates.logo_url = updates.logoUrl;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.status) dbUpdates.status = updates.status;

    dbUpdates.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('partner_accounts')
      .update(dbUpdates)
      .eq('id', partnerId)
      .select()
      .single();

    if (error) throw error;

    return data ? {
      id: data.id,
      name: data.name,
      email: data.email,
      company: data.company,
      website: data.website,
      logoUrl: data.logo_url,
      description: data.description,
      status: data.status as PartnerStatus,
      apiKey: data.api_key,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error updating partner account:', error);
    notify.error('Failed to update partner account');
    return null;
  }
}

// Partner Tool Functions

/**
 * Get all tools for a partner
 */
export async function getPartnerTools(partnerId: string): Promise<PartnerTool[]> {
  try {
    const { data, error } = await supabase
      .from('partner_tools')
      .select('*')
      .eq('partner_id', partnerId);

    if (error) throw error;

    return data ? data.map(tool => ({
      id: tool.id,
      partnerId: tool.partner_id,
      name: tool.name,
      description: tool.description,
      longDescription: tool.long_description,
      logoUrl: tool.logo_url,
      websiteUrl: tool.website_url,
      category: tool.category,
      tags: tool.tags,
      features: tool.features,
      pricing: tool.pricing,
      status: tool.status as ToolStatus,
      createdAt: tool.created_at,
      updatedAt: tool.updated_at
    })) : [];
  } catch (error) {
    console.error('Error getting partner tools:', error);
    notify.error('Failed to get tools');
    return [];
  }
}

/**
 * Get a tool by ID
 */
export async function getPartnerTool(toolId: string): Promise<PartnerTool | null> {
  try {
    const { data, error } = await supabase
      .from('partner_tools')
      .select('*')
      .eq('id', toolId)
      .single();

    if (error) throw error;

    return data ? {
      id: data.id,
      partnerId: data.partner_id,
      name: data.name,
      description: data.description,
      longDescription: data.long_description,
      logoUrl: data.logo_url,
      websiteUrl: data.website_url,
      category: data.category,
      tags: data.tags,
      features: data.features,
      pricing: data.pricing,
      status: data.status as ToolStatus,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error getting tool:', error);
    notify.error('Failed to get tool');
    return null;
  }
}

/**
 * Create a new tool
 */
export async function createPartnerTool(
  partnerId: string,
  name: string,
  description?: string,
  longDescription?: string,
  category?: string,
  websiteUrl?: string
): Promise<PartnerTool | null> {
  try {
    console.log('Creating new tool for partner:', partnerId);

    // Get partner account to use the name for client ID generation
    const partnerAccount = await getPartnerAccount(partnerId);
    if (!partnerAccount) {
      console.error('Partner account not found for ID:', partnerId);
      throw new Error('Partner account not found');
    }

    console.log('Partner account found:', partnerAccount.name);

    // Insert the tool
    const result = await supabase
      .from('partner_tools')
      .insert({
        partner_id: partnerId,
        name,
        description,
        long_description: longDescription,
        category,
        website_url: websiteUrl,
        status: 'draft'
      })
      .select()
      .maybeSingle();

    if (result.error) {
      console.error('Error inserting tool:', result.error);
      throw result.error;
    }

    const data = result.data;
    if (!data) {
      console.error('No data returned when creating tool');
      throw new Error('No data returned when creating tool');
    }

    console.log('Tool created successfully with ID:', data.id);

    // Create a tool object
    const tool = {
      id: data.id,
      partnerId: data.partner_id,
      name: data.name,
      description: data.description,
      longDescription: data.long_description,
      logoUrl: data.logo_url,
      websiteUrl: data.website_url,
      category: data.category,
      tags: data.tags,
      features: data.features,
      pricing: data.pricing,
      status: data.status as ToolStatus,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    // Generate OAuth credentials
    console.log('Generating OAuth credentials for new tool');
    try {
      const credentials = await generateOAuthCredentials(tool.id, partnerAccount.name);
      if (credentials) {
        console.log('OAuth credentials generated successfully');
      } else {
        console.warn('OAuth credentials generation returned null, but continuing with tool creation');
      }
    } catch (credError) {
      console.error('Error generating OAuth credentials:', credError);
      // Don't throw here, we still want to return the created tool
      notify.error('Tool created, but OAuth credentials could not be generated. You can generate them later.');
    }

    return tool;
  } catch (error) {
    console.error('Error creating tool:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      notify.error(`Failed to create tool: ${error.message}`);
    } else {
      notify.error('Failed to create tool: Unknown error');
    }
    return null;
  }
}

/**
 * Generate OAuth credentials for a tool
 * This creates a client ID and client secret and stores them in the database
 */
export async function generateOAuthCredentials(
  toolId: string,
  partnerName: string
): Promise<{ clientId: string, clientSecret: string } | null> {
  try {
    console.log('Generating OAuth credentials for tool:', toolId, 'partner:', partnerName);

    // Validate inputs
    if (!toolId) {
      throw new Error('Tool ID is required');
    }

    if (!partnerName) {
      throw new Error('Partner name is required');
    }

    // Generate client ID and secret
    const clientId = generateClientId(toolId, partnerName);
    console.log('Generated client ID:', clientId);

    const clientSecret = generateClientSecret();
    console.log('Generated client secret (length):', clientSecret.length);

    // Store the hashed client secret for security
    console.log('Hashing client secret...');
    let hashedSecret;
    try {
      hashedSecret = await hashClientSecret(clientSecret);
      console.log('Hashed client secret (length):', hashedSecret.length);
    } catch (hashError) {
      console.error('Error hashing client secret:', hashError);
      throw new Error(`Failed to hash client secret: ${hashError instanceof Error ? hashError.message : 'Unknown error'}`);
    }

    // Determine the base URL for auth endpoints
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://firenest.app';

    // Create default OAuth configuration
    const oauthConfig: OAuthConfig = {
      clientId,
      clientSecret: hashedSecret, // Store the hashed secret
      authorizationUrl: `${baseUrl}/auth/authorize`,
      tokenUrl: `${baseUrl}/auth/token`,
      redirectUrl: `${baseUrl}/auth/callback`,
      scope: ['openid', 'profile'],
      responseType: 'code'
    };

    console.log('Saving OAuth configuration...');

    // Save the OAuth configuration
    let savedConfig;
    try {
      savedConfig = await saveIntegrationConfig(toolId, 'oauth', oauthConfig);
    } catch (saveError) {
      console.error('Error saving integration config:', saveError);
      throw new Error(`Failed to save OAuth configuration: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`);
    }

    if (!savedConfig) {
      console.error('Failed to save integration config - saveIntegrationConfig returned null');
      throw new Error('Failed to save OAuth configuration');
    }

    console.log('OAuth configuration saved successfully');

    // Return the client ID and the original (unhashed) client secret
    // This is the only time the unhashed client secret will be available
    return {
      clientId,
      clientSecret
    };
  } catch (error) {
    console.error('Error generating OAuth credentials:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      notify.error(`Failed to generate OAuth credentials: ${error.message}`);
    } else {
      notify.error('Failed to generate OAuth credentials: Unknown error');
    }
    return null;
  }
}

/**
 * Update a tool
 */
export async function updatePartnerTool(
  toolId: string,
  updates: Partial<PartnerTool>
): Promise<PartnerTool | null> {
  try {
    // Convert from camelCase to snake_case for Supabase
    const dbUpdates: any = {};
    if (updates.name) dbUpdates.name = updates.name;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.longDescription !== undefined) dbUpdates.long_description = updates.longDescription;
    if (updates.logoUrl !== undefined) dbUpdates.logo_url = updates.logoUrl;
    if (updates.websiteUrl !== undefined) dbUpdates.website_url = updates.websiteUrl;
    if (updates.category !== undefined) dbUpdates.category = updates.category;
    if (updates.tags !== undefined) dbUpdates.tags = updates.tags;
    if (updates.features !== undefined) dbUpdates.features = updates.features;
    if (updates.pricing !== undefined) dbUpdates.pricing = updates.pricing;
    if (updates.status) dbUpdates.status = updates.status;

    dbUpdates.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('partner_tools')
      .update(dbUpdates)
      .eq('id', toolId)
      .select()
      .single();

    if (error) throw error;

    return data ? {
      id: data.id,
      partnerId: data.partner_id,
      name: data.name,
      description: data.description,
      longDescription: data.long_description,
      logoUrl: data.logo_url,
      websiteUrl: data.website_url,
      category: data.category,
      tags: data.tags,
      features: data.features,
      pricing: data.pricing,
      status: data.status as ToolStatus,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error updating tool:', error);
    notify.error('Failed to update tool');
    return null;
  }
}

/**
 * Delete a tool
 */
export async function deletePartnerTool(toolId: string): Promise<boolean> {
  try {
    // First, delete related records
    // Delete integration configs
    await supabase
      .from('integration_configs')
      .delete()
      .eq('tool_id', toolId);

    // Delete code snippets
    await supabase
      .from('integration_code_snippets')
      .delete()
      .eq('tool_id', toolId);

    // Delete the tool
    const { error } = await supabase
      .from('partner_tools')
      .delete()
      .eq('id', toolId);

    if (error) throw error;

    return true;
  } catch (error) {
    console.error('Error deleting tool:', error);
    notify.error('Failed to delete tool');
    return false;
  }
}

// Integration Config Functions

/**
 * Get integration config for a tool
 */
export async function getIntegrationConfig(toolId: string): Promise<IntegrationConfig | null> {
  try {
    console.log('Getting integration config for tool:', toolId);

    // First check if the tool ID is valid
    if (!toolId) {
      console.error('Invalid tool ID provided to getIntegrationConfig');
      return null;
    }

    // Use select() instead of maybeSingle() to get all matching rows
    const result = await supabase
      .from('integration_configs')
      .select('*')
      .eq('tool_id', toolId);

    if (result.error) {
      console.error('Error getting integration config:', result.error);
      console.error('Error details:', result.error.message);
      console.error('Error code:', result.error.code);
      throw result.error;
    }

    // Check if we have any results
    if (!result.data || result.data.length === 0) {
      console.log('No integration config found for tool ID:', toolId);
      return null;
    }

    // If we have multiple results, log a warning and use the most recently updated one
    if (result.data.length > 1) {
      console.warn(`Multiple integration configs found for tool ID: ${toolId}. Using the most recent one.`);

      // Sort by updated_at in descending order to get the most recent one
      result.data.sort((a, b) => {
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      });
    }

    // Use the first (or only) result
    const data = result.data[0];
    console.log('Integration config found:', data ? 'Yes' : 'No');

    return data ? {
      id: data.id,
      toolId: data.tool_id,
      authMethod: data.auth_method as PartnerAuthMethod,
      configData: data.config_data,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error getting integration config:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      notify.error(`Failed to get integration configuration: ${error.message}`);
    } else {
      notify.error('Failed to get integration configuration: Unknown error');
    }
    return null;
  }
}

/**
 * Create or update integration config
 */
export async function saveIntegrationConfig(
  toolId: string,
  authMethod: PartnerAuthMethod,
  configData: any
): Promise<IntegrationConfig | null> {
  try {
    console.log('Saving integration config for tool:', toolId, 'auth method:', authMethod);

    // Check if config already exists
    const existingConfigResult = await supabase
      .from('integration_configs')
      .select('id')
      .eq('tool_id', toolId)
      .maybeSingle();

    if (existingConfigResult.error) {
      console.error('Error checking for existing config:', existingConfigResult.error);
      throw new Error(`Failed to check for existing config: ${existingConfigResult.error.message}`);
    }

    const existingConfig = existingConfigResult.data;
    console.log('Existing config found:', existingConfig ? 'Yes' : 'No');

    let result;

    if (existingConfig) {
      // Update existing config
      console.log('Updating existing config with ID:', existingConfig.id);
      result = await supabase
        .from('integration_configs')
        .update({
          auth_method: authMethod,
          config_data: configData,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingConfig.id)
        .select()
        .maybeSingle();
    } else {
      // Create new config
      console.log('Creating new integration config');
      result = await supabase
        .from('integration_configs')
        .insert({
          tool_id: toolId,
          auth_method: authMethod,
          config_data: configData
        })
        .select()
        .maybeSingle();
    }

    if (result.error) {
      console.error('Error saving integration config:', result.error);
      console.error('Error details:', result.error.message);
      console.error('Error code:', result.error.code);
      console.error('Error hint:', result.error.hint);
      console.error('Error details:', result.error.details);
      throw result.error;
    }

    const data = result.data;
    console.log('Integration config saved successfully:', data ? 'Yes' : 'No');

    return data ? {
      id: data.id,
      toolId: data.tool_id,
      authMethod: data.auth_method as PartnerAuthMethod,
      configData: data.config_data,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error saving integration config:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      notify.error(`Failed to save integration configuration: ${error.message}`);
    } else {
      notify.error('Failed to save integration configuration: Unknown error');
    }
    return null;
  }
}

/**
 * Regenerate OAuth client secret for a tool
 * This creates a new client secret and updates the existing OAuth configuration
 */
export async function regenerateClientSecret(toolId: string): Promise<string | null> {
  try {
    console.log('Regenerating client secret for tool:', toolId);

    // Get the existing config
    const config = await getIntegrationConfig(toolId);
    if (!config || config.authMethod !== 'oauth') {
      throw new Error('No OAuth configuration found for this tool');
    }

    console.log('Found existing OAuth configuration');

    // Generate a new client secret
    const newClientSecret = generateClientSecret();
    console.log('Generated new client secret (length):', newClientSecret.length);

    // Hash the new client secret for storage
    console.log('Hashing new client secret...');
    let hashedSecret;
    try {
      hashedSecret = await hashClientSecret(newClientSecret);
      console.log('Hashed client secret (length):', hashedSecret.length);
    } catch (hashError) {
      console.error('Error hashing client secret:', hashError);
      throw new Error(`Failed to hash client secret: ${hashError instanceof Error ? hashError.message : 'Unknown error'}`);
    }

    // Update the config with the new hashed secret
    const oauthConfig = config.configData as OAuthConfig;
    oauthConfig.clientSecret = hashedSecret;

    // Save the updated config
    console.log('Saving updated OAuth configuration...');
    try {
      await saveIntegrationConfig(toolId, 'oauth', oauthConfig);
      console.log('OAuth configuration updated successfully');
    } catch (saveError) {
      console.error('Error saving updated integration config:', saveError);
      throw new Error(`Failed to save updated OAuth configuration: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`);
    }

    // Return the new unhashed client secret
    // This is the only time the unhashed client secret will be available
    return newClientSecret;
  } catch (error) {
    console.error('Error regenerating client secret:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      notify.error(`Failed to regenerate client secret: ${error.message}`);
    } else {
      notify.error('Failed to regenerate client secret: Unknown error');
    }
    return null;
  }
}

/**
 * Regenerate OAuth client ID for a tool
 * This creates a new client ID and updates the existing OAuth configuration
 */
export async function regenerateClientId(toolId: string): Promise<string | null> {
  try {
    console.log('Regenerating client ID for tool:', toolId);

    // Get the tool to use its name for client ID generation
    const tool = await getPartnerTool(toolId);
    if (!tool) {
      throw new Error('Tool not found');
    }
    console.log('Found tool:', tool.name);

    // Get the partner account to use its name for client ID generation
    const partnerAccount = await getPartnerAccount(tool.partnerId);
    if (!partnerAccount) {
      throw new Error('Partner account not found');
    }
    console.log('Found partner account:', partnerAccount.name);

    // Get the existing config
    const config = await getIntegrationConfig(toolId);
    if (!config || config.authMethod !== 'oauth') {
      throw new Error('No OAuth configuration found for this tool');
    }
    console.log('Found existing OAuth configuration');

    // Generate a new client ID
    const newClientId = generateClientId(toolId, partnerAccount.name);
    console.log('Generated new client ID:', newClientId);

    // Update the config with the new client ID
    const oauthConfig = config.configData as OAuthConfig;
    oauthConfig.clientId = newClientId;

    // Save the updated config
    console.log('Saving updated OAuth configuration...');
    try {
      await saveIntegrationConfig(toolId, 'oauth', oauthConfig);
      console.log('OAuth configuration updated successfully');
    } catch (saveError) {
      console.error('Error saving updated integration config:', saveError);
      throw new Error(`Failed to save updated OAuth configuration: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`);
    }

    // Return the new client ID
    return newClientId;
  } catch (error) {
    console.error('Error regenerating client ID:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      notify.error(`Failed to regenerate client ID: ${error.message}`);
    } else {
      notify.error('Failed to regenerate client ID: Unknown error');
    }
    return null;
  }
}

// Code Snippet Functions

/**
 * Get code snippets for a tool
 */
export async function getCodeSnippets(toolId: string): Promise<CodeSnippet[]> {
  try {
    const { data, error } = await supabase
      .from('integration_code_snippets')
      .select('*')
      .eq('tool_id', toolId);

    if (error) throw error;

    return data ? data.map(snippet => ({
      id: snippet.id,
      toolId: snippet.tool_id,
      platform: snippet.platform,
      codeSnippet: snippet.code_snippet,
      createdAt: snippet.created_at,
      updatedAt: snippet.updated_at
    })) : [];
  } catch (error) {
    console.error('Error getting code snippets:', error);
    notify.error('Failed to get code snippets');
    return [];
  }
}

/**
 * Save a code snippet
 */
export async function saveCodeSnippet(
  toolId: string,
  platform: string,
  codeSnippet: string
): Promise<CodeSnippet | null> {
  try {
    // Check if snippet already exists for this platform
    const { data: existingSnippet } = await supabase
      .from('integration_code_snippets')
      .select('id')
      .eq('tool_id', toolId)
      .eq('platform', platform)
      .single();

    let result;

    if (existingSnippet) {
      // Update existing snippet
      result = await supabase
        .from('integration_code_snippets')
        .update({
          code_snippet: codeSnippet,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSnippet.id)
        .select()
        .single();
    } else {
      // Create new snippet
      result = await supabase
        .from('integration_code_snippets')
        .insert({
          tool_id: toolId,
          platform,
          code_snippet: codeSnippet
        })
        .select()
        .single();
    }

    const { data, error } = result;

    if (error) throw error;

    return data ? {
      id: data.id,
      toolId: data.tool_id,
      platform: data.platform,
      codeSnippet: data.code_snippet,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error saving code snippet:', error);
    notify.error('Failed to save code snippet');
    return null;
  }
}

// Webhook Functions

/**
 * Get webhook configuration for a tool
 */
export async function getWebhookConfig(toolId: string): Promise<WebhookConfig | null> {
  try {
    const { data, error } = await supabase
      .from('webhook_configs')
      .select('*')
      .eq('tool_id', toolId)
      .maybeSingle();

    if (error) throw error;

    if (!data) return null;

    return {
      id: data.id,
      toolId: data.tool_id,
      webhookUrl: data.webhook_url,
      secret: data.secret,
      enabledEvents: {
        sessionStart: data.enabled_events?.session_start ?? true,
        sessionEnd: data.enabled_events?.session_end ?? true,
        featureUse: data.enabled_events?.feature_use ?? true,
        creditConsume: data.enabled_events?.credit_consume ?? true,
        creditLow: data.enabled_events?.credit_low ?? true,
        subscriptionChange: data.enabled_events?.subscription_change ?? true
      },
      retryEnabled: data.retry_enabled ?? true,
      maxRetries: data.max_retries,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  } catch (error) {
    console.error('Error getting webhook configuration:', error);
    notify.error('Failed to get webhook configuration');
    return null;
  }
}

/**
 * Save webhook configuration for a tool
 */
export async function saveWebhookConfig(config: WebhookConfig): Promise<WebhookConfig | null> {
  try {
    // Check if a webhook config already exists for this tool
    const existingConfig = await getWebhookConfig(config.toolId);

    // Convert from camelCase to snake_case for Supabase
    const dbConfig = {
      tool_id: config.toolId,
      webhook_url: config.webhookUrl,
      secret: config.secret,
      enabled_events: {
        session_start: config.enabledEvents.sessionStart,
        session_end: config.enabledEvents.sessionEnd,
        feature_use: config.enabledEvents.featureUse,
        credit_consume: config.enabledEvents.creditConsume,
        credit_low: config.enabledEvents.creditLow,
        subscription_change: config.enabledEvents.subscriptionChange
      },
      retry_enabled: config.retryEnabled,
      max_retries: config.retryEnabled ? config.maxRetries : null,
      updated_at: new Date().toISOString()
    };

    let result;
    if (existingConfig) {
      // Update existing config
      result = await supabase
        .from('webhook_configs')
        .update(dbConfig)
        .eq('tool_id', config.toolId)
        .select()
        .single();
    } else {
      // Insert new config
      dbConfig['created_at'] = new Date().toISOString();
      result = await supabase
        .from('webhook_configs')
        .insert(dbConfig)
        .select()
        .single();
    }

    if (result.error) throw result.error;

    const data = result.data;
    return data ? {
      id: data.id,
      toolId: data.tool_id,
      webhookUrl: data.webhook_url,
      secret: data.secret,
      enabledEvents: {
        sessionStart: data.enabled_events?.session_start ?? true,
        sessionEnd: data.enabled_events?.session_end ?? true,
        featureUse: data.enabled_events?.feature_use ?? true,
        creditConsume: data.enabled_events?.credit_consume ?? true,
        creditLow: data.enabled_events?.credit_low ?? true,
        subscriptionChange: data.enabled_events?.subscription_change ?? true
      },
      retryEnabled: data.retry_enabled ?? true,
      maxRetries: data.max_retries,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    } : null;
  } catch (error) {
    console.error('Error saving webhook configuration:', error);
    notify.error('Failed to save webhook configuration');
    return null;
  }
}

/**
 * Test a webhook endpoint
 */
export async function testWebhook(
  toolId: string,
  webhookUrl: string,
  secret?: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Call the webhook test endpoint
    const response = await fetch('/api/v1/partner/test-webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        toolId,
        webhookUrl,
        secret
      })
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error testing webhook:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
