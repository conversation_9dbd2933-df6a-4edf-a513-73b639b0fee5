/**
 * Model Preview
 * Visual preview of pricing model with sample calculations
 */

import React, { useState } from 'react'
import { 
  Calculator, 
  TrendingUp, 
  DollarSign, 
  BarChart3,
  Play,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { getComponentIcon, getComponentColor } from './ComponentPalette'
import type { ModelComponent } from './ModelBuilder'

interface ModelPreviewProps {
  modelName: string
  modelType: string
  components: ModelComponent[]
}

interface SampleCalculation {
  usage: number
  breakdown: {
    component: ModelComponent
    amount: number
    description: string
  }[]
  total: number
}

export function ModelPreview({ modelName, modelType, components }: ModelPreviewProps) {
  const [sampleUsage, setSampleUsage] = useState(1000)
  const [calculation, setCalculation] = useState<SampleCalculation | null>(null)

  const calculateSample = () => {
    if (components.length === 0) return

    const breakdown: SampleCalculation['breakdown'] = []
    let total = 0

    // Process components in order
    components.forEach(component => {
      const { config } = component
      let amount = 0
      let description = ''

      switch (component.type) {
        case 'BASE_FEE':
          amount = config.amount || 0
          description = `Base fee: $${amount}/${config.period}`
          break

        case 'PER_UNIT_RATE':
          amount = (config.unitRate || 0) * sampleUsage
          description = `${sampleUsage} ${config.metricName || 'units'} × $${config.unitRate} = $${amount.toFixed(2)}`
          break

        case 'TIERED_RATE':
          if (config.tiers && config.tiers.length > 0) {
            let remainingUsage = sampleUsage
            let tieredAmount = 0
            const tierBreakdown: string[] = []

            config.tiers.forEach((tier: any, index: number) => {
              if (remainingUsage <= 0) return

              const tierLimit = tier.upTo === 'infinity' ? remainingUsage : Math.min(tier.upTo - (index > 0 ? config.tiers[index - 1].upTo : 0), remainingUsage)
              const tierCost = tierLimit * tier.unitRate
              tieredAmount += tierCost
              tierBreakdown.push(`${tierLimit} × $${tier.unitRate} = $${tierCost.toFixed(2)}`)
              remainingUsage -= tierLimit
            })

            amount = tieredAmount
            description = `Tiered: ${tierBreakdown.join(' + ')}`
          }
          break

        case 'MINIMUM_FEE':
          // Minimum fee is enforced after other calculations
          if (total < (config.amount || 0)) {
            amount = (config.amount || 0) - total
            description = `Minimum fee adjustment: $${amount.toFixed(2)}`
          }
          break

        case 'MAXIMUM_FEE':
          // Maximum fee caps the total
          if (total + amount > (config.amount || 0)) {
            amount = Math.max(0, (config.amount || 0) - total)
            description = `Maximum fee cap applied: $${amount.toFixed(2)}`
          }
          break
      }

      if (amount > 0) {
        breakdown.push({
          component,
          amount,
          description
        })
        total += amount
      }
    })

    setCalculation({
      usage: sampleUsage,
      breakdown,
      total
    })
  }

  React.useEffect(() => {
    calculateSample()
  }, [components, sampleUsage])

  return (
    <div className="space-y-6">
      {/* Model Summary */}
      <div className="firenest-card">
        <div className="flex items-center space-x-4 mb-6">
          <Calculator className="w-8 h-8 text-fiery" />
          <div>
            <h3 className="text-xl font-bold text-white">
              {modelName || 'Untitled Model'}
            </h3>
            <div className="flex items-center space-x-3 mt-1">
              <Badge variant="secondary">{modelType.replace('_', ' ')}</Badge>
              <span className="text-sm text-gray-400">
                {components.length} component{components.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>

        {/* Component Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {['BASE_FEE', 'PER_UNIT_RATE', 'TIERED_RATE', 'MINIMUM_FEE'].map(type => {
            const count = components.filter(c => c.type === type).length
            const ComponentIcon = getComponentIcon(type as any)
            const color = getComponentColor(type as any)
            
            return (
              <div key={type} className="firenest-nested-card text-center">
                <ComponentIcon className={`w-6 h-6 ${color} mx-auto mb-2`} />
                <div className="text-lg font-semibold text-white">{count}</div>
                <div className="text-xs text-gray-400">
                  {type.replace('_', ' ').toLowerCase()}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Sample Calculation */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Sample Calculation</h3>
          <Button size="sm" onClick={calculateSample}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Recalculate
          </Button>
        </div>

        {/* Usage Input */}
        <div className="mb-6">
          <label className="form-label mb-2">Sample Usage Amount</label>
          <div className="flex items-center space-x-4">
            <input
              type="number"
              value={sampleUsage}
              onChange={(e) => setSampleUsage(parseInt(e.target.value) || 0)}
              className="form-input flex-1"
              placeholder="Enter usage amount"
              min="0"
            />
            <Button onClick={calculateSample} variant="fiery">
              <Play className="w-4 h-4 mr-2" />
              Calculate
            </Button>
          </div>
        </div>

        {/* Calculation Results */}
        {calculation && (
          <div className="space-y-4">
            {calculation.breakdown.length > 0 ? (
              <>
                {/* Breakdown */}
                <div className="space-y-3">
                  {calculation.breakdown.map((item, index) => {
                    const ComponentIcon = getComponentIcon(item.component.type)
                    const color = getComponentColor(item.component.type)
                    
                    return (
                      <div key={index} className="firenest-nested-card">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <ComponentIcon className={`w-4 h-4 ${color}`} />
                            <div>
                              <div className="text-sm font-medium text-white">
                                {item.component.type.replace('_', ' ')}
                              </div>
                              <div className="text-xs text-gray-400">
                                {item.description}
                              </div>
                            </div>
                          </div>
                          <div className="text-sm font-semibold text-white">
                            ${item.amount.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Total */}
                <div className="border-t border-white/10 pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <DollarSign className="w-5 h-5 text-fiery" />
                      <span className="text-lg font-semibold text-white">
                        Total Monthly Charge
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-fiery">
                      ${calculation.total.toFixed(2)}
                    </div>
                  </div>
                </div>

                {/* Usage Summary */}
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold text-white">
                        {calculation.usage.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-400">Usage Units</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-white">
                        ${(calculation.total / calculation.usage).toFixed(4)}
                      </div>
                      <div className="text-xs text-gray-400">Cost per Unit</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-white">
                        ${(calculation.total * 12).toFixed(0)}
                      </div>
                      <div className="text-xs text-gray-400">Annual Revenue</div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <Calculator className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400">
                  Add pricing components to see calculation preview
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Revenue Scenarios */}
      {calculation && calculation.breakdown.length > 0 && (
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Revenue Scenarios</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { label: 'Low Usage', multiplier: 0.5 },
              { label: 'Current', multiplier: 1 },
              { label: 'High Usage', multiplier: 2 }
            ].map(scenario => {
              const scenarioUsage = Math.round(sampleUsage * scenario.multiplier)
              const scenarioRevenue = calculation.total * scenario.multiplier
              
              return (
                <div key={scenario.label} className="firenest-nested-card text-center">
                  <div className="text-sm font-medium text-white mb-2">
                    {scenario.label}
                  </div>
                  <div className="text-xs text-gray-400 mb-2">
                    {scenarioUsage.toLocaleString()} units
                  </div>
                  <div className="text-lg font-bold text-fiery">
                    ${scenarioRevenue.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-400">
                    ${(scenarioRevenue * 12).toFixed(0)}/year
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
