/**
 * Security Monitor Component
 * SOC 2 Alignment: CC7.1 (System Operations), CC7.2 (System Monitoring)
 */

import React, { useState, useEffect } from 'react'
import { Shield, AlertTriangle, CheckCircle, Clock, Activity } from 'lucide-react'
import { Badge } from '@/components/ui/Badge'

interface SecurityEvent {
  id: string
  type: 'login' | 'data_access' | 'permission_change' | 'security_alert'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: string
  resolved: boolean
}

export function SecurityMonitor() {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([
    {
      id: '1',
      type: 'login',
      severity: 'low',
      message: 'Successful login from new device',
      timestamp: new Date().toISOString(),
      resolved: true
    },
    {
      id: '2',
      type: 'data_access',
      severity: 'medium',
      message: 'Large dataset accessed',
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      resolved: true
    }
  ])

  const [securityStatus, setSecurityStatus] = useState({
    overallStatus: 'secure',
    lastScan: new Date().toISOString(),
    threatsDetected: 0,
    activeMonitoring: true
  })

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return AlertTriangle
      case 'medium':
        return Clock
      case 'low':
        return CheckCircle
      default:
        return Activity
    }
  }

  return (
    <div className="space-y-6">
      {/* Security Status Overview */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Security Status</h3>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-400">Monitoring Active</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <Shield className="w-8 h-8 text-green-400 mx-auto mb-2" />
            <p className="text-green-400 font-medium">Secure</p>
            <p className="text-xs text-gray-400">System Status</p>
          </div>
          
          <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <CheckCircle className="w-8 h-8 text-blue-400 mx-auto mb-2" />
            <p className="text-blue-400 font-medium">{securityEvents.length}</p>
            <p className="text-xs text-gray-400">Events Today</p>
          </div>
          
          <div className="text-center p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
            <Activity className="w-8 h-8 text-purple-400 mx-auto mb-2" />
            <p className="text-purple-400 font-medium">0</p>
            <p className="text-xs text-gray-400">Active Threats</p>
          </div>
        </div>
      </div>

      {/* Recent Security Events */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Security Events</h3>
        
        <div className="space-y-3">
          {securityEvents.map((event) => {
            const SeverityIcon = getSeverityIcon(event.severity)
            const severityColor = getSeverityColor(event.severity)
            
            return (
              <div key={event.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 ${severityColor.replace('text-', 'bg-').replace('-400', '-500/20')} rounded-lg flex items-center justify-center`}>
                    <SeverityIcon className={`w-4 h-4 ${severityColor}`} />
                  </div>
                  <div>
                    <p className="text-white font-medium">{event.message}</p>
                    <p className="text-sm text-gray-400">
                      {new Date(event.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant={event.severity === 'critical' || event.severity === 'high' ? 'destructive' : 'secondary'}
                  >
                    {event.severity.toUpperCase()}
                  </Badge>
                  {event.resolved && (
                    <Badge variant="success">
                      Resolved
                    </Badge>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Compliance Status */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-4">Compliance Status</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-medium">SOC 2 Type II</span>
            </div>
            <p className="text-sm text-gray-400">Certified and compliant</p>
          </div>
          
          <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-medium">Data Encryption</span>
            </div>
            <p className="text-sm text-gray-400">AES-256 at rest and in transit</p>
          </div>
          
          <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-medium">Access Controls</span>
            </div>
            <p className="text-sm text-gray-400">Role-based permissions active</p>
          </div>
          
          <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-medium">Audit Logging</span>
            </div>
            <p className="text-sm text-gray-400">All activities tracked</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SecurityMonitor
