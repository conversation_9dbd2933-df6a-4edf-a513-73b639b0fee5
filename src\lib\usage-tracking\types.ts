/**
 * Usage Tracking System Types
 * 
 * This file defines the core types for the usage tracking system,
 * which enables real-time tracking of service usage.
 */

// Usage metric types
export type MetricType = 'time' | 'api_calls' | 'resources' | 'custom';

// Usage event types
export type UsageEventType = 'session_start' | 'session_end' | 'activity' | 'api_call' | 'resource_consumption';

// Usage event
export interface UsageEvent {
  id: string;
  userId: string;
  serviceId: string;
  sessionId: string;
  timestamp: Date;
  type: UsageEventType;
  data: {
    timeSpentSeconds?: number;
    apiCallsMade?: number;
    resourcesConsumed?: number;
    customMetric?: number;
    details?: any;
  };
}

// Usage summary
export interface UsageSummary {
  userId: string;
  serviceId: string;
  period: 'day' | 'week' | 'month' | 'all';
  startDate: Date;
  endDate: Date;
  metrics: {
    timeSpentSeconds: number;
    apiCallsMade: number;
    resourcesConsumed: number;
    customMetric?: number;
    creditsUsed: number;
  };
}

// Service usage configuration
export interface ServiceUsageConfig {
  serviceId: string;
  metricType: MetricType;
  unitName: string;
  costPerUnit: number;
  minimumUsage?: number;
}

// User usage limits
export interface UserUsageLimits {
  userId: string;
  dailyLimit?: number;
  weeklyLimit?: number;
  monthlyLimit?: number;
  serviceLimits?: {
    serviceId: string;
    dailyLimit?: number;
    weeklyLimit?: number;
    monthlyLimit?: number;
  }[];
}

// Usage alert
export interface UsageAlert {
  id: string;
  userId: string;
  serviceId?: string;
  timestamp: Date;
  type: 'limit_approaching' | 'limit_reached' | 'unusual_activity';
  threshold: number;
  currentUsage: number;
  message: string;
  acknowledged: boolean;
}

// Usage report
export interface UsageReport {
  userId: string;
  period: 'day' | 'week' | 'month';
  date: Date;
  services: {
    serviceId: string;
    serviceName: string;
    timeSpentSeconds: number;
    apiCallsMade: number;
    resourcesConsumed: number;
    customMetric?: number;
    creditsUsed: number;
  }[];
  totalCreditsUsed: number;
  comparisonWithPrevious?: {
    percentageChange: number;
    previousTotal: number;
  };
}
