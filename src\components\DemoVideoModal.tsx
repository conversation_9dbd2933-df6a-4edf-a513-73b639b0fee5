import React from 'react';
import { X } from 'lucide-react';

interface DemoVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DemoVideoModal: React.FC<DemoVideoModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in">
      <div className="bg-dark-900 border border-white/10 rounded-xl w-full max-w-4xl overflow-hidden relative">
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 bg-black/40 hover:bg-black/60 rounded-full p-2 text-white/80 hover:text-white transition-all z-10"
        >
          <X className="w-5 h-5" />
        </button>
        
        <div className="aspect-video w-full">
          <iframe 
            className="w-full h-full"
            src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1" 
            title="Firenest Demo Video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        </div>
        
        <div className="p-6 bg-gradient-to-b from-dark-900/90 to-dark-900">
          <h3 className="text-xl font-bold mb-2">Firenest Platform Demo</h3>
          <p className="text-white/70">
            See how Firenest can transform your startup's AI capabilities while reducing costs.
            This demo showcases our credit-based system and the various AI tools available on our platform.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DemoVideoModal;
