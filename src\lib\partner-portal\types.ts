/**
 * Partner Portal Types
 *
 * This file contains types for the partner portal.
 */

// Partner account status
export type PartnerStatus = 'pending' | 'active' | 'suspended';

// Tool status
export type ToolStatus = 'draft' | 'pending_review' | 'active' | 'suspended';

// Authentication methods
export type PartnerAuthMethod = 'oauth' | 'oidc' | 'api_key' | 'credentials' | 'ip_based';

// Partner account
export interface PartnerAccount {
  id: string;
  name: string;
  email: string;
  company: string;
  website?: string;
  logoUrl?: string;
  description?: string;
  status: PartnerStatus;
  apiKey?: string;
  createdAt: string;
  updatedAt: string;
}

// Partner tool
export interface PartnerTool {
  id: string;
  partnerId: string;
  name: string;
  description?: string;
  longDescription?: string;
  logoUrl?: string;
  websiteUrl?: string;
  category?: string;
  tags?: string[];
  features?: {
    name: string;
    description: string;
  }[];
  pricing?: {
    model: string;
    price?: number;
    currency?: string;
    billingPeriod?: string;
  };
  status: ToolStatus;
  createdAt: string;
  updatedAt: string;
}

// OAuth configuration
export interface OAuthConfig {
  clientId: string;
  clientSecret?: string;
  authorizationUrl: string;
  tokenUrl: string;
  redirectUrl: string;
  scope: string[];
  responseType: 'code' | 'token';
}

// OIDC configuration
export interface OIDCConfig {
  clientId: string;
  clientSecret?: string;
  issuer: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  jwksUrl: string;
  redirectUrl: string;
  scope: string[];
  responseType: 'code' | 'token' | 'id_token';
  // Advanced security options
  pkceEnabled?: boolean;
  nonceValidationEnabled?: boolean;
  codeChallengeMethod?: 'S256' | 'plain';
  // Discovery options
  discoveryUrl?: string;
  discoveryEnabled?: boolean;
}

// API key configuration
export interface ApiKeyConfig {
  headerName: string;
  queryParamName?: string;
  isBearer?: boolean;
}

// Credentials configuration
export interface CredentialsConfig {
  loginUrl: string;
  usernameField: string;
  passwordField: string;
  cookiesToCapture: string[];
}

// IP-based configuration
export interface IpBasedConfig {
  allowedIps: string[];
  ipHeaderName?: string;
}

// Webhook configuration
export interface WebhookConfig {
  id?: string;
  toolId: string;
  webhookUrl: string;
  secret?: string;
  enabledEvents: {
    sessionStart: boolean;
    sessionEnd: boolean;
    featureUse: boolean;
    creditConsume: boolean;
    creditLow: boolean;
    subscriptionChange: boolean;
  };
  retryEnabled: boolean;
  maxRetries?: number;
  createdAt?: string;
  updatedAt?: string;
}

// Integration configuration
export interface IntegrationConfig {
  id: string;
  toolId: string;
  authMethod: PartnerAuthMethod;
  configData: OAuthConfig | OIDCConfig | ApiKeyConfig | CredentialsConfig | IpBasedConfig;
  createdAt: string;
  updatedAt: string;
}

// Code snippet
export interface CodeSnippet {
  id: string;
  toolId: string;
  platform: string;
  codeSnippet: string;
  createdAt: string;
  updatedAt: string;
}

// API log
export interface PartnerApiLog {
  id: string;
  partnerId: string;
  endpoint: string;
  method: string;
  statusCode: number;
  requestData?: any;
  responseData?: any;
  createdAt: string;
}
