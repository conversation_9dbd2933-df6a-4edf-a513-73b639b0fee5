import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { Mail, KeyRound } from 'lucide-react';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSendingMagicLink, setIsSendingMagicLink] = useState(false);
  const [error, setError] = useState<string>('');
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const [activeTab, setActiveTab] = useState('password');
  const router = useRouter();
  const { returnUrl } = router.query;

  // Check if user is already logged in
  useEffect(() => {
    const checkUser = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        if (returnUrl && typeof returnUrl === 'string') {
          router.push(returnUrl);
        } else {
          router.push('/dashboard');
        }
      }
    };
    
    checkUser();
  }, [router, returnUrl]);

  const handleMagicLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email) {
      setError('Email is required');
      return;
    }

    setIsSendingMagicLink(true);

    try {
      console.log('Attempting to send magic link to:', email);
      
      const redirectTo = typeof window !== 'undefined' 
        ? `${window.location.origin}/auth/callback`
        : undefined;
        
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: redirectTo,
        },
      });

      if (error) {
        console.error('Magic link failed:', error);
        setError(error.message || 'Failed to send magic link');
      } else {
        setMagicLinkSent(true);
      }
    } catch (err) {
      console.error('Unexpected error sending magic link:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSendingMagicLink(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email || !password) {
      setError('Email and password are required');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Attempting to login with:', email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Login failed:', error);
        setError(error.message || 'Login failed. Please check your credentials.');
      } else if (data.user) {
        console.log('Login successful, redirecting...');
        if (returnUrl && typeof returnUrl === 'string') {
          router.push(returnUrl);
        } else {
          router.push('/dashboard');
        }
      }
    } catch (err) {
      console.error('Unexpected error during login:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-900">
      <div className="w-full max-w-md p-6 bg-gray-800 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-white mb-2">Welcome Back</h1>
        <p className="text-center text-gray-400 mb-6">Log in to access your Firenest dashboard</p>
        
        <div className="mb-6">
          <div className="flex border-b border-gray-700">
            <button
              className={`flex items-center gap-2 px-4 py-2 ${
                activeTab === 'password' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-400'
              }`}
              onClick={() => setActiveTab('password')}
            >
              <KeyRound className="h-4 w-4" />
              Password
            </button>
            <button
              className={`flex items-center gap-2 px-4 py-2 ${
                activeTab === 'magic-link' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-400'
              }`}
              onClick={() => setActiveTab('magic-link')}
            >
              <Mail className="h-4 w-4" />
              Magic Link
            </button>
          </div>
        </div>

        {activeTab === 'password' && (
          <form onSubmit={handlePasswordSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-3 mb-4 rounded">
                {error}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                  Password
                </label>
                <Link href="/forgot-password" className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
                  Forgot password?
                </Link>
              </div>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                required
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Logging in...' : 'Log In'}
            </button>
          </form>
        )}

        {activeTab === 'magic-link' && (
          <>
            {magicLinkSent ? (
              <div className="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-6 text-center rounded">
                <Mail className="h-12 w-12 mx-auto mb-4 text-green-400" />
                <h3 className="text-lg font-medium mb-2">Magic Link Sent!</h3>
                <p>We've sent a login link to <strong>{email}</strong></p>
                <p className="mt-4 text-sm">Check your email and click the link to sign in.</p>
                <button
                  type="button"
                  className="mt-4 px-4 py-2 border border-gray-500 rounded-md hover:bg-gray-700 text-gray-300 transition-colors"
                  onClick={() => setMagicLinkSent(false)}
                >
                  Send another link
                </button>
              </div>
            ) : (
              <form onSubmit={handleMagicLinkSubmit} className="space-y-4">
                {error && (
                  <div className="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-3 mb-4 rounded">
                    {error}
                  </div>
                )}

                <div>
                  <label htmlFor="magic-email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    id="magic-email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSendingMagicLink}
                  className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSendingMagicLink ? 'Sending link...' : 'Send Magic Link'}
                </button>

                <div className="bg-blue-500/20 border border-blue-500/50 text-blue-200 px-4 py-3 text-sm rounded">
                  <p>We'll send a secure login link to your email. No password needed!</p>
                </div>
              </form>
            )}
          </>
        )}

        <div className="text-center mt-6">
          <p className="text-gray-400 text-sm">
            Don't have an account?{' '}
            <Link href="/signup" className="text-blue-400 hover:text-blue-300 transition-colors">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
