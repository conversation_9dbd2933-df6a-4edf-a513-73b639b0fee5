/**
 * Run Test Environment
 * 
 * This script runs both the Firenest server and the test partner application.
 */

const { spawn } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Log with timestamp and color
function log(message, color = colors.reset) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${color}${message}${colors.reset}`);
}

// Run a command in a new process
function runCommand(command, args, options = {}) {
  const proc = spawn(command, args, {
    stdio: 'pipe',
    ...options
  });
  
  const { name = command } = options;
  
  proc.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        log(`[${name}] ${line}`, options.color || colors.reset);
      }
    });
  });
  
  proc.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        log(`[${name}] ${line}`, colors.red);
      }
    });
  });
  
  proc.on('close', (code) => {
    if (code === 0) {
      log(`[${name}] Process exited successfully`, colors.green);
    } else {
      log(`[${name}] Process exited with code ${code}`, colors.red);
    }
  });
  
  proc.on('error', (err) => {
    log(`[${name}] Error: ${err.message}`, colors.red);
  });
  
  return proc;
}

// Main function
async function main() {
  log('Starting test environment...', colors.bright + colors.blue);
  

  
  // Start Firenest server
  log('Starting Firenest server...', colors.green);
  const firenestServer = runCommand('npm', ['run', 'dev'], {
    name: 'Firenest',
    color: colors.green
  });
  
  // Start test partner application
  log('Starting test partner application...', colors.cyan);
  const testPartner = runCommand('node', ['test-partner/index.js'], {
    name: 'Partner',
    color: colors.cyan
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    log('Shutting down test environment...', colors.bright + colors.blue);
    firenestServer.kill();
    testPartner.kill();
    process.exit(0);
  });
  
  // Log instructions
  setTimeout(() => {
    log('', colors.bright);
    log('Test environment is running!', colors.bright + colors.blue);
    log('', colors.bright);
    log('Firenest server: http://localhost:3333', colors.green);
    log('Test partner app: http://localhost:3001', colors.cyan);
    log('', colors.bright);
    log('Press Ctrl+C to shut down the test environment', colors.bright);
  }, 5000);
}

// Run the main function
main().catch(error => {
  log(`Error: ${error.message}`, colors.red);
  process.exit(1);
});
