import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { createPartnerTool } from '@/lib/partner-portal/api';
import { PartnerTool } from '@/lib/partner-portal/types';
import { ArrowLeft, ArrowRight, Check, Info, Layers, PlusCircle, Target, X } from 'lucide-react';

// Tool categories
const TOOL_CATEGORIES = [
  'AI Assistant',
  'Image Generation',
  'Text Generation',
  'Code Assistant',
  'Data Analysis',
  'Audio Processing',
  'Video Generation',
  'Translation',
  'Summarization',
  'Research Assistant',
  'Other'
];

interface AddToolModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddToolModal: React.FC<AddToolModalProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { partner, refreshToolsData } = usePartner();

  // Form state
  const [step, setStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [longDescription, setLongDescription] = useState('');
  const [category, setCategory] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  // Error state
  const [nameError, setNameError] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const [categoryError, setCategoryError] = useState('');
  const [websiteUrlError, setWebsiteUrlError] = useState('');
  const [longDescriptionError, setLongDescriptionError] = useState('');

  // Reset form
  const resetForm = () => {
    setStep(0);
    setName('');
    setDescription('');
    setLongDescription('');
    setCategory('');
    setWebsiteUrl('');
    setTags([]);
    setTagInput('');
    setNameError('');
    setDescriptionError('');
    setCategoryError('');
    setWebsiteUrlError('');
    setLongDescriptionError('');
  };

  // Handle close
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Validate step
  const validateStep = (currentStep: number): boolean => {
    let isValid = true;

    if (currentStep === 0) {
      // Validate name
      if (!name.trim()) {
        setNameError('Tool name is required');
        isValid = false;
      } else if (name.length < 3) {
        setNameError('Tool name must be at least 3 characters');
        isValid = false;
      } else {
        setNameError('');
      }

      // Validate description
      if (!description.trim()) {
        setDescriptionError('Short description is required');
        isValid = false;
      } else if (description.length < 10) {
        setDescriptionError('Description must be at least 10 characters');
        isValid = false;
      } else if (description.length > 150) {
        setDescriptionError('Description must be less than 150 characters');
        isValid = false;
      } else {
        setDescriptionError('');
      }
    } else if (currentStep === 1) {
      // Validate category
      if (!category) {
        setCategoryError('Category is required');
        isValid = false;
      } else {
        setCategoryError('');
      }

      // Validate website URL
      if (websiteUrl && !isValidUrl(websiteUrl)) {
        setWebsiteUrlError('Please enter a valid URL');
        isValid = false;
      } else {
        setWebsiteUrlError('');
      }
    } else if (currentStep === 2) {
      // Validate long description
      if (!longDescription.trim()) {
        setLongDescriptionError('Detailed description is required');
        isValid = false;
      } else if (longDescription.length < 50) {
        setLongDescriptionError('Detailed description must be at least 50 characters');
        isValid = false;
      } else {
        setLongDescriptionError('');
      }
    }

    return isValid;
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  // Handle back
  const handleBack = () => {
    setStep(step - 1);
  };

  // Add tag
  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  // Remove tag
  const removeTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  // Check if URL is valid
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Create tool
  const handleCreateTool = async () => {
    if (!partner) return;

    // Validate all steps
    for (let i = 0; i <= 2; i++) {
      if (!validateStep(i)) {
        setStep(i);
        return;
      }
    }

    setIsCreating(true);

    try {
      const newTool = await createPartnerTool(
        partner.id,
        name,
        description,
        longDescription,
        category,
        websiteUrl
      );

      if (!newTool) {
        throw new Error('Failed to create tool');
      }

      await refreshToolsData();
      notify.success('Tool created successfully');
      handleClose();

      // Navigate to tool management page with setup tab active
      navigate(`/partner/tools/${newTool.id}?tab=setup`);
    } catch (error: any) {
      console.error('Error creating tool:', error);
      notify.error(error.message || 'Failed to create tool');
    } finally {
      setIsCreating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Modal content */}
      <div className="relative z-10 w-full max-w-3xl mx-auto" onClick={(e) => e.stopPropagation()}>
        <Card className="firenest-card border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-2xl text-white flex items-center">
              <PlusCircle className="w-6 h-6 mr-2 text-fiery" />
              Add New AI Tool
            </CardTitle>
            <CardDescription>
              Complete the following steps to add your AI tool to Firenest
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Progress indicator */}
            <div className="flex items-center justify-between mb-8">
              {[
                { step: 0, icon: <Info className="w-5 h-5" />, label: "Basic Info" },
                { step: 1, icon: <Layers className="w-5 h-5" />, label: "Details" },
                { step: 2, icon: <Target className="w-5 h-5" />, label: "Description" }
              ].map((item, index) => (
                <React.Fragment key={index}>
                  {/* Step indicator */}
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        step === item.step
                          ? "bg-fiery text-white"
                          : step > item.step
                            ? "bg-green-500/20 text-green-400 border border-green-500/30"
                            : "bg-dark-800 text-white/60 border border-white/10"
                      }`}
                    >
                      {step > item.step ? <Check className="w-5 h-5" /> : item.icon}
                    </div>
                    <span
                      className={`mt-2 text-sm ${
                        step === item.step
                          ? "text-white font-medium"
                          : step > item.step
                            ? "text-green-400"
                            : "text-white/60"
                      }`}
                    >
                      {item.label}
                    </span>
                  </div>

                  {/* Connector line */}
                  {index < 2 && (
                    <div
                      className={`h-px w-full max-w-[100px] ${
                        step > index ? "bg-green-500/50" : "bg-white/10"
                      }`}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>

            {/* Step 1: Basic Info */}
            {step === 0 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="name" className="text-base font-medium">Tool Name</Label>
                    {nameError && <p className="text-red-500 text-sm">{nameError}</p>}
                  </div>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter your AI tool's name"
                    className={nameError ? "border-red-500" : ""}
                  />
                  <p className="text-white/60 text-sm">
                    Choose a clear, descriptive name that reflects your tool's functionality.
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="description" className="text-base font-medium">Short Description</Label>
                    {descriptionError && <p className="text-red-500 text-sm">{descriptionError}</p>}
                  </div>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Briefly describe what your tool does (max 150 characters)"
                    maxLength={150}
                    rows={3}
                    className={descriptionError ? "border-red-500" : ""}
                  />
                  <div className="flex justify-between">
                    <p className="text-white/60 text-sm">
                      This will appear in search results and tool cards.
                    </p>
                    <p className="text-white/60 text-sm">
                      {description.length}/150
                    </p>
                  </div>
                </div>

                <div className="pt-4 flex justify-end">
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      className="border-white/10 hover:bg-white/5"
                      onClick={handleClose}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                      onClick={handleNext}
                    >
                      Next Step
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Details */}
            {step === 1 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="category" className="text-base font-medium">Category</Label>
                    {categoryError && <p className="text-red-500 text-sm">{categoryError}</p>}
                  </div>
                  <Select
                    value={category}
                    onValueChange={setCategory}
                  >
                    <SelectTrigger className={categoryError ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {TOOL_CATEGORIES.map((cat) => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-white/60 text-sm">
                    Categorizing your tool helps users find it more easily.
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="websiteUrl" className="text-base font-medium">Website URL</Label>
                    {websiteUrlError && <p className="text-red-500 text-sm">{websiteUrlError}</p>}
                  </div>
                  <Input
                    id="websiteUrl"
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    placeholder="https://yourtool.com"
                    type="url"
                    className={websiteUrlError ? "border-red-500" : ""}
                  />
                  <p className="text-white/60 text-sm">
                    The URL where users will be redirected when they launch your tool.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags" className="text-base font-medium">Tags (Optional)</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="tags"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add a tag"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag();
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addTag}
                      className="border-white/10 hover:bg-white/5"
                    >
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tags.map((tag) => (
                      <div
                        key={tag}
                        className="bg-dark-800 text-white/80 px-3 py-1 rounded-full text-sm flex items-center"
                      >
                        <span>{tag}</span>
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-2 text-white/60 hover:text-white"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <p className="text-white/60 text-sm">
                    Tags help users discover your tool when searching.
                  </p>
                </div>

                <div className="pt-4 flex justify-between">
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={handleBack}
                  >
                    <ArrowLeft className="mr-2 w-4 h-4" />
                    Back
                  </Button>
                  <Button
                    className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                    onClick={handleNext}
                  >
                    Next Step
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Step 3: Long Description */}
            {step === 2 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="longDescription" className="text-base font-medium">Detailed Description</Label>
                    {longDescriptionError && <p className="text-red-500 text-sm">{longDescriptionError}</p>}
                  </div>
                  <Textarea
                    id="longDescription"
                    value={longDescription}
                    onChange={(e) => setLongDescription(e.target.value)}
                    placeholder="Provide a comprehensive description of your tool, its features, capabilities, and use cases."
                    rows={10}
                    className={longDescriptionError ? "border-red-500" : ""}
                  />
                  <div className="flex justify-between">
                    <p className="text-white/60 text-sm">
                      This will be displayed on your tool's detail page.
                    </p>
                    <p className="text-white/60 text-sm">
                      {longDescription.length} characters
                    </p>
                  </div>
                </div>

                <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                  <div className="flex items-start">
                    <div className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        <circle cx="12" cy="12" r="5"></circle>
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-1">Writing Tips</h4>
                      <ul className="text-white/70 text-sm space-y-1 list-disc pl-4">
                        <li>Clearly explain what problems your tool solves</li>
                        <li>Highlight key features and capabilities</li>
                        <li>Include example use cases</li>
                        <li>Mention any limitations or requirements</li>
                        <li>Use simple, clear language</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="pt-4 flex justify-between">
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={handleBack}
                  >
                    <ArrowLeft className="mr-2 w-4 h-4" />
                    Back
                  </Button>
                  <Button
                    className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                    onClick={handleCreateTool}
                    disabled={isCreating}
                  >
                    {isCreating ? (
                      <>
                        <Loading size="sm" className="mr-2" />
                        Creating Tool...
                      </>
                    ) : (
                      <>
                        <Check className="mr-2 w-4 h-4" />
                        Create Tool
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
