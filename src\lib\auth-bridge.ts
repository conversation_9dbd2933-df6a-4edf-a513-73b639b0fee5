// Mock authentication bridge for service sessions

interface ServiceSession {
  id: string;
  serviceId: string;
  serviceName: string;
  createdAt: string;
  expiresAt: string;
  status: 'active' | 'expired';
}

// Mock hook for service sessions
export const useServiceSessions = () => {
  // Mock session data
  const sessions: ServiceSession[] = [
    {
      id: 'sess_1',
      serviceId: 'chatgpt',
      serviceName: 'ChatGPT',
      createdAt: new Date(Date.now() - 3600000).toISOString(),
      expiresAt: new Date(Date.now() + 86400000).toISOString(),
      status: 'active'
    }
  ];

  // Mock refresh function
  const refreshSessions = async () => {
    console.log('Refreshing sessions...');
    return { success: true };
  };

  return { sessions, refreshSessions };
};
