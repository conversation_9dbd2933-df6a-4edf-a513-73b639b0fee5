/**
 * Workspaces Management Page
 * SOC 2 Alignment: CC6.1 (Logical Access), CC6.2 (Access Control)
 */

import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate, useParams } from 'react-router-dom'
import {
  Building2,
  Plus,
  Users,
  Settings,
  MoreVertical,
  Edit,
  Trash2,
  UserPlus,
  Crown,
  Shield,
  Search,
  Filter,
  ArrowLeft,
  FolderOpen,
  Activity,
  Calendar,
  Mail
} from 'lucide-react'
import { workspacesApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate } from '@/lib/utils'
import toast from 'react-hot-toast'

// Create Workspace Modal Component
interface CreateWorkspaceModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

function CreateWorkspaceModal({ isOpen, onClose, onSuccess }: CreateWorkspaceModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) {
      toast.error('Workspace name is required')
      return
    }

    setIsSubmitting(true)
    try {
      await workspacesApi.create(formData)
      toast.success('Workspace created successfully!')
      onSuccess()
      onClose()
      setFormData({ name: '', description: '' })
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to create workspace')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-sidebar-background border border-white/10 rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-white mb-4">Create New Workspace</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Workspace Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              placeholder="Enter workspace name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white placeholder-gray-400 h-24 resize-none"
              placeholder="Describe your workspace"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="bg-fiery hover:bg-fiery/90">
              {isSubmitting ? <LoadingSpinner size="sm" /> : 'Create Workspace'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Workspace Card Component
interface WorkspaceCardProps {
  workspace: any
  onDelete: () => void
  onClick: () => void
}

function WorkspaceCard({ workspace, onDelete, onClick }: WorkspaceCardProps) {
  const [showMenu, setShowMenu] = useState(false)

  return (
    <div className="firenest-card hover:bg-white/5 transition-colors cursor-pointer relative">
      <div onClick={onClick} className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-fiery/20 rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-fiery" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">{workspace.name}</h3>
              <p className="text-sm text-gray-400">
                {workspace.is_owner ? 'Owner' : 'Member'}
              </p>
            </div>
          </div>

          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                setShowMenu(!showMenu)
              }}
            >
              <MoreVertical className="w-4 h-4" />
            </Button>

            {showMenu && (
              <div className="absolute right-0 top-8 bg-sidebar-background border border-white/10 rounded-lg shadow-lg z-10 min-w-[150px]">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onClick()
                    setShowMenu(false)
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white/5 flex items-center"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </button>
                {workspace.is_owner && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onDelete()
                      setShowMenu(false)
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-red-400 hover:bg-red-500/10 flex items-center"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {workspace.description && (
          <p className="text-gray-400 text-sm mb-4 line-clamp-2">
            {workspace.description}
          </p>
        )}

        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-gray-400">
              <Users className="w-4 h-4 mr-1" />
              <span>{workspace.member_count || 1}</span>
            </div>
            <div className="flex items-center text-gray-400">
              <FolderOpen className="w-4 h-4 mr-1" />
              <span>{workspace.project_count || 0}</span>
            </div>
          </div>
          <span className="text-gray-500">
            {formatDate(workspace.updated_at)}
          </span>
        </div>
      </div>
    </div>
  )
}

export function WorkspacesPage() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch workspaces
  const { data: workspaces, isLoading, error } = useQuery({
    queryKey: ['workspaces'],
    queryFn: async () => {
      const response = await workspacesApi.list({ limit: 50 })
      return response.data
    }
  })

  // Delete workspace mutation
  const deleteMutation = useMutation({
    mutationFn: async (workspaceId: string) => {
      const response = await workspacesApi.delete(workspaceId)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] })
      toast.success('Workspace deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete workspace')
    }
  })

  const handleCreateSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['workspaces'] })
  }

  const handleDeleteWorkspace = async (workspaceId: string, workspaceName: string) => {
    if (window.confirm(`Are you sure you want to delete "${workspaceName}"? This action cannot be undone.`)) {
      deleteMutation.mutate(workspaceId)
    }
  }

  // Filter workspaces based on search
  const filteredWorkspaces = workspaces?.data?.filter((workspace: any) =>
    workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workspace.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="main-content">
        <div className="text-center py-12">
          <Building2 className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Error Loading Workspaces</h2>
          <p className="text-gray-400 mb-4">Failed to load workspaces. Please try again.</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Workspaces</h1>
          <p className="text-gray-400 mt-1">
            Organize your pricing projects into collaborative workspaces
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="bg-fiery hover:bg-fiery/90"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Workspace
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search workspaces..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-sidebar-background border border-white/10 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Workspaces Grid */}
      {filteredWorkspaces.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">
            {searchTerm ? 'No workspaces found' : 'No workspaces yet'}
          </h3>
          <p className="text-gray-400 mb-6">
            {searchTerm
              ? 'Try adjusting your search terms'
              : 'Create your first workspace to get started with pricing intelligence'
            }
          </p>
          {!searchTerm && (
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-fiery hover:bg-fiery/90"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Workspace
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkspaces.map((workspace: any) => (
            <WorkspaceCard
              key={workspace.id}
              workspace={workspace}
              onDelete={() => handleDeleteWorkspace(workspace.id, workspace.name)}
              onClick={() => navigate(`/workspaces/${workspace.id}`)}
            />
          ))}
        </div>
      )}

      {/* Create Workspace Modal */}
      <CreateWorkspaceModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
      />
    </div>
  )
}

export function WorkspaceDetailPage() {
  const { workspaceId } = useParams<{ workspaceId: string }>()
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  // Fetch workspace details
  const { data: workspace, isLoading, error } = useQuery({
    queryKey: ['workspace', workspaceId],
    queryFn: async () => {
      if (!workspaceId) throw new Error('Workspace ID is required')
      const response = await workspacesApi.get(workspaceId)
      return response.data.data
    },
    enabled: !!workspaceId
  })

  // For now, we'll mock members data since the API endpoint might not be implemented
  const members = {
    data: [
      {
        id: '1',
        user_name: 'Demo User',
        user_email: '<EMAIL>',
        role: 'OWNER'
      }
    ]
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !workspace) {
    return (
      <div className="main-content">
        <div className="text-center py-12">
          <Building2 className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Workspace Not Found</h2>
          <p className="text-gray-400 mb-4">The workspace you're looking for doesn't exist or you don't have access to it.</p>
          <Button onClick={() => navigate('/workspaces')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Workspaces
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/workspaces')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-white">{workspace.name}</h1>
            <p className="text-gray-400 mt-1">{workspace.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <UserPlus className="w-4 h-4 mr-2" />
            Invite Members
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Workspace Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <FolderOpen className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{workspace.project_count || 0}</p>
              <p className="text-gray-400">Projects</p>
            </div>
          </div>
        </div>

        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{members?.data?.length || 1}</p>
              <p className="text-gray-400">Members</p>
            </div>
          </div>
        </div>

        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">Active</p>
              <p className="text-gray-400">Status</p>
            </div>
          </div>
        </div>
      </div>

      {/* Members Section */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Members</h3>
          <Button variant="outline" size="sm">
            <UserPlus className="w-4 h-4 mr-2" />
            Invite
          </Button>
        </div>

        <div className="space-y-4">
          {members?.data?.map((member: any) => (
            <div key={member.id} className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-fiery/20 rounded-full flex items-center justify-center">
                  <span className="text-fiery font-medium">
                    {member.user_name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
                <div>
                  <p className="text-white font-medium">{member.user_name || 'Unknown User'}</p>
                  <p className="text-sm text-gray-400">{member.user_email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge variant={member.role === 'OWNER' ? 'success' : 'info'}>
                  {member.role === 'OWNER' && <Crown className="w-3 h-3 mr-1" />}
                  {member.role}
                </Badge>
                {member.role !== 'OWNER' && (
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          )) || (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">No members found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
