# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=firenest_sandbox
DB_USERNAME=sandbox_admin
DB_PASSWORD=your_password
DB_SSL=false

# Authentication (Development)
JWT_SECRET=your-32-character-secret-key-here-dev-12345678
AUTH_PROVIDER_TYPE=development
AUTH_PROVIDER_DOMAIN=localhost
AUTH_PROVIDER_CLIENT_ID=dev-client-id
AUTH_PROVIDER_CLIENT_SECRET=dev-client-secret

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-dev
HASH_SALT=12

# Application
NODE_ENV=development
PORT=3001
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Logging
LOG_LEVEL=debug

# AWS (Optional - comment out if not using)
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# S3_BUCKET_NAME=your_s3_bucket
# SQS_QUEUE_URL=your_sqs_queue_url
# SQS_DLQ_URL=your_dlq_url

# Monitoring (Optional)
# CLOUDWATCH_LOG_GROUP=/aws/ecs/firenest-sandbox
# SENTRY_DSN=your_sentry_dsn
