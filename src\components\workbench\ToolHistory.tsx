import React, { useState } from 'react';
import {
  Clock,
  Search,
  Filter,
  Download,
  MoreHorizontal,
  Trash2,
  Repeat,
  ExternalLink,
  MessageSquare,
  Image,
  Headphones,
  Code,
  FileText,
  Zap,
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { notify } from '@/components/ui/notification-system';

interface ToolHistoryProps {
  tools: any[];
  onLaunchTool: (toolId: string) => void;
}

interface HistoryEntry {
  id: string;
  toolId: string;
  prompt: string;
  result: string;
  status: 'success' | 'error' | 'partial';
  timestamp: string;
  duration: string;
  credits: number;
  metadata?: {
    tokens?: {
      input: number;
      output: number;
    };
    settings?: any;
  };
}

const ToolHistory: React.FC<ToolHistoryProps> = ({ tools, onLaunchTool }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('all-time');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [expandedEntries, setExpandedEntries] = useState<string[]>([]);

  // Mock history data
  const historyEntries: HistoryEntry[] = [
    {
      id: 'hist-1',
      toolId: 'chatgpt',
      prompt: 'Write a professional email to a client explaining a project delay',
      result: 'Dear [Client Name],\n\nI hope this email finds you well. I am writing to inform you about a slight delay in our project timeline...',
      status: 'success',
      timestamp: '2023-11-25T14:30:00Z',
      duration: '3.2s',
      credits: 2,
      metadata: {
        tokens: {
          input: 12,
          output: 156
        },
        settings: {
          temperature: 0.7,
          maxTokens: 500
        }
      }
    },
    {
      id: 'hist-2',
      toolId: 'dalle',
      prompt: 'A futuristic cityscape with flying cars and neon lights, digital art style',
      result: 'https://example.com/image1.jpg',
      status: 'success',
      timestamp: '2023-11-24T10:15:00Z',
      duration: '5.8s',
      credits: 5,
      metadata: {
        settings: {
          size: '1024x1024',
          quality: 'standard'
        }
      }
    },
    {
      id: 'hist-3',
      toolId: 'claude',
      prompt: 'Explain quantum computing to a high school student',
      result: 'Quantum computing is a fascinating field that uses the principles of quantum mechanics to process information...',
      status: 'success',
      timestamp: '2023-11-23T16:45:00Z',
      duration: '2.9s',
      credits: 3,
      metadata: {
        tokens: {
          input: 8,
          output: 245
        }
      }
    },
    {
      id: 'hist-4',
      toolId: 'midjourney',
      prompt: 'A photorealistic portrait of an elderly wise man with deep wrinkles and kind eyes',
      result: 'https://example.com/image2.jpg',
      status: 'partial',
      timestamp: '2023-11-22T09:20:00Z',
      duration: '8.1s',
      credits: 10
    },
    {
      id: 'hist-5',
      toolId: 'chatgpt',
      prompt: 'Generate a Python script to analyze stock market data and predict trends',
      result: 'Error: The request exceeded the maximum context length',
      status: 'error',
      timestamp: '2023-11-21T13:10:00Z',
      duration: '1.5s',
      credits: 0
    }
  ];

  // Get appropriate icon for tool
  const getToolIcon = (toolId: string) => {
    const tool = tools.find(t => t.id === toolId);
    if (!tool) return <Zap className="h-5 w-5 text-fiery" />;

    switch(tool.category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch(status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-400" />;
      case 'partial':
        return <AlertCircle className="h-4 w-4 text-amber-400" />;
      default:
        return <Clock className="h-4 w-4 text-white/70" />;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch(status) {
      case 'success':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/20">Success</Badge>;
      case 'error':
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/20">Error</Badge>;
      case 'partial':
        return <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/20">Partial</Badge>;
      default:
        return <Badge className="bg-white/10 text-white/70 border-white/20">Unknown</Badge>;
    }
  };

  // Filter history entries
  const filteredEntries = historyEntries.filter(entry => {
    // Filter by search query
    if (searchQuery && !entry.prompt.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Filter by tab
    if (activeTab !== 'all' && entry.status !== activeTab) {
      return false;
    }

    // Filter by status
    if (selectedStatus !== 'all' && entry.status !== selectedStatus) {
      return false;
    }

    // Filter by period
    if (selectedPeriod !== 'all-time') {
      const now = new Date();
      const entryDate = new Date(entry.timestamp);
      const diffDays = Math.floor((now.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24));

      switch(selectedPeriod) {
        case 'today':
          return diffDays < 1;
        case 'week':
          return diffDays < 7;
        case 'month':
          return diffDays < 30;
        default:
          return true;
      }
    }

    return true;
  });

  // Toggle expanded entry
  const toggleExpandEntry = (entryId: string) => {
    setExpandedEntries(prev => {
      if (prev.includes(entryId)) {
        return prev.filter(id => id !== entryId);
      } else {
        return [...prev, entryId];
      }
    });
  };

  // Handle retry
  const handleRetry = (entry: HistoryEntry) => {
    notify.success(`Retrying prompt with ${tools.find(t => t.id === entry.toolId)?.name}`, {
      duration: 3000
    });
    onLaunchTool(entry.toolId);
  };

  // Handle delete
  const handleDelete = (entryId: string) => {
    notify.success('History entry deleted', {
      duration: 3000
    });
  };

  // Handle export
  const handleExport = (format: string) => {
    notify.success(`Exporting history as ${format.toUpperCase()}`, {
      duration: 3000
    });
  };

  // Render history entry
  const renderHistoryEntry = (entry: HistoryEntry) => {
    const tool = tools.find(t => t.id === entry.toolId);
    const isExpanded = expandedEntries.includes(entry.id);

    return (
      <Card key={entry.id} className="firenest-card">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                {getToolIcon(entry.toolId)}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-white">{tool?.name || entry.toolId}</h3>
                  {getStatusBadge(entry.status)}
                </div>
                <p className="text-xs text-white/70">{formatDate(entry.timestamp)}</p>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white/70 hover:text-white hover:bg-white/5">
                  <MoreHorizontal className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="firenest-card border text-white">
                <DropdownMenuItem
                  className="hover:bg-white/5 cursor-pointer"
                  onClick={() => handleRetry(entry)}
                >
                  <Repeat className="h-4 w-4 mr-2" />
                  Retry Prompt
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="hover:bg-white/5 cursor-pointer"
                  onClick={() => {
                    navigator.clipboard.writeText(entry.prompt);
                    notify.success('Prompt copied to clipboard', { duration: 2000 });
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Copy Prompt
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="hover:bg-white/5 cursor-pointer"
                  onClick={() => {
                    navigator.clipboard.writeText(entry.result);
                    notify.success('Result copied to clipboard', { duration: 2000 });
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Copy Result
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="hover:bg-white/5 cursor-pointer text-red-400 hover:text-red-300"
                  onClick={() => handleDelete(entry.id)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Entry
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <h4 className="text-xs font-medium text-white/70 mb-1">Prompt:</h4>
            <p className="text-sm text-white/80 firenest-card p-2">
              {entry.prompt}
            </p>
          </div>

          {entry.status !== 'error' && (
            <div>
              <h4 className="text-xs font-medium text-white/70 mb-1">Result:</h4>
              <div className="text-sm text-white/80 firenest-card p-2 max-h-20 overflow-hidden relative">
                {entry.status === 'success' ? (
                  entry.result.startsWith('http') ? (
                    <div className="flex items-center justify-center h-16">
                      <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
                        <Image className="h-4 w-4 mr-2" />
                        View Image
                      </Button>
                    </div>
                  ) : (
                    <p className="whitespace-pre-line line-clamp-3">{entry.result}</p>
                  )
                ) : (
                  <p className="text-amber-400">{entry.result}</p>
                )}
                {!isExpanded && (
                  <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-dark-700 to-transparent"></div>
                )}
              </div>
            </div>
          )}

          {isExpanded && (
            <>
              {entry.status === 'error' && (
                <div>
                  <h4 className="text-xs font-medium text-white/70 mb-1">Error:</h4>
                  <p className="text-sm text-red-400 bg-red-500/10 p-2 rounded border border-red-500/20">
                    {entry.result}
                  </p>
                </div>
              )}

              <div className="pt-2 border-t border-white/10">
                <h4 className="text-xs font-medium text-white/70 mb-2">Details:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="firenest-card p-2">
                    <span className="text-white/50">Duration:</span>
                    <span className="text-white float-right">{entry.duration}</span>
                  </div>
                  <div className="firenest-card p-2">
                    <span className="text-white/50">Credits:</span>
                    <span className="text-white float-right">{entry.credits}</span>
                  </div>
                  {entry.metadata?.tokens && (
                    <>
                      <div className="firenest-card p-2">
                        <span className="text-white/50">Input Tokens:</span>
                        <span className="text-white float-right">{entry.metadata.tokens.input}</span>
                      </div>
                      <div className="firenest-card p-2">
                        <span className="text-white/50">Output Tokens:</span>
                        <span className="text-white float-right">{entry.metadata.tokens.output}</span>
                      </div>
                    </>
                  )}
                </div>

                {entry.metadata?.settings && (
                  <div className="mt-2">
                    <h4 className="text-xs font-medium text-white/70 mb-2">Settings Used:</h4>
                    <div className="firenest-card p-2">
                      <pre className="text-xs text-white/80 overflow-x-auto">
                        {JSON.stringify(entry.metadata.settings, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
        <CardFooter className="border-t border-white/10 pt-3 flex justify-between items-center">
          <div className="flex items-center gap-3 text-xs text-white/50">
            <div className="flex items-center gap-1">
              <Clock className="h-3.5 w-3.5" />
              <span>{entry.duration}</span>
            </div>
            <div className="flex items-center gap-1">
              <Zap className="h-3.5 w-3.5 text-fiery" />
              <span>{entry.credits} credits</span>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-white/70 hover:text-white hover:bg-white/5"
            onClick={() => toggleExpandEntry(entry.id)}
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" />
                Show Less
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                Show More
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-white">Tool Usage History</h2>
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </PopoverTrigger>
            <PopoverContent className="firenest-card border text-white w-48">
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start hover:bg-white/5"
                  onClick={() => handleExport('csv')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Export as CSV
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start hover:bg-white/5"
                  onClick={() => handleExport('json')}
                >
                  <Code className="h-4 w-4 mr-2" />
                  Export as JSON
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start hover:bg-white/5"
                  onClick={() => handleExport('pdf')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Export as PDF
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
          <Input
            type="search"
            placeholder="Search history..."
            className="pl-10 firenest-card focus:border-fiery/50 w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[130px] firenest-card">
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent className="firenest-card border text-white">
              <SelectItem value="all-time">All Time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            className="border-white/10 hover:bg-white/5"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="firenest-card">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="text-sm font-medium text-white mb-2">Status</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-all"
                      checked={selectedStatus === 'all'}
                      onCheckedChange={() => setSelectedStatus('all')}
                      className="data-[state=checked]:bg-fiery data-[state=checked]:border-fiery"
                    />
                    <label htmlFor="status-all" className="text-sm text-white cursor-pointer">
                      All
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-success"
                      checked={selectedStatus === 'success'}
                      onCheckedChange={() => setSelectedStatus('success')}
                      className="data-[state=checked]:bg-fiery data-[state=checked]:border-fiery"
                    />
                    <label htmlFor="status-success" className="text-sm text-white cursor-pointer">
                      Success
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-error"
                      checked={selectedStatus === 'error'}
                      onCheckedChange={() => setSelectedStatus('error')}
                      className="data-[state=checked]:bg-fiery data-[state=checked]:border-fiery"
                    />
                    <label htmlFor="status-error" className="text-sm text-white cursor-pointer">
                      Error
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-partial"
                      checked={selectedStatus === 'partial'}
                      onCheckedChange={() => setSelectedStatus('partial')}
                      className="data-[state=checked]:bg-fiery data-[state=checked]:border-fiery"
                    />
                    <label htmlFor="status-partial" className="text-sm text-white cursor-pointer">
                      Partial
                    </label>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-white mb-2">Tools</h3>
                <div className="space-y-2">
                  {tools.slice(0, 4).map(tool => (
                    <div key={tool.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`tool-${tool.id}`}
                        className="data-[state=checked]:bg-fiery data-[state=checked]:border-fiery"
                      />
                      <label htmlFor={`tool-${tool.id}`} className="text-sm text-white cursor-pointer">
                        {tool.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-white mb-2">Date Range</h3>
                <div className="space-y-3">
                  <div className="flex flex-col space-y-1">
                    <label htmlFor="date-from" className="text-xs text-white/70">
                      From
                    </label>
                    <Input
                      id="date-from"
                      type="date"
                      className="firenest-card focus:border-fiery/50"
                    />
                  </div>
                  <div className="flex flex-col space-y-1">
                    <label htmlFor="date-to" className="text-xs text-white/70">
                      To
                    </label>
                    <Input
                      id="date-to"
                      type="date"
                      className="firenest-card focus:border-fiery/50"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="firenest-card">
          <TabsTrigger value="all" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            All
          </TabsTrigger>
          <TabsTrigger value="success" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Success
          </TabsTrigger>
          <TabsTrigger value="error" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Errors
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredEntries.length > 0 ? (
            <div className="space-y-4">
              {filteredEntries.map(renderHistoryEntry)}
            </div>
          ) : (
            <Card className="firenest-card">
              <CardContent className="p-8 flex flex-col items-center justify-center">
                <div className="h-16 w-16 rounded-full bg-white/5 flex items-center justify-center mb-4">
                  <Clock className="h-8 w-8 text-white/30" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">No history entries found</h3>
                <p className="text-white/70 text-center max-w-md mb-4">
                  {searchQuery
                    ? `No results match your search for "${searchQuery}"`
                    : 'Your tool usage history will appear here after you use tools'
                  }
                </p>
                {searchQuery && (
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={() => setSearchQuery('')}
                  >
                    Clear Search
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ToolHistory;
