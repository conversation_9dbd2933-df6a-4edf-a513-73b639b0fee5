import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { saveApiKeyConnection } from '@/lib/connections';
import Modal from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Key, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ApiKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  tool: any;
  onRefresh: () => void;
}

const ApiKeyModal: React.FC<ApiKeyModalProps> = ({
  isOpen,
  onClose,
  tool,
  onRefresh
}) => {
  const { user } = useAuth();
  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('You must be logged in to connect to tools');
      return;
    }
    
    if (!apiKey.trim()) {
      setError('Please enter an API key');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      const success = await saveApiKeyConnection(user.id, tool.id, apiKey);
      
      if (success) {
        onRefresh();
        onClose();
      }
    } catch (error) {
      console.error('Error saving API key:', error);
      setError('Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setApiKey('');
    setError(null);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Connect to ${tool.name}`}
      size="md"
      className="firenest-card"
      contentClassName="bg-dark-900"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex items-center gap-3 p-4 bg-dark-800/50 rounded-lg border border-white/5">
          <div className="h-10 w-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
            <Key className="h-5 w-5 text-yellow-400" />
          </div>
          <div>
            <h3 className="font-medium text-white">API Key Authentication</h3>
            <p className="text-sm text-white/70">
              Enter your {tool.name} API key to connect your account
            </p>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="bg-red-500/10 border-red-500/20 text-red-400">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="apiKey" className="text-white">API Key</Label>
          <Input
            id="apiKey"
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="Enter your API key"
            className="bg-dark-800/50 border-white/10"
            autoComplete="off"
          />
          <p className="text-xs text-white/50">
            Your API key is securely stored and never shared with third parties
          </p>
        </div>

        <div className="flex justify-end gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="border-white/10 hover:bg-white/5"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-fiery hover:bg-fiery-600"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Connecting...
              </>
            ) : (
              'Connect'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default ApiKeyModal;
