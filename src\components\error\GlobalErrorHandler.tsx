import { useEffect } from 'react';
import { redirectToErrorDiagnostics, ErrorCategory, logError } from '@/lib/error-utils';
import { notify } from '@/components/ui/notification-system';

/**
 * GlobalErrorHandler component that sets up global event listeners for unhandled errors
 * and promise rejections, then redirects to the error diagnostic page.
 */
const GlobalErrorHandler: React.FC = () => {
  useEffect(() => {
    // Handler for uncaught errors
    const handleError = (event: ErrorEvent) => {
      event.preventDefault();
      
      const error = event.error || new Error(event.message);
      const errorMessage = error.message || 'Unknown error';
      
      // Log the error
      logError(error, null, undefined, ErrorCategory.UNKNOWN);
      
      // Show a toast notification
      notify.error('An unexpected error occurred', {
        title: 'Application Error',
        duration: 3000
      });
      
      // Redirect to error diagnostics page after a short delay
      setTimeout(() => {
        redirectToErrorDiagnostics(error, ErrorCategory.UNKNOWN);
      }, 1000);
    };
    
    // Handler for unhandled promise rejections
    const handleRejection = (event: PromiseRejectionEvent) => {
      event.preventDefault();
      
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      
      // Log the error
      logError(error, null, undefined, ErrorCategory.UNKNOWN);
      
      // Show a toast notification
      notify.error('An unhandled promise rejection occurred', {
        title: 'Application Error',
        duration: 3000
      });
      
      // Redirect to error diagnostics page after a short delay
      setTimeout(() => {
        redirectToErrorDiagnostics(error, ErrorCategory.UNKNOWN);
      }, 1000);
    };
    
    // Add event listeners
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleRejection);
    
    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, []);
  
  // This component doesn't render anything
  return null;
};

export default GlobalErrorHandler;
