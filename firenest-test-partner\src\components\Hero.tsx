import LoginSignup from './LoginSignup';

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-dark-900 py-20 md:py-32">
      {/* Background gradient */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute -top-24 -left-24 w-96 h-96 bg-primary rounded-full filter blur-3xl opacity-20"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-secondary rounded-full filter blur-3xl opacity-20"></div>
        <div className="absolute -bottom-24 -right-24 w-96 h-96 bg-primary rounded-full filter blur-3xl opacity-20"></div>
      </div>

      <div className="container relative z-10">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Left side - Content */}
          <div className="w-full lg:w-1/2 text-left">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-dark-700 border border-dark-500 mb-8">
              <span className="badge-primary mr-2">New</span>
              <span className="text-light text-sm">Introducing AtlasAI 2.0 with advanced features</span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              <span className="gradient-text">AI-Powered</span> Content Creation for Teams
            </h1>

            <p className="text-xl text-light-600 mb-8">
              Create, edit, and optimize content with our advanced AI tools. Save time and boost productivity across your entire organization.
            </p>

            <a href="#pricing" className="btn btn-outline text-lg px-8 py-4 inline-flex">
              View Pricing
            </a>

            <div className="mt-8">
              <div className="bg-dark-800 border border-dark-600 rounded-xl p-4 inline-flex items-center">
                <div className="flex -space-x-2 mr-4">
                  <img className="w-10 h-10 rounded-full border-2 border-dark-800" src="https://randomuser.me/api/portraits/women/32.jpg" alt="User" />
                  <img className="w-10 h-10 rounded-full border-2 border-dark-800" src="https://randomuser.me/api/portraits/men/44.jpg" alt="User" />
                  <img className="w-10 h-10 rounded-full border-2 border-dark-800" src="https://randomuser.me/api/portraits/women/56.jpg" alt="User" />
                  <div className="w-10 h-10 rounded-full border-2 border-dark-800 bg-dark-700 flex items-center justify-center text-light text-xs">+5k</div>
                </div>
                <div className="text-left">
                  <p className="text-light text-sm">Trusted by <span className="text-primary font-semibold">5,000+</span> teams</p>
                  <div className="flex items-center">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <svg key={star} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="text-light text-xs ml-1">4.9/5</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Login/Signup Form */}
          <div className="w-full lg:w-1/2">
            <LoginSignup />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
