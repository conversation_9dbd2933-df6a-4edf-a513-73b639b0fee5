import React, { useState } from 'react';
import { Flame, RefreshCw, Home, ChevronDown, ChevronUp, Copy, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { notify } from '@/components/ui/notification-system';
import { formatErrorStack, createDiagnosticReport } from '@/lib/error-utils';

interface ErrorPageProps {
  error: Error | null;
  errorInfo?: React.ErrorInfo | null;
  title?: string;
  message?: string;
  onReset?: () => void;
}

const ErrorPage: React.FC<ErrorPageProps> = ({
  error,
  errorInfo,
  title = 'Something went wrong',
  message = 'We encountered an unexpected error',
  onReset
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const errorMessage = error?.message || 'Unknown error';
  const errorStack = error?.stack || '';
  const componentStack = errorInfo?.componentStack || '';

  const handleRefresh = () => {
    if (onReset) {
      onReset();
    } else {
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const copyErrorDetails = () => {
    const report = createDiagnosticReport(error, {
      path: window.location.pathname,
      additionalData: { componentStack }
    });

    navigator.clipboard.writeText(report)
      .then(() => notify.success('Error details copied to clipboard', {
        title: 'Copied',
        duration: 3000
      }))
      .catch(() => notify.error('Failed to copy error details', {
        title: 'Copy Failed',
        duration: 4000
      }));
  };

  const downloadErrorReport = () => {
    const report = createDiagnosticReport(error, {
      path: window.location.pathname,
      additionalData: { componentStack }
    });

    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `firenest-error-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    notify.success('Error report downloaded', {
      title: 'Download Complete',
      duration: 3000
    });
  };

  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />

      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>

      <main className="flex-grow flex items-center justify-center p-6">
        <div className="w-full max-w-3xl">
          <div className="glass-card p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

            <div className="flex flex-col items-center mb-8">
              <div className="w-20 h-20 bg-fiery/10 rounded-full flex items-center justify-center mb-4">
                <Flame className="h-10 w-10 text-fiery animate-pulse-slow" />
              </div>
              <h2 className="text-2xl font-bold mb-2">{title}</h2>
              <p className="text-white/70 text-center">{message}</p>
            </div>

            <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4 mb-6">
              <div className="text-white/90 font-mono text-sm break-words">
                {errorMessage}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <Button
                onClick={handleRefresh}
                className="flex-1 bg-fiery hover:bg-fiery-600 text-white"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Page
              </Button>

              <Button
                onClick={handleGoHome}
                variant="outline"
                className="flex-1 border-white/20"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
            </div>

            <div className="border-t border-white/10 pt-4">
              <button
                onClick={toggleDetails}
                className="flex items-center justify-between w-full text-white/70 hover:text-white transition-colors py-2"
              >
                <span className="font-medium">Technical Details</span>
                {showDetails ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </button>

              {showDetails && (
                <div className="mt-4 space-y-4">
                  {errorStack && (
                    <div>
                      <h3 className="text-sm font-medium text-white/80 mb-2">Error Stack</h3>
                      <pre className="bg-dark-900/70 border border-white/10 rounded-md p-3 text-xs text-white/70 overflow-x-auto">
                        {formatErrorStack(errorStack)}
                      </pre>
                    </div>
                  )}

                  {componentStack && (
                    <div>
                      <h3 className="text-sm font-medium text-white/80 mb-2">Component Stack</h3>
                      <pre className="bg-dark-900/70 border border-white/10 rounded-md p-3 text-xs text-white/70 overflow-x-auto">
                        {formatErrorStack(componentStack)}
                      </pre>
                    </div>
                  )}

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={copyErrorDetails}
                      variant="outline"
                      size="sm"
                      className="border-white/20"
                    >
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Details
                    </Button>

                    <Button
                      onClick={downloadErrorReport}
                      variant="outline"
                      size="sm"
                      className="border-white/20"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download Report
                    </Button>
                  </div>

                  <p className="text-xs text-white/50 mt-4">
                    If this issue persists, please contact our support team and provide them with these error details.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <footer className="py-4 px-6 text-center text-white/50 text-sm">
        <p>
          <a href="/" className="text-fiery hover:text-fiery-400 hover:underline">
            Firenest
          </a>{' '}
          &copy; {new Date().getFullYear()} All rights reserved.
        </p>
      </footer>
    </div>
  );
};

export default ErrorPage;
