import React from 'react';
import { X, Zap, IndianRupee, BarChart3 } from 'lucide-react';

interface DetailPageProps {
  id: string;
  isOpen: boolean;
  onClose: () => void;
}

const DetailPages: React.FC<DetailPageProps> = ({ id, isOpen, onClose }) => {
  if (!isOpen) return null;

  const getContent = () => {
    switch (id) {
      case 'details':
        return <CoreFeaturesDetail />;
      case 'for-startups-details':
        return <StartupSolutionsDetail />;
      case 'ai-tools-details':
        return <AIToolsDetail />;
      case 'enterprise-details':
        return <EnterpriseDetail />;
      case 'startup-resources':
        return <StartupResourcesDetail />;
      case 'security-details':
        return <SecurityDetail />;
      case 'integration-details':
        return <IntegrationDetail />;
      default:
        return <div>Content not found</div>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in overflow-y-auto">
      <div className="bg-dark-900 border border-white/10 rounded-xl w-full max-w-3xl my-4 md:my-6 relative max-h-[90vh] overflow-y-auto">
        <button 
          onClick={onClose}
          className="fixed top-4 right-4 bg-black/60 hover:bg-black/80 rounded-full p-2 text-white/80 hover:text-white transition-all z-20 shadow-lg"
        >
          <X className="w-5 h-5" />
        </button>
        
        <div className="p-5 md:p-6">
          {getContent()}
        </div>
      </div>
    </div>
  );
};

const CoreFeaturesDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-fiery/20 to-fiery/5 flex items-center justify-center">
        <Zap className="w-6 h-6 text-fiery" />
      </div>
      <h2 className="text-2xl font-bold">Pay-As-You-Go AI Tools</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Firenest offers a revolutionary approach to AI tools for Indian startups. Our credit-based system eliminates the need for expensive subscriptions, allowing you to pay only for what you use.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">How Credits Work</h3>
        <p className="text-white/70">
          Purchase credits in advance and use them across any AI tool on our platform. Credits never expire, so you can use them at your own pace.
        </p>
      </div>
      
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Cost Efficiency</h3>
        <p className="text-white/70">
          Save up to 70% compared to individual subscriptions. Our platform aggregates multiple AI tools under one credit system.
        </p>
      </div>
    </div>
    
    <h3 className="text-xl font-bold mb-4">Available AI Tools</h3>
    <div className="space-y-4 mb-6">
      {[
        "Text Generation & Summarization",
        "Image Generation & Editing",
        "Code Assistance & Debugging",
        "Data Analysis & Visualization",
        "Voice & Speech Recognition",
        "Translation & Localization"
      ].map((tool, index) => (
        <div key={index} className="flex items-center gap-3">
          <div className="w-2 h-2 rounded-full bg-fiery"></div>
          <span>{tool}</span>
        </div>
      ))}
    </div>
    
    <div className="bg-gradient-to-r from-fiery/20 to-transparent p-6 rounded-lg border border-fiery/30 mt-8">
      <h3 className="text-lg font-semibold mb-2">Early Access Bonus</h3>
      <p className="text-white/80 mb-4">
        Join our waitlist today and receive 500 free credits when we launch.
      </p>
      <button className="pop-button py-2 px-4">Join Waitlist</button>
    </div>
  </div>
);

const StartupSolutionsDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-500/5 flex items-center justify-center text-3xl">
        🚀
      </div>
      <h2 className="text-2xl font-bold">For Startups</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Firenest is designed specifically for the unique challenges faced by Indian startups. Our solutions help you leverage cutting-edge AI technology while preserving your runway.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Startup Package</h3>
        <p className="text-white/70">
          Get started with 1000 credits for ₹5000, perfect for early-stage startups looking to experiment with AI tools.
        </p>
      </div>
      
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Growth Package</h3>
        <p className="text-white/70">
          Scale up with 5000 credits for ₹20000, ideal for startups that are ready to integrate AI into their core processes.
        </p>
      </div>
    </div>
    
    <h3 className="text-xl font-bold mb-4">Startup Benefits</h3>
    <div className="space-y-4 mb-6">
      {[
        "Dedicated startup success manager",
        "Free technical integration support",
        "Monthly AI strategy consultation",
        "Access to startup community events",
        "Priority feature requests",
        "Startup India partnership benefits"
      ].map((benefit, index) => (
        <div key={index} className="flex items-center gap-3">
          <div className="w-2 h-2 rounded-full bg-purple-500"></div>
          <span>{benefit}</span>
        </div>
      ))}
    </div>
    
    <div className="bg-gradient-to-r from-purple-500/20 to-transparent p-6 rounded-lg border border-purple-500/30 mt-8">
      <h3 className="text-lg font-semibold mb-2">Startup India Partnership</h3>
      <p className="text-white/80 mb-4">
        As a Startup India partner, we offer additional benefits to registered startups. Contact us to learn more.
      </p>
      <button className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-md transition-colors">Contact Us</button>
    </div>
  </div>
);

const AIToolsDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-green-500/20 to-green-500/5 flex items-center justify-center text-3xl">
        ⚡
      </div>
      <h2 className="text-2xl font-bold">AI Tools</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Access premium AI models without the premium subscription costs. Our platform aggregates the best AI tools available, all accessible through our simple credit system.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Text AI</h3>
        <ul className="space-y-2 text-white/70">
          <li>• Content generation</li>
          <li>• Summarization</li>
          <li>• Paraphrasing</li>
          <li>• Translation</li>
        </ul>
      </div>
      
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Visual AI</h3>
        <ul className="space-y-2 text-white/70">
          <li>• Image generation</li>
          <li>• Image editing</li>
          <li>• Design assistance</li>
          <li>• Video creation</li>
        </ul>
      </div>
      
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Business AI</h3>
        <ul className="space-y-2 text-white/70">
          <li>• Data analysis</li>
          <li>• Market research</li>
          <li>• Customer insights</li>
          <li>• Process automation</li>
        </ul>
      </div>
    </div>
    
    <h3 className="text-xl font-bold mb-4">How It Works</h3>
    <div className="flex flex-col md:flex-row gap-6 mb-8">
      {[
        {
          step: "1",
          title: "Purchase Credits",
          description: "Buy credits through our simple payment system."
        },
        {
          step: "2",
          title: "Choose Your Tool",
          description: "Select from our wide range of AI tools."
        },
        {
          step: "3",
          title: "Use Credits",
          description: "Pay for only what you use with our credit system."
        },
        {
          step: "4",
          title: "Get Results",
          description: "Receive high-quality AI outputs instantly."
        }
      ].map((item, index) => (
        <div key={index} className="flex-1 bg-white/5 p-6 rounded-lg border border-white/10 relative">
          <div className="absolute -top-3 -left-3 w-8 h-8 rounded-full bg-green-500 flex items-center justify-center font-bold">
            {item.step}
          </div>
          <h4 className="text-lg font-semibold mb-2 mt-2">{item.title}</h4>
          <p className="text-white/70">{item.description}</p>
        </div>
      ))}
    </div>
    
    <div className="bg-gradient-to-r from-green-500/20 to-transparent p-6 rounded-lg border border-green-500/30 mt-8">
      <h3 className="text-lg font-semibold mb-2">Try Before You Buy</h3>
      <p className="text-white/80 mb-4">
        Sign up for early access and get 100 free credits to test our AI tools.
      </p>
      <button className="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition-colors">Get Free Credits</button>
    </div>
  </div>
);

const EnterpriseDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center text-3xl">
        🏢
      </div>
      <h2 className="text-2xl font-bold">Enterprise Solutions</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Firenest offers tailored enterprise solutions for larger organizations that need scalable AI capabilities, enhanced security, and dedicated support.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Custom Deployment</h3>
        <p className="text-white/70">
          On-premise or private cloud deployment options with enhanced security and compliance features.
        </p>
      </div>
      
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Dedicated Resources</h3>
        <p className="text-white/70">
          Dedicated AI compute resources for consistent performance and priority processing.
        </p>
      </div>
    </div>
    
    <h3 className="text-xl font-bold mb-4">Enterprise Features</h3>
    <div className="space-y-4 mb-6">
      {[
        "Custom AI model training and fine-tuning",
        "Advanced data security and compliance",
        "API access for seamless integration",
        "Dedicated account management team",
        "24/7 priority support",
        "Custom reporting and analytics"
      ].map((feature, index) => (
        <div key={index} className="flex items-center gap-3">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <span>{feature}</span>
        </div>
      ))}
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">Enterprise Pricing</h3>
        <p className="text-white/70">
          Custom pricing based on your organization's specific needs and usage requirements. Volume discounts available.
        </p>
      </div>
      
      <div className="bg-white/5 p-6 rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold mb-3">SLA Guarantees</h3>
        <p className="text-white/70">
          Service Level Agreements with guaranteed uptime, performance metrics, and response times.
        </p>
      </div>
    </div>
    
    <div className="bg-gradient-to-r from-blue-500/20 to-transparent p-6 rounded-lg border border-blue-500/30 mt-8">
      <h3 className="text-lg font-semibold mb-2">Schedule a Consultation</h3>
      <p className="text-white/80 mb-4">
        Our enterprise team will work with you to create a customized solution that meets your specific needs.
      </p>
      <button className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors">Contact Enterprise Team</button>
    </div>
  </div>
);

const StartupResourcesDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-pink-500/20 to-pink-500/5 flex items-center justify-center text-3xl">
        💡
      </div>
      <h2 className="text-2xl font-bold">Startup Resources</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Access exclusive resources, mentorship, and networking opportunities designed specifically for Indian startups. Our platform goes beyond just AI tools to provide comprehensive support for your growth journey.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-pink-400">Mentorship Network</h3>
        <p className="text-white/70 mb-4">
          Connect with experienced entrepreneurs and industry experts who can provide guidance, feedback, and support for your startup journey.
        </p>
        <ul className="space-y-2">
          <li className="flex items-start gap-2">
            <span className="text-pink-400 mt-1">•</span>
            <span>1-on-1 mentorship sessions</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-pink-400 mt-1">•</span>
            <span>Group coaching sessions</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-pink-400 mt-1">•</span>
            <span>Industry-specific advisors</span>
          </li>
        </ul>
      </div>
      
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-pink-400">Funding Resources</h3>
        <p className="text-white/70 mb-4">
          Get access to resources that can help you secure funding for your startup, including investor connections and pitch preparation.
        </p>
        <ul className="space-y-2">
          <li className="flex items-start gap-2">
            <span className="text-pink-400 mt-1">•</span>
            <span>Investor database</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-pink-400 mt-1">•</span>
            <span>Pitch deck reviews</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-pink-400 mt-1">•</span>
            <span>Funding opportunity alerts</span>
          </li>
        </ul>
      </div>
    </div>
    
    <div className="glass-card p-5 mb-8">
      <h3 className="text-xl font-bold mb-3 text-pink-400">Community & Networking</h3>
      <p className="text-white/70 mb-4">
        Join a vibrant community of like-minded founders and entrepreneurs to share experiences, collaborate, and grow together.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white/5 p-4 rounded-lg">
          <h4 className="font-bold mb-2">Monthly Meetups</h4>
          <p className="text-white/70 text-sm">Virtual and in-person events to connect with other founders</p>
        </div>
        <div className="bg-white/5 p-4 rounded-lg">
          <h4 className="font-bold mb-2">Founder Forums</h4>
          <p className="text-white/70 text-sm">Online discussion groups to share challenges and solutions</p>
        </div>
        <div className="bg-white/5 p-4 rounded-lg">
          <h4 className="font-bold mb-2">Collaboration Opportunities</h4>
          <p className="text-white/70 text-sm">Find partners, clients, and talent within our community</p>
        </div>
      </div>
    </div>
    
    <div className="text-center">
      <button className="fire-button">Join Our Community</button>
    </div>
  </div>
);

const SecurityDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-teal-500/20 to-teal-500/5 flex items-center justify-center text-3xl">
        🔒
      </div>
      <h2 className="text-2xl font-bold">Security & Compliance</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Enterprise-grade security with data residency, encryption, and compliance features to ensure your data and AI operations are protected and compliant with regulations.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-teal-400">Data Security</h3>
        <ul className="space-y-3">
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>End-to-end encryption</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Secure data storage</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Access controls</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Regular security audits</span>
          </li>
        </ul>
      </div>
      
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-teal-400">Data Residency</h3>
        <ul className="space-y-3">
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>India data centers</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Regional compliance</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Data sovereignty</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Geographic redundancy</span>
          </li>
        </ul>
      </div>
      
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-teal-400">Compliance</h3>
        <ul className="space-y-3">
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>GDPR compliant</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>ISO 27001 certified</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>PDPB ready</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-teal-400 mt-1">✓</span>
            <span>Regular compliance updates</span>
          </li>
        </ul>
      </div>
    </div>
    
    <div className="glass-card p-6 mb-8">
      <h3 className="text-xl font-bold mb-4 text-teal-400">Enterprise Security Features</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="font-bold mb-2">Single Sign-On (SSO)</h4>
          <p className="text-white/70 mb-4">Integrate with your existing identity provider for seamless and secure authentication.</p>
          
          <h4 className="font-bold mb-2">Audit Logging</h4>
          <p className="text-white/70">Comprehensive logs of all activities for security monitoring and compliance reporting.</p>
        </div>
        
        <div>
          <h4 className="font-bold mb-2">Role-Based Access Control</h4>
          <p className="text-white/70 mb-4">Granular control over who can access what within your organization.</p>
          
          <h4 className="font-bold mb-2">Data Retention Policies</h4>
          <p className="text-white/70">Configure how long data is stored and when it should be automatically deleted.</p>
        </div>
      </div>
    </div>
    
    <div className="text-center">
      <button className="fire-button">Request Security Whitepaper</button>
    </div>
  </div>
);

const IntegrationDetail = () => (
  <div>
    <div className="flex items-center gap-4 mb-6">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-orange-500/20 to-orange-500/5 flex items-center justify-center text-3xl">
        🤝
      </div>
      <h2 className="text-2xl font-bold">Custom Integration</h2>
    </div>
    
    <p className="text-white/70 mb-6">
      Seamlessly integrate Firenest with your existing systems through our enterprise API and dedicated support team. Our flexible integration options ensure that Firenest works with your current tech stack.
    </p>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-orange-400">API Integration</h3>
        <p className="text-white/70 mb-4">
          Our comprehensive API allows you to integrate Firenest's AI capabilities directly into your applications and workflows.
        </p>
        <ul className="space-y-2">
          <li className="flex items-start gap-2">
            <span className="text-orange-400 mt-1">•</span>
            <span>RESTful API with comprehensive documentation</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-orange-400 mt-1">•</span>
            <span>Webhooks for real-time events</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-orange-400 mt-1">•</span>
            <span>SDKs for popular programming languages</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-orange-400 mt-1">•</span>
            <span>Secure authentication methods</span>
          </li>
        </ul>
      </div>
      
      <div className="glass-card p-5">
        <h3 className="text-xl font-bold mb-3 text-orange-400">Pre-built Integrations</h3>
        <p className="text-white/70 mb-4">
          Connect Firenest with your favorite tools and platforms using our pre-built integrations.
        </p>
        <div className="grid grid-cols-3 gap-3">
          <div className="bg-white/10 p-3 rounded-lg text-center">
            <span className="block text-2xl mb-1">💬</span>
            <span className="text-sm">Slack</span>
          </div>
          <div className="bg-white/10 p-3 rounded-lg text-center">
            <span className="block text-2xl mb-1">📊</span>
            <span className="text-sm">Tableau</span>
          </div>
          <div className="bg-white/10 p-3 rounded-lg text-center">
            <span className="block text-2xl mb-1">📝</span>
            <span className="text-sm">Notion</span>
          </div>
          <div className="bg-white/10 p-3 rounded-lg text-center">
            <span className="block text-2xl mb-1">📈</span>
            <span className="text-sm">Salesforce</span>
          </div>
          <div className="bg-white/10 p-3 rounded-lg text-center">
            <span className="block text-2xl mb-1">🗂️</span>
            <span className="text-sm">Zapier</span>
          </div>
          <div className="bg-white/10 p-3 rounded-lg text-center">
            <span className="block text-2xl mb-1">⚙️</span>
            <span className="text-sm">More</span>
          </div>
        </div>
      </div>
    </div>
    
    <div className="glass-card p-6 mb-8">
      <h3 className="text-xl font-bold mb-4 text-orange-400">Dedicated Integration Support</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white/5 p-4 rounded-lg">
          <h4 className="font-bold mb-2">Integration Planning</h4>
          <p className="text-white/70 text-sm">Our team works with you to design the optimal integration strategy for your needs.</p>
        </div>
        <div className="bg-white/5 p-4 rounded-lg">
          <h4 className="font-bold mb-2">Implementation Support</h4>
          <p className="text-white/70 text-sm">Technical assistance during the implementation process to ensure smooth deployment.</p>
        </div>
        <div className="bg-white/5 p-4 rounded-lg">
          <h4 className="font-bold mb-2">Ongoing Maintenance</h4>
          <p className="text-white/70 text-sm">Continuous support to keep your integrations running smoothly as your systems evolve.</p>
        </div>
      </div>
    </div>
    
    <div className="text-center">
      <button className="fire-button">Schedule Integration Consultation</button>
    </div>
  </div>
);

export default DetailPages;
