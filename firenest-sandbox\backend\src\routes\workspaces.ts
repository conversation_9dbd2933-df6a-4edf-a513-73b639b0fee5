/**
 * Workspace Routes
 * Multi-tenant workspace management with RLS
 * SOC 2 Alignment: CC6.2 (Access Control)
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { queryWithUserContext, transactionWithUserContext } from '@/config/database';
import { logger } from '@/utils/logger';
import { validate, schemas } from '@/utils/validation';
import { asyncHandler, NotFoundError, ConflictError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';

const router = Router();

// Get all workspaces for the authenticated user
router.get('/',
  validate(schemas.pagination, 'query'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { page, limit, sortBy, sortOrder } = req.query;
    const offset = (page - 1) * limit;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT w.id, w.name, w.description, w.settings, w.created_at, w.updated_at,
              w.owner_id = $1 as is_owner,
              (SELECT COUNT(*) FROM workspace_members wm WHERE wm.workspace_id = w.id) as member_count,
              (SELECT COUNT(*) FROM sandbox_projects sp WHERE sp.workspace_id = w.id) as project_count
       FROM workspaces w
       WHERE w.owner_id = $1 OR w.id IN (
         SELECT workspace_id FROM workspace_members WHERE user_id = $1
       )
       ORDER BY ${sortBy || 'created_at'} ${sortOrder}
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );

    const countResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT COUNT(*) as total
       FROM workspaces w
       WHERE w.owner_id = $1 OR w.id IN (
         SELECT workspace_id FROM workspace_members WHERE user_id = $1
       )`,
      [req.user.id]
    );

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  })
);

// Get workspace by ID
router.get('/:workspaceId',
  validate(schemas.uuid, 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { workspaceId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT w.id, w.name, w.description, w.settings, w.created_at, w.updated_at,
              w.owner_id = $2 as is_owner,
              u.email as owner_email
       FROM workspaces w
       JOIN users u ON w.owner_id = u.id
       WHERE w.id = $1`,
      [workspaceId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Workspace not found');
    }

    // Get workspace members
    const membersResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT wm.id, wm.role, wm.created_at, u.email, u.id as user_id
       FROM workspace_members wm
       JOIN users u ON wm.user_id = u.id
       WHERE wm.workspace_id = $1`,
      [workspaceId]
    );

    const workspace = result.rows[0];
    workspace.members = membersResult.rows;

    res.json({
      success: true,
      data: workspace
    });
  })
);

// Create new workspace
router.post('/',
  validate(schemas.createWorkspace),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { name, description, settings } = req.body;

    const result = await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        // Create workspace
        const workspaceResult = await client.query(
          `INSERT INTO workspaces (id, name, description, owner_id, settings)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING id, name, description, settings, created_at, updated_at`,
          [uuidv4(), name, description, req.user.id, JSON.stringify(settings || {})]
        );

        return workspaceResult.rows[0];
      }
    );

    logger.info('Workspace created', {
      workspaceId: result.id,
      userId: req.user.id,
      name
    });

    res.status(201).json({
      success: true,
      data: result
    });
  })
);

// Update workspace
router.put('/:workspaceId',
  validate(schemas.uuid, 'params'),
  validate(schemas.updateWorkspace),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { workspaceId } = req.params;
    const { name, description, settings } = req.body;

    // Check if user is workspace owner
    const ownerCheck = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT 1 FROM workspaces WHERE id = $1 AND owner_id = $2',
      [workspaceId, req.user.id]
    );

    if (ownerCheck.rows.length === 0) {
      throw new NotFoundError('Workspace not found or access denied');
    }

    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex++}`);
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex++}`);
      updateValues.push(description);
    }
    if (settings !== undefined) {
      updateFields.push(`settings = $${paramIndex++}`);
      updateValues.push(JSON.stringify(settings));
    }

    updateFields.push(`updated_at = NOW()`);
    updateValues.push(workspaceId);

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `UPDATE workspaces 
       SET ${updateFields.join(', ')}
       WHERE id = $${paramIndex}
       RETURNING id, name, description, settings, created_at, updated_at`,
      updateValues
    );

    logger.info('Workspace updated', {
      workspaceId,
      userId: req.user.id,
      changes: { name, description, settings }
    });

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Delete workspace
router.delete('/:workspaceId',
  validate(schemas.uuid, 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { workspaceId } = req.params;

    // Check if user is workspace owner
    const ownerCheck = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT name FROM workspaces WHERE id = $1 AND owner_id = $2',
      [workspaceId, req.user.id]
    );

    if (ownerCheck.rows.length === 0) {
      throw new NotFoundError('Workspace not found or access denied');
    }

    // Check if workspace has projects
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT COUNT(*) as count FROM sandbox_projects WHERE workspace_id = $1',
      [workspaceId]
    );

    if (parseInt(projectCheck.rows[0].count) > 0) {
      throw new ConflictError('Cannot delete workspace with existing projects');
    }

    await queryWithUserContext(
      req.user.authProviderId,
      'DELETE FROM workspaces WHERE id = $1',
      [workspaceId]
    );

    logger.info('Workspace deleted', {
      workspaceId,
      userId: req.user.id,
      workspaceName: ownerCheck.rows[0].name
    });

    res.json({
      success: true,
      message: 'Workspace deleted successfully'
    });
  })
);

// Add member to workspace
router.post('/:workspaceId/members',
  validate(schemas.uuid, 'params'),
  validate(Joi.object({
    email: schemas.email,
    role: Joi.string().valid('member', 'admin').optional().default('member')
  })),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { workspaceId } = req.params;
    const { email, role } = req.body;

    // Check if user is workspace owner
    const ownerCheck = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT 1 FROM workspaces WHERE id = $1 AND owner_id = $2',
      [workspaceId, req.user.id]
    );

    if (ownerCheck.rows.length === 0) {
      throw new NotFoundError('Workspace not found or access denied');
    }

    // Find user by email
    const userResult = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT id FROM users WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      throw new NotFoundError('User not found');
    }

    const userId = userResult.rows[0].id;

    // Check if user is already a member
    const memberCheck = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT 1 FROM workspace_members WHERE workspace_id = $1 AND user_id = $2',
      [workspaceId, userId]
    );

    if (memberCheck.rows.length > 0) {
      throw new ConflictError('User is already a member of this workspace');
    }

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `INSERT INTO workspace_members (id, workspace_id, user_id, role)
       VALUES ($1, $2, $3, $4)
       RETURNING id, role, created_at`,
      [uuidv4(), workspaceId, userId, role]
    );

    logger.info('Member added to workspace', {
      workspaceId,
      userId: req.user.id,
      memberEmail: email,
      memberRole: role
    });

    res.status(201).json({
      success: true,
      data: {
        ...result.rows[0],
        email,
        userId
      }
    });
  })
);

export default router;
