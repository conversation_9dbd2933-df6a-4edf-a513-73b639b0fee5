import { useState } from 'react';

const FeatureComparison = () => {
  const [activeCategory, setActiveCategory] = useState('content');

  const categories = [
    { id: 'content', name: 'Content Creation' },
    { id: 'seo', name: 'SEO Tools' },
    { id: 'collaboration', name: 'Collaboration' },
    { id: 'analytics', name: 'Analytics' },
    { id: 'integrations', name: 'Integrations' }
  ];

  const features = {
    content: [
      { name: 'AI Content Generation', starter: true, professional: true, enterprise: true },
      { name: 'Monthly Word Limit', starter: '10,000', professional: '50,000', enterprise: 'Unlimited' },
      { name: 'Content Templates', starter: '50', professional: '200+', enterprise: 'All' },
      { name: 'Custom Templates', starter: false, professional: true, enterprise: true },
      { name: 'Multilingual Support', starter: false, professional: '10 languages', enterprise: '50+ languages' },
      { name: 'Tone & Style Customization', starter: 'Basic', professional: 'Advanced', enterprise: 'Enterprise' },
      { name: 'Content Repurposing', starter: false, professional: true, enterprise: true },
      { name: 'Custom AI Training', starter: false, professional: false, enterprise: true }
    ],
    seo: [
      { name: 'Keyword Research', starter: 'Basic', professional: 'Advanced', enterprise: 'Enterprise' },
      { name: 'SEO Content Optimization', starter: true, professional: true, enterprise: true },
      { name: 'Readability Analysis', starter: true, professional: true, enterprise: true },
      { name: 'Meta Tag Generation', starter: true, professional: true, enterprise: true },
      { name: 'Competitor Analysis', starter: false, professional: true, enterprise: true },
      { name: 'Rank Tracking', starter: false, professional: '50 keywords', enterprise: 'Unlimited' },
      { name: 'Content Performance', starter: false, professional: true, enterprise: true },
      { name: 'SEO Audit Tools', starter: false, professional: false, enterprise: true }
    ],
    collaboration: [
      { name: 'Team Members', starter: '5', professional: '15', enterprise: 'Unlimited' },
      { name: 'Commenting', starter: true, professional: true, enterprise: true },
      { name: 'Approval Workflows', starter: false, professional: true, enterprise: true },
      { name: 'Role-Based Access', starter: false, professional: true, enterprise: true },
      { name: 'Content Calendar', starter: false, professional: true, enterprise: true },
      { name: 'Task Assignment', starter: false, professional: true, enterprise: true },
      { name: 'Version History', starter: '30 days', professional: '90 days', enterprise: 'Unlimited' },
      { name: 'Team Performance Analytics', starter: false, professional: false, enterprise: true }
    ],
    analytics: [
      { name: 'Basic Analytics', starter: true, professional: true, enterprise: true },
      { name: 'Content Performance', starter: false, professional: true, enterprise: true },
      { name: 'Custom Reports', starter: false, professional: true, enterprise: true },
      { name: 'Data Export', starter: false, professional: true, enterprise: true },
      { name: 'ROI Tracking', starter: false, professional: false, enterprise: true },
      { name: 'Conversion Analytics', starter: false, professional: false, enterprise: true },
      { name: 'Custom Dashboards', starter: false, professional: false, enterprise: true },
      { name: 'AI-Powered Insights', starter: false, professional: false, enterprise: true }
    ],
    integrations: [
      { name: 'Number of Integrations', starter: '1', professional: '10', enterprise: 'Unlimited' },
      { name: 'WordPress', starter: true, professional: true, enterprise: true },
      { name: 'Shopify', starter: false, professional: true, enterprise: true },
      { name: 'HubSpot', starter: false, professional: true, enterprise: true },
      { name: 'Google Analytics', starter: false, professional: true, enterprise: true },
      { name: 'Zapier', starter: false, professional: true, enterprise: true },
      { name: 'Custom Integrations', starter: false, professional: false, enterprise: true },
      { name: 'API Access', starter: false, professional: false, enterprise: true }
    ]
  };

  const renderValue = (value) => {
    if (value === true) {
      return (
        <svg className="w-6 h-6 text-primary mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    } else if (value === false) {
      return (
        <svg className="w-6 h-6 text-dark-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    } else {
      return <span className="text-light">{value}</span>;
    }
  };

  return (
    <section className="section bg-dark-900">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Compare <span className="gradient-text">All Features</span>
          </h2>
          <p className="text-light-600 text-lg">
            Detailed breakdown of all features available in each plan.
          </p>
        </div>
        
        <div className="mb-8 flex flex-wrap justify-center gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeCategory === category.id
                  ? 'bg-primary text-white'
                  : 'bg-dark-700 text-light-600 hover:bg-dark-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-dark-600">
                <th className="py-4 px-6 text-left text-light-600 font-medium">Feature</th>
                <th className="py-4 px-6 text-center text-light-600 font-medium">Starter</th>
                <th className="py-4 px-6 text-center text-light-600 font-medium">Professional</th>
                <th className="py-4 px-6 text-center text-light-600 font-medium">Enterprise</th>
              </tr>
            </thead>
            <tbody>
              {features[activeCategory].map((feature, index) => (
                <tr 
                  key={index} 
                  className={`border-b border-dark-700 hover:bg-dark-800 transition-colors ${
                    index % 2 === 0 ? 'bg-dark-800/50' : ''
                  }`}
                >
                  <td className="py-4 px-6 text-left text-light">{feature.name}</td>
                  <td className="py-4 px-6 text-center">{renderValue(feature.starter)}</td>
                  <td className="py-4 px-6 text-center">{renderValue(feature.professional)}</td>
                  <td className="py-4 px-6 text-center">{renderValue(feature.enterprise)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default FeatureComparison;
