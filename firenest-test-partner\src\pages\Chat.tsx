import { useEffect, useState } from 'react';
import Header from '../components/Header';
import ChatInterface from '../components/ChatInterface';
import SuccessToast from '../components/SuccessToast';
import { useFirenestAuth, reportUsageToFirenest } from '../lib/firenest-integration';

const Chat = () => {
  const { userId, sessionId, token } = useFirenestAuth();
  const [sessionActive, setSessionActive] = useState(false);
  const [showSuccessToast, setShowSuccessToast] = useState(false);

  // Check for successful login and show toast
  useEffect(() => {
    const loginSuccess = sessionStorage.getItem('firenest_login_success');
    if (loginSuccess === 'true') {
      setShowSuccessToast(true);
      // Remove the flag so it doesn't show again on refresh
      sessionStorage.removeItem('firenest_login_success');
    }
  }, []);

  // Report session start when the chat page is loaded
  useEffect(() => {
    const reportSessionStart = async () => {
      if (userId && sessionId && token) {
        const success = await reportUsageToFirenest(
          userId,
          sessionId,
          token,
          'session_start',
          1
        );

        if (success) {
          setSessionActive(true);
          console.log('Session started successfully');
        }
      }
    };

    reportSessionStart();

    // Report session end when the component unmounts
    return () => {
      const reportSessionEnd = async () => {
        if (userId && sessionId && token && sessionActive) {
          await reportUsageToFirenest(
            userId,
            sessionId,
            token,
            'session_end',
            1
          );
          console.log('Session ended successfully');
        }
      };

      reportSessionEnd();
    };
  }, [userId, sessionId, token]);

  // Set up a heartbeat to keep the session active
  useEffect(() => {
    if (!sessionActive) return;

    const heartbeatInterval = setInterval(async () => {
      if (userId && sessionId && token) {
        await reportUsageToFirenest(
          userId,
          sessionId,
          token,
          'session_heartbeat',
          0
        );
        console.log('Session heartbeat sent');
      }
    }, 60000); // Send heartbeat every minute

    return () => clearInterval(heartbeatInterval);
  }, [userId, sessionId, token, sessionActive]);

  return (
    <div className="min-h-screen flex flex-col bg-dark-900">
      <Header />
      <ChatInterface />
      {showSuccessToast && (
        <SuccessToast
          message="Authentication successful! Premium features are now unlocked."
          duration={6000}
          onClose={() => setShowSuccessToast(false)}
        />
      )}
    </div>
  );
};

export default Chat;