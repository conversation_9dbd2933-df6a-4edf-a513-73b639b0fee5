/**
 * Integration Framework Service
 * 
 * This service manages integrations with third-party services.
 */

import { 
  IntegrationPartner,
  IntegrationMetadata,
  IntegrationConfig,
  IntegrationAdapter,
  IntegrationTestResult,
  IntegrationHealthStatus
} from './types';
import { partners } from './partners';
import { integrations } from './integrations';
import { adapters } from './adapters';
import { authBridge } from '../auth-bridge';
import { usageTracking } from '../usage-tracking';

class IntegrationFrameworkService {
  private partners: Map<string, IntegrationPartner> = new Map();
  private integrations: Map<string, IntegrationMetadata> = new Map();
  private configs: Map<string, IntegrationConfig> = new Map();
  private adapters: Map<string, IntegrationAdapter> = new Map();
  private healthStatus: Map<string, IntegrationHealthStatus> = new Map();
  private testResults: Map<string, IntegrationTestResult> = new Map();
  
  constructor() {
    // Load partners
    partners.forEach(partner => {
      this.partners.set(partner.id, partner);
    });
    
    // Load integrations
    integrations.forEach(integration => {
      this.integrations.set(integration.id, integration);
    });
    
    // Load adapters
    adapters.forEach(adapter => {
      this.adapters.set(adapter.id, adapter);
    });
    
    // Initialize health status
    this.initializeHealthStatus();
    
    // Set up interval to check health status
    setInterval(() => this.checkHealthStatus(), 300000); // Every 5 minutes
  }
  
  /**
   * Get all partners
   */
  public getAllPartners(): IntegrationPartner[] {
    return Array.from(this.partners.values());
  }
  
  /**
   * Get a partner by ID
   */
  public getPartner(partnerId: string): IntegrationPartner | undefined {
    return this.partners.get(partnerId);
  }
  
  /**
   * Get all integrations
   */
  public getAllIntegrations(): IntegrationMetadata[] {
    return Array.from(this.integrations.values());
  }
  
  /**
   * Get an integration by ID
   */
  public getIntegration(integrationId: string): IntegrationMetadata | undefined {
    return this.integrations.get(integrationId);
  }
  
  /**
   * Get integrations by partner ID
   */
  public getIntegrationsByPartner(partnerId: string): IntegrationMetadata[] {
    return Array.from(this.integrations.values()).filter(
      integration => integration.partnerId === partnerId
    );
  }
  
  /**
   * Get integrations by category
   */
  public getIntegrationsByCategory(category: string): IntegrationMetadata[] {
    return Array.from(this.integrations.values()).filter(
      integration => integration.category === category
    );
  }
  
  /**
   * Get popular integrations
   */
  public getPopularIntegrations(): IntegrationMetadata[] {
    return Array.from(this.integrations.values()).filter(
      integration => integration.popular
    );
  }
  
  /**
   * Get new integrations
   */
  public getNewIntegrations(): IntegrationMetadata[] {
    return Array.from(this.integrations.values()).filter(
      integration => integration.new
    );
  }
  
  /**
   * Get integration health status
   */
  public getHealthStatus(integrationId: string): IntegrationHealthStatus | undefined {
    return this.healthStatus.get(integrationId);
  }
  
  /**
   * Get all integration health statuses
   */
  public getAllHealthStatuses(): IntegrationHealthStatus[] {
    return Array.from(this.healthStatus.values());
  }
  
  /**
   * Get integration test results
   */
  public getTestResults(integrationId: string): IntegrationTestResult | undefined {
    return this.testResults.get(integrationId);
  }
  
  /**
   * Run integration tests
   */
  public async runTests(integrationId: string): Promise<IntegrationTestResult> {
    const integration = this.integrations.get(integrationId);
    
    if (!integration) {
      throw new Error(`Integration with ID ${integrationId} not found`);
    }
    
    const adapter = this.adapters.get(integrationId);
    
    if (!adapter) {
      throw new Error(`Adapter for integration with ID ${integrationId} not found`);
    }
    
    const startTime = Date.now();
    const tests: any[] = [];
    let success = true;
    
    // Test authentication
    try {
      const authResult = await adapter.authenticate('test-user', {});
      tests.push({
        name: 'Authentication',
        success: true,
        duration: Date.now() - startTime
      });
    } catch (error) {
      tests.push({
        name: 'Authentication',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      });
      success = false;
    }
    
    // Test API call
    try {
      const apiResult = await adapter.callApi('test-user', 'test', 'GET');
      tests.push({
        name: 'API Call',
        success: true,
        duration: Date.now() - startTime
      });
    } catch (error) {
      tests.push({
        name: 'API Call',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      });
      success = false;
    }
    
    // Test usage tracking
    try {
      const usageResult = await adapter.getUsage('test-user', new Date(), new Date());
      tests.push({
        name: 'Usage Tracking',
        success: true,
        duration: Date.now() - startTime
      });
    } catch (error) {
      tests.push({
        name: 'Usage Tracking',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      });
      success = false;
    }
    
    const endTime = Date.now();
    const overallDuration = endTime - startTime;
    
    const testResult: IntegrationTestResult = {
      id: integrationId,
      partnerId: integration.partnerId,
      timestamp: new Date(),
      success,
      tests,
      overallDuration
    };
    
    // Store test results
    this.testResults.set(integrationId, testResult);
    
    return testResult;
  }
  
  /**
   * Launch an integration for a user
   */
  public async launchIntegration(integrationId: string, userId: string): Promise<any> {
    const integration = this.integrations.get(integrationId);
    
    if (!integration) {
      throw new Error(`Integration with ID ${integrationId} not found`);
    }
    
    // Use the auth bridge to initialize authentication
    const result = await authBridge.initializeAuth(integrationId, userId);
    
    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to launch integration');
    }
    
    // Track session start
    usageTracking.trackSessionStart(userId, integrationId, `${integrationId}:${userId}`);
    
    return result;
  }
  
  /**
   * End an integration session for a user
   */
  public async endIntegrationSession(integrationId: string, userId: string): Promise<boolean> {
    // End the session in the auth bridge
    const result = await authBridge.endSession(integrationId, userId);
    
    if (!result) {
      return false;
    }
    
    // Track session end
    usageTracking.trackSessionEnd(userId, integrationId, `${integrationId}:${userId}`, {});
    
    return true;
  }
  
  /**
   * Handle a webhook event
   */
  public async handleWebhook(integrationId: string, event: string, payload: any): Promise<any> {
    const adapter = this.adapters.get(integrationId);
    
    if (!adapter || !adapter.handleWebhook) {
      throw new Error(`Adapter for integration with ID ${integrationId} not found or does not support webhooks`);
    }
    
    return adapter.handleWebhook(event, payload);
  }
  
  // Private methods
  
  private initializeHealthStatus(): void {
    // Initialize health status for all integrations
    this.integrations.forEach(integration => {
      this.healthStatus.set(integration.id, {
        id: integration.id,
        partnerId: integration.partnerId,
        timestamp: new Date(),
        status: 'healthy',
        latency: 0,
        uptime: 100,
        incidents: []
      });
    });
  }
  
  private async checkHealthStatus(): Promise<void> {
    // Check health status for all integrations
    for (const [integrationId, integration] of this.integrations.entries()) {
      const adapter = this.adapters.get(integrationId);
      
      if (!adapter) {
        continue;
      }
      
      const currentStatus = this.healthStatus.get(integrationId);
      
      if (!currentStatus) {
        continue;
      }
      
      try {
        const startTime = Date.now();
        
        // Make a simple API call to check health
        await adapter.callApi('health-check', 'health', 'GET');
        
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        // Update health status
        this.healthStatus.set(integrationId, {
          ...currentStatus,
          timestamp: new Date(),
          status: 'healthy',
          latency
        });
      } catch (error) {
        // Update health status to degraded or down
        const newStatus = currentStatus.status === 'degraded' ? 'down' : 'degraded';
        
        this.healthStatus.set(integrationId, {
          ...currentStatus,
          timestamp: new Date(),
          status: newStatus,
          incidents: [
            ...currentStatus.incidents,
            {
              timestamp: new Date(),
              message: error instanceof Error ? error.message : 'Unknown error',
              resolved: false
            }
          ]
        });
      }
    }
  }
}

// Create and export a singleton instance
export const integrationFramework = new IntegrationFrameworkService();
