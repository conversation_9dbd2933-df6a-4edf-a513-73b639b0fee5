import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ErrorCategory, logError } from '@/lib/error-utils';
import { notify } from '@/components/ui/notification-system';
import ErrorDiagnosticPage from '@/pages/ErrorDiagnosticPage';

interface ErrorHandlerProps {
  error: Error | unknown;
  category?: ErrorCategory;
  component?: string;
  resetError?: () => void;
}

/**
 * Component to handle errors and display the error diagnostic page
 * This component is used by the ErrorBoundary and other error handling mechanisms
 */
const ErrorHandler: React.FC<ErrorHandlerProps> = ({
  error,
  category = ErrorCategory.UNKNOWN,
  component,
  resetError
}) => {
  const navigate = useNavigate();
  const [showDiagnostics, setShowDiagnostics] = useState(true);

  useEffect(() => {
    // Log the error
    logError(error, null, undefined, category, {
      component,
      path: window.location.pathname
    });

    // Show a toast notification
    notify.error('An unexpected error occurred', {
      title: 'Application Error',
      duration: 5000
    });
  }, [error, category, component]);

  // Extract error details
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorStack = error instanceof Error ? error.stack : undefined;

  const handleReset = () => {
    setShowDiagnostics(false);
    if (resetError) {
      resetError();
    } else {
      // Navigate to home page if no reset function is provided
      navigate('/');
    }
  };

  if (!showDiagnostics) {
    return null;
  }

  return (
    <ErrorDiagnosticPage
      message={errorMessage}
      stack={errorStack}
      category={category}
      component={component}
      path={window.location.pathname}
      onReset={handleReset}
    />
  );
};

export default ErrorHandler;
