// Mock integration data and types for the Workbench page

export interface IntegrationMetadata {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  tags: string[];
  rating: number;
  popular: boolean;
  new: boolean;
  status: string;
  pricing: {
    costPerUnit: number;
    currency: string;
  };
}

// Mock hook for integrations
export const useIntegrations = () => {
  // Mock integration data
  const integrations: IntegrationMetadata[] = [
    {
      id: 'chatgpt',
      name: 'ChatGPT',
      description: 'Advanced AI assistant for text generation and conversation',
      icon: 'Bot',
      category: 'text_generation',
      tags: ['AI', 'Text', 'Assistant', 'OpenAI'],
      rating: 4.8,
      popular: true,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 5,
        currency: 'USD'
      }
    },
    {
      id: 'midjourney',
      name: 'Midjourney',
      description: 'AI image generation from text prompts',
      icon: 'Image',
      category: 'image_generation',
      tags: ['AI', 'Image', 'Art', 'Creative'],
      rating: 4.7,
      popular: true,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 10,
        currency: 'USD'
      }
    },
    {
      id: 'claude',
      name: '<PERSON>',
      description: 'Anthrop<PERSON>\'s AI assistant for helpful, harmless, and honest conversations',
      icon: 'Bot',
      category: 'text_generation',
      tags: ['AI', 'Text', 'Assistant', 'Anthropic'],
      rating: 4.6,
      popular: true,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 5,
        currency: 'USD'
      }
    },
    {
      id: 'dalle',
      name: 'DALL-E',
      description: 'OpenAI\'s image generation model',
      icon: 'Image',
      category: 'image_generation',
      tags: ['AI', 'Image', 'Art', 'OpenAI'],
      rating: 4.5,
      popular: false,
      new: true,
      status: 'live',
      pricing: {
        costPerUnit: 8,
        currency: 'USD'
      }
    },
    {
      id: 'stable-diffusion',
      name: 'Stable Diffusion',
      description: 'Open-source image generation model',
      icon: 'Image',
      category: 'image_generation',
      tags: ['AI', 'Image', 'Art', 'Open Source'],
      rating: 4.4,
      popular: false,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 6,
        currency: 'USD'
      }
    },
    {
      id: 'whisper',
      name: 'Whisper',
      description: 'Speech-to-text transcription and translation',
      icon: 'Audio',
      category: 'audio_processing',
      tags: ['AI', 'Audio', 'Transcription', 'OpenAI'],
      rating: 4.3,
      popular: false,
      new: true,
      status: 'live',
      pricing: {
        costPerUnit: 4,
        currency: 'USD'
      }
    }
  ];

  return { integrations };
};
