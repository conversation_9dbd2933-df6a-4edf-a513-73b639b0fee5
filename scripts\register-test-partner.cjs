/**
 * Register Test Partner
 * 
 * This script registers a test partner in the Firenest database for testing the SDK integration.
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration. Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Test partner configuration
const testPartner = {
  name: 'Test Partner',
  email: '<EMAIL>',
  company: 'AtlasAI Inc',
  client_id: 'test-partner-client',
  client_secret: 'test-partner-secret',
  redirect_uris: ['http://localhost:3001/auth/callback'],
  website: 'http://localhost:3001',
  description: 'Test partner for SDK integration',
  logo_url: 'https://via.placeholder.com/150',
  status: 'active'
};

// Test features
const testFeatures = [
  {
    feature_id: 'data-export-pro',
    display_name: 'Advanced Data Export',
    description: 'Export your data in multiple formats with advanced filtering options',
    default_credit_cost: 5,
    metric_type: 'api_calls'
  },
  {
    feature_id: 'ai-content-gen',
    display_name: 'AI Content Generation',
    description: 'Generate high-quality content using advanced AI models',
    default_credit_cost: 10,
    metric_type: 'resources'
  },
  {
    feature_id: 'custom-reports',
    display_name: 'Custom Report Builder',
    description: 'Create custom reports with advanced analytics and visualizations',
    default_credit_cost: 8,
    metric_type: 'resources'
  },
  {
    feature_id: 'priority-api',
    display_name: 'Priority API Access',
    description: 'Get priority access to our API with higher rate limits',
    default_credit_cost: 3,
    metric_type: 'time'
  }
];

// Register the test partner
async function registerTestPartner() {
  try {
    console.log('Registering test partner...');
    
    // Check if the partner already exists
    const { data: existingPartner, error: checkError } = await supabase
      .from('partner_accounts')
      .select('id, client_id')
      .eq('client_id', testPartner.client_id)
      .maybeSingle();
    
    if (checkError) {
      throw new Error(`Error checking for existing partner: ${checkError.message}`);
    }
    
    let partnerId;
    
    if (existingPartner) {
      console.log(`Partner already exists with ID: ${existingPartner.id}`);
      partnerId = existingPartner.id;
      
      // Update the existing partner
      const { error: updateError } = await supabase
        .from('partner_accounts')
        .update({
          name: testPartner.name,
          email: testPartner.email,
          company: testPartner.company,
          client_secret: testPartner.client_secret,
          redirect_uris: testPartner.redirect_uris,
          website: testPartner.website,
          description: testPartner.description,
          logo_url: testPartner.logo_url,
          status: testPartner.status,
          updated_at: new Date().toISOString()
        })
        .eq('id', partnerId);
      
      if (updateError) {
        throw new Error(`Error updating partner: ${updateError.message}`);
      }
      
      console.log('Partner updated successfully');
    } else {
      // Insert a new partner
      const { data: newPartner, error: insertError } = await supabase
        .from('partner_accounts')
        .insert({
          name: testPartner.name,
          email: testPartner.email,
          company: testPartner.company,
          client_id: testPartner.client_id,
          client_secret: testPartner.client_secret,
          redirect_uris: testPartner.redirect_uris,
          website: testPartner.website,
          description: testPartner.description,
          logo_url: testPartner.logo_url,
          status: testPartner.status
        })
        .select()
        .single();
      
      if (insertError) {
        throw new Error(`Error inserting partner: ${insertError.message}`);
      }
      
      partnerId = newPartner.id;
      console.log(`Partner created with ID: ${partnerId}`);
    }
    
    // Register features
    console.log('Registering test features...');
    
    for (const feature of testFeatures) {
      // Check if the feature already exists
      const { data: existingFeature, error: featureCheckError } = await supabase
        .from('partner_features')
        .select('id')
        .eq('partner_id', partnerId)
        .eq('feature_id', feature.feature_id)
        .maybeSingle();
      
      if (featureCheckError) {
        throw new Error(`Error checking for existing feature: ${featureCheckError.message}`);
      }
      
      if (existingFeature) {
        // Update the existing feature
        const { error: featureUpdateError } = await supabase
          .from('partner_features')
          .update({
            display_name: feature.display_name,
            description: feature.description,
            default_credit_cost: feature.default_credit_cost,
            metric_type: feature.metric_type,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingFeature.id);
        
        if (featureUpdateError) {
          throw new Error(`Error updating feature: ${featureUpdateError.message}`);
        }
        
        console.log(`Feature '${feature.feature_id}' updated`);
      } else {
        // Insert a new feature
        const { error: featureInsertError } = await supabase
          .from('partner_features')
          .insert({
            partner_id: partnerId,
            feature_id: feature.feature_id,
            display_name: feature.display_name,
            description: feature.description,
            default_credit_cost: feature.default_credit_cost,
            metric_type: feature.metric_type
          });
        
        if (featureInsertError) {
          throw new Error(`Error inserting feature: ${featureInsertError.message}`);
        }
        
        console.log(`Feature '${feature.feature_id}' created`);
      }
    }
    
    console.log('Test partner registration completed successfully');
  } catch (error) {
    console.error('Error registering test partner:', error);
    process.exit(1);
  }
}

// Run the registration
registerTestPartner();
