import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  MessageSquare,
  Image,
  Headphones,
  Code,
  FileText,
  Sparkles,
  Layers,
  Zap,
  Star,
  Clock
} from 'lucide-react';

interface CategoryFilterProps {
  categories: string[];
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onSelectCategory
}) => {
  // Get icon for category
  const getCategoryIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-4 w-4 mr-2" />;
      case 'Image Generation':
        return <Image className="h-4 w-4 mr-2" />;
      case 'Audio Processing':
        return <Headphones className="h-4 w-4 mr-2" />;
      case 'Code Generation':
        return <Code className="h-4 w-4 mr-2" />;
      case 'Document Processing':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'All Tools':
        return <Layers className="h-4 w-4 mr-2" />;
      case 'Popular':
        return <Star className="h-4 w-4 mr-2" />;
      case 'New':
        return <Sparkles className="h-4 w-4 mr-2" />;
      case 'Recently Used':
        return <Clock className="h-4 w-4 mr-2" />;
      default:
        return <Zap className="h-4 w-4 mr-2" />;
    }
  };

  // Get background color for category
  const getCategoryColor = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return 'from-blue-500/20 to-blue-500/5';
      case 'Image Generation':
        return 'from-purple-500/20 to-purple-500/5';
      case 'Audio Processing':
        return 'from-teal-500/20 to-teal-500/5';
      case 'Code Generation':
        return 'from-green-500/20 to-green-500/5';
      case 'Document Processing':
        return 'from-amber-500/20 to-amber-500/5';
      default:
        return 'from-fiery/20 to-fiery/5';
    }
  };

  // Get text color for category
  const getCategoryTextColor = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return 'text-blue-400';
      case 'Image Generation':
        return 'text-purple-400';
      case 'Audio Processing':
        return 'text-teal-400';
      case 'Code Generation':
        return 'text-green-400';
      case 'Document Processing':
        return 'text-amber-400';
      default:
        return 'text-fiery';
    }
  };

  return (
    <div className="flex flex-wrap gap-1.5 overflow-x-auto pb-1 scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
      <Button
        key="all"
        variant={selectedCategory === 'All Tools' ? "default" : "outline"}
        size="sm"
        className={`
          whitespace-nowrap transition-all duration-300 rounded-full px-3 py-0 h-7 text-xs
          ${selectedCategory === 'All Tools'
            ? `bg-gradient-to-r from-fiery/20 to-fiery/5 border-none shadow-sm hover:shadow text-fiery`
            : "border-white/10 hover:bg-white/5 text-white/80 hover:border-white/30"
          }
        `}
        onClick={() => onSelectCategory('All Tools')}
      >
        <span className={`${selectedCategory === 'All Tools' ? 'text-fiery' : 'text-white/70'} transition-colors duration-300`}>
          <Layers className="h-3 w-3 mr-1" />
        </span>
        All
      </Button>

      {categories.filter(c => c !== 'All Tools').map((category) => {
        const isSelected = selectedCategory === category;
        const bgColor = getCategoryColor(category);
        const textColor = getCategoryTextColor(category);

        return (
          <Button
            key={category}
            variant={isSelected ? "default" : "outline"}
            size="sm"
            className={`
              whitespace-nowrap transition-all duration-300 rounded-full px-3 py-0 h-7 text-xs
              ${isSelected
                ? `bg-gradient-to-r ${bgColor} border-none shadow-sm hover:shadow ${textColor}`
                : "border-white/10 hover:bg-white/5 text-white/80 hover:border-white/30"
              }
            `}
            onClick={() => onSelectCategory(category)}
          >
            <span className={`${isSelected ? textColor : 'text-white/70'} transition-colors duration-300`}>
              {getCategoryIcon(category)}
            </span>
            {category}
          </Button>
        );
      })}
    </div>
  );
};

export default CategoryFilter;
