import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api } from '@/lib/api'
import toast from 'react-hot-toast'

export interface User {
  id: string
  email: string
  name: string
  role: string
  auth0Id?: string
  avatar?: string
  lastLogin?: string
  permissions?: string[]
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (idToken: string, provider?: string) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<void>
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,

      login: async (idToken: string, provider = 'auth0') => {
        try {
          set({ isLoading: true })
          
          const response = await api.post('/auth/login', {
            idToken,
            provider
          })

          const { token, user } = response.data

          // Set authorization header for future requests
          api.defaults.headers.common['Authorization'] = `Bear<PERSON> ${token}`

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false
          })

          toast.success('Login successful!')
        } catch (error: any) {
          console.error('Login failed:', error)
          set({ isLoading: false })
          
          const message = error.response?.data?.message || 'Login failed'
          toast.error(message)
          throw error
        }
      },

      logout: async () => {
        try {
          // Call logout endpoint for audit logging
          await api.post('/auth/logout')
        } catch (error) {
          console.error('Logout API call failed:', error)
        }

        // Clear auth state
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })

        // Clear authorization header
        delete api.defaults.headers.common['Authorization']

        toast.success('Logged out successfully')
      },

      refreshToken: async () => {
        try {
          const { token } = get()
          if (!token) {
            throw new Error('No token to refresh')
          }

          const response = await api.post('/auth/refresh', { token })
          const { token: newToken, user } = response.data

          // Update authorization header
          api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`

          set({
            user,
            token: newToken,
            isAuthenticated: true
          })
        } catch (error) {
          console.error('Token refresh failed:', error)
          // Force logout on refresh failure
          get().logout()
          throw error
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'firenest-sandbox-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.token) {
          // Set authorization header on app load
          api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`
        }
        // Set loading to false after rehydration
        state?.setLoading(false)
      }
    }
  )
)

// Auto-refresh token before expiration
let refreshTimer: NodeJS.Timeout | null = null

const scheduleTokenRefresh = (token: string) => {
  if (refreshTimer) {
    clearTimeout(refreshTimer)
  }

  try {
    // Decode JWT to get expiration time
    const payload = JSON.parse(atob(token.split('.')[1]))
    const expirationTime = payload.exp * 1000 // Convert to milliseconds
    const currentTime = Date.now()
    const timeUntilExpiry = expirationTime - currentTime
    
    // Refresh 5 minutes before expiration
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 0)

    if (refreshTime > 0) {
      refreshTimer = setTimeout(() => {
        const { refreshToken } = useAuthStore.getState()
        refreshToken().catch(() => {
          // Refresh failed, user will be logged out
        })
      }, refreshTime)
    }
  } catch (error) {
    console.error('Failed to schedule token refresh:', error)
  }
}

// Subscribe to token changes to schedule refresh
useAuthStore.subscribe(
  (state) => state.token,
  (token) => {
    if (token) {
      scheduleTokenRefresh(token)
    } else if (refreshTimer) {
      clearTimeout(refreshTimer)
      refreshTimer = null
    }
  }
)
