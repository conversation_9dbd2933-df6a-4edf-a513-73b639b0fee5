import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface LoadingProps {
  variant?: 'default' | 'spinner' | 'pulse' | 'dots';
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
  textClassName?: string;
  fullScreen?: boolean;
}

/**
 * Modern loading component with multiple variants
 */
export function Loading({
  variant = 'default',
  size = 'md',
  text,
  className,
  textClassName,
  fullScreen = false,
}: LoadingProps) {
  const containerClasses = cn(
    'flex flex-col items-center justify-center',
    fullScreen && 'min-h-screen bg-dark-950',
    className
  );

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  const renderLoader = () => {
    switch (variant) {
      case 'spinner':
        return (
          <div className="relative">
            <div className={cn(
              "border-4 border-fiery/30 border-t-fiery rounded-full animate-spin",
              size === 'sm' ? 'h-6 w-6' : size === 'md' ? 'h-10 w-10' : 'h-16 w-16'
            )}></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className={cn(
                "bg-fiery rounded-full opacity-80",
                size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-5 w-5' : 'h-8 w-8'
              )}></div>
            </div>
          </div>
        );
      
      case 'pulse':
        return (
          <div className="relative">
            <div className={cn(
              "rounded-full bg-fiery/20 animate-pulse",
              size === 'sm' ? 'h-6 w-6' : size === 'md' ? 'h-10 w-10' : 'h-16 w-16'
            )}>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className={cn(
                  "bg-fiery rounded-full animate-ping opacity-75",
                  size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-5 w-5' : 'h-8 w-8'
                )}></div>
              </div>
            </div>
          </div>
        );
      
      case 'dots':
        return (
          <div className="flex space-x-2">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  "bg-fiery rounded-full animate-bounce",
                  size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : 'h-4 w-4',
                )}
                style={{ animationDelay: `${i * 0.1}s` }}
              ></div>
            ))}
          </div>
        );
      
      case 'default':
      default:
        return (
          <div className="relative">
            {/* Outer ring */}
            <div className={cn(
              "rounded-full border-2 border-fiery/30 animate-spin",
              sizeClasses[size]
            )}>
              {/* Inner gradient */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-fiery to-cool-500 opacity-40 blur-sm"></div>
              {/* Spinner arc */}
              <div className="absolute inset-0 rounded-full border-t-2 border-fiery animate-spin" 
                   style={{ animationDirection: 'reverse', animationDuration: '0.8s' }}></div>
              {/* Center dot */}
              <div className="absolute inset-0 m-auto rounded-full bg-fiery"
                   style={{ width: '30%', height: '30%' }}></div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={containerClasses}>
      {renderLoader()}
      {text && (
        <p className={cn(
          "mt-4 text-white/70 font-medium",
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base',
          textClassName
        )}>
          {text}
        </p>
      )}
    </div>
  );
}

/**
 * Full-screen loading component with enhanced design
 */
export function FullScreenLoading({
  text = 'Loading...',
  variant = 'default',
}) {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-dark-950 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Top gradient */}
        <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent z-10" />
        
        {/* Animated background shapes */}
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
      </div>
      
      {/* Content */}
      <div className="z-10 flex flex-col items-center justify-center p-8">
        <div className="glass-card p-8 flex flex-col items-center justify-center relative">
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
          <div className="absolute bottom-0 left-0 w-40 h-40 bg-cool-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>
          
          <Loading variant={variant} size="lg" text={text} />
          
          <div className="mt-6 text-white/50 text-sm max-w-md text-center">
            <p>We're preparing your experience. This should only take a moment.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Inline loading spinner for buttons and small UI elements
 */
export function InlineLoading({ className }: { className?: string }) {
  return <Loader2 className={cn("animate-spin", className || "h-4 w-4")} />;
}
