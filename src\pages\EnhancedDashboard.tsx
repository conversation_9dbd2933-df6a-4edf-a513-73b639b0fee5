import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Layers,
  Zap,
  Clock,
  ArrowRight,
  Star,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import EnhancedOverviewSection from '@/components/dashboard/EnhancedOverviewSection';
import AIToolIcon from '@/components/AIToolIcon';

/**
 * Enhanced Dashboard Page with professional design patterns
 * Inspired by industry leaders like Zapier and HubSpot
 */
const EnhancedDashboard = () => {
  const { user, credits, transactions } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for recent tools
  const recentTools = [
    {
      id: 'chatgpt',
      name: 'ChatGPT',
      description: 'AI assistant for text generation',
      icon: 'Bot',
      lastUsed: new Date(Date.now() - 3600000 * 2).toISOString(),
      category: 'Text Generation',
      usageCount: 24
    },
    {
      id: 'midjourney',
      name: 'Midjourney',
      description: 'AI image generation from text prompts',
      icon: 'Image',
      lastUsed: new Date(Date.now() - 3600000 * 5).toISOString(),
      category: 'Image Generation',
      usageCount: 18
    },
    {
      id: 'claude',
      name: 'Claude',
      description: 'Anthropic\'s AI assistant',
      icon: 'Bot',
      lastUsed: new Date(Date.now() - 3600000 * 24).toISOString(),
      category: 'Text Generation',
      usageCount: 12
    }
  ];

  // Mock data for usage chart
  const usageData = [
    { day: 'Mon', credits: 25 },
    { day: 'Tue', credits: 40 },
    { day: 'Wed', credits: 30 },
    { day: 'Thu', credits: 50 },
    { day: 'Fri', credits: 45 },
    { day: 'Sat', credits: 20 },
    { day: 'Sun', credits: 15 },
  ];

  // Calculate max value for chart scaling
  const maxCredits = Math.max(...usageData.map(day => day.credits));

  // Calculate total credits used this week
  const totalCreditsThisWeek = usageData.reduce((sum, day) => sum + day.credits, 0);

  // Mock data for recommended tools
  const recommendedTools = [
    {
      id: 'dalle',
      name: 'DALL-E',
      description: 'OpenAI\'s image generation model',
      icon: 'Image',
      category: 'Image Generation',
      rating: 4.8,
      reason: 'Based on your usage of Midjourney'
    },
    {
      id: 'whisper',
      name: 'Whisper',
      description: 'Speech-to-text transcription',
      icon: 'Audio',
      category: 'Audio Processing',
      rating: 4.6,
      reason: 'Popular with users like you'
    },
    {
      id: 'stable-diffusion',
      name: 'Stable Diffusion',
      description: 'Open-source image generation',
      icon: 'Image',
      category: 'Image Generation',
      rating: 4.7,
      reason: 'Trending in your industry'
    }
  ];

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard</h1>
          <p className="text-white/70">Welcome back, {user?.name || 'User'}</p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="border-white/10 hover:bg-white/5"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button className="bg-fiery hover:bg-fiery-600 text-white">
            <Layers className="h-4 w-4 mr-2" />
            Launch Tool
          </Button>
        </div>
      </div>

      {/* Main content */}
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="bg-dark-800 border border-white/10">
          <TabsTrigger value="overview" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="activity" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Activity
          </TabsTrigger>
          <TabsTrigger value="tools" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            My Tools
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {isLoading ? (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <Card key={i} className="firenest-card">
                    <CardHeader className="pb-2">
                      <div className="h-4 w-32 bg-white/5 rounded"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-8 w-20 bg-white/5 rounded mb-2"></div>
                      <div className="h-3 w-full bg-white/5 rounded mb-4"></div>
                      <div className="h-12 w-full bg-white/5 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <Card className="firenest-card">
                <CardHeader>
                  <div className="h-6 w-40 bg-white/5 rounded"></div>
                  <div className="h-4 w-60 bg-white/5 rounded"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="h-16 bg-white/5 rounded"></div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <EnhancedOverviewSection
              credits={credits}
              transactions={transactions}
              recentTools={recentTools}
              usageData={usageData}
              maxCredits={maxCredits}
              totalCreditsThisWeek={totalCreditsThisWeek}
            />
          )}

          {/* Recommended Tools Section */}
          {!isLoading && (
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-lg text-white flex items-center">
                  <Star className="h-5 w-5 mr-2 text-yellow-400" />
                  Recommended Tools
                </CardTitle>
                <CardDescription>Based on your usage patterns and preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {recommendedTools.map((tool) => (
                    <div
                      key={tool.id}
                      className="firenest-card-accent p-4 cursor-pointer"
                    >
                      <div className="flex items-center gap-3 mb-3">
                        <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                          <AIToolIcon iconName={tool.icon} className="h-5 w-5 text-fiery" />
                        </div>
                        <div>
                          <h3 className="font-medium text-white">{tool.name}</h3>
                          <p className="text-xs text-white/70">{tool.category}</p>
                        </div>
                      </div>
                      <p className="text-sm text-white/80 mb-3 line-clamp-2">{tool.description}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Star className="h-3 w-3 mr-1 text-yellow-400" />
                          <span className="text-xs text-white/70">{tool.rating.toFixed(1)}</span>
                        </div>
                        <Button
                          size="sm"
                          className="bg-fiery hover:bg-fiery-600 h-8"
                        >
                          <Zap className="h-3 w-3 mr-1" />
                          <span>Try Now</span>
                        </Button>
                      </div>
                      <div className="mt-3 bg-fiery/10 rounded p-2">
                        <p className="text-xs text-fiery">{tool.reason}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card className="firenest-card-accent">
            <CardHeader>
              <CardTitle className="text-lg text-white flex items-center">
                <Clock className="h-5 w-5 mr-2 text-fiery" />
                Recent Activity
              </CardTitle>
              <CardDescription>Your recent interactions with AI tools</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTools.map((tool) => (
                  <div
                    key={tool.id}
                    className="flex items-center justify-between p-4 firenest-nested-card"
                  >
                    <div className="flex items-center gap-4">
                      <div className="h-12 w-12 rounded-md bg-fiery/20 flex items-center justify-center">
                        <AIToolIcon iconName={tool.icon} className="h-6 w-6 text-fiery" />
                      </div>
                      <div>
                        <h3 className="font-medium text-white">{tool.name}</h3>
                        <p className="text-xs text-white/70">{tool.category}</p>
                        <p className="text-xs text-white/50 mt-1">
                          Last used: {formatDate(tool.lastUsed)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <p className="text-sm text-white/90">{tool.usageCount} uses</p>
                        <p className="text-xs text-white/50">this month</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-fiery/30 text-fiery hover:bg-fiery/10"
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        Launch
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-lg text-white flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-fiery" />
                Usage Insights
              </CardTitle>
              <CardDescription>Understand your tool usage patterns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="firenest-card p-4">
                  <h3 className="text-sm font-medium text-white mb-2">Most Used Categories</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span className="text-white/70">Text Generation</span>
                        <span className="text-white">65%</span>
                      </div>
                      <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                        <div className="h-full bg-fiery rounded-full" style={{ width: '65%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span className="text-white/70">Image Generation</span>
                        <span className="text-white">25%</span>
                      </div>
                      <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                        <div className="h-full bg-fiery-600 rounded-full" style={{ width: '25%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span className="text-white/70">Audio Processing</span>
                        <span className="text-white">10%</span>
                      </div>
                      <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                        <div className="h-full bg-fiery-300 rounded-full" style={{ width: '10%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="firenest-card p-4">
                  <h3 className="text-sm font-medium text-white mb-2">Usage Efficiency</h3>
                  <div className="flex items-center justify-between">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">87%</div>
                      <p className="text-xs text-white/70">Efficiency Score</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">12.3</div>
                      <p className="text-xs text-white/70">Avg. Credits/Task</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">24%</div>
                      <p className="text-xs text-white/70">Credit Savings</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tools" className="space-y-6">
          <Card className="firenest-card-accent">
            <CardHeader>
              <CardTitle className="text-lg text-white flex items-center">
                <Layers className="h-5 w-5 mr-2 text-fiery" />
                My Tools
              </CardTitle>
              <CardDescription>Tools you've used recently</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recentTools.map((tool) => (
                  <div
                    key={tool.id}
                    className="firenest-card-accent p-4 cursor-pointer"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                        <AIToolIcon iconName={tool.icon} className="h-5 w-5 text-fiery" />
                      </div>
                      <div>
                        <h3 className="font-medium text-white">{tool.name}</h3>
                        <p className="text-xs text-white/70">{tool.category}</p>
                      </div>
                    </div>
                    <p className="text-sm text-white/80 mb-3">{tool.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-white/70">
                        Used {tool.usageCount} times
                      </div>
                      <Button
                        size="sm"
                        className="bg-fiery hover:bg-fiery-600 h-8"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        <span>Launch</span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <div className="px-6 py-4 border-t border-white/10 flex justify-center">
              <Button
                variant="outline"
                className="border-white/10 hover:bg-white/5"
                asChild
              >
                <a href="/dashboard/workbench">
                  View All Tools
                  <ArrowRight className="h-4 w-4 ml-2" />
                </a>
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedDashboard;
