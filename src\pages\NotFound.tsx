import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import { FileQuestion, Search, Home, RefreshCw, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { logError, ErrorCategory, ErrorSeverity } from "@/lib/error-utils";

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Common pages that users might be looking for
  const suggestions = [
    { path: "/", name: "Home" },
    { path: "/login", name: "Login" },
    { path: "/signup", name: "Sign Up" },
    { path: "/dashboard", name: "Dashboard" },
    { path: "/profile", name: "<PERSON>" },
    { path: "/credits", name: "Credits" },
    { path: "/support", name: "Support" },
  ];

  useEffect(() => {
    // Log the 404 error
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );

    // Log to our error tracking system
    logError(
      new Error(`Page not found: ${location.pathname}`),
      null,
      ErrorSeverity.WARNING,
      ErrorCategory.RENDERING,
      { path: location.pathname }
    );
  }, [location.pathname]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // In a real implementation, this would search the site
      // For now, just redirect to home
      navigate("/");
    }
  };

  const handleGoHome = () => {
    navigate("/");
  };

  const toggleSuggestions = () => {
    setShowSuggestions(!showSuggestions);
  };

  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />

      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>

      <main className="flex-grow flex items-center justify-center p-6">
        <div className="w-full max-w-2xl">
          <div className="glass-card p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

            <div className="flex flex-col items-center mb-8">
              <div className="w-20 h-20 bg-amber-500/10 rounded-full flex items-center justify-center mb-4">
                <FileQuestion className="h-10 w-10 text-amber-500 animate-pulse-slow" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Page Not Found</h2>
              <p className="text-white/70 text-center">We couldn't find the page you're looking for</p>
              <div className="mt-2 px-3 py-1 bg-dark-800/50 rounded-full text-white/60 text-sm">
                Error 404
              </div>
            </div>

            <div className="mb-6">
              <form onSubmit={handleSearch} className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
                  <Input
                    type="text"
                    placeholder="Search for pages..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-dark-800 border-white/10"
                  />
                </div>
                <Button type="submit" className="bg-fiery hover:bg-fiery-600 text-white">
                  Search
                </Button>
              </form>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <Button
                onClick={handleGoHome}
                className="flex-1 bg-fiery hover:bg-fiery-600 text-white"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>

              <Button
                onClick={() => window.history.back()}
                variant="outline"
                className="flex-1 border-white/20"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>

            <div className="border-t border-white/10 pt-4">
              <button
                onClick={toggleSuggestions}
                className="flex items-center justify-between w-full text-white/70 hover:text-white transition-colors py-2"
              >
                <span className="font-medium">Popular Pages</span>
                {showSuggestions ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </button>

              {showSuggestions && (
                <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {suggestions.map((suggestion) => (
                    <Link
                      key={suggestion.path}
                      to={suggestion.path}
                      className="px-4 py-2 bg-dark-800/50 hover:bg-dark-700/50 border border-white/10 rounded-md text-white/80 hover:text-white transition-colors"
                    >
                      {suggestion.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <footer className="py-4 px-6 text-center text-white/50 text-sm">
        <p>
          <Link to="/" className="text-fiery hover:text-fiery-400 hover:underline">
            Firenest
          </Link>{' '}
          &copy; {new Date().getFullYear()} All rights reserved.
        </p>
      </footer>
    </div>
  );
};

export default NotFound;
