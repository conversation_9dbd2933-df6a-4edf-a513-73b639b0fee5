# Email Verification System

This document explains the email verification system implemented in the Firenest application.

## Overview

The email verification system has two main components:

1. **Supabase Email Verification**: The primary method using Supabase's built-in email verification system
2. **Manual Verification**: A fallback method for users who don't receive the verification email

## How It Works

### Supabase Email Verification

1. When a user signs up, Supabase sends a verification email to the user's email address
2. The email contains a link that redirects to `/verify-email-confirm` in our application
3. When the user clicks the link, our application verifies the token and confirms the email

### Manual Verification (Fallback)

1. If a user doesn't receive the verification email, they can use the manual verification system
2. When a user requests to resend the verification email, a 6-digit verification code is generated
3. The code is stored in localStorage (in a real production app, this would be in a database)
4. The user can enter this code on the manual verification page to verify their email

## Implementation Details

### Files

- `src/pages/EmailVerification.tsx`: The main verification page that users see after signup
- `src/pages/VerifyEmailConfirm.tsx`: Handles the verification link from the email
- `src/pages/ManualVerification.tsx`: Allows users to verify their email with a code
- `src/lib/verification.ts`: Utility functions for the manual verification system
- `email-templates/confirmation.html`: Custom email template for Supabase

### Routes

- `/verify-email`: Main verification page
- `/verify-email-confirm`: Handles the verification link from the email
- `/manual-verification`: Manual verification page

## Setup Instructions

### Supabase Configuration

See the `SUPABASE_EMAIL_SETUP.md` file for detailed instructions on setting up Supabase email verification.

### Local Development

For local development and testing:

1. The system automatically generates and displays verification codes in a toast notification
2. The backup code `123456` will always work for testing purposes
3. Email verification status is checked using multiple methods to ensure reliability

## Troubleshooting

### Common Issues

1. **Emails not being sent**: Check the Supabase SMTP configuration
2. **Verification link not working**: Ensure the redirect URL in the code matches the allowed URLs in Supabase
3. **Manual verification not working**: Check browser console for errors

### Debugging

- Check browser console logs for detailed error messages
- Verification codes are logged to the console during development
- The system includes detailed error handling and user feedback

## Security Considerations

- In production, verification codes should be stored in a secure database with expiration
- The backup code (`123456`) should be removed in production
- Consider implementing rate limiting for verification attempts
- Email templates should be properly sanitized to prevent XSS attacks

## Future Improvements

- Add SMS verification as an alternative method
- Implement server-side verification code storage
- Add rate limiting for verification attempts
- Improve error handling and user feedback
- Add analytics to track verification success rates
