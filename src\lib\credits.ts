import { supabase } from './supabase';
import { notify } from '@/components/ui/notification-system';
import { v4 as uuidv4 } from 'uuid';

export interface PurchaseCreditsOptions {
  userId: string;
  amount: number;
  paymentMethod: 'card' | 'paypal' | 'crypto';
  description?: string;
  price?: number;
  paymentCurrency?: string;
  idempotencyKey?: string;
  modifiedBy?: string;
}

export interface UseCreditsOptions {
  userId: string;
  amount: number;
  serviceId: string;
  description?: string;
  idempotencyKey?: string;
  modifiedBy?: string;
}

export interface CreditTransactionResult {
  success: boolean;
  data?: {
    transactionId: string;
    newTotalCredits: number;
    newAvailableCredits: number;
  };
  error?: any;
}

/**
 * Purchase credits for a user
 * This function:
 * 1. Updates the user's total credits
 * 2. Records a purchase transaction
 */
export async function purchaseCredits(options: PurchaseCreditsOptions): Promise<CreditTransactionResult> {
  const { userId, amount, paymentMethod, description, price } = options;

  if (!userId) {
    return {
      success: false,
      error: { message: 'User ID is required' }
    };
  }

  if (!amount || amount <= 0) {
    return {
      success: false,
      error: { message: 'Credit amount must be greater than 0' }
    };
  }

  try {
    // Generate a payment reference (simulating a payment processor reference)
    const paymentReference = `payment_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Generate an idempotency key if not provided
    const idempotencyKey = options.idempotencyKey || `purchase_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Use the Supabase function to purchase credits
    const { data, error } = await supabase
      .rpc('purchase_credits', {
        user_id: userId,
        amount: amount,
        description: description || `Purchased ${amount} credits${price ? ` for $${price}` : ''}`,
        payment_method: paymentMethod,
        payment_reference: paymentReference,
        payment_amount: price,
        payment_currency: options.paymentCurrency || 'USD',
        idempotency_key: idempotencyKey,
        modified_by: options.modifiedBy || userId
      });

    if (error) {
      console.error('Error purchasing credits:', error);
      return {
        success: false,
        error: {
          message: 'Failed to purchase credits',
          details: error
        }
      };
    }

    // Parse the result
    if (data && data.success) {
      return {
        success: true,
        data: data.data
      };
    } else if (data && !data.success) {
      return {
        success: false,
        error: {
          message: data.error || 'Failed to purchase credits'
        }
      };
    }

    // Fallback error
    return {
      success: false,
      error: {
        message: 'Unknown error occurred'
      }
    };

  } catch (error) {
    console.error('Error purchasing credits:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Use credits for a service
 * This function:
 * 1. Updates the user's used credits
 * 2. Records a usage transaction
 * 3. Checks if the user has enough credits
 */
export async function useCredits(options: UseCreditsOptions): Promise<CreditTransactionResult> {
  const { userId, amount, serviceId, description } = options;

  if (!userId) {
    return {
      success: false,
      error: { message: 'User ID is required' }
    };
  }

  if (!amount || amount <= 0) {
    return {
      success: false,
      error: { message: 'Credit amount must be greater than 0' }
    };
  }

  if (!serviceId) {
    return {
      success: false,
      error: { message: 'Service ID is required' }
    };
  }

  try {
    // Generate an idempotency key if not provided
    const idempotencyKey = options.idempotencyKey || `use_${userId}_${serviceId}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Use the Supabase function to use credits
    const { data, error } = await supabase
      .rpc('use_credits', {
        user_id: userId,
        amount: amount,
        service_id: serviceId,
        description: description || `Used ${amount} credits for ${serviceId}`,
        idempotency_key: idempotencyKey,
        modified_by: options.modifiedBy || userId
      });

    if (error) {
      console.error('Error using credits:', error);
      return {
        success: false,
        error: {
          message: 'Failed to use credits',
          details: error
        }
      };
    }

    // Parse the result
    if (data && data.success) {
      return {
        success: true,
        data: data.data
      };
    } else if (data && !data.success) {
      return {
        success: false,
        error: {
          message: data.error || 'Failed to use credits'
        }
      };
    }

    // Fallback error
    return {
      success: false,
      error: {
        message: 'Unknown error occurred'
      }
    };

  } catch (error) {
    console.error('Error using credits:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Add bonus credits to a user
 * This function:
 * 1. Updates the user's total credits
 * 2. Records a bonus transaction
 */
export async function addBonusCredits(
  userId: string,
  amount: number,
  description?: string
): Promise<CreditTransactionResult> {
  if (!userId) {
    return {
      success: false,
      error: { message: 'User ID is required' }
    };
  }

  if (!amount || amount <= 0) {
    return {
      success: false,
      error: { message: 'Credit amount must be greater than 0' }
    };
  }

  try {
    // Generate an idempotency key if not provided
    const idempotencyKey = `bonus_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Use the Supabase function to add bonus credits
    const { data, error } = await supabase
      .rpc('add_bonus_credits', {
        user_id: userId,
        amount: amount,
        description: description || `Bonus: ${amount} credits added`,
        idempotency_key: idempotencyKey,
        modified_by: userId
      });

    if (error) {
      console.error('Error adding bonus credits:', error);
      return {
        success: false,
        error: {
          message: 'Failed to add bonus credits',
          details: error
        }
      };
    }

    // Parse the result
    if (data && data.success) {
      return {
        success: true,
        data: data.data
      };
    } else if (data && !data.success) {
      return {
        success: false,
        error: {
          message: data.error || 'Failed to add bonus credits'
        }
      };
    }

    // Fallback error
    return {
      success: false,
      error: {
        message: 'Unknown error occurred'
      }
    };

  } catch (error) {
    console.error('Error adding bonus credits:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Refund credits to a user
 * This function:
 * 1. Updates the user's used credits (decreases them)
 * 2. Records a refund transaction
 */
export async function refundCredits(
  userId: string,
  amount: number,
  description?: string
): Promise<CreditTransactionResult> {
  if (!userId) {
    return {
      success: false,
      error: { message: 'User ID is required' }
    };
  }

  if (!amount || amount <= 0) {
    return {
      success: false,
      error: { message: 'Credit amount must be greater than 0' }
    };
  }

  try {
    // Generate an idempotency key if not provided
    const idempotencyKey = `refund_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Use the Supabase function to refund credits
    const { data, error } = await supabase
      .rpc('refund_credits', {
        user_id: userId,
        amount: amount,
        description: description || `Refund: ${amount} credits`,
        idempotency_key: idempotencyKey,
        modified_by: userId
      });

    if (error) {
      console.error('Error refunding credits:', error);
      return {
        success: false,
        error: {
          message: 'Failed to refund credits',
          details: error
        }
      };
    }

    // Parse the result
    if (data && data.success) {
      return {
        success: true,
        data: data.data
      };
    } else if (data && !data.success) {
      return {
        success: false,
        error: {
          message: data.error || 'Failed to refund credits'
        }
      };
    }

    // Fallback error
    return {
      success: false,
      error: {
        message: 'Unknown error occurred'
      }
    };

  } catch (error) {
    console.error('Error refunding credits:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Get a mock payment processor token
 * In a real application, this would interact with a payment processor API
 */
export async function getMockPaymentToken(
  paymentMethod: 'card' | 'paypal' | 'crypto',
  amount: number
): Promise<{ success: boolean; token?: string; error?: any }> {
  // Simulate API call to payment processor
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Generate a random token
  const token = `${paymentMethod}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

  // Simulate success (95% of the time)
  if (Math.random() > 0.05) {
    return { success: true, token };
  } else {
    return {
      success: false,
      error: { message: 'Payment processor error: Transaction declined' }
    };
  }
}

/**
 * Process a payment with a mock payment processor
 * In a real application, this would interact with a payment processor API
 */
export async function processMockPayment(
  token: string,
  amount: number,
  currency: string = 'USD'
): Promise<{ success: boolean; transactionId?: string; error?: any }> {
  // Simulate API call to payment processor
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Generate a random transaction ID
  const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

  // Simulate success (98% of the time)
  if (Math.random() > 0.02) {
    return { success: true, transactionId };
  } else {
    return {
      success: false,
      error: { message: 'Payment processor error: Payment failed' }
    };
  }
}
