/**
 * Simulation Results Dashboard
 * Advanced analytics and revenue intelligence visualization
 */

import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Download,
  GitCompare,
  Target,
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'
import { simulationsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatCurrency, formatNumber, formatPercentage } from '@/lib/utils'

interface SimulationResultsProps {
  simulationId: string
  onBack?: () => void
}

export function SimulationResults({ simulationId, onBack }: SimulationResultsProps) {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'models' | 'customers' | 'insights'>('overview')

  const { data: simulation, isLoading } = useQuery({
    queryKey: ['simulation', simulationId],
    queryFn: () => simulationsApi.get(simulationId)
  })

  if (isLoading) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  const simulationData = simulation?.data?.data
  if (!simulationData) {
    return (
      <div className="firenest-card">
        <div className="text-center py-12">
          <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Results Not Available</h3>
          <p className="text-gray-400">Simulation results could not be loaded.</p>
        </div>
      </div>
    )
  }

  const results = simulationData.results || []
  const totalRevenue = results.reduce((sum: number, result: any) => sum + (result.total_revenue || 0), 0)
  const totalCustomers = results.reduce((sum: number, result: any) => sum + (result.customer_count || 0), 0) / results.length // Average across models
  const avgRevenuePerCustomer = totalCustomers > 0 ? totalRevenue / totalCustomers : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {simulationData.name || 'Simulation Results'}
              </h1>
              <p className="text-gray-400">
                Completed {new Date(simulationData.completed_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex space-x-3">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <GitCompare className="w-4 h-4 mr-2" />
              Compare
            </Button>
            {onBack && (
              <Button variant="outline" onClick={onBack}>
                Back
              </Button>
            )}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Total Revenue</p>
                <p className="metric-value">{formatCurrency(totalRevenue)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Customers Analyzed</p>
                <p className="metric-value">{formatNumber(Math.round(totalCustomers))}</p>
              </div>
              <Users className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Avg Revenue/Customer</p>
                <p className="metric-value">{formatCurrency(avgRevenuePerCustomer)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-400" />
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Models Compared</p>
                <p className="metric-value">{results.length}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-fiery" />
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="firenest-card">
        <div className="flex space-x-1 p-1 bg-muted rounded-lg">
          {[
            { key: 'overview', label: 'Overview', icon: BarChart3 },
            { key: 'models', label: 'Model Comparison', icon: GitCompare },
            { key: 'customers', label: 'Customer Analysis', icon: Users },
            { key: 'insights', label: 'Insights', icon: Target }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedTab(tab.key as any)}
              className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedTab === tab.key
                  ? 'bg-fiery text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {selectedTab === 'overview' && (
        <OverviewTab results={results} simulationData={simulationData} />
      )}

      {selectedTab === 'models' && (
        <ModelComparisonTab results={results} />
      )}

      {selectedTab === 'customers' && (
        <CustomerAnalysisTab results={results} />
      )}

      {selectedTab === 'insights' && (
        <InsightsTab results={results} />
      )}
    </div>
  )
}

function OverviewTab({ results, simulationData }: { results: any[], simulationData: any }) {
  const bestModel = results.reduce((best, current) => 
    (current.total_revenue > (best?.total_revenue || 0)) ? current : best, null
  )

  const worstModel = results.reduce((worst, current) => 
    (current.total_revenue < (worst?.total_revenue || Infinity)) ? current : worst, null
  )

  return (
    <div className="space-y-6">
      {/* Revenue Comparison */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-6">Revenue by Model</h3>
        
        <div className="space-y-4">
          {results.map((result, index) => {
            const percentage = results.length > 1 ? 
              (result.total_revenue / Math.max(...results.map((r: any) => r.total_revenue))) * 100 : 100
            
            return (
              <div key={result.model_id} className="firenest-nested-card">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-fiery/20 rounded-lg flex items-center justify-center">
                      <span className="text-fiery text-sm font-bold">{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">{result.model_name}</h4>
                      <Badge variant="secondary" className="text-xs">
                        {result.model_type?.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-lg font-semibold text-white">
                      {formatCurrency(result.total_revenue)}
                    </div>
                    <div className="text-sm text-gray-400">
                      {formatCurrency(result.avg_revenue_per_customer)}/customer
                    </div>
                  </div>
                </div>

                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-fiery h-2 rounded-full transition-all duration-500"
                    style={{ width: `${percentage}%` }}
                  />
                </div>

                <div className="flex justify-between text-xs text-gray-400 mt-2">
                  <span>{formatNumber(result.customer_count)} customers</span>
                  <span>{formatPercentage(percentage / 100)} of max revenue</span>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Performance Highlights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {bestModel && (
          <div className="firenest-card border-l-4 border-green-500">
            <div className="flex items-center space-x-3 mb-4">
              <ArrowUp className="w-6 h-6 text-green-400" />
              <h3 className="text-lg font-semibold text-white">Best Performing Model</h3>
            </div>
            
            <div className="space-y-3">
              <div>
                <h4 className="text-white font-medium">{bestModel.model_name}</h4>
                <p className="text-sm text-gray-400">{bestModel.model_type?.replace('_', ' ')}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-lg font-semibold text-green-400">
                    {formatCurrency(bestModel.total_revenue)}
                  </div>
                  <div className="text-xs text-gray-400">Total Revenue</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-green-400">
                    {formatCurrency(bestModel.avg_revenue_per_customer)}
                  </div>
                  <div className="text-xs text-gray-400">Avg/Customer</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {worstModel && bestModel && worstModel.model_id !== bestModel.model_id && (
          <div className="firenest-card border-l-4 border-red-500">
            <div className="flex items-center space-x-3 mb-4">
              <ArrowDown className="w-6 h-6 text-red-400" />
              <h3 className="text-lg font-semibold text-white">Lowest Performing Model</h3>
            </div>
            
            <div className="space-y-3">
              <div>
                <h4 className="text-white font-medium">{worstModel.model_name}</h4>
                <p className="text-sm text-gray-400">{worstModel.model_type?.replace('_', ' ')}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-lg font-semibold text-red-400">
                    {formatCurrency(worstModel.total_revenue)}
                  </div>
                  <div className="text-xs text-gray-400">Total Revenue</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-red-400">
                    {formatCurrency(worstModel.avg_revenue_per_customer)}
                  </div>
                  <div className="text-xs text-gray-400">Avg/Customer</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function ModelComparisonTab({ results }: { results: any[] }) {
  return (
    <div className="firenest-card">
      <h3 className="text-lg font-semibold text-white mb-6">Detailed Model Comparison</h3>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-white/10">
              <th className="text-left py-3 px-4 text-gray-400 font-medium">Model</th>
              <th className="text-right py-3 px-4 text-gray-400 font-medium">Total Revenue</th>
              <th className="text-right py-3 px-4 text-gray-400 font-medium">Customers</th>
              <th className="text-right py-3 px-4 text-gray-400 font-medium">Avg/Customer</th>
              <th className="text-right py-3 px-4 text-gray-400 font-medium">Min Revenue</th>
              <th className="text-right py-3 px-4 text-gray-400 font-medium">Max Revenue</th>
            </tr>
          </thead>
          <tbody>
            {results.map((result) => (
              <tr key={result.model_id} className="border-b border-white/5">
                <td className="py-4 px-4">
                  <div>
                    <div className="text-white font-medium">{result.model_name}</div>
                    <div className="text-sm text-gray-400">{result.model_type?.replace('_', ' ')}</div>
                  </div>
                </td>
                <td className="py-4 px-4 text-right text-white font-semibold">
                  {formatCurrency(result.total_revenue)}
                </td>
                <td className="py-4 px-4 text-right text-white">
                  {formatNumber(result.customer_count)}
                </td>
                <td className="py-4 px-4 text-right text-white">
                  {formatCurrency(result.avg_revenue_per_customer)}
                </td>
                <td className="py-4 px-4 text-right text-gray-400">
                  {formatCurrency(result.min_customer_revenue)}
                </td>
                <td className="py-4 px-4 text-right text-gray-400">
                  {formatCurrency(result.max_customer_revenue)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

function CustomerAnalysisTab({ results }: { results: any[] }) {
  return (
    <div className="firenest-card">
      <h3 className="text-lg font-semibold text-white mb-6">Customer Revenue Distribution</h3>
      <p className="text-gray-400 mb-6">
        Analysis of how different pricing models affect customer segments
      </p>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {results.map((result) => (
          <div key={result.model_id} className="firenest-nested-card">
            <h4 className="text-white font-medium mb-4">{result.model_name}</h4>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Minimum Revenue:</span>
                <span className="text-white">{formatCurrency(result.min_customer_revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Average Revenue:</span>
                <span className="text-white">{formatCurrency(result.avg_revenue_per_customer)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Maximum Revenue:</span>
                <span className="text-white">{formatCurrency(result.max_customer_revenue)}</span>
              </div>
              {result.median_revenue && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Median Revenue:</span>
                  <span className="text-white">{formatCurrency(result.median_revenue)}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function InsightsTab({ results }: { results: any[] }) {
  const insights = generateInsights(results)

  return (
    <div className="space-y-6">
      {insights.map((insight, index) => (
        <div key={index} className="firenest-card">
          <div className="flex items-start space-x-4">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${insight.color}`}>
              <insight.icon className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">{insight.title}</h3>
              <p className="text-gray-400 mb-4">{insight.description}</p>
              {insight.recommendation && (
                <div className="p-3 bg-fiery/10 border border-fiery/20 rounded-lg">
                  <p className="text-sm text-fiery font-medium">Recommendation:</p>
                  <p className="text-sm text-gray-300">{insight.recommendation}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

function generateInsights(results: any[]) {
  const insights = []

  if (results.length > 1) {
    const revenues = results.map(r => r.total_revenue)
    const maxRevenue = Math.max(...revenues)
    const minRevenue = Math.min(...revenues)
    const revenueDiff = maxRevenue - minRevenue
    const percentDiff = (revenueDiff / minRevenue) * 100

    if (percentDiff > 20) {
      insights.push({
        icon: TrendingUp,
        color: 'bg-green-500',
        title: 'Significant Revenue Opportunity',
        description: `The best performing model generates ${formatPercentage(percentDiff / 100)} more revenue than the lowest performing model.`,
        recommendation: 'Consider implementing the highest performing pricing model to maximize revenue potential.'
      })
    }

    // Customer impact analysis
    const avgRevenuePerCustomer = results.map(r => r.avg_revenue_per_customer)
    const maxAvg = Math.max(...avgRevenuePerCustomer)
    const minAvg = Math.min(...avgRevenuePerCustomer)
    const avgDiff = ((maxAvg - minAvg) / minAvg) * 100

    if (avgDiff > 15) {
      insights.push({
        icon: Users,
        color: 'bg-blue-500',
        title: 'Customer Value Optimization',
        description: `Different pricing models show up to ${formatPercentage(avgDiff / 100)} variation in average revenue per customer.`,
        recommendation: 'Analyze customer segments to understand which pricing model works best for different user types.'
      })
    }
  }

  // Add general insights
  insights.push({
    icon: Target,
    color: 'bg-purple-500',
    title: 'Pricing Strategy Validation',
    description: 'Simulation results provide data-driven insights for pricing optimization and strategic decision making.',
    recommendation: 'Use these results to refine your pricing strategy and consider A/B testing the top performing models.'
  })

  return insights
}
