import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { getIntegrationConfig, saveIntegrationConfig } from '@/lib/partner-portal/api';
import { PartnerAuthMethod } from '@/lib/partner-portal/types';
import PartnerFooter from '@/components/partner/PartnerFooter';
import { ArrowLeft, Save } from 'lucide-react';

const PartnerAuthentication: React.FC = () => {
  const navigate = useNavigate();
  const { partner, tools } = usePartner();

  const [selectedToolId, setSelectedToolId] = useState<string>('');
  const [authMethod, setAuthMethod] = useState<PartnerAuthMethod>('oauth');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // OAuth config state
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [authorizationUrl, setAuthorizationUrl] = useState('');
  const [tokenUrl, setTokenUrl] = useState('');
  const [scope, setScope] = useState('');
  const [responseType, setResponseType] = useState<'code' | 'token'>('code');

  // OIDC config state
  const [oidcIssuer, setOidcIssuer] = useState('');
  const [oidcClientId, setOidcClientId] = useState('');
  const [oidcClientSecret, setOidcClientSecret] = useState('');
  const [oidcScope, setOidcScope] = useState('openid profile email');

  // API Key config state
  const [headerName, setHeaderName] = useState('Authorization');
  const [isBearer, setIsBearer] = useState(true);
  const [queryParamName, setQueryParamName] = useState('');

  useEffect(() => {
    // Set the first tool as selected by default if available
    if (tools.length > 0 && !selectedToolId) {
      setSelectedToolId(tools[0].id);
    }
  }, [tools, selectedToolId]);

  useEffect(() => {
    const loadConfig = async () => {
      if (!selectedToolId) return;

      setIsLoading(true);
      try {
        const config = await getIntegrationConfig(selectedToolId);

        if (config) {
          setAuthMethod(config.authMethod);

          // Set form values based on auth method
          if (config.authMethod === 'oauth') {
            const oauthConfig = config.configData as any;
            setClientId(oauthConfig.clientId || '');
            setClientSecret(oauthConfig.clientSecret || '');
            setAuthorizationUrl(oauthConfig.authorizationUrl || '');
            setTokenUrl(oauthConfig.tokenUrl || '');
            setScope(oauthConfig.scope?.join(' ') || '');
            setResponseType(oauthConfig.responseType || 'code');
          } else if (config.authMethod === 'oidc') {
            const oidcConfig = config.configData as any;
            setOidcIssuer(oidcConfig.issuer || '');
            setOidcClientId(oidcConfig.clientId || '');
            setOidcClientSecret(oidcConfig.clientSecret || '');
            setOidcScope(oidcConfig.scope?.join(' ') || 'openid profile email');
          } else if (config.authMethod === 'api_key') {
            const apiKeyConfig = config.configData as any;
            setHeaderName(apiKeyConfig.headerName || 'Authorization');
            setIsBearer(apiKeyConfig.isBearer || true);
            setQueryParamName(apiKeyConfig.queryParamName || '');
          }
        }
      } catch (error) {
        console.error('Error loading integration config:', error);
        notify.error('Failed to load authentication configuration');
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, [selectedToolId]);

  const handleSaveOAuth = async () => {
    if (!selectedToolId) return;

    setIsSaving(true);

    try {
      const configData = {
        clientId,
        clientSecret,
        authorizationUrl,
        tokenUrl,
        redirectUrl: `${window.location.origin}/auth/callback/partner`,
        scope: scope.split(' ').filter(s => s),
        responseType
      };

      const savedConfig = await saveIntegrationConfig(selectedToolId, 'oauth', configData);

      if (!savedConfig) {
        throw new Error('Failed to save OAuth configuration');
      }

      notify.success('OAuth configuration saved successfully');
    } catch (error: any) {
      console.error('Error saving OAuth config:', error);
      notify.error(error.message || 'Failed to save OAuth configuration');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveOIDC = async () => {
    if (!selectedToolId) return;

    setIsSaving(true);

    try {
      // Get the values of the checkboxes
      const enablePkce = (document.getElementById('enablePkce') as HTMLInputElement)?.checked ?? true;
      const validateNonce = (document.getElementById('validateNonce') as HTMLInputElement)?.checked ?? true;

      const configData = {
        issuer: oidcIssuer,
        clientId: oidcClientId,
        clientSecret: oidcClientSecret,
        redirectUrl: `${window.location.origin}/auth/callback/partner`,
        scope: oidcScope.split(' ').filter(s => s),
        responseType: 'code',
        pkceEnabled: enablePkce,
        nonceValidationEnabled: validateNonce,
        // Add discovery endpoints based on the issuer URL if available
        ...(oidcIssuer ? {
          authorizationUrl: `${oidcIssuer.replace(/\/$/, '')}/auth`,
          tokenUrl: `${oidcIssuer.replace(/\/$/, '')}/token`,
          userInfoUrl: `${oidcIssuer.replace(/\/$/, '')}/userinfo`,
          jwksUrl: `${oidcIssuer.replace(/\/$/, '')}/.well-known/jwks.json`,
        } : {})
      };

      const savedConfig = await saveIntegrationConfig(selectedToolId, 'oidc', configData);

      if (!savedConfig) {
        throw new Error('Failed to save OIDC configuration');
      }

      notify.success('OIDC configuration saved successfully');
    } catch (error: any) {
      console.error('Error saving OIDC config:', error);
      notify.error(error.message || 'Failed to save OIDC configuration');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveApiKey = async () => {
    if (!selectedToolId) return;

    setIsSaving(true);

    try {
      const configData = {
        headerName,
        isBearer,
        queryParamName: queryParamName || undefined
      };

      const savedConfig = await saveIntegrationConfig(selectedToolId, 'api_key', configData);

      if (!savedConfig) {
        throw new Error('Failed to save API Key configuration');
      }

      notify.success('API Key configuration saved successfully');
    } catch (error: any) {
      console.error('Error saving API Key config:', error);
      notify.error(error.message || 'Failed to save API Key configuration');
    } finally {
      setIsSaving(false);
    }
  };

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Authentication Configuration</h1>
                <p className="text-white/60">Set up how users will authenticate with your tools</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Tool Selection</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="toolSelect">Select Tool</Label>
                      <Select value={selectedToolId} onValueChange={setSelectedToolId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tool" />
                        </SelectTrigger>
                        <SelectContent>
                          {tools.map(tool => (
                            <SelectItem key={tool.id} value={tool.id}>{tool.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedToolId && (
                      <div className="space-y-2">
                        <Label htmlFor="authMethod">Authentication Method</Label>
                        <Select value={authMethod} onValueChange={(value) => setAuthMethod(value as PartnerAuthMethod)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select authentication method" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="oauth">OAuth 2.0</SelectItem>
                            <SelectItem value="oidc">OpenID Connect</SelectItem>
                            <SelectItem value="api_key">API Key</SelectItem>
                            <SelectItem value="credentials">Username/Password</SelectItem>
                            <SelectItem value="ip_based">IP-Based</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {selectedToolId && (
                <Card className="firenest-card">
                  <CardHeader>
                    <CardTitle className="text-lg text-white">Authentication Help</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <p className="text-white/70 text-sm">
                        Configure how users will authenticate with your tool. Choose the authentication method that best suits your needs.
                      </p>
                      <div className="space-y-2">
                        <div className="flex items-start space-x-2">
                          <div className="w-5 h-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5">
                            <span className="text-fiery text-xs">1</span>
                          </div>
                          <p className="text-white/70 text-sm">Select your tool from the dropdown</p>
                        </div>
                        <div className="flex items-start space-x-2">
                          <div className="w-5 h-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5">
                            <span className="text-fiery text-xs">2</span>
                          </div>
                          <p className="text-white/70 text-sm">Choose an authentication method</p>
                        </div>
                        <div className="flex items-start space-x-2">
                          <div className="w-5 h-5 rounded-full bg-fiery/20 flex items-center justify-center mt-0.5">
                            <span className="text-fiery text-xs">3</span>
                          </div>
                          <p className="text-white/70 text-sm">Configure the required settings</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full mt-2 text-white/70 hover:text-white"
                        onClick={() => navigate('/partner/documentation/oidc')}
                      >
                        View Documentation
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {selectedToolId && (
          <>
            {authMethod === 'oauth' && (
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">OAuth 2.0 Configuration</CardTitle>
                  <CardDescription>
                    Configure OAuth 2.0 for secure user authentication
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="clientId">Client ID</Label>
                      <Input
                        id="clientId"
                        value={clientId}
                        onChange={(e) => setClientId(e.target.value)}
                        placeholder="Your OAuth client ID"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="clientSecret">Client Secret</Label>
                      <Input
                        id="clientSecret"
                        type="password"
                        value={clientSecret}
                        onChange={(e) => setClientSecret(e.target.value)}
                        placeholder="Your OAuth client secret"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="authUrl">Authorization URL</Label>
                    <Input
                      id="authUrl"
                      value={authorizationUrl}
                      onChange={(e) => setAuthorizationUrl(e.target.value)}
                      placeholder="https://yourtool.com/oauth/authorize"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tokenUrl">Token URL</Label>
                    <Input
                      id="tokenUrl"
                      value={tokenUrl}
                      onChange={(e) => setTokenUrl(e.target.value)}
                      placeholder="https://yourtool.com/oauth/token"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="redirectUrl">Redirect URL (provided by Firenest)</Label>
                    <Input
                      id="redirectUrl"
                      value={`${window.location.origin}/auth/callback/partner`}
                      readOnly
                    />
                    <p className="text-sm text-white/60">
                      Add this URL to your OAuth application's allowed redirect URLs.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="scope">Scopes</Label>
                    <Input
                      id="scope"
                      value={scope}
                      onChange={(e) => setScope(e.target.value)}
                      placeholder="read write profile"
                    />
                    <p className="text-sm text-white/60">
                      Space-separated list of OAuth scopes required by your application.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="responseType">Response Type</Label>
                    <Select value={responseType} onValueChange={(value) => setResponseType(value as 'code' | 'token')}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select response type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="code">Authorization Code (recommended)</SelectItem>
                        <SelectItem value="token">Implicit</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    className="bg-fiery hover:bg-fiery/90 text-white flex items-center space-x-2"
                    onClick={handleSaveOAuth}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loading size="sm" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        <span>Save OAuth Configuration</span>
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            )}

            {authMethod === 'oidc' && (
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">OpenID Connect Configuration</CardTitle>
                  <CardDescription>
                    Configure OpenID Connect for secure user authentication
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="oidcIssuer">OIDC Issuer URL</Label>
                    <Input
                      id="oidcIssuer"
                      value={oidcIssuer}
                      onChange={(e) => setOidcIssuer(e.target.value)}
                      placeholder="https://yourtool.com"
                    />
                    <p className="text-sm text-white/60">
                      The base URL of your OpenID Connect provider.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="oidcClientId">Client ID</Label>
                      <Input
                        id="oidcClientId"
                        value={oidcClientId}
                        onChange={(e) => setOidcClientId(e.target.value)}
                        placeholder="Your OIDC client ID"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="oidcClientSecret">Client Secret</Label>
                      <Input
                        id="oidcClientSecret"
                        type="password"
                        value={oidcClientSecret}
                        onChange={(e) => setOidcClientSecret(e.target.value)}
                        placeholder="Your OIDC client secret"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="oidcRedirectUrl">Redirect URL (provided by Firenest)</Label>
                    <div className="relative">
                      <Input
                        id="oidcRedirectUrl"
                        value={`${window.location.origin}/auth/callback/partner`}
                        readOnly
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 px-2 text-white/70 hover:text-white"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.origin}/auth/callback/partner`);
                          notify.success('Redirect URL copied to clipboard');
                        }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                        </svg>
                      </Button>
                    </div>
                    <p className="text-sm text-white/60">
                      Add this URL to your OIDC application's allowed redirect URLs.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="oidcScope">Scopes</Label>
                    <Input
                      id="oidcScope"
                      value={oidcScope}
                      onChange={(e) => setOidcScope(e.target.value)}
                      placeholder="openid profile email"
                    />
                    <p className="text-sm text-white/60">
                      Space-separated list of OIDC scopes. The 'openid' scope is required.
                    </p>
                  </div>

                  <div className="bg-blue-900/30 p-4 rounded-lg border border-blue-700/50">
                    <h4 className="text-white font-medium mb-2 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-blue-400">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="16" x2="12" y2="12"></line>
                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                      </svg>
                      Advanced Security Options
                    </h4>
                    <div className="space-y-4 mt-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="enablePkce"
                          defaultChecked
                          className="rounded border-white/20 bg-dark-800/50"
                        />
                        <Label htmlFor="enablePkce" className="text-sm font-normal cursor-pointer">
                          Enable PKCE (Proof Key for Code Exchange)
                        </Label>
                      </div>
                      <p className="text-xs text-white/60">
                        PKCE enhances security by preventing authorization code interception attacks.
                        Recommended for all public clients and mobile applications.
                      </p>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="validateNonce"
                          defaultChecked
                          className="rounded border-white/20 bg-dark-800/50"
                        />
                        <Label htmlFor="validateNonce" className="text-sm font-normal cursor-pointer">
                          Validate nonce parameter
                        </Label>
                      </div>
                      <p className="text-xs text-white/60">
                        The nonce parameter helps prevent replay attacks by ensuring each
                        authentication request is unique.
                      </p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    className="text-white border-white/20 hover:bg-white/10"
                    onClick={() => navigate('/partner/documentation/oidc')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                    </svg>
                    View Documentation
                  </Button>
                  <Button
                    className="bg-fiery hover:bg-fiery/90 text-white flex items-center space-x-2"
                    onClick={handleSaveOIDC}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loading size="sm" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        <span>Save OIDC Configuration</span>
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            )}

            {authMethod === 'api_key' && (
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">API Key Configuration</CardTitle>
                  <CardDescription>
                    Configure API Key authentication for your tool
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="headerName">Header Name</Label>
                    <Input
                      id="headerName"
                      value={headerName}
                      onChange={(e) => setHeaderName(e.target.value)}
                      placeholder="Authorization"
                    />
                    <p className="text-sm text-white/60">
                      The HTTP header name that will contain the API key.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={isBearer}
                        onChange={(e) => setIsBearer(e.target.checked)}
                        className="rounded border-white/20 bg-dark-800/50"
                      />
                      <span>Use Bearer Token Format</span>
                    </Label>
                    <p className="text-sm text-white/60">
                      If enabled, the API key will be sent as "Bearer {apiKey}". Otherwise, it will be sent as is.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="queryParamName">Query Parameter Name (Optional)</Label>
                    <Input
                      id="queryParamName"
                      value={queryParamName}
                      onChange={(e) => setQueryParamName(e.target.value)}
                      placeholder="api_key"
                    />
                    <p className="text-sm text-white/60">
                      If provided, the API key will also be sent as a query parameter with this name.
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    className="bg-fiery hover:bg-fiery/90 text-white flex items-center space-x-2"
                    onClick={handleSaveApiKey}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loading size="sm" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        <span>Save API Key Configuration</span>
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            )}

            {authMethod === 'credentials' && (
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">Username/Password Configuration</CardTitle>
                  <CardDescription>
                    Configure username/password authentication for your tool
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/60 mb-6">
                    This authentication method allows users to log in with their username and password. You'll need to provide the login endpoint and field names.
                  </p>

                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6">
                    <p className="text-yellow-500">
                      Note: Username/password authentication is less secure than OAuth or OIDC. We recommend using those methods when possible.
                    </p>
                  </div>

                  {/* Placeholder for credentials configuration */}
                  <p className="text-white/60 text-center py-4">
                    Username/Password configuration coming soon
                  </p>
                </CardContent>
              </Card>
            )}

            {authMethod === 'ip_based' && (
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">IP-Based Authentication</CardTitle>
                  <CardDescription>
                    Configure IP-based authentication for your tool
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/60 mb-6">
                    This authentication method allows users to access your tool based on their IP address. You'll need to specify allowed IP ranges.
                  </p>

                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6">
                    <p className="text-yellow-500">
                      Note: IP-based authentication should be used in combination with other authentication methods for better security.
                    </p>
                  </div>

                  {/* Placeholder for IP-based configuration */}
                  <p className="text-white/60 text-center py-4">
                    IP-Based authentication configuration coming soon
                  </p>
                </CardContent>
              </Card>
            )}
          </>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerAuthentication;
