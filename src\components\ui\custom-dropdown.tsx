import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DropdownItem {
  id: string;
  label: React.ReactNode;
  icon?: React.ReactNode;
  onClick?: () => void;
  href?: string;
  className?: string;
  disabled?: boolean;
}

interface DropdownSection {
  title?: string;
  items: DropdownItem[];
}

interface CustomDropdownProps {
  trigger: React.ReactNode;
  sections: DropdownSection[];
  align?: 'left' | 'right';
  width?: string;
  className?: string;
  triggerClassName?: string;
  menuClassName?: string;
  closeOnClick?: boolean;
}

export const CustomDropdown: React.FC<CustomDropdownProps> = ({
  trigger,
  sections,
  align = 'left',
  width = 'w-56',
  className,
  triggerClassName,
  menuClassName,
  closeOnClick = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleItemClick = (item: DropdownItem) => {
    if (item.onClick) {
      item.onClick();
    }
    if (closeOnClick) {
      setIsOpen(false);
    }
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <div
        className={cn(
          "cursor-pointer",
          triggerClassName
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        {trigger}
      </div>

      {isOpen && (
        <div
          className={cn(
            "absolute mt-2 firenest-card backdrop-blur-md text-white shadow-lg rounded-md overflow-hidden z-50",
            width,
            align === 'right' ? 'right-0' : 'left-0',
            menuClassName
          )}
        >
          {sections.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              {section.title && (
                <div className="px-4 py-2 text-xs font-semibold text-white/60 uppercase tracking-wider bg-dark-900/30">
                  {section.title}
                </div>
              )}
              <div className="p-1">
                {section.items.map((item) => (
                  <div
                    key={item.id}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                      item.disabled
                        ? "opacity-50 cursor-not-allowed text-white/50"
                        : "cursor-pointer hover:bg-white/5",
                      item.className
                    )}
                    onClick={() => !item.disabled && handleItemClick(item)}
                  >
                    {item.icon && <span className="mr-2">{item.icon}</span>}
                    {item.href ? (
                      <a
                        href={item.href}
                        className="flex-1"
                        onClick={(e) => {
                          if (item.disabled) {
                            e.preventDefault();
                          }
                        }}
                      >
                        {item.label}
                      </a>
                    ) : (
                      <span className="flex-1">{item.label}</span>
                    )}
                  </div>
                ))}
              </div>
              {sectionIndex < sections.length - 1 && (
                <div className="border-t border-white/10 my-1"></div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface DropdownButtonProps {
  label: React.ReactNode;
  sections: DropdownSection[];
  align?: 'left' | 'right';
  width?: string;
  className?: string;
  buttonClassName?: string;
  menuClassName?: string;
  icon?: React.ReactNode;
  showChevron?: boolean;
}

export const DropdownButton: React.FC<DropdownButtonProps> = ({
  label,
  sections,
  align = 'left',
  width = 'w-56',
  className,
  buttonClassName,
  menuClassName,
  icon,
  showChevron = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <CustomDropdown
      trigger={
        <button
          className={cn(
            "flex items-center gap-2 px-3 py-2 rounded-md text-white/80 hover:text-white hover:bg-white/5 transition-colors",
            buttonClassName
          )}
        >
          {icon && <span>{icon}</span>}
          <span>{label}</span>
          {showChevron && (
            <ChevronDown
              className={cn(
                "h-4 w-4 transition-transform duration-200",
                isOpen ? "rotate-180" : ""
              )}
            />
          )}
        </button>
      }
      sections={sections}
      align={align}
      width={width}
      className={className}
      menuClassName={menuClassName}
      closeOnClick={true}
    />
  );
};

export default CustomDropdown;
