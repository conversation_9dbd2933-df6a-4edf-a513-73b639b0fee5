/**
 * Settings Page - User Management & Security Settings
 * SOC 2 Alignment: CC6.1 (Logical Access), CC6.2 (Access Control), CC6.3 (Access Revocation)
 */

import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Settings,
  User,
  Shield,
  Bell,
  Key,
  Download,
  Trash2,
  Edit,
  Save,
  X,
  Check,
  AlertTriangle,
  Eye,
  EyeOff,
  Clock,
  Activity,
  FileText,
  Lock
} from 'lucide-react'
import { authApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate } from '@/lib/utils'
import toast from 'react-hot-toast'

export function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'notifications' | 'audit'>('profile')

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white">Settings</h1>
        <p className="text-gray-400 mt-1">
          Manage your account, security, and preferences
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="firenest-card">
        <div className="flex space-x-1 p-1 bg-muted rounded-lg">
          <button
            onClick={() => setActiveTab('profile')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'profile'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <User className="w-4 h-4 mr-2" />
            Profile
          </button>
          <button
            onClick={() => setActiveTab('security')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'security'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Shield className="w-4 h-4 mr-2" />
            Security
          </button>
          <button
            onClick={() => setActiveTab('notifications')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'notifications'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Bell className="w-4 h-4 mr-2" />
            Notifications
          </button>
          <button
            onClick={() => setActiveTab('audit')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'audit'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Activity className="w-4 h-4 mr-2" />
            Audit Log
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'profile' && <ProfileSettings />}
      {activeTab === 'security' && <SecuritySettings />}
      {activeTab === 'notifications' && <NotificationSettings />}
      {activeTab === 'audit' && <AuditLogSettings />}
    </div>
  )
}

// Profile Settings Component
function ProfileSettings() {
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: 'Demo User',
    email: '<EMAIL>',
    title: 'Pricing Analyst',
    company: 'Firenest Demo',
    timezone: 'UTC'
  })

  const handleSave = () => {
    // In real implementation, this would call the API
    toast.success('Profile updated successfully!')
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      {/* Profile Information */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Profile Information</h3>
          <Button
            variant={isEditing ? "outline" : "ghost"}
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? <X className="w-4 h-4 mr-2" /> : <Edit className="w-4 h-4 mr-2" />}
            {isEditing ? 'Cancel' : 'Edit'}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Full Name
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white"
              />
            ) : (
              <p className="text-white">{formData.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Email Address
            </label>
            {isEditing ? (
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white"
              />
            ) : (
              <p className="text-white">{formData.email}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Job Title
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white"
              />
            ) : (
              <p className="text-white">{formData.title}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Company
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.company}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white"
              />
            ) : (
              <p className="text-white">{formData.company}</p>
            )}
          </div>
        </div>

        {isEditing && (
          <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-white/10">
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="bg-fiery hover:bg-fiery/90">
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

// Security Settings Component
function SecuritySettings() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const handlePasswordChange = () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error('New passwords do not match')
      return
    }
    // In real implementation, this would call the API
    toast.success('Password updated successfully!')
    setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
  }

  return (
    <div className="space-y-6">
      {/* Password Change */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-6">Change Password</h3>

        <div className="space-y-4 max-w-md">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Current Password
            </label>
            <div className="relative">
              <input
                type={showCurrentPassword ? "text" : "password"}
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 pr-10 text-white"
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              New Password
            </label>
            <div className="relative">
              <input
                type={showNewPassword ? "text" : "password"}
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 pr-10 text-white"
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Confirm New Password
            </label>
            <input
              type="password"
              value={passwordForm.confirmPassword}
              onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
              className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <Button onClick={handlePasswordChange} className="bg-fiery hover:bg-fiery/90">
            <Lock className="w-4 h-4 mr-2" />
            Update Password
          </Button>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-white">Two-Factor Authentication</h3>
            <p className="text-gray-400 text-sm">Add an extra layer of security to your account</p>
          </div>
          <Badge variant="destructive">Disabled</Badge>
        </div>

        <Button variant="outline">
          <Shield className="w-4 h-4 mr-2" />
          Enable 2FA
        </Button>
      </div>

      {/* Active Sessions */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-6">Active Sessions</h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <Check className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <p className="text-white font-medium">Current Session</p>
                <p className="text-sm text-gray-400">Windows • Chrome • *************</p>
              </div>
            </div>
            <span className="text-sm text-gray-400">Active now</span>
          </div>
        </div>
      </div>

      {/* Data Export */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-white">Data Export</h3>
            <p className="text-gray-400 text-sm">Download your data for backup or migration</p>
          </div>
        </div>

        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* Danger Zone */}
      <div className="firenest-card border-red-500/20">
        <div className="flex items-start space-x-3 mb-6">
          <AlertTriangle className="w-6 h-6 text-red-400 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-white">Danger Zone</h3>
            <p className="text-gray-400 text-sm">Irreversible and destructive actions</p>
          </div>
        </div>

        <Button variant="destructive">
          <Trash2 className="w-4 h-4 mr-2" />
          Delete Account
        </Button>
      </div>
    </div>
  )
}

// Notification Settings Component
function NotificationSettings() {
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    simulationComplete: true,
    dataProcessed: true,
    weeklyReports: false,
    securityAlerts: true,
    marketingEmails: false
  })

  const handleToggle = (key: keyof typeof notifications) => {
    setNotifications(prev => ({ ...prev, [key]: !prev[key] }))
    toast.success('Notification preferences updated')
  }

  return (
    <div className="space-y-6">
      {/* Email Notifications */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-6">Email Notifications</h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Email Notifications</p>
              <p className="text-sm text-gray-400">Receive notifications via email</p>
            </div>
            <button
              onClick={() => handleToggle('emailNotifications')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.emailNotifications ? 'bg-fiery' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notifications.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Simulation Complete</p>
              <p className="text-sm text-gray-400">When simulations finish processing</p>
            </div>
            <button
              onClick={() => handleToggle('simulationComplete')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.simulationComplete ? 'bg-fiery' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notifications.simulationComplete ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Data Processed</p>
              <p className="text-sm text-gray-400">When data uploads are validated</p>
            </div>
            <button
              onClick={() => handleToggle('dataProcessed')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.dataProcessed ? 'bg-fiery' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notifications.dataProcessed ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Weekly Reports</p>
              <p className="text-sm text-gray-400">Summary of your activity</p>
            </div>
            <button
              onClick={() => handleToggle('weeklyReports')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.weeklyReports ? 'bg-fiery' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notifications.weeklyReports ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Security Alerts</p>
              <p className="text-sm text-gray-400">Important security notifications</p>
            </div>
            <button
              onClick={() => handleToggle('securityAlerts')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.securityAlerts ? 'bg-fiery' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notifications.securityAlerts ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Audit Log Settings Component
function AuditLogSettings() {
  // Mock audit log data
  const auditLogs = [
    {
      id: '1',
      action: 'User Login',
      timestamp: new Date().toISOString(),
      ip: '*************',
      userAgent: 'Chrome/91.0.4472.124',
      status: 'success'
    },
    {
      id: '2',
      action: 'Project Created',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      ip: '*************',
      userAgent: 'Chrome/91.0.4472.124',
      status: 'success'
    },
    {
      id: '3',
      action: 'Data Upload',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      ip: '*************',
      userAgent: 'Chrome/91.0.4472.124',
      status: 'success'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Audit Log */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export Log
          </Button>
        </div>

        <div className="space-y-3">
          {auditLogs.map((log) => (
            <div key={log.id} className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <Activity className="w-4 h-4 text-green-400" />
                </div>
                <div>
                  <p className="text-white font-medium">{log.action}</p>
                  <p className="text-sm text-gray-400">
                    {log.ip} • {log.userAgent.split('/')[0]}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <Badge variant="success" className="mb-1">
                  {log.status}
                </Badge>
                <p className="text-sm text-gray-400">
                  {formatDate(log.timestamp)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Compliance Information */}
      <div className="firenest-card">
        <div className="flex items-start space-x-3">
          <FileText className="w-6 h-6 text-blue-400 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">SOC 2 Compliance</h3>
            <p className="text-gray-400 text-sm mb-4">
              All user activities are logged and monitored in accordance with SOC 2 Type II requirements.
              Audit logs are retained for 7 years and are available for compliance reviews.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Clock className="w-6 h-6 text-blue-400 mx-auto mb-2" />
                <p className="text-white font-medium">7 Years</p>
                <p className="text-xs text-gray-400">Retention Period</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Shield className="w-6 h-6 text-green-400 mx-auto mb-2" />
                <p className="text-white font-medium">Encrypted</p>
                <p className="text-xs text-gray-400">At Rest & Transit</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <Check className="w-6 h-6 text-fiery mx-auto mb-2" />
                <p className="text-white font-medium">SOC 2</p>
                <p className="text-xs text-gray-400">Type II Certified</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
