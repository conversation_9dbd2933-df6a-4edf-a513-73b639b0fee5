/**
 * Component Palette
 * Drag-and-drop pricing components for model building
 */

import React from 'react'
import { 
  DollarSign, 
  TrendingUp, 
  BarChart3, 
  ArrowDown, 
  ArrowUp,
  Plus
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface ComponentPaletteProps {
  onAddComponent: (componentType: string) => void
}

const COMPONENT_TYPES = [
  {
    type: 'BASE_FEE',
    name: 'Base Fee',
    description: 'Fixed monthly or yearly fee',
    icon: DollarSign,
    color: 'text-green-400',
    bgColor: 'bg-green-400/20',
    example: '$99/month'
  },
  {
    type: 'PER_UNIT_RATE',
    name: 'Per-Unit Rate',
    description: 'Charge per usage unit',
    icon: TrendingUp,
    color: 'text-blue-400',
    bgColor: 'bg-blue-400/20',
    example: '$0.10 per API call'
  },
  {
    type: 'TIERED_RATE',
    name: 'Tiered Pricing',
    description: 'Different rates for usage tiers',
    icon: BarChart3,
    color: 'text-purple-400',
    bgColor: 'bg-purple-400/20',
    example: '$0.10 for 0-1K, $0.05 for 1K+'
  },
  {
    type: 'MINIMUM_FEE',
    name: 'Minimum Fee',
    description: 'Guaranteed minimum charge',
    icon: ArrowUp,
    color: 'text-orange-400',
    bgColor: 'bg-orange-400/20',
    example: 'Min $50/month'
  },
  {
    type: 'MAXIMUM_FEE',
    name: 'Maximum Fee',
    description: 'Cap on total charges',
    icon: ArrowDown,
    color: 'text-red-400',
    bgColor: 'bg-red-400/20',
    example: 'Max $1000/month'
  }
]

export function ComponentPalette({ onAddComponent }: ComponentPaletteProps) {
  return (
    <div className="firenest-card">
      <h3 className="text-lg font-semibold text-white mb-4">Pricing Components</h3>
      <p className="text-sm text-gray-400 mb-6">
        Click to add components to your pricing model
      </p>
      
      <div className="space-y-3">
        {COMPONENT_TYPES.map((component) => (
          <ComponentCard
            key={component.type}
            component={component}
            onAdd={() => onAddComponent(component.type)}
          />
        ))}
      </div>

      {/* Best Practices */}
      <div className="mt-8 p-4 bg-muted/50 rounded-lg">
        <h4 className="text-sm font-medium text-white mb-3">💡 Best Practices</h4>
        <ul className="space-y-2 text-xs text-gray-400">
          <li>• Start with a base fee for predictable revenue</li>
          <li>• Use tiered pricing to encourage higher usage</li>
          <li>• Add minimums to ensure profitability</li>
          <li>• Set maximums to reduce customer risk</li>
        </ul>
      </div>
    </div>
  )
}

interface ComponentCardProps {
  component: typeof COMPONENT_TYPES[0]
  onAdd: () => void
}

function ComponentCard({ component, onAdd }: ComponentCardProps) {
  return (
    <div className="group relative">
      <button
        onClick={onAdd}
        className="w-full p-4 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200 text-left bg-gradient-to-br from-transparent to-white/5 hover:to-white/10"
      >
        <div className="flex items-start space-x-3">
          <div className={`w-10 h-10 rounded-lg ${component.bgColor} flex items-center justify-center flex-shrink-0`}>
            <component.icon className={`w-5 h-5 ${component.color}`} />
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-white mb-1">
              {component.name}
            </h4>
            <p className="text-xs text-gray-400 mb-2">
              {component.description}
            </p>
            <div className="text-xs text-gray-500 font-mono">
              {component.example}
            </div>
          </div>
        </div>
        
        {/* Add button overlay */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="w-6 h-6 bg-fiery rounded-full flex items-center justify-center">
            <Plus className="w-3 h-3 text-white" />
          </div>
        </div>
      </button>
    </div>
  )
}

// Component type definitions for better type safety
export type ComponentType = 'BASE_FEE' | 'PER_UNIT_RATE' | 'TIERED_RATE' | 'MINIMUM_FEE' | 'MAXIMUM_FEE'

export const getComponentInfo = (type: ComponentType) => {
  return COMPONENT_TYPES.find(comp => comp.type === type)
}

export const getComponentIcon = (type: ComponentType) => {
  const info = getComponentInfo(type)
  return info?.icon || DollarSign
}

export const getComponentColor = (type: ComponentType) => {
  const info = getComponentInfo(type)
  return info?.color || 'text-gray-400'
}
