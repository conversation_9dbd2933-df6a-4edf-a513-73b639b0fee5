import { createClient } from '@supabase/supabase-js';

// Get Supabase URL and key from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://wjwguxccykrbarehqgpq.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indqd2d1eGNjeWtyYmFyZWhxZ3BxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwMjk0NTMsImV4cCI6MjA1OTYwNTQ1M30.woVFqaqa5oji3A9OOohYcUwzdZ3miBgOtexmdTxqLbU';

console.log('Initializing Supabase client with:', {
  url: supabaseUrl.substring(0, 15) + '...',
  hasKey: !!supabaseAnonKey
});

// Create Supabase client with custom options
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce' // Use PKCE flow for better security
  },
  global: {
    headers: {
      'Content-Type': 'application/json'
    }
  },
  db: {
    schema: 'public'
  }
});

// Helper function to check if Supabase connection is working
export const checkSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase.from('partners').select('count').limit(1);
    if (error) {
      console.error('Supabase connection error:', error);
      return false;
    }
    console.log('Supabase connection successful');
    return true;
  } catch (error) {
    console.error('Supabase connection error:', error);
    return false;
  }
};

// For development, we can check if email verification is required
export async function isEmailVerificationRequired(): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('auth_config')
      .select('email_confirmation_required')
      .single();

    if (error || !data) {
      console.log('Could not determine if email verification is required, assuming it is');
      return true;
    }

    return data.email_confirmation_required === true;
  } catch (error) {
    console.error('Error checking email verification requirement:', error);
    return true; // Default to requiring verification
  }
}

// Helper functions for form submissions
export async function submitEarlyAccess(email: string, name?: string) {
  try {
    console.log('Supabase client config:', { url: supabaseUrl.substring(0, 15) + '...', hasKey: !!supabaseAnonKey });
    console.log('Attempting to insert into early_access_requests table:', { email, name });

    // First check if the table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('early_access_requests')
      .select('id')
      .limit(1);

    if (tableError) {
      console.error('Error checking if table exists:', tableError);
      return { success: false, error: tableError, message: 'Table may not exist: ' + tableError.message };
    }

    // Now attempt the insert
    const { data, error } = await supabase
      .from('early_access_requests')
      .insert([
        {
          email,
          name: name || null,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Supabase insert error:', error);
      return { success: false, error, message: `Insert failed: ${error.message}` };
    }

    console.log('Successfully inserted data:', data);
    return { success: true, data };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error submitting early access request:', errorMessage);
    return { success: false, error, message: errorMessage };
  }
}

export async function submitContactForm(formData: {
  name: string;
  email: string;
  message: string;
  company?: string;
  phone?: string;
}) {
  try {
    const { data, error } = await supabase
      .from('contact_messages')
      .insert([
        {
          ...formData,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error submitting contact form:', error);
    return { success: false, error };
  }
}

export async function subscribeToNewsletter(email: string) {
  try {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .insert([
        {
          email,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error subscribing to newsletter:', error);
    return { success: false, error };
  }
}
