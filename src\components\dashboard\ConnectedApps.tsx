import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { getUserTokens, revokeUserPartnerTokens } from '@/lib/auth/oauth-utils';
import { useAuth } from '@/contexts/AuthContext';
import { Trash2, ExternalLink, AlertTriangle } from 'lucide-react';

interface ConnectedApp {
  id: string;
  clientId: string;
  partnerId: string;
  partnerName?: string;
  scope: string;
  createdAt: string;
  expiresAt: string;
}

const ConnectedApps: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [connectedApps, setConnectedApps] = useState<ConnectedApp[]>([]);
  const [isRevoking, setIsRevoking] = useState<string | null>(null);
  
  useEffect(() => {
    if (!user) return;
    
    const loadConnectedApps = async () => {
      setIsLoading(true);
      try {
        const tokens = await getUserTokens(user.id);
        
        // Transform tokens into connected apps
        const apps = tokens.map(token => ({
          id: token.id,
          clientId: token.client_id,
          partnerId: token.partner_id,
          scope: token.scope,
          createdAt: token.created_at,
          expiresAt: token.expires_at
        }));
        
        setConnectedApps(apps);
      } catch (error) {
        console.error('Error loading connected apps:', error);
        notify.error('Failed to load connected applications');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadConnectedApps();
  }, [user]);
  
  const handleRevokeAccess = async (app: ConnectedApp) => {
    if (!user) return;
    
    if (!window.confirm(`Are you sure you want to revoke access for this application? This action cannot be undone.`)) {
      return;
    }
    
    setIsRevoking(app.id);
    try {
      const success = await revokeUserPartnerTokens(user.id, app.partnerId);
      
      if (success) {
        setConnectedApps(prevApps => prevApps.filter(a => a.partnerId !== app.partnerId));
        notify.success('Application access revoked successfully');
      } else {
        throw new Error('Failed to revoke access');
      }
    } catch (error) {
      console.error('Error revoking access:', error);
      notify.error('Failed to revoke application access');
    } finally {
      setIsRevoking(null);
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };
  
  const formatScope = (scope: string) => {
    if (!scope) return 'No specific permissions';
    
    return scope.split(' ').map(s => {
      switch (s) {
        case 'openid':
          return 'Verify identity';
        case 'profile':
          return 'Access profile';
        case 'email':
          return 'Access email';
        case 'credits':
          return 'Use credits';
        default:
          return s;
      }
    }).join(', ');
  };
  
  if (isLoading) {
    return (
      <Card className="firenest-card">
        <CardHeader>
          <CardTitle className="text-xl text-white">Connected Applications</CardTitle>
          <CardDescription>
            Manage applications that have access to your Firenest account
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loading size="lg" />
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="firenest-card">
      <CardHeader>
        <CardTitle className="text-xl text-white">Connected Applications</CardTitle>
        <CardDescription>
          Manage applications that have access to your Firenest account
        </CardDescription>
      </CardHeader>
      <CardContent>
        {connectedApps.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-white/60">You don't have any connected applications</p>
          </div>
        ) : (
          <div className="space-y-4">
            {connectedApps.map(app => (
              <div key={app.id} className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-white font-medium">{app.clientId}</h3>
                    <p className="text-white/60 text-sm mt-1">
                      Connected on {formatDate(app.createdAt)}
                    </p>
                    <p className="text-white/60 text-sm mt-2">
                      <span className="text-white/80">Permissions:</span> {formatScope(app.scope)}
                    </p>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="bg-red-500/10 hover:bg-red-500/20 text-red-500 border-0"
                    onClick={() => handleRevokeAccess(app)}
                    disabled={isRevoking === app.id}
                  >
                    {isRevoking === app.id ? (
                      <Loading size="sm" className="mr-2" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-2" />
                    )}
                    Revoke Access
                  </Button>
                </div>
              </div>
            ))}
            
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mt-6">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-yellow-500 font-medium mb-1">Security Notice</h4>
                  <p className="text-white/70 text-sm">
                    Revoking access will prevent the application from accessing your Firenest account. You'll need to authorize the application again if you want to use it in the future.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ConnectedApps;
