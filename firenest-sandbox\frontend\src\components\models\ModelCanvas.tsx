/**
 * Model Canvas
 * Visual representation of pricing model components
 * Drag-and-drop reordering and component management
 */

import React, { useState } from 'react'
import { 
  GripVertical, 
  Edit, 
  Trash2, 
  Plus,
  Calculator,
  ArrowDown
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { getComponentIcon, getComponentColor, getComponentInfo } from './ComponentPalette'
import type { ModelComponent } from './ModelBuilder'

interface ModelCanvasProps {
  components: ModelComponent[]
  selectedComponent: ModelComponent | null
  onSelectComponent: (component: ModelComponent) => void
  onDeleteComponent: (componentId: string) => void
  onReorderComponents: (components: ModelComponent[]) => void
}

export function ModelCanvas({
  components,
  selectedComponent,
  onSelectComponent,
  onDeleteComponent,
  onReorderComponents
}: ModelCanvasProps) {
  const [draggedComponent, setDraggedComponent] = useState<ModelComponent | null>(null)

  const handleDragStart = (e: React.DragEvent, component: ModelComponent) => {
    setDraggedComponent(component)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, targetComponent: ModelComponent) => {
    e.preventDefault()
    
    if (!draggedComponent || draggedComponent.id === targetComponent.id) {
      return
    }

    const newComponents = [...components]
    const draggedIndex = newComponents.findIndex(c => c.id === draggedComponent.id)
    const targetIndex = newComponents.findIndex(c => c.id === targetComponent.id)

    // Remove dragged component and insert at target position
    newComponents.splice(draggedIndex, 1)
    newComponents.splice(targetIndex, 0, draggedComponent)

    onReorderComponents(newComponents)
    setDraggedComponent(null)
  }

  const formatComponentValue = (component: ModelComponent) => {
    const { config } = component
    
    switch (component.type) {
      case 'BASE_FEE':
        return `$${config.amount}/${config.period}`
      case 'PER_UNIT_RATE':
        return `$${config.unitRate} per ${config.metricName || 'unit'}`
      case 'TIERED_RATE':
        if (config.tiers && config.tiers.length > 0) {
          const firstTier = config.tiers[0]
          return `$${firstTier.unitRate} for 0-${firstTier.upTo === 'infinity' ? '∞' : firstTier.upTo}`
        }
        return 'Tiered pricing'
      case 'MINIMUM_FEE':
        return `Min $${config.amount}/${config.period}`
      case 'MAXIMUM_FEE':
        return `Max $${config.amount}/${config.period}`
      default:
        return 'Component'
    }
  }

  if (components.length === 0) {
    return (
      <div className="firenest-card">
        <div className="text-center py-12">
          <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Start Building Your Model</h3>
          <p className="text-gray-400 mb-6">
            Add pricing components from the palette to create your pricing strategy
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <Plus className="w-4 h-4" />
            <span>Click components on the left to add them</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="firenest-card">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Pricing Model Structure</h3>
        <Badge variant="secondary">
          {components.length} component{components.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      <div className="space-y-3">
        {components.map((component, index) => (
          <ComponentItem
            key={component.id}
            component={component}
            index={index}
            isSelected={selectedComponent?.id === component.id}
            isLast={index === components.length - 1}
            onSelect={() => onSelectComponent(component)}
            onDelete={() => onDeleteComponent(component.id)}
            onDragStart={(e) => handleDragStart(e, component)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, component)}
            formatValue={formatComponentValue}
          />
        ))}
      </div>

      {/* Calculation Flow Indicator */}
      <div className="mt-6 p-4 bg-muted/50 rounded-lg">
        <h4 className="text-sm font-medium text-white mb-3">💰 Calculation Flow</h4>
        <div className="text-xs text-gray-400 space-y-1">
          <div>1. Base fees are applied first</div>
          <div>2. Usage-based charges are calculated</div>
          <div>3. Minimum fees are enforced</div>
          <div>4. Maximum fees are applied as caps</div>
        </div>
      </div>
    </div>
  )
}

interface ComponentItemProps {
  component: ModelComponent
  index: number
  isSelected: boolean
  isLast: boolean
  onSelect: () => void
  onDelete: () => void
  onDragStart: (e: React.DragEvent) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent) => void
  formatValue: (component: ModelComponent) => string
}

function ComponentItem({
  component,
  index,
  isSelected,
  isLast,
  onSelect,
  onDelete,
  onDragStart,
  onDragOver,
  onDrop,
  formatValue
}: ComponentItemProps) {
  const ComponentIcon = getComponentIcon(component.type)
  const componentColor = getComponentColor(component.type)
  const componentInfo = getComponentInfo(component.type)

  return (
    <div className="relative">
      <div
        draggable
        onDragStart={onDragStart}
        onDragOver={onDragOver}
        onDrop={onDrop}
        onClick={onSelect}
        className={`group relative p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
          isSelected
            ? 'border-fiery bg-fiery/10'
            : 'border-white/10 hover:border-white/20 bg-gradient-to-br from-transparent to-white/5'
        }`}
      >
        {/* Drag Handle */}
        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
          <GripVertical className="w-4 h-4 text-gray-400" />
        </div>

        <div className="flex items-center justify-between ml-6">
          <div className="flex items-center space-x-3 flex-1">
            <div className={`w-8 h-8 rounded-lg ${componentInfo?.bgColor} flex items-center justify-center`}>
              <ComponentIcon className={`w-4 h-4 ${componentColor}`} />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className="text-sm font-medium text-white">
                  {componentInfo?.name}
                </h4>
                {component.isNew && (
                  <Badge variant="info" className="text-xs">New</Badge>
                )}
              </div>
              <p className="text-xs text-gray-400 mb-1">
                {componentInfo?.description}
              </p>
              <div className="text-sm font-mono text-fiery">
                {formatValue(component)}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); onSelect(); }}>
              <Edit className="w-3 h-3" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={(e) => { e.stopPropagation(); onDelete(); }}
              className="text-red-400 hover:text-red-300"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* Order indicator */}
        <div className="absolute top-2 right-2 w-6 h-6 bg-muted rounded-full flex items-center justify-center">
          <span className="text-xs font-medium text-gray-400">{index + 1}</span>
        </div>
      </div>

      {/* Flow arrow */}
      {!isLast && (
        <div className="flex justify-center py-2">
          <ArrowDown className="w-4 h-4 text-gray-600" />
        </div>
      )}
    </div>
  )
}
