/**
 * Models Page
 * Phase 2: Pricing model management and creation
 */

import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {
  Calculator,
  Plus,
  ArrowLeft,
  Edit,
  Trash2,
  Copy,
  Play,
  TrendingUp,
  Layers,
  DollarSign
} from 'lucide-react'
import { modelsApi, projectsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ModelBuilder } from '@/components/models/ModelBuilder'
import { formatDate } from '@/lib/utils'

export function ModelsPage() {
  const { projectId } = useParams<{ projectId: string }>()
  const navigate = useNavigate()
  const [showBuilder, setShowBuilder] = useState(false)
  const [selectedModel, setSelectedModel] = useState<any>(null)

  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: ['projects', projectId],
    queryFn: () => projectsApi.get(projectId!),
    enabled: !!projectId
  })

  const { data: models, isLoading: modelsLoading, refetch } = useQuery({
    queryKey: ['models', projectId],
    queryFn: () => modelsApi.listByProject(projectId!, { sortBy: 'created_at', sortOrder: 'desc' }),
    enabled: !!projectId
  })

  if (projectLoading || modelsLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!project?.data?.data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white mb-2">Project not found</h2>
          <Button variant="outline" onClick={() => navigate('/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  const projectData = project.data.data
  const modelsList = models?.data?.data || []

  if (showBuilder) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container-responsive py-8">
          <ModelBuilder
            projectId={projectId!}
            model={selectedModel}
            onSave={() => {
              setShowBuilder(false)
              setSelectedModel(null)
              refetch()
            }}
            onCancel={() => {
              setShowBuilder(false)
              setSelectedModel(null)
            }}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur">
        <div className="container-responsive py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/projects/${projectId}`)}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Project
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Pricing Models</h1>
                <p className="text-gray-400">{projectData.name}</p>
              </div>
            </div>

            <Button onClick={() => setShowBuilder(true)} variant="fiery">
              <Plus className="w-4 h-4 mr-2" />
              Create Model
            </Button>
          </div>
        </div>
      </div>

      <div className="container-responsive py-8">
        {/* Project Status Check */}
        {projectData.status !== 'READY' && projectData.status !== 'COMPLETE' && (
          <div className="firenest-card mb-8 border-l-4 border-yellow-500">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <Calculator className="w-4 h-4 text-yellow-400" />
              </div>
              <div>
                <h3 className="text-white font-medium">Data Required</h3>
                <p className="text-gray-400 text-sm">
                  Upload and validate data files before creating pricing models.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Models List */}
        {modelsList.length === 0 ? (
          <div className="firenest-card">
            <div className="text-center py-12">
              <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No pricing models yet</h3>
              <p className="text-gray-400 mb-6">
                Create your first pricing model to start analyzing revenue scenarios
              </p>
              <Button
                onClick={() => setShowBuilder(true)}
                variant="fiery"
                disabled={projectData.status !== 'READY' && projectData.status !== 'COMPLETE'}
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Model
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {modelsList.map((model: any) => (
              <ModelCard
                key={model.id}
                model={model}
                onEdit={() => {
                  setSelectedModel(model)
                  setShowBuilder(true)
                }}
                onDelete={() => {
                  // TODO: Implement delete confirmation
                }}
                onDuplicate={() => {
                  // TODO: Implement model duplication
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

interface ModelCardProps {
  model: any
  onEdit: () => void
  onDelete: () => void
  onDuplicate: () => void
}

function ModelCard({ model, onEdit, onDelete, onDuplicate }: ModelCardProps) {
  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'USAGE_BASED':
        return TrendingUp
      case 'TIERED':
        return Layers
      case 'HYBRID':
        return Calculator
      case 'SUBSCRIPTION':
        return DollarSign
      default:
        return Calculator
    }
  }

  const getModelTypeColor = (type: string) => {
    switch (type) {
      case 'USAGE_BASED':
        return 'text-blue-400'
      case 'TIERED':
        return 'text-purple-400'
      case 'HYBRID':
        return 'text-green-400'
      case 'SUBSCRIPTION':
        return 'text-fiery'
      default:
        return 'text-gray-400'
    }
  }

  const ModelIcon = getModelTypeIcon(model.model_type)
  const iconColor = getModelTypeColor(model.model_type)

  return (
    <div className="firenest-card-interactive">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center">
            <ModelIcon className={`w-5 h-5 ${iconColor}`} />
          </div>
          <div>
            <h3 className="text-white font-semibold">{model.name}</h3>
            <Badge variant="secondary" className="text-xs">
              {model.model_type.replace('_', ' ')}
            </Badge>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" onClick={onEdit}>
            <Edit className="w-3 h-3" />
          </Button>
          <Button variant="ghost" size="sm" onClick={onDuplicate}>
            <Copy className="w-3 h-3" />
          </Button>
          <Button variant="ghost" size="sm" onClick={onDelete} className="text-red-400">
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {model.description && (
        <p className="text-gray-400 text-sm mb-4">{model.description}</p>
      )}

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{model.component_count || 0}</div>
          <div className="text-xs text-gray-400">Components</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{model.simulation_count || 0}</div>
          <div className="text-xs text-gray-400">Simulations</div>
        </div>
      </div>

      <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
        <span>Created {formatDate(model.created_at)}</span>
        <span>Updated {formatDate(model.updated_at)}</span>
      </div>

      <div className="flex space-x-2">
        <Button variant="outline" size="sm" onClick={onEdit} className="flex-1">
          <Edit className="w-3 h-3 mr-1" />
          Edit
        </Button>
        <Button variant="outline" size="sm" className="flex-1">
          <Play className="w-3 h-3 mr-1" />
          Simulate
        </Button>
      </div>
    </div>
  )
}

export function ModelDetailPage() {
  const { modelId } = useParams<{ modelId: string }>()
  const navigate = useNavigate()

  const { data: model, isLoading } = useQuery({
    queryKey: ['models', modelId],
    queryFn: () => modelsApi.get(modelId!),
    enabled: !!modelId
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!model?.data?.data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white mb-2">Model not found</h2>
          <Button variant="outline" onClick={() => navigate('/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  const modelData = model.data.data

  return (
    <div className="min-h-screen bg-background">
      <div className="container-responsive py-8">
        <ModelBuilder
          projectId={modelData.project_id}
          model={modelData}
          onSave={() => navigate(`/projects/${modelData.project_id}/models`)}
          onCancel={() => navigate(`/projects/${modelData.project_id}/models`)}
        />
      </div>
    </div>
  )
}
