import { useState } from 'react';
import { notify } from '@/components/ui/notification-system';
import { purchaseCredits, getMockPaymentToken, processMockPayment } from '@/lib/credits';
import { useAuth } from '@/contexts/AuthContext';
// Card components no longer needed as we're using ModernCard
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import ModernCard from '@/components/ui/modern-card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CreditCard,
  RefreshCw,
  TrendingUp,
  Zap,
  Plus,
  Download,
  Calendar,
  ChevronRight,
  ArrowUpRight,
  ArrowDownRight,
  Search,
  CheckCircle
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';
import AIToolIcon from '@/components/AIToolIcon';

/**
 * Enhanced Credits Page with professional design patterns
 * Inspired by industry leaders like Zapier and HubSpot
 */
const NewCredits = () => {
  const { user, credits, transactions, refreshUserData } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filter] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading] = useState(false);

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshUserData();
    setIsRefreshing(false);
  };

  // Filter transactions
  const filteredTransactions = transactions
    .filter(t => filter ? t.transactionType === filter : true)
    .filter(t => {
      if (dateRange === 'all') return true;
      const date = new Date(t.createdAt);
      const now = new Date();

      if (dateRange === 'today') {
        return date.toDateString() === now.toDateString();
      } else if (dateRange === 'week') {
        const weekAgo = new Date();
        weekAgo.setDate(now.getDate() - 7);
        return date >= weekAgo;
      } else if (dateRange === 'month') {
        const monthAgo = new Date();
        monthAgo.setMonth(now.getMonth() - 1);
        return date >= monthAgo;
      }
      return true;
    })
    .filter(t => {
      if (!searchQuery) return true;
      return t.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
             t.id.toLowerCase().includes(searchQuery.toLowerCase());
    });

  // Calculate usage statistics
  const usageStats = {
    today: transactions
      .filter(t => t.transactionType === 'usage' && new Date(t.createdAt).toDateString() === new Date().toDateString())
      .reduce((acc, t) => acc + Math.abs(t.amount), 0),
    week: transactions
      .filter(t => {
        const date = new Date(t.createdAt);
        const weekAgo = new Date();
        weekAgo.setDate(new Date().getDate() - 7);
        return t.transactionType === 'usage' && date >= weekAgo;
      })
      .reduce((acc, t) => acc + Math.abs(t.amount), 0),
    month: transactions
      .filter(t => {
        const date = new Date(t.createdAt);
        const monthAgo = new Date();
        monthAgo.setMonth(new Date().getMonth() - 1);
        return t.transactionType === 'usage' && date >= monthAgo;
      })
      .reduce((acc, t) => acc + Math.abs(t.amount), 0),
    total: credits?.usedCredits || 0
  };

  // Mock data for usage chart
  const usageData = [
    { day: 'Mon', credits: 25 },
    { day: 'Tue', credits: 40 },
    { day: 'Wed', credits: 30 },
    { day: 'Thu', credits: 50 },
    { day: 'Fri', credits: 45 },
    { day: 'Sat', credits: 20 },
    { day: 'Sun', credits: 15 },
  ];

  // Calculate max value for chart scaling
  const maxCredits = Math.max(...usageData.map(day => day.credits));

  // Calculate average daily usage
  const avgDailyUsage = usageStats.week / 7;

  // Estimate days remaining based on current usage
  const estimatedDaysRemaining = avgDailyUsage > 0
    ? Math.round((credits?.availableCredits || 0) / avgDailyUsage)
    : 999;

  // Credit pricing tiers - better rates for larger purchases with savings information
  const creditPricingTiers = [
    { id: 1, credits: 100, price: 49, pricePerCredit: 0.49, popular: false, savings: "0%" },
    { id: 2, credits: 500, price: 199, pricePerCredit: 0.40, popular: true, savings: "18%" },
    { id: 3, credits: 1000, price: 349, pricePerCredit: 0.35, popular: false, savings: "29%" },
    { id: 4, credits: 2000, price: 599, pricePerCredit: 0.30, popular: false, savings: "39%" },
    { id: 5, credits: 5000, price: 1299, pricePerCredit: 0.26, popular: false, savings: "47%" },
  ];

  // State for custom credit amount and purchase process
  const [customCredits, setCustomCredits] = useState<number | ''>(500);
  const [selectedTier, setSelectedTier] = useState<number>(2); // Default to the popular tier
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('card');

  // Calculate price for custom credit amount
  const calculateCustomPrice = (credits: number): number => {
    if (credits <= 0) return 0;

    // Find the appropriate tier based on credit amount
    const tier = creditPricingTiers
      .slice()
      .reverse()
      .find(tier => credits >= tier.credits) || creditPricingTiers[0];

    return Math.round(credits * tier.pricePerCredit);
  };

  const customPrice = typeof customCredits === 'number' ? calculateCustomPrice(customCredits) : 0;

  // Handle credit purchase
  const handlePurchaseCredits = async () => {
    if (typeof customCredits !== 'number' || customCredits <= 0) {
      notify.warning('Please enter a valid credit amount');
      return;
    }

    if (!user) {
      notify.error('You must be logged in to purchase credits');
      return;
    }

    setIsPurchasing(true);

    try {
      // Calculate price
      const price = calculateCustomPrice(customCredits);

      // 1. Get payment token from payment processor
      notify.info('Processing payment...');
      const paymentResult = await getMockPaymentToken(paymentMethod as 'card' | 'paypal' | 'crypto', price);

      if (!paymentResult.success || !paymentResult.token) {
        throw new Error(paymentResult.error?.message || 'Failed to process payment');
      }

      // 2. Process the payment
      const processResult = await processMockPayment(paymentResult.token, price);

      if (!processResult.success) {
        throw new Error(processResult.error?.message || 'Payment processing failed');
      }

      // 3. Add credits to user account
      const purchaseResult = await purchaseCredits({
        userId: user.id,
        amount: customCredits,
        paymentMethod: paymentMethod as 'card' | 'paypal' | 'crypto',
        description: `Purchased ${customCredits} credits`,
        price
      });

      if (!purchaseResult.success) {
        throw new Error(purchaseResult.error?.message || 'Failed to add credits to your account');
      }

      // 4. Show success message
      notify.success(`Successfully purchased ${customCredits} credits!`);

      // 5. Refresh user data to show updated credits
      await refreshUserData();
    } catch (error) {
      console.error('Error purchasing credits:', error);
      notify.error(error instanceof Error ? error.message : 'Failed to purchase credits. Please try again.');
    } finally {
      setIsPurchasing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Consolidated Credits & Billing Header */}
      <div className="firenest-card p-6 border-l-4 border-l-fiery">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">Credits & Billing</h1>
            <p className="text-white/70 mt-1">Manage your credits and subscription</p>
          </div>

          <div className="flex items-center gap-3">
            <div className="hidden md:flex items-center gap-3 bg-dark-800/50 rounded-lg px-4 py-2">
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-fiery" />
                <div>
                  <div className="text-xs text-white/70">Available</div>
                  <div className="text-lg font-bold text-white">{credits?.availableCredits || 0}</div>
                </div>
              </div>
              <div className="h-8 w-px bg-white/10"></div>
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-fiery" />
                <div>
                  <div className="text-xs text-white/70">Used</div>
                  <div className="text-lg font-bold text-white">{usageStats.month}</div>
                </div>
              </div>
            </div>

            <Button
              className="bg-fiery hover:bg-fiery-600 text-white"
              onClick={() => document.getElementById('purchase-credits-section')?.scrollIntoView({ behavior: 'smooth' })}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Credits
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="border-white/10 hover:bg-white/5"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </div>

      {/* Credit overview cards - Modern Design */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Available Credits */}
        <ModernCard
          title="Available Credits"
          value={
            <div className="space-y-2">
              <div className="text-4xl font-bold text-white">{credits?.availableCredits || 0}</div>
              <div className="text-sm text-white/70">
                of {credits?.totalCredits || 0} total credits
              </div>
              <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-fiery to-fiery-600 rounded-full"
                  style={{
                    width: `${credits ? (credits.availableCredits / credits.totalCredits) * 100 : 0}%`
                  }}
                ></div>
              </div>
            </div>
          }
          icon={<CreditCard />}
          iconColor="red"
          description={
            credits?.availableCredits < 50
              ? "Running low on credits"
              : "Credits healthy"
          }
          badge={estimatedDaysRemaining > 0 ? `${estimatedDaysRemaining} days left` : "Add credits"}
          badgeColor={estimatedDaysRemaining > 30 ? "green" : estimatedDaysRemaining > 7 ? "blue" : "red"}
          footer={
            <Button variant="outline" className="w-full border-white/10 hover:bg-white/5">
              <Download className="h-4 w-4 mr-2" />
              Export Credit History
            </Button>
          }
        />

        {/* Credit Usage */}
        <ModernCard
          title="Credit Usage"
          value={
            <div className="space-y-2">
              <div className="text-4xl font-bold text-white">{credits?.usedCredits || 0}</div>
              <div className="text-sm text-white/70">
                credits used in total
              </div>
              <div className="flex items-end h-12 gap-1 mt-4">
                {usageData.map((day, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center group/bar">
                    <div
                      className="w-full bg-fiery/80 rounded-sm"
                      style={{
                        height: `${(day.credits / maxCredits) * 100}%`,
                        opacity: 0.3 + (day.credits / maxCredits) * 0.7
                      }}
                    ></div>
                    <div className="text-[10px] text-white/50 mt-1">{day.day}</div>
                  </div>
                ))}
              </div>
            </div>
          }
          icon={<Zap />}
          iconColor="blue"
          footer={
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span className="text-white/70">Today</span>
                <span className="font-medium text-white">{usageStats.today} credits</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-white/70">This week</span>
                <span className="font-medium text-white">{usageStats.week} credits</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-white/70">This month</span>
                <span className="font-medium text-white">{usageStats.month} credits</span>
              </div>
            </div>
          }
        />

        {/* Savings */}
        <ModernCard
          title="Estimated Savings"
          value={
            <div className="space-y-2">
              <div className="text-4xl font-bold text-white">{formatCurrency(credits?.usedCredits ? credits.usedCredits * 20 : 0)}</div>
              <div className="text-sm text-white/70">
                compared to individual subscriptions
              </div>
            </div>
          }
          icon={<TrendingUp />}
          iconColor="green"
          badge="70% savings"
          badgeColor="green"
          footer={
            <div className="space-y-3">
              <div className="flex justify-between items-center text-sm">
                <span className="text-white/70">Per credit value</span>
                <span className="font-medium text-white">{formatCurrency(20)}</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-white/70">Monthly subscription value</span>
                <span className="font-medium text-white">{formatCurrency(2999)}</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-white/70">Annual savings</span>
                <span className="font-medium text-green-400">{formatCurrency(35988)}</span>
              </div>
            </div>
          }
        />
      </div>

      {/* Purchase Credits - Modern Design */}
      <div id="purchase-credits-section" className="firenest-card p-6">
        <div className="flex items-center mb-4">
          <CreditCard className="h-5 w-5 text-fiery mr-2" />
          <h2 className="text-xl font-bold text-white">Purchase Credits</h2>
        </div>
        <p className="text-white/70 mb-6">Buy credits to use across all AI tools on Firenest</p>

        {/* Credit package cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          {creditPricingTiers.map((tier) => (
            <div
              key={tier.id}
              className={`firenest-card p-4 cursor-pointer transition-all duration-200 ${
                selectedTier === tier.id
                  ? 'border-2 border-fiery shadow-lg shadow-fiery/10'
                  : 'border border-white/10 hover:border-white/30'
              }`}
              onClick={() => {
                setSelectedTier(tier.id);
                setCustomCredits(tier.credits);
              }}
            >
              <div className="flex justify-between items-start mb-2">
                <div className="text-lg font-bold text-white">{tier.credits}</div>
                {tier.popular && (
                  <Badge className="bg-fiery text-white border-none">Popular</Badge>
                )}
              </div>

              <div className="text-2xl font-bold text-white mb-1">{formatCurrency(tier.price)}</div>
              <div className="text-sm text-white/70 mb-3">${tier.pricePerCredit.toFixed(2)} per credit</div>

              {tier.savings !== "0%" && (
                <div className="text-sm text-green-400 font-medium">Save {tier.savings}</div>
              )}
            </div>
          ))}
        </div>

        {/* Custom amount */}
        <div className="firenest-card p-4 mb-6">
          <h3 className="text-white font-medium mb-3">Custom Amount</h3>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="flex-1">
              <label className="text-sm text-white/70 mb-2 block">Enter number of credits</label>
              <Input
                type="number"
                min="1"
                placeholder="Enter credit amount"
                className="firenest-card focus:border-fiery/50"
                value={customCredits}
                onChange={(e) => {
                  const value = e.target.value === '' ? '' : parseInt(e.target.value, 10);
                  setCustomCredits(value);
                  // Deselect tier buttons when custom value is entered
                  if (value !== '' && !creditPricingTiers.some(tier => tier.credits === value)) {
                    setSelectedTier(0);
                  } else {
                    // Select matching tier if value matches a tier
                    const matchingTier = creditPricingTiers.find(tier => tier.credits === value);
                    if (matchingTier) setSelectedTier(matchingTier.id);
                  }
                }}
              />
            </div>

            <div className="md:w-1/3">
              {typeof customCredits === 'number' && customCredits > 0 && (
                <div className="bg-dark-800/50 p-3 rounded-lg">
                  <div className="text-sm text-white/70">Total Price</div>
                  <div className="text-xl font-bold text-white">{formatCurrency(customPrice)}</div>
                  <div className="text-xs text-white/50 mt-1">
                    ${(customPrice / customCredits).toFixed(2)} per credit
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Payment method */}
        <div className="firenest-card p-4 mb-6">
          <h3 className="text-white font-medium mb-3">Payment Method</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div
              className={`p-3 rounded-lg cursor-pointer flex items-center ${
                paymentMethod === 'card'
                  ? 'bg-fiery/10 border border-fiery/50'
                  : 'bg-dark-800/50 border border-white/10 hover:border-white/30'
              }`}
              onClick={() => setPaymentMethod('card')}
            >
              <CreditCard className="h-5 w-5 mr-3 text-fiery" />
              <div>
                <div className="text-sm font-medium text-white">Credit Card</div>
                <div className="text-xs text-white/70">Visa, Mastercard, Amex</div>
              </div>
            </div>

            <div
              className={`p-3 rounded-lg cursor-pointer flex items-center ${
                paymentMethod === 'paypal'
                  ? 'bg-fiery/10 border border-fiery/50'
                  : 'bg-dark-800/50 border border-white/10 hover:border-white/30'
              }`}
              onClick={() => setPaymentMethod('paypal')}
            >
              <svg className="h-5 w-5 mr-3 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.59 3.025-2.566 4.643-5.813 4.643h-2.189c-.11 0-.203.077-.22.185l-.771 4.88 4.145-4.186h3.86c1.345 0 2.913-.455 4.033-1.84.67-.825 1.31-2.371 1.593-3.695a6.019 6.019 0 0 0-3.99.3z" />
              </svg>
              <div>
                <div className="text-sm font-medium text-white">PayPal</div>
                <div className="text-xs text-white/70">Fast and secure</div>
              </div>
            </div>

            <div
              className={`p-3 rounded-lg cursor-pointer flex items-center ${
                paymentMethod === 'crypto'
                  ? 'bg-fiery/10 border border-fiery/50'
                  : 'bg-dark-800/50 border border-white/10 hover:border-white/30'
              }`}
              onClick={() => setPaymentMethod('crypto')}
            >
              <svg className="h-5 w-5 mr-3 text-yellow-400" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.638 14.904c-1.602 6.425-8.113 10.34-14.542 8.736C2.67 22.05-1.244 15.525.362 9.105 1.962 2.67 8.475-1.243 14.9.358c6.43 1.605 10.342 8.115 8.738 14.548v-.002zm-6.35-4.613c.24-1.59-.974-2.45-2.64-3.03l.54-2.153-1.315-.33-.525 2.107c-.345-.087-.705-.17-1.064-.25l.526-2.127-1.32-.33-.54 2.165c-.285-.067-.565-.132-.84-.2l-1.815-.45-.35 1.4s.975.225.955.236c.535.136.63.486.615.766l-1.477 5.92c-.075.166-.24.415-.614.32.015.02-.96-.24-.96-.24l-.66 1.51 1.71.426.93.242-.54 2.19 1.32.327.54-2.17c.36.1.705.19 1.05.273l-.51 2.154 1.32.33.545-2.19c2.24.427 3.93.257 4.64-1.774.57-1.637-.03-2.58-1.217-3.196.854-.193 1.5-.76 1.68-1.93h.01zm-3.01 4.22c-.404 1.64-3.157.75-4.05.53l.72-2.9c.896.23 3.757.67 3.33 2.37zm.41-4.24c-.37 1.49-2.662.735-3.405.55l.654-2.64c.744.18 3.137.52 2.75 2.084v.006z" />
              </svg>
              <div>
                <div className="text-sm font-medium text-white">Cryptocurrency</div>
                <div className="text-xs text-white/70">BTC, ETH, USDC</div>
              </div>
            </div>
          </div>
        </div>

        {/* Purchase button */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-sm text-white/70 max-w-md">
            By purchasing credits, you agree to our <a href="/terms" className="text-fiery hover:underline">Terms of Service</a> and <a href="/privacy" className="text-fiery hover:underline">Privacy Policy</a>.
          </div>

          <Button
            className="bg-fiery hover:bg-fiery-600 text-white w-full md:w-auto px-8 py-6"
            onClick={handlePurchaseCredits}
            disabled={isPurchasing || typeof customCredits !== 'number' || customCredits <= 0}
          >
            {isPurchasing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              `Buy ${typeof customCredits === 'number' ? customCredits : 0} Credits for ${formatCurrency(customPrice)}`
            )}
          </Button>
        </div>

        {/* Benefits */}
        <div className="mt-8 pt-6 border-t border-white/10">
          <h3 className="text-white font-medium mb-4">Benefits of Firenest Credits</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-4 w-4 text-green-400" />
              </div>
              <div>
                <h4 className="text-white font-medium">No Expiration</h4>
                <p className="text-sm text-white/70 mt-1">Your credits never expire, use them at your own pace</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-4 w-4 text-blue-400" />
              </div>
              <div>
                <h4 className="text-white font-medium">Universal Access</h4>
                <p className="text-sm text-white/70 mt-1">Use your credits with any AI tool in our marketplace</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                <CheckCircle className="h-4 w-4 text-purple-400" />
              </div>
              <div>
                <h4 className="text-white font-medium">Cost Effective</h4>
                <p className="text-sm text-white/70 mt-1">Save up to 70% compared to individual subscriptions</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Transaction history - Modern Design */}
      <div className="firenest-card p-6">
        <div className="flex items-center mb-4">
          <Download className="h-5 w-5 text-fiery mr-2" />
          <h2 className="text-xl font-bold text-white">Transaction History</h2>
        </div>
        <p className="text-white/70 mb-6">View and export your credit transactions</p>

        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <TabsList className="bg-dark-800/50 border border-white/10 p-1">
                <TabsTrigger value="all" className="data-[state=active]:bg-fiery data-[state=active]:text-white rounded-md">
                  All Transactions
                </TabsTrigger>
                <TabsTrigger value="usage" className="data-[state=active]:bg-fiery data-[state=active]:text-white rounded-md">
                  Usage
                </TabsTrigger>
                <TabsTrigger value="purchase" className="data-[state=active]:bg-fiery data-[state=active]:text-white rounded-md">
                  Purchases
                </TabsTrigger>
                <TabsTrigger value="bonus" className="data-[state=active]:bg-fiery data-[state=active]:text-white rounded-md">
                  Bonuses
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
                  <Input
                    type="search"
                    placeholder="Search transactions..."
                    className="pl-10 bg-dark-800/50 border-white/10 focus:border-fiery/50 w-[200px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-[140px] bg-dark-800/50 border-white/10">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-white/70" />
                      <SelectValue placeholder="Time period" />
                    </div>
                  </SelectTrigger>
                  <SelectContent className="bg-dark-800 border-white/10 text-white">
                    <SelectItem value="all">All time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">This week</SelectItem>
                    <SelectItem value="month">This month</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>

            <TabsContent value="all" className="mt-6">
              <TransactionTable
                transactions={filteredTransactions}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="usage" className="mt-6">
              <TransactionTable
                transactions={filteredTransactions.filter(t => t.transactionType === 'usage')}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="purchase" className="mt-6">
              <TransactionTable
                transactions={filteredTransactions.filter(t => t.transactionType === 'purchase')}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="bonus" className="mt-6">
              <TransactionTable
                transactions={filteredTransactions.filter(t => t.transactionType === 'bonus')}
                isLoading={isLoading}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

// Transaction table component with modern design
interface TransactionTableProps {
  transactions: any[];
  isLoading: boolean;
}

const TransactionTable = ({ transactions, isLoading }: TransactionTableProps) => {
  if (isLoading) {
    return (
      <div className="bg-dark-800/50 rounded-lg border border-white/10">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/10">
                <th className="text-left p-4 text-white/90 font-medium">Transaction</th>
                <th className="text-left p-4 text-white/90 font-medium">Date</th>
                <th className="text-left p-4 text-white/90 font-medium">Type</th>
                <th className="text-right p-4 text-white/90 font-medium">Amount</th>
              </tr>
            </thead>
            <tbody>
              {[1, 2, 3, 4, 5].map((i) => (
                <tr key={i} className="border-b border-white/5">
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full bg-white/5" />
                      <div>
                        <Skeleton className="h-4 w-32 bg-white/5" />
                        <Skeleton className="h-3 w-20 bg-white/5 mt-1" />
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <Skeleton className="h-4 w-24 bg-white/5" />
                  </td>
                  <td className="p-4">
                    <Skeleton className="h-6 w-16 rounded-full bg-white/5" />
                  </td>
                  <td className="p-4 text-right">
                    <Skeleton className="h-4 w-20 bg-white/5 ml-auto" />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="bg-dark-800/50 rounded-lg border border-white/10 p-8 flex flex-col items-center justify-center">
        <div className="h-16 w-16 rounded-full bg-white/5 flex items-center justify-center mb-4">
          <Search className="h-8 w-8 text-white/30" />
        </div>
        <h3 className="text-lg font-medium text-white mb-2">No transactions found</h3>
        <p className="text-white/70 text-center max-w-md mb-4">
          We couldn't find any transactions matching your search criteria. Try adjusting your filters or search query.
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-white/10 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-dark-800/70">
            <tr className="border-b border-white/10">
              <th className="text-left p-4 text-white/90 font-medium">Transaction</th>
              <th className="text-left p-4 text-white/90 font-medium">Date</th>
              <th className="text-left p-4 text-white/90 font-medium">Type</th>
              <th className="text-right p-4 text-white/90 font-medium">Amount</th>
            </tr>
          </thead>
          <tbody className="bg-dark-800/50">
            {transactions.map((transaction) => (
              <tr key={transaction.id} className="border-b border-white/5 hover:bg-white/5 transition-colors">
                <td className="p-4">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full ${
                      transaction.transactionType === 'usage' ? 'bg-red-500/20' :
                      transaction.transactionType === 'purchase' ? 'bg-green-500/20' :
                      transaction.transactionType === 'bonus' ? 'bg-blue-500/20' : 'bg-purple-500/20'
                    } flex items-center justify-center`}>
                      <AIToolIcon
                        iconName={
                          transaction.transactionType === 'usage' ? 'Bot' :
                          transaction.transactionType === 'purchase' ? 'CreditCard' :
                          transaction.transactionType === 'bonus' ? 'Zap' : 'Lightbulb'
                        }
                        className={`h-4 w-4 ${
                          transaction.transactionType === 'usage' ? 'text-red-400' :
                          transaction.transactionType === 'purchase' ? 'text-green-400' :
                          transaction.transactionType === 'bonus' ? 'text-blue-400' : 'text-purple-400'
                        }`}
                      />
                    </div>
                    <div>
                      <div className="font-medium text-white">{transaction.description}</div>
                      <div className="text-xs text-white/70">ID: {transaction.id.substring(0, 8)}</div>
                    </div>
                  </div>
                </td>
                <td className="p-4">
                  <div className="text-sm text-white/90">{formatDate(transaction.createdAt)}</div>
                  <div className="text-xs text-white/50">{new Date(transaction.createdAt).toLocaleTimeString()}</div>
                </td>
                <td className="p-4">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                    transaction.transactionType === 'usage' ? 'bg-red-500/20 text-red-400' :
                    transaction.transactionType === 'purchase' ? 'bg-green-500/20 text-green-400' :
                    transaction.transactionType === 'bonus' ? 'bg-blue-500/20 text-blue-400' : 'bg-purple-500/20 text-purple-400'
                  }`}>
                    <span>{transaction.transactionType.charAt(0).toUpperCase() + transaction.transactionType.slice(1)}</span>
                  </span>
                </td>
                <td className="p-4 text-right">
                  <span className={`font-medium flex items-center justify-end ${transaction.amount > 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {transaction.amount > 0 ? (
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDownRight className="h-3 w-3 mr-1" />
                    )}
                    {transaction.amount > 0 ? '+' : ''}{transaction.amount} credits
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center p-4 border-t border-white/10 bg-dark-800/70">
        <div className="text-sm text-white/70">
          Showing <span className="font-medium text-white">{transactions.length}</span> transactions
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
            <ChevronRight className="h-4 w-4 rotate-180" />
          </Button>
          <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewCredits;
