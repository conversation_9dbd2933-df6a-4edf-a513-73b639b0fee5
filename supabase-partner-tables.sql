-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create partner_accounts table for storing SaaS partner information
CREATE TABLE IF NOT EXISTS partner_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  company TEXT NOT NULL,
  website TEXT,
  logo_url TEXT,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'active', 'suspended'
  api_key TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create partner_tools table for storing tool information
CREATE TABLE IF NOT EXISTS partner_tools (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  partner_id UUID REFERENCES partner_accounts(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  long_description TEXT,
  logo_url TEXT,
  website_url TEXT,
  category TEXT,
  tags JSONB,
  features JSON<PERSON>,
  pricing JSONB,
  status TEXT NOT NULL DEFAULT 'draft', -- 'draft', 'pending_review', 'active', 'suspended'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create integration_configs table for storing authentication configurations
CREATE TABLE IF NOT EXISTS integration_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tool_id UUID REFERENCES partner_tools(id) ON DELETE CASCADE,
  auth_method TEXT NOT NULL, -- 'oauth', 'oidc', 'api_key', 'credentials', 'ip_based'
  config_data JSONB NOT NULL, -- Stores OAuth client IDs, redirect URIs, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create integration_code_snippets table for storing code snippets
CREATE TABLE IF NOT EXISTS integration_code_snippets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tool_id UUID REFERENCES partner_tools(id) ON DELETE CASCADE,
  platform TEXT NOT NULL, -- 'javascript', 'python', 'ruby', 'php', 'java', etc.
  code_snippet TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create partner_api_logs table for tracking API usage
CREATE TABLE IF NOT EXISTS partner_api_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  partner_id UUID REFERENCES partner_accounts(id) ON DELETE CASCADE,
  endpoint TEXT NOT NULL,
  method TEXT NOT NULL,
  status_code INTEGER,
  request_data JSONB,
  response_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE partner_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE partner_tools ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_code_snippets ENABLE ROW LEVEL SECURITY;
ALTER TABLE partner_api_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for partner_accounts table
CREATE POLICY "Partners can view their own account"
  ON partner_accounts FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Partners can update their own account"
  ON partner_accounts FOR UPDATE
  USING (auth.uid() = id);

-- Create policies for partner_tools table
CREATE POLICY "Partners can view their own tools"
  ON partner_tools FOR SELECT
  USING (auth.uid() = partner_id);

CREATE POLICY "Partners can insert their own tools"
  ON partner_tools FOR INSERT
  WITH CHECK (auth.uid() = partner_id);

CREATE POLICY "Partners can update their own tools"
  ON partner_tools FOR UPDATE
  USING (auth.uid() = partner_id);

CREATE POLICY "Partners can delete their own tools"
  ON partner_tools FOR DELETE
  USING (auth.uid() = partner_id);

-- Create policies for integration_configs table
CREATE POLICY "Partners can view their own integration configs"
  ON integration_configs FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_configs.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

CREATE POLICY "Partners can insert their own integration configs"
  ON integration_configs FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_configs.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

CREATE POLICY "Partners can update their own integration configs"
  ON integration_configs FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_configs.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

-- Create policies for integration_code_snippets table
CREATE POLICY "Partners can view their own code snippets"
  ON integration_code_snippets FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_code_snippets.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

CREATE POLICY "Partners can insert their own code snippets"
  ON integration_code_snippets FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_code_snippets.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

CREATE POLICY "Partners can update their own code snippets"
  ON integration_code_snippets FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_code_snippets.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

CREATE POLICY "Partners can delete their own code snippets"
  ON integration_code_snippets FOR DELETE
  USING (EXISTS (
    SELECT 1 FROM partner_tools
    WHERE partner_tools.id = integration_code_snippets.tool_id
    AND partner_tools.partner_id = auth.uid()
  ));

-- Create policies for partner_api_logs table
CREATE POLICY "Partners can view their own API logs"
  ON partner_api_logs FOR SELECT
  USING (partner_id = auth.uid());

CREATE POLICY "Partners can insert their own API logs"
  ON partner_api_logs FOR INSERT
  WITH CHECK (partner_id = auth.uid());

CREATE POLICY "System can view all API logs"
  ON partner_api_logs FOR SELECT
  USING (auth.uid() IN (
    SELECT id FROM partner_accounts WHERE status = 'admin'
  ));

-- Create function to generate API key for partners
CREATE OR REPLACE FUNCTION generate_partner_api_key()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'active' AND (NEW.api_key IS NULL OR NEW.api_key = '') THEN
    NEW.api_key = 'pk_' || encode(gen_random_bytes(24), 'hex');
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to generate API key when partner is activated
CREATE TRIGGER generate_partner_api_key_trigger
BEFORE INSERT OR UPDATE ON partner_accounts
FOR EACH ROW
EXECUTE FUNCTION generate_partner_api_key();
