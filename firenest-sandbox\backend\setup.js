#!/usr/bin/env node

/**
 * Backend Setup Script
 * Creates .env file and installs dependencies
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Firenest Sandbox Backend...\n');

// Check if .env exists
const envPath = path.join(__dirname, '.env');
const envExamplePath = path.join(__dirname, '.env.example');

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    console.log('📝 Creating .env file from .env.example...');
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created!\n');
  } else {
    console.log('📝 Creating basic .env file...');
    const envContent = `# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=firenest_sandbox
DB_USERNAME=sandbox_admin
DB_PASSWORD=your_password
DB_SSL=false

# Authentication (Development)
JWT_SECRET=your-32-character-secret-key-here-dev-12345678
AUTH_PROVIDER_TYPE=development
AUTH_PROVIDER_DOMAIN=localhost
AUTH_PROVIDER_CLIENT_ID=dev-client-id
AUTH_PROVIDER_CLIENT_SECRET=dev-client-secret

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-dev
HASH_SALT=12

# Application
NODE_ENV=development
PORT=3001
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Logging
LOG_LEVEL=debug
`;
    fs.writeFileSync(envPath, envContent);
    console.log('✅ .env file created!\n');
  }
} else {
  console.log('✅ .env file already exists\n');
}

console.log('📋 Next steps:');
console.log('1. Install dependencies: npm install');
console.log('2. Set up PostgreSQL database (see README.md)');
console.log('3. Start development server: npm run dev');
console.log('\n🎯 Happy coding!');
