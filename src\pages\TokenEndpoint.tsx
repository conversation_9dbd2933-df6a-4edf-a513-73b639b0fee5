import React, { useEffect, useState, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { verifyClientSecret } from '@/lib/partner-portal/oauth-utils';
import { generateToken, storeAccessToken } from '@/lib/auth/token-utils';

interface TokenResponse {
  access_token?: string;
  token_type?: string;
  expires_in?: number;
  refresh_token?: string;
  scope?: string;
  error?: string;
  error_description?: string;
}

const TokenEndpoint: React.FC = () => {
  const [response, setResponse] = useState<TokenResponse | null>(null);
  const location = useLocation();

  useEffect(() => {
    const processTokenRequest = async () => {
      try {
        // Handle both GET and POST requests
        // For GET requests, parse from URL query parameters
        // For POST requests, we'll simulate by parsing from URL query parameters
        // In a real implementation, this would come from the request body

        let params: URLSearchParams;

        // Check if this is a POST request with form data (simulated)
        if (location.search.includes('grant_type=')) {
          params = new URLSearchParams(location.search);
        } else {
          // Assume it's a POST request with JSON body (simulated)
          // In a real implementation, you would parse the request body
          params = new URLSearchParams(location.search);
        }

        console.log('Processing token request with params:', Object.fromEntries(params.entries()));

        const grant_type = params.get('grant_type');
        const code = params.get('code');
        const redirect_uri = params.get('redirect_uri');
        const client_id = params.get('client_id');
        const client_secret = params.get('client_secret');

        // Validate required parameters
        if (!grant_type) {
          setResponse({
            error: 'invalid_request',
            error_description: 'Missing required parameter: grant_type'
          });
          return;
        }

        // Handle different grant types
        if (grant_type === 'authorization_code') {
          if (!code || !redirect_uri || !client_id) {
            setResponse({
              error: 'invalid_request',
              error_description: 'Missing required parameters for authorization_code grant'
            });
            return;
          }

          // Verify the authorization code
          const { data: authCode, error: authCodeError } = await supabase
            .from('auth_codes')
            .select('*')
            .eq('code', code)
            .eq('client_id', client_id)
            .eq('redirect_uri', redirect_uri)
            .eq('used', false)
            .maybeSingle();

          if (authCodeError) {
            console.error('Error verifying authorization code:', authCodeError);
            setResponse({
              error: 'server_error',
              error_description: 'An error occurred while verifying the authorization code'
            });
            return;
          }

          if (!authCode) {
            setResponse({
              error: 'invalid_grant',
              error_description: 'Invalid authorization code'
            });
            return;
          }

          // Check if the code has expired
          if (new Date(authCode.expires_at) < new Date()) {
            setResponse({
              error: 'invalid_grant',
              error_description: 'Authorization code has expired'
            });
            return;
          }

          // Verify client credentials if client_secret is provided
          if (client_secret) {
            // In a real implementation, you would verify the client_secret against the stored hash
            // For now, we'll skip this step
          }

          // Mark the authorization code as used
          await supabase
            .from('auth_codes')
            .update({ used: true })
            .eq('code', code);

          // Generate tokens
          const access_token = generateToken('access');
          const refresh_token = generateToken('refresh');

          // Store the tokens in the database
          const { error: tokenError } = await supabase
            .from('access_tokens')
            .insert({
              access_token,
              refresh_token,
              user_id: authCode.user_id,
              client_id,
              partner_id: authCode.partner_id,
              scope: authCode.scope,
              expires_at: new Date(Date.now() + 3600 * 1000).toISOString() // 1 hour expiration
            });

          if (tokenError) {
            console.error('Error storing tokens:', tokenError);
            setResponse({
              error: 'server_error',
              error_description: 'An error occurred while generating tokens'
            });
            return;
          }

          // Return the tokens
          setResponse({
            access_token,
            token_type: 'Bearer',
            expires_in: 3600,
            refresh_token,
            scope: authCode.scope
          });
        } else if (grant_type === 'refresh_token') {
          const refresh_token = params.get('refresh_token');

          if (!refresh_token || !client_id) {
            setResponse({
              error: 'invalid_request',
              error_description: 'Missing required parameters for refresh_token grant'
            });
            return;
          }

          // Verify the refresh token
          const { data: tokenData, error: tokenError } = await supabase
            .from('access_tokens')
            .select('*')
            .eq('refresh_token', refresh_token)
            .eq('client_id', client_id)
            .maybeSingle();

          if (tokenError) {
            console.error('Error verifying refresh token:', tokenError);
            setResponse({
              error: 'server_error',
              error_description: 'An error occurred while verifying the refresh token'
            });
            return;
          }

          if (!tokenData) {
            setResponse({
              error: 'invalid_grant',
              error_description: 'Invalid refresh token'
            });
            return;
          }

          // Generate new tokens
          const access_token = generateToken('access');
          const new_refresh_token = generateToken('refresh');

          // Update the tokens in the database
          const { error: updateError } = await supabase
            .from('access_tokens')
            .update({
              access_token,
              refresh_token: new_refresh_token,
              expires_at: new Date(Date.now() + 3600 * 1000).toISOString() // 1 hour expiration
            })
            .eq('refresh_token', refresh_token);

          if (updateError) {
            console.error('Error updating tokens:', updateError);
            setResponse({
              error: 'server_error',
              error_description: 'An error occurred while refreshing tokens'
            });
            return;
          }

          // Return the new tokens
          setResponse({
            access_token,
            token_type: 'Bearer',
            expires_in: 3600,
            refresh_token: new_refresh_token,
            scope: tokenData.scope
          });
        } else {
          setResponse({
            error: 'unsupported_grant_type',
            error_description: `Unsupported grant type: ${grant_type}`
          });
        }
      } catch (error) {
        console.error('Error processing token request:', error);
        setResponse({
          error: 'server_error',
          error_description: 'An unexpected error occurred'
        });
      }
    };

    processTokenRequest();
  }, [location.search]);

  // Using the standardized token generation utility from token-utils.ts

  // Form refs
  const grantTypeRef = useRef<HTMLSelectElement>(null);
  const codeRef = useRef<HTMLInputElement>(null);
  const redirectUriRef = useRef<HTMLInputElement>(null);
  const clientIdRef = useRef<HTMLInputElement>(null);
  const clientSecretRef = useRef<HTMLInputElement>(null);
  const refreshTokenRef = useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Get form values
      const grantType = grantTypeRef.current?.value;
      const code = codeRef.current?.value;
      const redirectUri = redirectUriRef.current?.value;
      const clientId = clientIdRef.current?.value;
      const clientSecret = clientSecretRef.current?.value;
      const refreshToken = refreshTokenRef.current?.value;

      // Create form data
      const formData = new URLSearchParams();
      formData.append('grant_type', grantType || '');

      if (grantType === 'authorization_code') {
        formData.append('code', code || '');
        formData.append('redirect_uri', redirectUri || '');
        formData.append('client_id', clientId || '');
        if (clientSecret) formData.append('client_secret', clientSecret);
      } else if (grantType === 'refresh_token') {
        formData.append('refresh_token', refreshToken || '');
        formData.append('client_id', clientId || '');
        if (clientSecret) formData.append('client_secret', clientSecret);
      }

      // Process the token request
      const params = formData;

      console.log('Processing token request with params:', Object.fromEntries(params.entries()));

      const grant_type = params.get('grant_type');
      const code_val = params.get('code');
      const redirect_uri = params.get('redirect_uri');
      const client_id = params.get('client_id');
      const client_secret = params.get('client_secret');
      const refresh_token = params.get('refresh_token');

      // Validate required parameters
      if (!grant_type) {
        setResponse({
          error: 'invalid_request',
          error_description: 'Missing required parameter: grant_type'
        });
        return;
      }

      // Handle different grant types
      if (grant_type === 'authorization_code') {
        if (!code_val || !redirect_uri || !client_id) {
          setResponse({
            error: 'invalid_request',
            error_description: 'Missing required parameters for authorization_code grant'
          });
          return;
        }

        // Verify the authorization code
        const { data: authCode, error: authCodeError } = await supabase
          .from('auth_codes')
          .select('*')
          .eq('code', code_val)
          .eq('client_id', client_id)
          .eq('redirect_uri', redirect_uri)
          .eq('used', false)
          .maybeSingle();

        if (authCodeError) {
          console.error('Error verifying authorization code:', authCodeError);
          setResponse({
            error: 'server_error',
            error_description: 'An error occurred while verifying the authorization code'
          });
          return;
        }

        if (!authCode) {
          setResponse({
            error: 'invalid_grant',
            error_description: 'Invalid authorization code'
          });
          return;
        }

        // Check if the code has expired
        if (new Date(authCode.expires_at) < new Date()) {
          setResponse({
            error: 'invalid_grant',
            error_description: 'Authorization code has expired'
          });
          return;
        }

        // Verify client credentials if client_secret is provided
        if (client_secret) {
          // Get the partner's hashed client secret
          const { data: partnerData, error: partnerError } = await supabase
            .from('partners')
            .select('client_secret')
            .eq('client_id', client_id)
            .maybeSingle();

          if (partnerError || !partnerData) {
            console.error('Error fetching partner data:', partnerError);
            setResponse({
              error: 'server_error',
              error_description: 'Error verifying client credentials'
            });
            return;
          }

          // Verify the client secret
          const isSecretValid = await verifyClientSecret(client_secret, partnerData.client_secret);
          if (!isSecretValid) {
            setResponse({
              error: 'invalid_client',
              error_description: 'Invalid client credentials'
            });
            return;
          }
        }

        // Mark the authorization code as used
        await supabase
          .from('auth_codes')
          .update({ used: true })
          .eq('code', code_val);

        // Generate tokens
        const access_token = generateToken('access');
        const refresh_token = generateToken('refresh');

        // Store the tokens in the database
        const { error: tokenError } = await supabase
          .from('access_tokens')
          .insert({
            access_token,
            refresh_token,
            user_id: authCode.user_id,
            client_id,
            partner_id: authCode.partner_id,
            scope: authCode.scope,
            expires_at: new Date(Date.now() + 3600 * 1000).toISOString() // 1 hour expiration
          });

        if (tokenError) {
          console.error('Error storing tokens:', tokenError);
          setResponse({
            error: 'server_error',
            error_description: 'An error occurred while generating tokens'
          });
          return;
        }

        // Return the tokens
        setResponse({
          access_token,
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token,
          scope: authCode.scope
        });
      } else if (grant_type === 'refresh_token') {
        // Handle refresh token grant
        // Similar to the code above
        setResponse({
          error: 'not_implemented',
          error_description: 'Refresh token grant is not implemented in this demo'
        });
      } else {
        setResponse({
          error: 'unsupported_grant_type',
          error_description: `Unsupported grant type: ${grant_type}`
        });
      }
    } catch (error) {
      console.error('Error processing token request:', error);
      setResponse({
        error: 'server_error',
        error_description: 'An unexpected error occurred'
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
      <div className="firenest-card max-w-lg w-full p-6 rounded-lg border border-white/10">
        <h1 className="text-xl font-bold text-white mb-4">Token Endpoint</h1>
        <p className="text-white/70 mb-6">
          This is a simulated token endpoint for demonstration purposes. In a production environment, this would be a server-side API endpoint.
        </p>

        <form onSubmit={handleSubmit} className="space-y-4 mb-6">
          <div>
            <label htmlFor="grant_type" className="block text-white/70 mb-1">Grant Type</label>
            <select
              id="grant_type"
              ref={grantTypeRef}
              className="w-full bg-dark-800/50 border border-white/10 rounded-md p-2 text-white"
              defaultValue="authorization_code"
            >
              <option value="authorization_code">Authorization Code</option>
              <option value="refresh_token">Refresh Token</option>
            </select>
          </div>

          <div>
            <label htmlFor="code" className="block text-white/70 mb-1">Authorization Code</label>
            <input
              id="code"
              ref={codeRef}
              className="w-full bg-dark-800/50 border border-white/10 rounded-md p-2 text-white"
              placeholder="Enter authorization code"
            />
          </div>

          <div>
            <label htmlFor="redirect_uri" className="block text-white/70 mb-1">Redirect URI</label>
            <input
              id="redirect_uri"
              ref={redirectUriRef}
              className="w-full bg-dark-800/50 border border-white/10 rounded-md p-2 text-white"
              placeholder="http://localhost:3001/auth/callback"
              defaultValue="http://localhost:3001/auth/callback"
            />
          </div>

          <div>
            <label htmlFor="client_id" className="block text-white/70 mb-1">Client ID</label>
            <input
              id="client_id"
              ref={clientIdRef}
              className="w-full bg-dark-800/50 border border-white/10 rounded-md p-2 text-white"
              placeholder="Enter client ID"
            />
          </div>

          <div>
            <label htmlFor="client_secret" className="block text-white/70 mb-1">Client Secret</label>
            <input
              id="client_secret"
              ref={clientSecretRef}
              className="w-full bg-dark-800/50 border border-white/10 rounded-md p-2 text-white"
              placeholder="Enter client secret"
              type="password"
            />
          </div>

          <div>
            <label htmlFor="refresh_token" className="block text-white/70 mb-1">Refresh Token</label>
            <input
              id="refresh_token"
              ref={refreshTokenRef}
              className="w-full bg-dark-800/50 border border-white/10 rounded-md p-2 text-white"
              placeholder="Enter refresh token"
            />
          </div>

          <Button type="submit" className="bg-fiery hover:bg-fiery/90 text-white w-full">
            Submit Token Request
          </Button>
        </form>

        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
          <h2 className="text-white font-medium mb-2">Response:</h2>
          <pre className="bg-dark-900 rounded p-3 overflow-auto text-sm text-white/80">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TokenEndpoint;
