import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Alert<PERSON><PERSON>gle, Bug, Flame, Server, Wifi, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { notify } from '@/components/ui/notification-system';
import { useDebug } from '@/contexts/DebugContext';
import { logError, ErrorSeverity, ErrorCategory } from '@/lib/error-utils';

const TestErrorPage: React.FC = () => {
  const { isDebugMode, toggleDebugMode, openDebugConsole } = useDebug();
  const [errorCount, setErrorCount] = useState(0);

  // Function to trigger a React error
  const triggerReactError = () => {
    // This will cause a React error
    throw new Error('This is a test React error');
  };

  // Function to trigger a JavaScript error
  const triggerJsError = () => {
    try {
      // @ts-ignore - Intentional error
      const obj = null;
      obj.nonExistentMethod();
    } catch (error) {
      logError(
        error,
        null,
        ErrorSeverity.ERROR,
        ErrorCategory.UNKNOWN,
        { component: 'TestErrorPage', action: 'triggerJsError' }
      );
      notify.error('JavaScript error triggered and logged', {
        title: 'JS Error',
        duration: 4000
      });
    }
  };

  // Function to trigger a network error
  const triggerNetworkError = () => {
    // Simulate a network error
    fetch('https://non-existent-domain-12345.com')
      .then(response => response.json())
      .catch(error => {
        logError(
          error,
          null,
          ErrorSeverity.ERROR,
          ErrorCategory.NETWORK,
          { component: 'TestErrorPage', action: 'triggerNetworkError' }
        );
        notify.error('Network error triggered and logged', {
          title: 'Network Error',
          duration: 4000
        });
      });
  };

  // Function to trigger an async error
  const triggerAsyncError = async () => {
    try {
      await new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Async operation failed')), 1000);
      });
    } catch (error) {
      logError(
        error,
        null,
        ErrorSeverity.ERROR,
        ErrorCategory.UNKNOWN,
        { component: 'TestErrorPage', action: 'triggerAsyncError' }
      );
      notify.error('Async error triggered and logged', {
        title: 'Async Error',
        duration: 4000
      });
    }
  };

  // Function to trigger multiple errors
  const triggerMultipleErrors = () => {
    setErrorCount(prev => prev + 1);

    // Log multiple errors
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        logError(
          new Error(`Multiple error test #${i + 1}`),
          null,
          ErrorSeverity.WARNING,
          ErrorCategory.UNKNOWN,
          { component: 'TestErrorPage', action: 'triggerMultipleErrors', additionalData: { index: i } }
        );
        notify.warning(`Multiple error #${i + 1} triggered and logged`, {
          title: 'Multiple Errors',
          duration: 3000
        });
      }, i * 1000);
    }
  };

  // Function to simulate a memory leak
  const simulateMemoryLeak = () => {
    const leakyArray: any[] = [];

    // Create a large object
    const createLargeObject = () => {
      const obj: Record<string, string> = {};
      for (let i = 0; i < 10000; i++) {
        obj[`key_${i}`] = `This is a long string value for key ${i} that takes up memory space.`;
      }
      return obj;
    };

    // Add 10 large objects to the array
    for (let i = 0; i < 10; i++) {
      leakyArray.push(createLargeObject());
    }

    // Log the "leak"
    logError(
      new Error('Memory leak simulated'),
      null,
      ErrorSeverity.WARNING,
      ErrorCategory.UNKNOWN,
      {
        component: 'TestErrorPage',
        action: 'simulateMemoryLeak',
        additionalData: { objectCount: leakyArray.length }
      }
    );

    notify.warning('Memory leak simulated and logged', {
      title: 'Memory Warning',
      duration: 4000
    });

    // In a real app, this would cause a memory leak because we're not cleaning up the array
    // For this demo, we'll clean it up to avoid actual memory issues
    setTimeout(() => {
      while (leakyArray.length) {
        leakyArray.pop();
      }
    }, 5000);
  };

  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />

      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>

      <main className="flex-grow flex items-center justify-center p-6">
        <div className="w-full max-w-4xl">
          <div className="glass-card p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

            <div className="flex flex-col items-center mb-8">
              <div className="w-20 h-20 bg-fiery/10 rounded-full flex items-center justify-center mb-4">
                <Bug className="h-10 w-10 text-fiery animate-pulse-slow" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Error Testing Page</h2>
              <p className="text-white/70 text-center">Use this page to test the error handling system</p>

              {errorCount > 0 && (
                <div className="mt-2 px-3 py-1 bg-dark-800/50 rounded-full text-white/60 text-sm">
                  Errors Triggered: {errorCount}
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Flame className="h-5 w-5 text-fiery mr-2" />
                  <h3 className="text-lg font-medium">React Errors</h3>
                </div>
                <p className="text-white/70 text-sm mb-4">
                  Test React component errors that will be caught by the ErrorBoundary.
                </p>
                <Button
                  onClick={triggerReactError}
                  className="w-full bg-fiery hover:bg-fiery-600 text-white"
                >
                  Trigger React Error
                </Button>
              </div>

              <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Zap className="h-5 w-5 text-amber-500 mr-2" />
                  <h3 className="text-lg font-medium">JavaScript Errors</h3>
                </div>
                <p className="text-white/70 text-sm mb-4">
                  Test JavaScript errors that will be caught and logged.
                </p>
                <Button
                  onClick={triggerJsError}
                  className="w-full bg-amber-500 hover:bg-amber-600 text-white"
                >
                  Trigger JS Error
                </Button>
              </div>

              <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Wifi className="h-5 w-5 text-cool-500 mr-2" />
                  <h3 className="text-lg font-medium">Network Errors</h3>
                </div>
                <p className="text-white/70 text-sm mb-4">
                  Test network errors that will be caught and logged.
                </p>
                <Button
                  onClick={triggerNetworkError}
                  className="w-full bg-cool-500 hover:bg-cool-600 text-white"
                >
                  Trigger Network Error
                </Button>
              </div>

              <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                  <h3 className="text-lg font-medium">Async Errors</h3>
                </div>
                <p className="text-white/70 text-sm mb-4">
                  Test asynchronous errors that will be caught and logged.
                </p>
                <Button
                  onClick={triggerAsyncError}
                  className="w-full bg-amber-500 hover:bg-amber-600 text-white"
                >
                  Trigger Async Error
                </Button>
              </div>

              <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Server className="h-5 w-5 text-red-500 mr-2" />
                  <h3 className="text-lg font-medium">Multiple Errors</h3>
                </div>
                <p className="text-white/70 text-sm mb-4">
                  Trigger multiple errors in sequence to test error aggregation.
                </p>
                <Button
                  onClick={triggerMultipleErrors}
                  className="w-full bg-red-500 hover:bg-red-600 text-white"
                >
                  Trigger Multiple Errors
                </Button>
              </div>

              <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Bug className="h-5 w-5 text-purple-500 mr-2" />
                  <h3 className="text-lg font-medium">Memory Issues</h3>
                </div>
                <p className="text-white/70 text-sm mb-4">
                  Simulate memory leaks and performance issues.
                </p>
                <Button
                  onClick={simulateMemoryLeak}
                  className="w-full bg-purple-500 hover:bg-purple-600 text-white"
                >
                  Simulate Memory Leak
                </Button>
              </div>
            </div>

            <div className="border-t border-white/10 pt-6">
              <h3 className="text-lg font-medium mb-4">Error Page Examples</h3>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Link
                  to="/error"
                  className="px-4 py-3 bg-dark-800/50 hover:bg-dark-700/50 border border-white/10 rounded-md text-white/80 hover:text-white transition-colors text-center"
                >
                  Generic Error Page
                </Link>

                <Link
                  to="/network-error"
                  className="px-4 py-3 bg-dark-800/50 hover:bg-dark-700/50 border border-white/10 rounded-md text-white/80 hover:text-white transition-colors text-center"
                >
                  Network Error Page
                </Link>

                <Link
                  to="/server-error"
                  className="px-4 py-3 bg-dark-800/50 hover:bg-dark-700/50 border border-white/10 rounded-md text-white/80 hover:text-white transition-colors text-center"
                >
                  Server Error Page
                </Link>
              </div>
            </div>

            <div className="border-t border-white/10 pt-6 mt-6">
              <h3 className="text-lg font-medium mb-4">Debug Tools</h3>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={toggleDebugMode}
                  variant="outline"
                  className="flex-1 border-white/20"
                >
                  {isDebugMode ? 'Disable Debug Mode' : 'Enable Debug Mode'}
                </Button>

                {isDebugMode && (
                  <Button
                    onClick={openDebugConsole}
                    className="flex-1 bg-fiery hover:bg-fiery-600 text-white"
                  >
                    <Bug className="mr-2 h-4 w-4" />
                    Open Debug Console
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="py-4 px-6 text-center text-white/50 text-sm">
        <p>
          <Link to="/" className="text-fiery hover:text-fiery-400 hover:underline">
            Firenest
          </Link>{' '}
          &copy; {new Date().getFullYear()} All rights reserved.
        </p>
      </footer>
    </div>
  );
};

export default TestErrorPage;
