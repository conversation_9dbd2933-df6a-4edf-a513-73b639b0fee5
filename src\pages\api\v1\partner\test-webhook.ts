/**
 * Webhook Test API Endpoint
 *
 * This endpoint tests a webhook URL by sending a test payload.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';
import crypto from 'crypto';
import {
  withAuth,
  StatusCodes,
  ErrorType,
  ErrorMessages,
  logApiRequest
} from '../utils';

// Handler for the webhook test endpoint
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
  context: { userId: string }
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(StatusCodes.METHOD_NOT_ALLOWED).json({
      success: false,
      error: ErrorType.METHOD_NOT_ALLOWED,
      message: ErrorMessages[ErrorType.METHOD_NOT_ALLOWED]
    });
  }

  try {
    // Log the API request
    logApiRequest(req, 'test-webhook');

    // Get the request body
    const { toolId, webhookUrl, secret } = req.body;

    // Validate required fields
    if (!toolId || !webhookUrl) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing required fields: toolId, webhookUrl'
      });
    }

    // Verify that the user has access to the tool
    const { data: tool, error: toolError } = await supabase
      .from('partner_tools')
      .select('partner_id')
      .eq('id', toolId)
      .single();

    if (toolError || !tool) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: ErrorType.NOT_FOUND,
        message: 'Tool not found'
      });
    }

    if (tool.partner_id !== context.userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        error: ErrorType.FORBIDDEN,
        message: 'You do not have access to this tool'
      });
    }

    // Create a test payload
    const timestamp = new Date().toISOString();
    const payload = {
      event: 'test',
      toolId,
      timestamp,
      data: {
        message: 'This is a test webhook from Firenest',
        testId: crypto.randomUUID()
      }
    };

    // Sign the payload if a secret is provided
    let signature;
    if (secret) {
      const hmac = crypto.createHmac('sha256', secret);
      hmac.update(JSON.stringify(payload));
      signature = hmac.digest('hex');
    }

    // Send the test webhook
    const webhookResponse = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Firenest-Webhook-Test',
        'X-Firenest-Event': 'test',
        'X-Firenest-Delivery': crypto.randomUUID(),
        'X-Firenest-Signature': signature || '',
        'X-Firenest-Timestamp': timestamp
      },
      body: JSON.stringify(payload)
    });

    // Check the response
    const success = webhookResponse.ok;
    const statusCode = webhookResponse.status;
    const responseText = await webhookResponse.text();

    // Log the webhook test
    await supabase
      .from('webhook_logs')
      .insert({
        tool_id: toolId,
        webhook_url: webhookUrl,
        event: 'test',
        status_code: statusCode,
        success,
        request_payload: payload,
        response_body: responseText.substring(0, 1000), // Limit response size
        created_at: new Date().toISOString()
      });

    // Return the result
    return res.status(StatusCodes.OK).json({
      success,
      statusCode,
      message: success
        ? 'Webhook test successful'
        : `Webhook test failed with status code ${statusCode}`,
      response: responseText.substring(0, 500) // Limit response size in the API response
    });
  } catch (error) {
    console.error('Error testing webhook:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.INTERNAL_SERVER_ERROR,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}

// Export the handler with authentication middleware
export default withAuth(handler);
