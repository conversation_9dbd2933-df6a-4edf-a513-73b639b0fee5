import React, { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnClickOutside?: boolean;
  showCloseButton?: boolean;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  closeButtonClassName?: string;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  closeOnClickOutside = true,
  showCloseButton = true,
  className,
  contentClassName,
  headerClassName,
  footerClassName,
  closeButtonClassName,
}) => {
  const [isClosing, setIsClosing] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle ESC key press
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      // Prevent scrolling of the body when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Handle animation on close
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 200); // Match this with the CSS transition duration
  };

  // Handle click outside
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnClickOutside && e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen && !isClosing) return null;

  // Size classes
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4',
  };

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm transition-opacity duration-200 overflow-hidden",
        isClosing ? "opacity-0" : "opacity-100"
      )}
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className={cn(
          "firenest-card backdrop-blur-md rounded-lg shadow-xl w-full transition-all duration-200 overflow-hidden max-h-[90vh] my-auto flex flex-col",
          sizeClasses[size],
          isClosing ? "scale-95 opacity-0" : "scale-100 opacity-100",
          className
        )}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className={cn(
            "flex items-center justify-between px-6 py-4 border-b border-white/10 flex-shrink-0",
            headerClassName
          )}>
            {title && (
              <h3 className="text-lg font-medium text-white">{title}</h3>
            )}
            {showCloseButton && (
              <button
                onClick={handleClose}
                className={cn(
                  "text-white/70 hover:text-white transition-colors p-1 rounded-md hover:bg-white/5",
                  closeButtonClassName
                )}
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div className={cn("px-6 py-4 overflow-y-auto custom-scrollbar", contentClassName)}>
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className={cn(
            "px-6 py-4 border-t border-white/10 bg-dark-900/50 flex-shrink-0",
            footerClassName
          )}>
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default Modal;
