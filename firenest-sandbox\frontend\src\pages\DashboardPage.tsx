/**
 * Dashboard Page
 * Main overview with key metrics and recent activity
 */

import React from 'react'
import { useNavigate } from 'react-router-dom'
import { 
  Plus, 
  TrendingUp, 
  Database, 
  Calculator, 
  Play,
  FileText,
  Building2,
  ArrowRight
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'

export function DashboardPage() {
  const navigate = useNavigate()

  return (
    <div className="main-content">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <p className="text-gray-400 mt-1">
            Welcome to your pricing intelligence workspace
          </p>
        </div>
        <Button onClick={() => navigate('/workspaces')} variant="fiery">
          <Plus className="w-4 h-4 mr-2" />
          New Workspace
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Workspaces</p>
              <p className="metric-value">3</p>
            </div>
            <Building2 className="w-8 h-8 text-fiery" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Active Projects</p>
              <p className="metric-value">7</p>
            </div>
            <FileText className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Data Files</p>
              <p className="metric-value">24</p>
            </div>
            <Database className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Simulations</p>
              <p className="metric-value">12</p>
            </div>
            <Play className="w-8 h-8 text-purple-400" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigate('/projects')}
            >
              <Plus className="w-4 h-4 mr-3" />
              Create New Project
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigate('/upload')}
            >
              <Database className="w-4 h-4 mr-3" />
              Upload Data Files
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigate('/models')}
            >
              <Calculator className="w-4 h-4 mr-3" />
              Build Pricing Model
            </Button>
          </div>
        </div>

        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Getting Started</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-fiery rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">1</span>
              </div>
              <div>
                <p className="text-white font-medium">Upload Your Data</p>
                <p className="text-sm text-gray-400">
                  Start by uploading customer usage and billing data
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">2</span>
              </div>
              <div>
                <p className="text-gray-400 font-medium">Create Pricing Models</p>
                <p className="text-sm text-gray-500">
                  Design and test different pricing strategies
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">3</span>
              </div>
              <div>
                <p className="text-gray-400 font-medium">Run Simulations</p>
                <p className="text-sm text-gray-500">
                  Analyze revenue impact and optimize pricing
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
          <Button variant="ghost" size="sm">
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
            <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
              <Database className="w-5 h-5 text-green-400" />
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">Data validation completed</p>
              <p className="text-sm text-gray-400">customer_usage_data.csv • 2 hours ago</p>
            </div>
            <Badge variant="success">Validated</Badge>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
            <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <Calculator className="w-5 h-5 text-blue-400" />
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">Pricing model created</p>
              <p className="text-sm text-gray-400">Usage-based model v2 • 4 hours ago</p>
            </div>
            <Badge variant="info">Created</Badge>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
            <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Play className="w-5 h-5 text-purple-400" />
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">Simulation completed</p>
              <p className="text-sm text-gray-400">Q4 pricing analysis • 6 hours ago</p>
            </div>
            <Badge variant="success">Complete</Badge>
          </div>
        </div>
      </div>
    </div>
  )
}
