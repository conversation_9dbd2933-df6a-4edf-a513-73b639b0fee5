# Firenest Notification System

This document provides an overview of the Firenest notification system, which is a wrapper around the Sonner toast library with additional features and consistent styling.

## Usage

```tsx
import { notify } from '@/components/ui/notification-system';

// Basic usage
notify.success('Operation completed successfully');
notify.error('Something went wrong');
notify.info('Here is some information');
notify.warning('Be careful with this action');

// With title and additional options
notify.success('Your changes have been saved', {
  title: 'Success',
  duration: 4000,
  position: 'top-right'
});

// With action button
notify.info('Your session will expire soon', {
  title: 'Session Expiring',
  action: {
    label: 'Renew',
    onClick: () => renewSession()
  }
});

// Loading notification
const loadingId = notify.loading('Processing your request', {
  title: 'Please Wait'
});

// Dismiss a specific notification
notify.dismiss(loadingId);

// Promise notification
notify.promise(
  fetchData(),
  {
    loading: 'Fetching data...',
    success: 'Data loaded successfully!',
    error: 'Failed to load data'
  },
  { title: 'Data Operation' }
);

// Custom notification
notify.custom(
  <div className="flex items-center gap-2">
    <CustomIcon />
    <span>Custom notification content</span>
  </div>,
  {
    title: 'Custom Notification',
    duration: 5000
  }
);

// Firenest branded notification
notify.firenest('Welcome to Firenest!', {
  title: 'Firenest',
  duration: 5000
});
```

## API

### Notification Types

- `notify.success(message, options?)` - Success notification with green styling
- `notify.error(message, options?)` - Error notification with red styling
- `notify.warning(message, options?)` - Warning notification with amber styling
- `notify.info(message, options?)` - Info notification with blue styling
- `notify.loading(message, options?)` - Loading notification with spinner
- `notify.firenest(message, options?)` - Firenest branded notification
- `notify.promise(promise, messages, options?)` - Promise-based notification that changes based on promise state
- `notify.custom(content, options?)` - Custom notification with React node content
- `notify.dismiss(id?)` - Dismiss a specific notification or all notifications

### Options

```typescript
interface NotificationOptions {
  title?: string;              // Title of the notification
  description?: string;        // Additional description (if title is provided, message becomes description)
  action?: {                   // Action button
    label: string;             // Button text
    onClick: () => void;       // Button click handler
  };
  position?: NotificationPosition;  // Position on screen
  duration?: number;           // Duration in milliseconds
  className?: string;          // Additional CSS classes
  closeButton?: boolean;       // Show close button
  id?: string;                 // Custom ID for the notification
  onDismiss?: () => void;      // Called when notification is dismissed
  onAutoClose?: () => void;    // Called when notification auto-closes
}

type NotificationPosition = 
  | 'top-left' 
  | 'top-center' 
  | 'top-right' 
  | 'bottom-left' 
  | 'bottom-center' 
  | 'bottom-right';
```

## Default Values

- Default duration: 4500ms (4000ms for success/info, 5000ms for error)
- Default position: 'top-right'
- Loading notifications stay until dismissed (Infinity duration)

## Backward Compatibility

For backward compatibility, the system also exports a `toast` object that is identical to `notify`. However, it's recommended to use `notify` for all new code.

```tsx
// Deprecated - use notify instead
import { toast } from '@/components/ui/notification-system';
toast.success('This works but is deprecated');
```

## Implementation Details

The notification system is implemented as a wrapper around the Sonner toast library. It provides:

1. Consistent styling across all notification types
2. Consistent API for all notification types
3. Default values for common options
4. Error handling to prevent crashes
5. Type safety with TypeScript

The system uses a helper function `createNotificationOptions` to ensure consistent options across all notification types.
