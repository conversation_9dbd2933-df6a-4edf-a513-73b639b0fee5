#!/bin/bash

echo "========================================"
echo "Firenest Sandbox - Quick Setup Script"
echo "========================================"
echo

echo "[1/6] Checking prerequisites..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

echo "Node.js found: $(node --version)"

echo
echo "[2/6] Installing backend dependencies..."
cd backend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install backend dependencies"
    exit 1
fi

echo
echo "[3/6] Installing frontend dependencies..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install frontend dependencies"
    exit 1
fi

echo
echo "[4/6] Creating environment files..."
cd ../backend
if [ ! -f .env ]; then
    echo "Creating backend .env file..."
    cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=firenest_sandbox
DB_USERNAME=sandbox_admin
DB_PASSWORD=your_password
DB_SSL=false

# Authentication (Development)
JWT_SECRET=your-32-character-secret-key-here-dev
AUTH_PROVIDER_TYPE=development
AUTH_PROVIDER_DOMAIN=localhost
AUTH_PROVIDER_CLIENT_ID=dev-client-id
AUTH_PROVIDER_CLIENT_SECRET=dev-client-secret

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-dev
HASH_SALT=12

# Application
NODE_ENV=development
PORT=3001
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Logging
LOG_LEVEL=debug

# AWS (Optional - comment out if not using)
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# S3_BUCKET_NAME=your_s3_bucket
# SQS_QUEUE_URL=your_sqs_queue_url
# SQS_DLQ_URL=your_dlq_url
EOF
    echo "Backend .env file created!"
else
    echo "Backend .env file already exists, skipping..."
fi

cd ../frontend
if [ ! -f .env ]; then
    echo "Creating frontend .env file..."
    cat > .env << EOF
VITE_API_URL=http://localhost:3001/api/v1
VITE_AUTH_DOMAIN=localhost
VITE_AUTH_CLIENT_ID=dev-client-id
VITE_ENVIRONMENT=development
EOF
    echo "Frontend .env file created!"
else
    echo "Frontend .env file already exists, skipping..."
fi

echo
echo "[5/6] Database setup instructions..."
echo
echo "IMPORTANT: You need to set up PostgreSQL database manually:"
echo
echo "Option 1 - Local PostgreSQL:"
echo "  1. Install PostgreSQL from https://www.postgresql.org/download/"
echo "  2. Create database: CREATE DATABASE firenest_sandbox;"
echo "  3. Create user: CREATE USER sandbox_admin WITH PASSWORD 'your_password';"
echo "  4. Grant privileges: GRANT ALL PRIVILEGES ON DATABASE firenest_sandbox TO sandbox_admin;"
echo "  5. Run schema: psql -U sandbox_admin -d firenest_sandbox -f database/schema.sql"
echo
echo "Option 2 - Docker PostgreSQL:"
echo "  1. Run: docker run --name firenest-postgres -e POSTGRES_DB=firenest_sandbox -e POSTGRES_USER=sandbox_admin -e POSTGRES_PASSWORD=your_password -p 5432:5432 -d postgres:14"
echo "  2. Wait 10 seconds, then run: docker exec -i firenest-postgres psql -U sandbox_admin -d firenest_sandbox < database/schema.sql"
echo

echo "[6/6] Setup complete!"
echo
echo "========================================"
echo "Next Steps:"
echo "========================================"
echo "1. Set up PostgreSQL database (see instructions above)"
echo "2. Start the backend: cd backend && npm run dev"
echo "3. Start the frontend: cd frontend && npm run dev"
echo "4. Open http://localhost:3000 in your browser"
echo
echo "For detailed setup instructions, see SETUP.md"
echo "========================================"

cd ..
