/**
 * Upload Routes
 * Phase 1: Secure Data Ingestion
 * SOC 2 Alignment: CC6.7 (Data Encryption in Transit), CC6.2 (Access Control)
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import AWS from 'aws-sdk';
import <PERSON><PERSON> from 'joi';
import { queryWithUserContext, transactionWithUserContext } from '@/config/database';
import { logger } from '@/utils/logger';
import { validate, schemas } from '@/utils/validation';
import { asyncHandler, NotFoundError, ValidationError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';
import { config } from '@/config/environment';

const router = Router();

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region,
  signatureVersion: 'v4'
});

// Configure AWS SQS
const sqs = new AWS.SQS({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region
});

// Step 1.1: Data Upload Initiation API
router.post('/initiate',
  validate(schemas.uploadInitiate),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { fileName, fileSize, fileType, projectId } = req.body;

    // Verify project access
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.id, sp.status, w.id as workspace_id
       FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const project = projectCheck.rows[0];

    // Validate file type
    const allowedExtensions = ['csv', 'json', 'xlsx'];
    const fileExtension = fileName.split('.').pop()?.toLowerCase();
    if (!allowedExtensions.includes(fileExtension || '')) {
      throw new ValidationError('Invalid file type. Allowed types: CSV, JSON, XLSX');
    }

    // Validate file size (100MB max)
    const maxFileSize = 100 * 1024 * 1024; // 100MB
    if (fileSize > maxFileSize) {
      throw new ValidationError('File size exceeds maximum limit of 100MB');
    }

    // Generate unique S3 key
    const uploadId = uuidv4();
    const s3Key = `uploads/${project.workspace_id}/${projectId}/${uploadId}/${fileName}`;

    try {
      // Generate pre-signed URL for upload
      const presignedUrl = s3.getSignedUrl('putObject', {
        Bucket: config.aws.s3.bucketName,
        Key: s3Key,
        Expires: config.aws.s3.presignedUrlExpiry, // 5 minutes
        ContentType: 'application/octet-stream',
        ServerSideEncryption: 'AES256',
        Metadata: {
          'user-id': req.user.id,
          'project-id': projectId,
          'file-type': fileType,
          'upload-id': uploadId
        }
      });

      // Log upload initiation
      logger.info('Upload initiated', {
        uploadId,
        projectId,
        userId: req.user.id,
        fileName,
        fileSize,
        fileType
      });

      res.json({
        success: true,
        data: {
          uploadId,
          uploadUrl: presignedUrl,
          key: s3Key,
          expiresIn: config.aws.s3.presignedUrlExpiry
        }
      });
    } catch (error) {
      logger.error('Failed to generate pre-signed URL', error);
      throw new Error('Failed to initiate upload');
    }
  })
);

// Step 1.3: Data Upload Finalization & Validation Job
router.post('/finalize',
  validate(schemas.uploadFinalize),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { key, fileName, fileType } = req.body;

    // Extract project ID from S3 key
    const keyParts = key.split('/');
    if (keyParts.length < 4 || keyParts[0] !== 'uploads') {
      throw new ValidationError('Invalid upload key format');
    }

    const projectId = keyParts[2];
    const uploadId = keyParts[3];

    // Verify project access
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.id, sp.status FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    try {
      // Verify file exists in S3
      const headResult = await s3.headObject({
        Bucket: config.aws.s3.bucketName,
        Key: key
      }).promise();

      const fileSize = headResult.ContentLength || 0;

      // Create data upload record
      const uploadResult = await transactionWithUserContext(
        req.user.authProviderId,
        async (client) => {
          const uploadRecord = await client.query(
            `INSERT INTO data_uploads (id, project_id, s3_key, original_filename, file_size, file_type, status)
             VALUES ($1, $2, $3, $4, $5, $6, $7)
             RETURNING id, project_id, original_filename, file_type, status, created_at`,
            [uploadId, projectId, key, fileName, fileSize, fileType, 'UPLOADED']
          );

          // Update project status to UPLOADING
          await client.query(
            'UPDATE sandbox_projects SET status = $1, updated_at = NOW() WHERE id = $2',
            ['UPLOADING', projectId]
          );

          return uploadRecord.rows[0];
        }
      );

      // Send validation job to SQS queue
      const jobMessage = {
        jobType: 'VALIDATE_UPLOAD',
        uploadId: uploadId,
        projectId: projectId,
        s3Key: key,
        fileType: fileType,
        userId: req.user.id,
        timestamp: new Date().toISOString()
      };

      await sqs.sendMessage({
        QueueUrl: config.aws.sqs.queueUrl,
        MessageBody: JSON.stringify(jobMessage),
        MessageAttributes: {
          jobType: {
            DataType: 'String',
            StringValue: 'VALIDATE_UPLOAD'
          },
          uploadId: {
            DataType: 'String',
            StringValue: uploadId
          }
        }
      }).promise();

      logger.info('Upload finalized and validation job queued', {
        uploadId,
        projectId,
        userId: req.user.id,
        fileName,
        fileSize
      });

      res.json({
        success: true,
        data: uploadResult,
        message: 'File uploaded successfully. Validation in progress.'
      });
    } catch (error) {
      logger.error('Upload finalization failed', error);
      throw new Error('Failed to finalize upload');
    }
  })
);

// Get upload status
router.get('/:uploadId',
  validate(Joi.object({ uploadId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { uploadId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT du.id, du.original_filename, du.file_type, du.status, du.file_size, 
              du.validation_errors, du.created_at, du.updated_at,
              sp.id as project_id, sp.name as project_name
       FROM data_uploads du
       JOIN sandbox_projects sp ON du.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE du.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [uploadId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Upload not found or access denied');
    }

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Get uploads for a project
router.get('/project/:projectId',
  validate(Joi.object({ projectId: schemas.uuid }), 'params'),
  validate(schemas.pagination, 'query'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { projectId } = req.params;
    const { page, limit, sortBy, sortOrder } = req.query;
    const offset = (page - 1) * limit;

    // Verify project access
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT 1 FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT id, original_filename, file_type, status, file_size, 
              validation_errors, created_at, updated_at
       FROM data_uploads
       WHERE project_id = $1
       ORDER BY ${sortBy || 'created_at'} ${sortOrder}
       LIMIT $2 OFFSET $3`,
      [projectId, limit, offset]
    );

    const countResult = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT COUNT(*) as total FROM data_uploads WHERE project_id = $1',
      [projectId]
    );

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  })
);

// Delete upload
router.delete('/:uploadId',
  validate(Joi.object({ uploadId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { uploadId } = req.params;

    // Get upload info and verify access
    const uploadResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT du.s3_key, du.original_filename, sp.status as project_status
       FROM data_uploads du
       JOIN sandbox_projects sp ON du.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE du.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [uploadId, req.user.id]
    );

    if (uploadResult.rows.length === 0) {
      throw new NotFoundError('Upload not found or access denied');
    }

    const upload = uploadResult.rows[0];

    // Don't allow deletion if project is simulating
    if (upload.project_status === 'SIMULATING') {
      throw new ValidationError('Cannot delete upload while simulation is running');
    }

    try {
      // Delete from S3
      await s3.deleteObject({
        Bucket: config.aws.s3.bucketName,
        Key: upload.s3_key
      }).promise();

      // Delete from database
      await queryWithUserContext(
        req.user.authProviderId,
        'DELETE FROM data_uploads WHERE id = $1',
        [uploadId]
      );

      logger.info('Upload deleted', {
        uploadId,
        userId: req.user.id,
        fileName: upload.original_filename
      });

      res.json({
        success: true,
        message: 'Upload deleted successfully'
      });
    } catch (error) {
      logger.error('Failed to delete upload', error);
      throw new Error('Failed to delete upload');
    }
  })
);

// Get real-time upload status with detailed progress
router.get('/:uploadId/status',
  validate(Joi.object({ uploadId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { uploadId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT du.id, du.original_filename, du.file_type, du.status, du.file_size,
              du.validation_errors, du.metadata, du.created_at, du.updated_at,
              sp.id as project_id, sp.name as project_name, sp.status as project_status
       FROM data_uploads du
       JOIN sandbox_projects sp ON du.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE du.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [uploadId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Upload not found or access denied');
    }

    const upload = result.rows[0];

    // Parse validation errors and metadata
    let validationErrors = [];
    let metadata = {};

    try {
      validationErrors = upload.validation_errors ? JSON.parse(upload.validation_errors) : [];
      metadata = upload.metadata ? JSON.parse(upload.metadata) : {};
    } catch (error) {
      logger.error('Failed to parse upload data:', error);
    }

    // Calculate progress percentage
    let progress = 0;
    switch (upload.status) {
      case 'UPLOADED':
        progress = 25;
        break;
      case 'VALIDATING':
        progress = 50;
        break;
      case 'VALIDATED':
        progress = 100;
        break;
      case 'INVALID':
        progress = 100;
        break;
      default:
        progress = 0;
    }

    // Determine next steps based on status
    let nextSteps: any[] = [];
    switch (upload.status) {
      case 'UPLOADED':
        nextSteps = ['File queued for validation'];
        break;
      case 'VALIDATING':
        nextSteps = ['Analyzing data structure', 'Checking data quality', 'Validating business rules'];
        break;
      case 'VALIDATED':
        nextSteps = ['Ready for pricing model creation', 'Data can be used in simulations'];
        break;
      case 'INVALID':
        nextSteps = ['Review validation errors', 'Fix data issues', 'Re-upload corrected file'];
        break;
    }

    res.json({
      success: true,
      data: {
        id: upload.id,
        fileName: upload.original_filename,
        fileType: upload.file_type,
        status: upload.status,
        fileSize: upload.file_size,
        progress,
        validationErrors,
        metadata,
        nextSteps,
        project: {
          id: upload.project_id,
          name: upload.project_name,
          status: upload.project_status
        },
        timestamps: {
          uploaded: upload.created_at,
          lastUpdated: upload.updated_at
        }
      }
    });
  })
);

// Retry failed validation
router.post('/:uploadId/retry',
  validate(Joi.object({ uploadId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { uploadId } = req.params;

    // Verify upload access and get details
    const uploadResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT du.id, du.s3_key, du.file_type, du.project_id, du.status
       FROM data_uploads du
       JOIN sandbox_projects sp ON du.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE du.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [uploadId, req.user.id]
    );

    if (uploadResult.rows.length === 0) {
      throw new NotFoundError('Upload not found or access denied');
    }

    const upload = uploadResult.rows[0];

    // Only allow retry for failed uploads
    if (upload.status !== 'INVALID') {
      throw new ValidationError('Can only retry failed validations');
    }

    try {
      // Reset upload status
      await queryWithUserContext(
        req.user.authProviderId,
        `UPDATE data_uploads
         SET status = 'UPLOADED', validation_errors = '[]', updated_at = NOW()
         WHERE id = $1`,
        [uploadId]
      );

      // Send validation job to SQS queue
      const jobMessage = {
        jobType: 'VALIDATE_UPLOAD',
        uploadId: uploadId,
        projectId: upload.project_id,
        s3Key: upload.s3_key,
        fileType: upload.file_type,
        userId: req.user.id,
        timestamp: new Date().toISOString()
      };

      await sqs.sendMessage({
        QueueUrl: config.aws.sqs.queueUrl,
        MessageBody: JSON.stringify(jobMessage),
        MessageAttributes: {
          jobType: {
            DataType: 'String',
            StringValue: 'VALIDATE_UPLOAD'
          },
          uploadId: {
            DataType: 'String',
            StringValue: uploadId
          }
        }
      }).promise();

      logger.info('Validation retry initiated', {
        uploadId,
        userId: req.user.id,
        projectId: upload.project_id
      });

      res.json({
        success: true,
        message: 'Validation retry initiated successfully'
      });
    } catch (error) {
      logger.error('Failed to retry validation', error);
      throw new Error('Failed to retry validation');
    }
  })
);

export default router;
