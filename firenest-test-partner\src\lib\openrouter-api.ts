import axios from 'axios';

// OpenRouter API configuration
const OPENROUTER_API_KEY = '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';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Message type
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// Response type for model information
export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
}

// Function to get available models
export const getAvailableModels = async (): Promise<ModelInfo[]> => {
  try {
    const response = await axios.get('https://openrouter.ai/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin,
      }
    });

    return response.data.data.map((model: any) => ({
      id: model.id,
      name: model.name || model.id.split('/').pop(),
      description: model.description
    }));
  } catch (error) {
    console.error('Error fetching available models:', error);
    return [];
  }
};

// Function to generate a chat response
export const generateChatResponse = async (
  messages: Message[],
  modelId: string = 'microsoft/phi-4-reasoning-plus:free'
): Promise<{ content: string; model: string; usage?: any }> => {
  try {
    const response = await axios.post(
      OPENROUTER_API_URL,
      {
        model: modelId,
        messages: [
          {
            role: 'system',
            content: 'You are AtlasAI, a helpful AI assistant for content creation. You help users write, edit, and brainstorm content. You are concise but thorough, and you always provide high-quality, well-structured responses.',
          },
          ...messages,
        ],
        temperature: 0.7,
        max_tokens: 1000,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AtlasAI - Firenest Test Partner',
        },
      }
    );

    return {
      content: response.data.choices[0].message.content,
      model: response.data.model,
      usage: response.data.usage
    };
  } catch (error: any) {
    console.error('Error generating chat response:', error);

    // Extract error message if available
    let errorMessage = 'Sorry, I encountered an error. Please try again.';
    if (error.response && error.response.data && error.response.data.error) {
      errorMessage = `Error: ${error.response.data.error.message || error.response.data.error}`;
    } else if (error.message) {
      errorMessage = `Error: ${error.message}`;
    }

    return {
      content: errorMessage,
      model: 'error'
    };
  }
};