
import { Twitter, Linkedin, Instagram, Github, Mail, Phone, MapPin, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

interface FooterProps {
  onOpenWaitlist?: () => void;
  onOpenContact?: () => void;
}

// Helper function for smooth scrolling
const scrollToSection = (sectionId: string) => (e: React.MouseEvent) => {
  e.preventDefault();
  const element = document.getElementById(sectionId);
  if (element) {
    window.scrollTo({
      top: element.offsetTop - 80, // Offset for header
      behavior: 'smooth'
    });
  }
};

const Footer = ({ onOpenWaitlist, onOpenContact }: FooterProps) => {
  const currentYear = new Date().getFullYear();
  
  // External links open in a new tab
  const openExternalLink = (url: string) => {
    window.open(url, '_blank');
  };
  
  return (
    <footer className="border-t border-white/10 py-12 px-6 glassy-bg mt-12">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* Company Info */}
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg">Firenest</h3>
            <p className="text-white/60 text-sm mb-4">
              Empowering startups with advanced analytics and financial tools to make data-driven decisions.
            </p>
            <div className="flex gap-4 mt-4">
              <button onClick={() => openExternalLink('https://twitter.com')} className="text-white/60 hover:text-white transition-colors hover:scale-110">
                <Twitter size={18} />
              </button>
              <button onClick={() => openExternalLink('https://linkedin.com')} className="text-white/60 hover:text-white transition-colors hover:scale-110">
                <Linkedin size={18} />
              </button>
              <button onClick={() => openExternalLink('https://instagram.com')} className="text-white/60 hover:text-white transition-colors hover:scale-110">
                <Instagram size={18} />
              </button>
              <button onClick={() => openExternalLink('https://github.com')} className="text-white/60 hover:text-white transition-colors hover:scale-110">
                <Github size={18} />
              </button>
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <button 
                  onClick={scrollToSection('features')} 
                  className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1"
                >
                  Features
                </button>
              </li>
              <li>
                <button 
                  onClick={scrollToSection('solutions')} 
                  className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1"
                >
                  Solutions
                </button>
              </li>
              <li>
                <button 
                  onClick={scrollToSection('testimonials')} 
                  className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1"
                >
                  Testimonials
                </button>
              </li>
              <li>
                <button 
                  onClick={scrollToSection('about')} 
                  className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1"
                >
                  About Us
                </button>
              </li>
              <li>
                <button 
                  onClick={onOpenWaitlist} 
                  className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1"
                >
                  Join Waitlist
                </button>
              </li>
            </ul>
          </div>
          
          {/* Legal */}
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg">Legal</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/privacy" className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1">
                  Privacy Policy <ExternalLink size={12} />
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1">
                  Terms of Service <ExternalLink size={12} />
                </Link>
              </li>
              <li>
                <Link to="/cookies" className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1">
                  Cookie Policy <ExternalLink size={12} />
                </Link>
              </li>
              <li>
                <Link to="/security" className="text-white/60 hover:text-white transition-colors inline-flex items-center gap-1">
                  Security <ExternalLink size={12} />
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg">Contact</h3>
            <ul className="space-y-3 text-sm">
              <li className="flex items-start gap-2">
                <MapPin size={16} className="text-fiery mt-0.5 flex-shrink-0" />
                <span className="text-white/60">123 Startup Avenue, Tech Hub, Silicon Valley, CA 94025</span>
              </li>
              <li className="flex items-center gap-2">
                <Mail size={16} className="text-fiery flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-white/60 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center gap-2">
                <Phone size={16} className="text-fiery flex-shrink-0" />
                <a href="tel:+14155552671" className="text-white/60 hover:text-white transition-colors">
                  +****************
                </a>
              </li>
              <li className="mt-4">
                <button 
                  onClick={onOpenContact} 
                  className="text-sm px-4 py-2 rounded-md bg-white/5 hover:bg-white/10 border border-white/10 transition-all hover:border-fiery/50 text-white/80 hover:text-white"
                >
                  Send Message
                </button>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-white/10 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-white/40 text-xs">
            © {currentYear} Firenest. All rights reserved.
          </p>
          <p className="text-white/40 text-xs">
            Designed with ❤️ for startups everywhere
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
