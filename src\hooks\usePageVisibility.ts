import { useState, useEffect } from 'react';

/**
 * Custom hook that tracks whether the page is visible or hidden
 * using the Page Visibility API
 * 
 * @returns {boolean} - Whether the page is currently visible
 */
export function usePageVisibility(): boolean {
  // Get the initial visibility state
  const [isVisible, setIsVisible] = useState<boolean>(!document.hidden);
  
  useEffect(() => {
    // Define the handler that updates the visibility state
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };
    
    // Add event listener for visibility change
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
  
  return isVisible;
}

/**
 * Custom hook that pauses certain operations when the page is not visible
 * and resumes them when the page becomes visible again
 * 
 * @param {Function} onVisible - Callback to run when the page becomes visible
 * @param {Function} onHidden - Callback to run when the page becomes hidden
 */
export function useVisibilityEffect(
  onVisible?: () => void,
  onHidden?: () => void
): void {
  useEffect(() => {
    // Define the handler for visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        onHidden?.();
      } else {
        onVisible?.();
      }
    };
    
    // Add event listener for visibility change
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [onVisible, onHidden]);
}

/**
 * Custom hook that prevents unnecessary re-renders when the page is not visible
 * 
 * @param {number} interval - The interval in milliseconds for updates when the page is visible
 * @param {number} hiddenInterval - The interval in milliseconds for updates when the page is hidden (default: null, which means no updates when hidden)
 * @returns {number} - A counter that increments based on the visibility state
 */
export function useVisibilityAwareInterval(
  interval: number,
  hiddenInterval: number | null = null
): number {
  const [counter, setCounter] = useState(0);
  const isVisible = usePageVisibility();
  
  useEffect(() => {
    // If the page is hidden and hiddenInterval is null, don't set up an interval
    if (!isVisible && hiddenInterval === null) {
      return;
    }
    
    // Use the appropriate interval based on visibility
    const currentInterval = isVisible ? interval : (hiddenInterval || interval);
    
    // Set up the interval
    const id = setInterval(() => {
      setCounter(c => c + 1);
    }, currentInterval);
    
    // Clean up the interval when the component unmounts or dependencies change
    return () => {
      clearInterval(id);
    };
  }, [isVisible, interval, hiddenInterval]);
  
  return counter;
}
