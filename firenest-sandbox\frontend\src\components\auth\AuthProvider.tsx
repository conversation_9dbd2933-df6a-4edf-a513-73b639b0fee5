/**
 * Authentication Provider
 * Handles authentication state and token management
 */

import React, { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { isLoading, setLoading } = useAuthStore()

  useEffect(() => {
    // Simulate auth check delay
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [setLoading])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-white mt-4">Loading Firenest Sandbox...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
