import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import ModernCard from '@/components/ui/modern-card';
import { Button } from '@/components/ui/button';
import {
  BarChart3,
  TrendingUp,
  Zap,
  CreditCard,
  Clock,
  ArrowRight,
  ChevronRight,
  Sparkles,
  Lightbulb
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { formatDate, formatCurrency } from '@/lib/utils';
import AIToolIcon from '@/components/AIToolIcon';

interface EnhancedOverviewSectionProps {
  credits: any;
  transactions: any[];
  recentTools: any[];
  usageData: any[];
  maxCredits: number;
  totalCreditsThisWeek: number;
}

/**
 * Enhanced Overview Section with professional design patterns
 * Inspired by industry leaders like Zapier and HubSpot
 */
const EnhancedOverviewSection = ({
  credits,
  transactions,
  recentTools,
  usageData,
  maxCredits,
  totalCreditsThisWeek
}: EnhancedOverviewSectionProps) => {
  return (
    <div className="space-y-6">
      {/* Stats cards with modern design */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Available Credits */}
        <ModernCard
          title="Available Credits"
          value={credits?.availableCredits || 0}
          icon={<CreditCard />}
          iconColor="red"
          description={`of ${credits?.totalCredits || 0} total credits`}
          footer={
            <div className="space-y-3">
              <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-fiery to-fiery-600 rounded-full"
                  style={{
                    width: `${credits ? (credits.availableCredits / credits.totalCredits) * 100 : 0}%`
                  }}
                ></div>
              </div>
              <div className="text-xs text-white/50">
                {credits?.availableCredits < 50 ? (
                  <span className="text-amber-400">Running low on credits</span>
                ) : (
                  <span>Credits healthy</span>
                )}
              </div>
            </div>
          }
        />

        {/* Credits Used */}
        <ModernCard
          title="Credits Used (This Week)"
          value={totalCreditsThisWeek}
          icon={<Zap />}
          iconColor="blue"
          trend={{
            value: totalCreditsThisWeek > 200 ? "20% more than last week" : "15% less than last week",
            isPositive: totalCreditsThisWeek <= 200
          }}
          footer={
            <div className="flex items-end h-12 gap-1">
              {usageData.map((day, index) => (
                <div key={index} className="flex-1 flex flex-col items-center group/bar">
                  <div
                    className="w-full bg-fiery/80 rounded-sm transition-all duration-300 group-hover/bar:bg-fiery"
                    style={{
                      height: `${(day.credits / maxCredits) * 100}%`,
                      opacity: 0.3 + (day.credits / maxCredits) * 0.7
                    }}
                  ></div>
                  <div className="text-[10px] text-white/50 mt-1 group-hover/bar:text-white transition-colors">
                    {day.day}
                  </div>
                  <div className="absolute opacity-0 group-hover/bar:opacity-100 transition-opacity duration-200 -mt-8 bg-dark-700 text-white text-xs py-1 px-2 rounded pointer-events-none">
                    {day.credits} credits
                  </div>
                </div>
              ))}
            </div>
          }
        />

        {/* Estimated Savings */}
        <ModernCard
          title="Estimated Savings"
          value={formatCurrency(credits?.usedCredits ? credits.usedCredits * 20 : 0)}
          icon={<TrendingUp />}
          iconColor="green"
          description="compared to individual subscriptions"
          badge="+24%"
          badgeColor="green"
          footer={
            <div className="bg-white/5 rounded-md p-2 text-xs text-white/80">
              <div className="flex items-center">
                <Sparkles className="h-3 w-3 text-green-400 mr-1" />
                <span>You've saved {formatCurrency(2499)} this year</span>
              </div>
            </div>
          }
        />

        {/* Recent Activity */}
        <ModernCard
          title="Recent Activity"
          value={
            <div className="divide-y divide-white/5 -mx-4 -mt-2">
              {transactions.slice(0, 3).map((transaction, index) => (
                <div
                  key={index}
                  className="px-4 py-2 flex items-center justify-between hover:bg-white/5 transition-colors"
                >
                  <div className="flex items-center">
                    <div className={`w-6 h-6 rounded-full ${
                      transaction.transactionType === 'usage' ? 'bg-red-500/20' :
                      transaction.transactionType === 'purchase' ? 'bg-green-500/20' : 'bg-fiery/20'
                    } flex items-center justify-center mr-2`}>
                      <AIToolIcon
                        iconName={
                          transaction.transactionType === 'usage' ? 'Bot' :
                          transaction.transactionType === 'purchase' ? 'CreditCard' : 'Zap'
                        }
                        className={`h-3 w-3 ${
                          transaction.transactionType === 'usage' ? 'text-red-400' :
                          transaction.transactionType === 'purchase' ? 'text-green-400' : 'text-fiery'
                        }`}
                      />
                    </div>
                    <div className="text-xs text-white/90 truncate max-w-[120px]">
                      {transaction.description}
                    </div>
                  </div>
                  <div className={`text-xs font-medium ${transaction.amount > 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                  </div>
                </div>
              ))}
            </div>
          }
          icon={<Clock />}
          iconColor="purple"
          footer={
            <Link
              to="/dashboard/credits"
              className="text-xs text-fiery hover:text-fiery-300 flex items-center transition-colors"
            >
              View all transactions
              <ChevronRight className="h-3 w-3 ml-1" />
            </Link>
          }
        />
      </div>

      {/* Getting Started Section - New */}
      <div className="firenest-card overflow-hidden">
        <div className="p-4">
          <div className="flex items-center mb-2">
            <div className="firenest-card-icon firenest-card-icon-blue">
              <Lightbulb className="h-3 w-3 text-white" />
            </div>
            <span className="text-lg text-white font-medium">Getting Started</span>
          </div>
          <p className="text-sm text-white/70 ml-7">Complete these steps to get the most out of Firenest</p>

          <div className="space-y-4 mt-4">
            <div className="flex items-center gap-4 p-3 firenest-nested-card hover:bg-dark-800/60">
              <div className="h-10 w-10 rounded-full bg-green-500/20 flex items-center justify-center text-green-400">
                <CheckCircleIcon className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-white">Complete your profile</h3>
                <p className="text-xs text-white/70">Your profile is complete and up to date</p>
              </div>
              <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                View
              </Button>
            </div>

            <div className="flex items-center gap-4 p-3 firenest-nested-card hover:bg-dark-800/60">
              <div className="h-10 w-10 rounded-full bg-fiery/20 flex items-center justify-center text-fiery">
                <Zap className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-white">Try your first AI tool</h3>
                <p className="text-xs text-white/70">Launch an AI tool from the workbench</p>
              </div>
              <Button variant="outline" size="sm" className="border-fiery/30 text-fiery hover:bg-fiery/10">
                Go to Workbench
              </Button>
            </div>

            <div className="flex items-center gap-4 p-3 firenest-nested-card hover:bg-dark-800/60">
              <div className="h-10 w-10 rounded-full bg-fiery/20 flex items-center justify-center text-fiery">
                <CreditCard className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-white">Add payment method</h3>
                <p className="text-xs text-white/70">Set up a payment method to buy credits</p>
              </div>
              <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/10">
                Add Method
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// CheckCircle icon component
const CheckCircleIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
    <polyline points="22 4 12 14.01 9 11.01" />
  </svg>
);

export default EnhancedOverviewSection;
