# Firenest Sandbox - Quick Start Guide

## 🚀 Get Running in 5 Minutes

### Step 1: Run Setup Script

**Windows:**
```bash
setup.bat
```

**Linux/macOS:**
```bash
chmod +x setup.sh
./setup.sh
```

This will:
- Install all dependencies
- Create environment files
- Show database setup instructions

### Step 2: Database Setup (Choose One Option)

#### Option A: Docker (Easiest)
```bash
# Start PostgreSQL in Docker
docker run --name firenest-postgres \
  -e POSTGRES_DB=firenest_sandbox \
  -e POSTGRES_USER=sandbox_admin \
  -e POSTGRES_PASSWORD=your_password \
  -p 5432:5432 \
  -d postgres:14

# Wait 10 seconds, then create tables
sleep 10
docker exec -i firenest-postgres psql -U sandbox_admin -d firenest_sandbox < database/schema.sql
```

#### Option B: Local PostgreSQL
```bash
# Install PostgreSQL, then:
psql -U postgres -c "CREATE DATABASE firenest_sandbox;"
psql -U postgres -c "CREATE USER sandbox_admin WITH PASSWORD 'your_password';"
psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE firenest_sandbox TO sandbox_admin;"
psql -U sandbox_admin -d firenest_sandbox -f database/schema.sql
```

### Step 3: Start the Application

**Terminal 1 - Backend:**
```bash
cd backend
npm run dev
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

### Step 4: Access the Application

Open your browser to: **http://localhost:3000**

## 🎯 What You Can Do

### 1. Create Your First Workspace
- Register/login at the homepage
- Create a new workspace for your organization
- Invite team members (optional)

### 2. Set Up a Project
- Navigate to "Projects"
- Click "Create Project"
- Enter project details and description

### 3. Upload Customer Data
- Go to your project dashboard
- Click "Upload Data"
- Upload CSV files with customer usage data
- Watch real-time validation progress

### 4. Build Pricing Models
- Navigate to "Pricing Models"
- Click "Create Model"
- Use the visual drag-and-drop builder:
  - Add base fees
  - Configure usage-based rates
  - Set up tiered pricing
  - Add minimum/maximum caps

### 5. Run Revenue Simulations
- Go to "Simulations"
- Click "New Simulation"
- Select multiple pricing models to compare
- Configure analysis options
- Monitor real-time processing
- View comprehensive results and insights

## 📊 Sample Data Format

Upload CSV files with these columns:

**Customer Usage Data:**
```csv
customer_id,metric_name,metric_value,period
cust_001,api_calls,15000,2024-01
cust_001,storage_gb,250,2024-01
cust_002,api_calls,8500,2024-01
```

**Billing Data:**
```csv
customer_id,plan_type,signup_date,region
cust_001,enterprise,2023-06-15,us-east
cust_002,professional,2023-08-22,eu-west
```

## 🔧 Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker ps | grep postgres  # For Docker
sudo systemctl status postgresql  # For local install

# Test connection
psql -U sandbox_admin -d firenest_sandbox -c "SELECT 1;"
```

### Port Already in Use
```bash
# Find what's using port 3001
lsof -i :3001  # macOS/Linux
netstat -ano | findstr :3001  # Windows

# Kill the process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

### Clear Cache and Restart
```bash
# Backend
cd backend
rm -rf node_modules dist
npm install
npm run dev

# Frontend
cd frontend
rm -rf node_modules dist
npm install
npm run dev
```

## 🎨 Key Features to Explore

### Phase 1: Data Ingestion ✅
- **Secure file upload** with drag-and-drop
- **Real-time validation** with progress tracking
- **Data quality analysis** with detailed reports
- **Enterprise security** with audit trails

### Phase 2: Pricing Models ✅
- **Visual model builder** with component palette
- **Drag-and-drop interface** for pricing components
- **Real-time preview** with sample calculations
- **Professional model management** dashboard

### Phase 3: Simulation Engine ✅
- **High-performance processing** with background workers
- **Multi-model comparison** for A/B testing
- **Advanced analytics** with revenue insights
- **Professional results** visualization

## 📈 Business Value

### For Pricing Teams
- **Reduce analysis time** from weeks to hours
- **Test pricing strategies** before implementation
- **Quantify revenue impact** of pricing changes
- **Professional presentations** for stakeholders

### For Engineering Teams
- **Production-ready platform** with enterprise security
- **Scalable architecture** supporting growth
- **Comprehensive API** for integrations
- **Modern tech stack** with best practices

### For Executives
- **Data-driven decisions** with transparent methodology
- **Risk mitigation** through scenario testing
- **Revenue optimization** opportunities identified
- **Competitive advantage** through pricing intelligence

## 🚀 Next Steps

1. **Explore the Platform**: Try all three phases with sample data
2. **Customize**: Modify components to fit your needs
3. **Integrate**: Use the API for custom integrations
4. **Deploy**: Follow `DEPLOYMENT.md` for production setup
5. **Scale**: Add team members and multiple projects

## 📚 Additional Resources

- **SETUP.md** - Detailed setup instructions
- **DEPLOYMENT.md** - Production deployment guide
- **API Documentation** - Available at `/api/v1` when running
- **Architecture Guide** - In `/docs` folder

## 💬 Support

If you need help:
1. Check the troubleshooting section above
2. Review the detailed documentation
3. Check browser console for frontend issues
4. Verify environment variables are correct
5. Ensure all prerequisites are installed

**Happy pricing! 🎯**
