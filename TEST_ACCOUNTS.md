# Test Accounts for Firenest

This document contains information about test accounts that can be used to verify the authentication flow in the Firenest application.

## Test Account Creation

To test the authentication flow, you should create a test account with a real email address that you have access to. This will allow you to verify the email verification process.

### Steps to Create a Test Account:

1. Go to the signup page (`/signup`)
2. Fill in the following information:
   - Full Name: Test User
   - Email: [Your real email address]
   - Password: TestPassword123
   - Confirm Password: TestPassword123
3. Click "Sign Up"
4. Check your email for a verification link
5. Click the verification link to verify your account
6. Go to the login page (`/login`)
7. Log in with your email and password

### Expected Behavior:

- After signing up, you should be redirected to the login page with a message to check your email
- After verifying your email, you should be able to log in
- After logging in, you should be redirected to the dashboard

## Testing Authentication Errors

To test error handling, you can try the following scenarios:

### Invalid Login Credentials:

1. Go to the login page (`/login`)
2. Enter an email that doesn't exist or an incorrect password
3. Click "Log In"
4. You should see an error message indicating that the credentials are invalid

### Unverified Email:

1. Create a new account but don't verify the email
2. Try to log in with the new account
3. You should see an error message indicating that the email needs to be verified

### Password Requirements:

1. Go to the signup page (`/signup`)
2. Try to create an account with a password that doesn't meet the requirements:
   - Less than 6 characters
   - No uppercase letters
   - No lowercase letters
   - No numbers
3. You should see error messages for each requirement that isn't met

## Notes

- The test accounts should be deleted after testing is complete
- Do not use production data for testing
- Make sure to test on all supported browsers and devices
