import { User } from "lucide-react";

type CounterStatProps = {
  number: number;
  label: string;
  compact?: boolean;
};

const CounterStat = ({ number, label, compact = false }: CounterStatProps) => {
  return (
    <div className={`flex items-center gap-3 ${compact ? 'firenest-card px-3 py-2' : 'firenest-card'}`}>
      <div className="flex -space-x-2">
        {[...Array(compact ? 2 : 3)].map((_, i) => (
          <div
            key={i}
            className={`${compact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full bg-gradient-to-br from-fiery to-fiery-600 border-2 border-dark flex items-center justify-center`}
          >
            <User className={`text-white ${compact ? 'h-3 w-3' : 'h-4 w-4'}`} />
          </div>
        ))}
      </div>
      <p className="text-white/80">
        <span className="font-bold text-white">{number}+</span> {label}
      </p>
    </div>
  );
};

export default CounterStat;
