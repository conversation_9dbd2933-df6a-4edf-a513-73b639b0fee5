# Fix for User Data Fetching Issue

There's an issue with the `get_auth_user_by_id` function that's causing errors when fetching user data during the OAuth flow. To fix this issue, you need to create or update the function in your Supabase database.

## Steps to Fix

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Create a new query
4. Paste the following SQL code:

```sql
-- Create the get_auth_user_by_id function
CREATE OR REPLACE FUNCTION get_auth_user_by_id(user_id_param UUID)
RETURNS TABLE (
  id UUID,
  email TEXT,
  raw_user_meta_data JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email,
    au.raw_user_meta_data
  FROM 
    auth.users au
  WHERE 
    au.id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users and service_role
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO authenticated;
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO service_role;
```

5. Run the query
6. Restart your API server

This will create the necessary function to fetch user data from the auth.users table.

## Alternative: Run via API

If you have the Supabase service key, you can also run this SQL via the Supabase REST API:

```javascript
const response = await fetch('https://wjwguxccykrbarehqgpq.supabase.co/rest/v1/rpc/pgbouncer_exec', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'apikey': 'YOUR_SUPABASE_SERVICE_KEY',
    'Authorization': 'Bearer YOUR_SUPABASE_SERVICE_KEY'
  },
  body: JSON.stringify({
    query: `
      -- Create the get_auth_user_by_id function
      CREATE OR REPLACE FUNCTION get_auth_user_by_id(user_id_param UUID)
      RETURNS TABLE (
        id UUID,
        email TEXT,
        raw_user_meta_data JSONB
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          au.id,
          au.email,
          au.raw_user_meta_data
        FROM 
          auth.users au
        WHERE 
          au.id = user_id_param;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Grant execute permission to authenticated users and service_role
      GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO authenticated;
      GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO service_role;
    `
  })
});
```
