import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Server, RefreshCw, Home, ChevronDown, ChevronUp, AlertTriangle, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { notify } from '@/components/ui/notification-system';
import { useDebug } from '@/contexts/DebugContext';

interface ServerErrorPageProps {
  title?: string;
  message?: string;
  errorCode?: string;
  errorDetails?: string;
  onRetry?: () => void;
}

const ServerErrorPage: React.FC<ServerErrorPageProps> = ({
  title = 'Server Error',
  message = 'We\'re experiencing some technical difficulties',
  errorCode = '500',
  errorDetails,
  onRetry
}) => {
  const navigate = useNavigate();
  const { isDebugMode, openDebugConsole } = useDebug();
  const [showDetails, setShowDetails] = useState(false);

  const handleRefresh = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const handleReportIssue = () => {
    // In a real implementation, this would open a form or send an email
    notify.info('Thank you for reporting this issue. Our team has been notified.', {
      title: 'Issue Reported',
      duration: 4000
    });
  };

  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />

      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>

      <main className="flex-grow flex items-center justify-center p-6">
        <div className="w-full max-w-2xl">
          <div className="glass-card p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

            <div className="flex flex-col items-center mb-8">
              <div className="w-20 h-20 bg-red-500/10 rounded-full flex items-center justify-center mb-4">
                <Server className="h-10 w-10 text-red-500 animate-pulse-slow" />
              </div>
              <h2 className="text-2xl font-bold mb-2">{title}</h2>
              <p className="text-white/70 text-center">{message}</p>
              <div className="mt-2 px-3 py-1 bg-dark-800/50 rounded-full text-white/60 text-sm">
                Error Code: {errorCode}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <Button
                onClick={handleRefresh}
                className="flex-1 bg-fiery hover:bg-fiery-600 text-white"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>

              <Button
                onClick={handleGoHome}
                variant="outline"
                className="flex-1 border-white/20"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
            </div>

            <div className="border-t border-white/10 pt-4">
              <button
                onClick={toggleDetails}
                className="flex items-center justify-between w-full text-white/70 hover:text-white transition-colors py-2"
              >
                <span className="font-medium">Error Details</span>
                {showDetails ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </button>

              {showDetails && (
                <div className="mt-4 space-y-4">
                  <div className="bg-dark-900/70 border border-white/10 rounded-md p-4 text-sm text-white/70">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium text-white/90">Server Error {errorCode}</p>
                        <p className="mt-1">Our server encountered an unexpected condition that prevented it from fulfilling your request.</p>

                        {errorDetails && (
                          <div className="mt-3 p-2 bg-dark-800/50 border border-white/5 rounded text-xs font-mono overflow-x-auto">
                            {errorDetails}
                          </div>
                        )}

                        <div className="mt-4">
                          <p className="text-sm font-medium text-white/80">What you can do:</p>
                          <ul className="list-disc pl-5 mt-1 space-y-1 text-xs">
                            <li>Try refreshing the page</li>
                            <li>Try again later</li>
                            <li>Clear your browser cache and cookies</li>
                            <li>Contact our support team if the problem persists</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={handleReportIssue}
                      variant="outline"
                      size="sm"
                      className="border-white/20"
                    >
                      Report This Issue
                    </Button>

                    {isDebugMode && (
                      <Button
                        onClick={openDebugConsole}
                        variant="outline"
                        size="sm"
                        className="border-white/20"
                      >
                        <Bug className="mr-2 h-4 w-4" />
                        Open Debug Console
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <footer className="py-4 px-6 text-center text-white/50 text-sm">
        <p>
          <Link to="/" className="text-fiery hover:text-fiery-400 hover:underline">
            Firenest
          </Link>{' '}
          &copy; {new Date().getFullYear()} All rights reserved.
        </p>
      </footer>
    </div>
  );
};

export default ServerErrorPage;
