import React, { useState } from 'react';
import { X } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { submitEarlyAccess } from '@/lib/supabase';

interface WaitlistPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const WaitlistPopup: React.FC<WaitlistPopupProps> = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [company, setCompany] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !name) {
      notify.error("Please fill out all required fields", {
        title: "Missing Information",
        position: "top-center",
        duration: 4000
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await submitEarlyAccess(email, name);

      if (result.success) {
        setEmail('');
        setName('');
        setCompany('');
        notify.success("You've been added to our waitlist! We'll notify you when we launch.", {
          title: "Waitlist Signup Complete",
          position: "top-center",
          duration: 5000
        });
        onClose();
      } else {
        notify.error("Something went wrong. Please try again.", {
          title: "Submission Failed",
          position: "top-center",
          duration: 5000
        });
      }
    } catch (error) {
      console.error("Error submitting waitlist request:", error);
      notify.error("Something went wrong. Please try again.", {
        title: "Error",
        position: "top-center",
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in">
      <div className="bg-gradient-to-br from-dark-800 to-dark-900 border border-white/10 rounded-xl w-full max-w-md overflow-hidden relative shadow-xl shadow-fiery/10">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 bg-black/40 hover:bg-black/60 rounded-full p-2 text-white/80 hover:text-white transition-all z-10"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="p-6">
          <div className="mb-6 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-fiery to-fiery/60 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🔥</span>
            </div>
            <h3 className="text-2xl font-bold mb-2">Join Our Waitlist</h3>
            <p className="text-white/70">
              Be among the first to access Firenest when we launch. Early adopters get special perks!
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm text-white/70 mb-1">Your Name</label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="w-full bg-white/10 border border-white/20 rounded-md px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-fiery"
                placeholder="John Doe"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm text-white/70 mb-1">Email Address</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full bg-white/10 border border-white/20 rounded-md px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-fiery"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="company" className="block text-sm text-white/70 mb-1">Company Name (Optional)</label>
              <input
                type="text"
                id="company"
                value={company}
                onChange={(e) => setCompany(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-md px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-fiery"
                placeholder="Your Company"
              />
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={isSubmitting}
                className="fire-button w-full py-3"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : "Join Waitlist"}
              </button>
            </div>

            <div className="text-xs text-white/60 text-center mt-4">
              <p>By joining, you agree to receive occasional updates about our product. We'll never spam you or share your information.</p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default WaitlistPopup;
