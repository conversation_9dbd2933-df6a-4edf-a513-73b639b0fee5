// /supabase/functions/purchase-credits/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

// IMPORTANT: Set Supabase URL and Anon Key in Function settings
// Also set SUPABASE_SERVICE_ROLE_KEY for admin operations
const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 1. Get user ID (from JWT) and request body
    const { amount, paymentProof } = await req.json() // Add payment proof validation logic
    const user = /* ... Get user from JWT ... */ // Implement JWT verification logic here
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }
    const userId = user.id;

    if (!amount || typeof amount !== 'number' || amount <= 0) {
       return new Response(JSON.stringify({ error: 'Invalid amount' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // 2. TODO: Validate paymentProof (e.g., call Stripe API, check internal token)
    // This is CRITICAL and depends heavily on your payment provider.
    const paymentIsValid = true; // Replace with actual validation
    if (!paymentIsValid) {
       return new Response(JSON.stringify({ error: 'Invalid payment proof' }), {
        status: 402, // Payment Required (or 400 Bad Request)
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // 3. Use a database function/transaction for atomicity
    const { data, error } = await supabaseAdmin.rpc('add_user_credits', {
      p_user_id: userId,
      p_amount: amount,
      p_transaction_description: `Purchase via payment proof: ${paymentProof?.id || 'N/A'}`, // Example description
      p_transaction_type: 'purchase'
    })

    if (error) throw error

    return new Response(JSON.stringify({ success: true, new_balance: data }), { // Assuming rpc returns new balance
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Purchase Credits Error:', error)
    return new Response(JSON.stringify({ error: error.message || 'Failed to purchase credits' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})

/*
-- Corresponding Supabase SQL Function (add in Supabase SQL Editor)
CREATE OR REPLACE FUNCTION add_user_credits(p_user_id uuid, p_amount numeric, p_transaction_description text, p_transaction_type text)
RETURNS numeric -- Returns the new available balance
LANGUAGE plpgsql
SECURITY DEFINER -- Important for updating tables securely
AS $$
DECLARE
  v_current_available numeric;
  v_new_available numeric;
BEGIN
  -- Ensure user_credits row exists, insert if not
  INSERT INTO public.user_credits (user_id, available, total_purchased, used)
  VALUES (p_user_id, 0, 0, 0)
  ON CONFLICT (user_id) DO NOTHING;

  -- Lock the row and update credits
  SELECT available INTO v_current_available
  FROM public.user_credits
  WHERE user_id = p_user_id
  FOR UPDATE; -- Lock the row for this transaction

  v_new_available := v_current_available + p_amount;

  UPDATE public.user_credits
  SET
    available = v_new_available,
    total_purchased = total_purchased + p_amount
  WHERE user_id = p_user_id;

  -- Log the transaction
  INSERT INTO public.credit_transactions (user_id, amount, type, description, ending_balance)
  VALUES (p_user_id, p_amount, p_transaction_type, p_transaction_description, v_new_available);

  RETURN v_new_available;
END;
$$;
*/
