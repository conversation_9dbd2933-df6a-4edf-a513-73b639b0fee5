#!/usr/bin/env node

/**
 * Infrastructure Validation Script
 * Comprehensive validation of AWS infrastructure, database, and networking
 * SOC 2 Alignment: CC7.1 (System Operations), CC7.2 (System Monitoring)
 */

const AWS = require('aws-sdk');
const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  aws: {
    region: process.env.AWS_REGION || 'ap-south-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'firenest_sandbox',
    user: process.env.DB_USERNAME || 'sandbox_admin',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true'
  },
  api: {
    url: process.env.API_URL || 'http://localhost:3001',
    healthEndpoint: '/health'
  },
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3000'
  }
};

class InfrastructureValidator {
  constructor() {
    this.results = {
      aws: {},
      database: {},
      api: {},
      frontend: {},
      security: {},
      overall: { passed: 0, failed: 0, warnings: 0 }
    };
    
    // Configure AWS
    if (config.aws.accessKeyId && config.aws.secretAccessKey) {
      AWS.config.update({
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
        region: config.aws.region
      });
    }
  }

  async validateAll() {
    console.log('🚀 Starting Infrastructure Validation...\n');
    
    try {
      await this.validateAWS();
      await this.validateDatabase();
      await this.validateAPI();
      await this.validateFrontend();
      await this.validateSecurity();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    }
  }

  async validateAWS() {
    console.log('☁️  Validating AWS Infrastructure...');
    
    try {
      // Test AWS credentials
      const sts = new AWS.STS();
      const identity = await sts.getCallerIdentity().promise();
      this.logSuccess('AWS', 'Credentials valid', `Account: ${identity.Account}`);
      
      // Validate S3
      const s3 = new AWS.S3();
      const buckets = await s3.listBuckets().promise();
      this.logSuccess('AWS', 'S3 accessible', `Found ${buckets.Buckets.length} buckets`);
      
      // Validate SQS
      const sqs = new AWS.SQS();
      const queues = await sqs.listQueues().promise();
      this.logSuccess('AWS', 'SQS accessible', `Found ${queues.QueueUrls?.length || 0} queues`);
      
      // Validate RDS connectivity (if configured)
      if (process.env.RDS_ENDPOINT) {
        const rds = new AWS.RDS();
        const instances = await rds.describeDBInstances().promise();
        this.logSuccess('AWS', 'RDS accessible', `Found ${instances.DBInstances.length} instances`);
      }
      
    } catch (error) {
      this.logError('AWS', 'Infrastructure validation failed', error.message);
    }
  }

  async validateDatabase() {
    console.log('🗄️  Validating Database Connection...');
    
    const pool = new Pool(config.database);
    
    try {
      // Test basic connection
      const client = await pool.connect();
      this.logSuccess('Database', 'Connection established', `Host: ${config.database.host}`);
      
      // Test database version
      const versionResult = await client.query('SELECT version()');
      const version = versionResult.rows[0].version;
      this.logSuccess('Database', 'Version check', version.split(' ')[0] + ' ' + version.split(' ')[1]);
      
      // Test required tables exist
      const tablesResult = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      const requiredTables = [
        'users', 'workspaces', 'projects', 'uploads', 
        'models', 'simulations', 'audit_logs'
      ];
      
      const existingTables = tablesResult.rows.map(row => row.table_name);
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));
      
      if (missingTables.length === 0) {
        this.logSuccess('Database', 'Schema validation', 'All required tables exist');
      } else {
        this.logWarning('Database', 'Schema validation', `Missing tables: ${missingTables.join(', ')}`);
      }
      
      // Test database performance
      const start = Date.now();
      await client.query('SELECT 1');
      const queryTime = Date.now() - start;
      
      if (queryTime < 100) {
        this.logSuccess('Database', 'Performance test', `Query time: ${queryTime}ms`);
      } else {
        this.logWarning('Database', 'Performance test', `Slow query time: ${queryTime}ms`);
      }
      
      client.release();
      
    } catch (error) {
      this.logError('Database', 'Connection failed', error.message);
    } finally {
      await pool.end();
    }
  }

  async validateAPI() {
    console.log('🔌 Validating API Endpoints...');
    
    try {
      // Test health endpoint
      const healthResponse = await axios.get(`${config.api.url}${config.api.healthEndpoint}`, {
        timeout: 5000
      });
      
      if (healthResponse.status === 200) {
        this.logSuccess('API', 'Health check', `Status: ${healthResponse.data.status}`);
      } else {
        this.logError('API', 'Health check', `Unexpected status: ${healthResponse.status}`);
      }
      
      // Test API response time
      const start = Date.now();
      await axios.get(`${config.api.url}${config.api.healthEndpoint}`);
      const responseTime = Date.now() - start;
      
      if (responseTime < 500) {
        this.logSuccess('API', 'Response time', `${responseTime}ms`);
      } else {
        this.logWarning('API', 'Response time', `Slow response: ${responseTime}ms`);
      }
      
      // Test CORS headers
      const corsResponse = await axios.options(`${config.api.url}/api/v1/health`);
      const corsHeaders = corsResponse.headers;
      
      if (corsHeaders['access-control-allow-origin']) {
        this.logSuccess('API', 'CORS configuration', 'Headers present');
      } else {
        this.logWarning('API', 'CORS configuration', 'CORS headers missing');
      }
      
    } catch (error) {
      this.logError('API', 'Endpoint validation', error.message);
    }
  }

  async validateFrontend() {
    console.log('🌐 Validating Frontend Application...');
    
    try {
      // Test frontend accessibility
      const response = await axios.get(config.frontend.url, {
        timeout: 10000
      });
      
      if (response.status === 200) {
        this.logSuccess('Frontend', 'Accessibility', 'Application loads successfully');
      }
      
      // Check for required meta tags and security headers
      const html = response.data;
      
      if (html.includes('<meta name="viewport"')) {
        this.logSuccess('Frontend', 'Mobile optimization', 'Viewport meta tag present');
      } else {
        this.logWarning('Frontend', 'Mobile optimization', 'Viewport meta tag missing');
      }
      
      // Check security headers
      const securityHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection'
      ];
      
      securityHeaders.forEach(header => {
        if (response.headers[header]) {
          this.logSuccess('Frontend', 'Security headers', `${header} present`);
        } else {
          this.logWarning('Frontend', 'Security headers', `${header} missing`);
        }
      });
      
    } catch (error) {
      this.logError('Frontend', 'Application validation', error.message);
    }
  }

  async validateSecurity() {
    console.log('🔒 Validating Security Configuration...');
    
    try {
      // Test SSL/TLS configuration
      if (config.api.url.startsWith('https://')) {
        this.logSuccess('Security', 'SSL/TLS', 'HTTPS enabled for API');
      } else {
        this.logWarning('Security', 'SSL/TLS', 'API not using HTTPS');
      }
      
      if (config.frontend.url.startsWith('https://')) {
        this.logSuccess('Security', 'SSL/TLS', 'HTTPS enabled for frontend');
      } else {
        this.logWarning('Security', 'SSL/TLS', 'Frontend not using HTTPS');
      }
      
      // Test rate limiting
      const requests = Array(10).fill().map(() => 
        axios.get(`${config.api.url}/health`).catch(err => err.response)
      );
      
      const responses = await Promise.all(requests);
      const rateLimited = responses.some(r => r?.status === 429);
      
      if (rateLimited) {
        this.logSuccess('Security', 'Rate limiting', 'Rate limiting active');
      } else {
        this.logWarning('Security', 'Rate limiting', 'Rate limiting not detected');
      }
      
      // Validate environment variables
      const requiredEnvVars = [
        'JWT_SECRET',
        'ENCRYPTION_KEY',
        'DB_PASSWORD'
      ];
      
      const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
      
      if (missingEnvVars.length === 0) {
        this.logSuccess('Security', 'Environment variables', 'All required variables set');
      } else {
        this.logError('Security', 'Environment variables', `Missing: ${missingEnvVars.join(', ')}`);
      }
      
    } catch (error) {
      this.logError('Security', 'Security validation', error.message);
    }
  }

  logSuccess(category, test, details) {
    console.log(`✅ ${category}: ${test} - ${details}`);
    this.results.overall.passed++;
    if (!this.results[category.toLowerCase()]) this.results[category.toLowerCase()] = {};
    this.results[category.toLowerCase()][test] = { status: 'passed', details };
  }

  logWarning(category, test, details) {
    console.log(`⚠️  ${category}: ${test} - ${details}`);
    this.results.overall.warnings++;
    if (!this.results[category.toLowerCase()]) this.results[category.toLowerCase()] = {};
    this.results[category.toLowerCase()][test] = { status: 'warning', details };
  }

  logError(category, test, details) {
    console.log(`❌ ${category}: ${test} - ${details}`);
    this.results.overall.failed++;
    if (!this.results[category.toLowerCase()]) this.results[category.toLowerCase()] = {};
    this.results[category.toLowerCase()][test] = { status: 'failed', details };
  }

  generateReport() {
    console.log('\n📊 Infrastructure Validation Report');
    console.log('=====================================');
    console.log(`✅ Passed: ${this.results.overall.passed}`);
    console.log(`⚠️  Warnings: ${this.results.overall.warnings}`);
    console.log(`❌ Failed: ${this.results.overall.failed}`);
    
    const totalTests = this.results.overall.passed + this.results.overall.warnings + this.results.overall.failed;
    const successRate = ((this.results.overall.passed / totalTests) * 100).toFixed(1);
    
    console.log(`\n📈 Success Rate: ${successRate}%`);
    
    // Save detailed report
    const reportPath = path.join(__dirname, '../reports/infrastructure-validation.json');
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    
    if (this.results.overall.failed > 0) {
      console.log('\n❌ Infrastructure validation failed. Please address the issues above.');
      process.exit(1);
    } else {
      console.log('\n🎉 Infrastructure validation completed successfully!');
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new InfrastructureValidator();
  validator.validateAll().catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

module.exports = InfrastructureValidator;
