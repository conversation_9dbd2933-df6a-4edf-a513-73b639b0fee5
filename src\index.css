@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-dark text-white font-sans antialiased overflow-x-hidden;
  }
}

@layer components {
  .gradient-text {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-fiery-400 to-fiery;
  }

  .gradient-firenest {
    @apply inline-flex;
  }

  /* Improved text visibility classes for dark theme */
  .text-enhanced {
    @apply text-white/90;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .text-enhanced-muted {
    @apply text-white/75;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .text-label {
    @apply text-white/85 font-medium;
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Custom button styles with better visibility */

  .pop-button {
    @apply relative overflow-hidden text-white font-medium py-2 px-4 rounded-md transition-all duration-300;
    background: linear-gradient(to bottom, #FF4500, #FF3000);
    border: 2px solid rgba(255, 69, 0, 0.6);
    box-shadow: 0 4px 12px -2px rgba(255, 69, 0, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.1);
    transform: translateY(0);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .pop-button:hover {
    background: linear-gradient(to bottom, #FF5500, #FF4000);
    border-color: rgba(255, 69, 0, 0.8);
    box-shadow: 0 8px 20px -3px rgba(255, 69, 0, 0.5), 0 0 15px -1px rgba(255, 69, 0, 0.4), inset 0 1px 4px rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  }

  .pop-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 69, 0, 0.1), rgba(255, 69, 0, 0.3), rgba(255, 69, 0, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 0.375rem;
  }

  .pop-button::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 69, 0, 0.8), transparent);
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .pop-button:hover::before {
    opacity: 1;
  }

  .pop-button:hover::after {
    opacity: 1;
    top: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 1), transparent);
    box-shadow: 0 0 15px 1px rgba(255, 69, 0, 0.6);
  }

  .pop-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px -3px rgba(255, 69, 0, 0.4);
  }

  .fire-button {
    @apply relative overflow-hidden bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-500 hover:to-fiery-700 transition-all duration-300 text-white font-medium py-3 px-6 rounded-md;
  }

  .fire-button::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-r from-fiery-400/30 via-fiery-300/0 to-fiery-500/20 opacity-0 transition-opacity duration-300;
  }

  .fire-button:hover::before {
    @apply opacity-100;
  }

  .orange-gradient-button {
    @apply relative overflow-hidden text-white font-medium py-2 px-4 rounded-md transition-all duration-300;
    background: linear-gradient(to bottom, #FF6B33, #CC3700);
    border: 1px solid rgba(255, 69, 0, 0.4);
    box-shadow: 0 4px 10px -3px rgba(255, 69, 0, 0.5);
    transform: translateY(0);
  }

  .orange-gradient-button:hover {
    background: linear-gradient(to bottom, #FF7D33, #E74000);
    border-color: rgba(255, 69, 0, 0.8);
    box-shadow: 0 8px 20px -3px rgba(255, 69, 0, 0.6), 0 0 15px -3px rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  }

  .orange-gradient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 0.375rem;
  }

  .orange-gradient-button:hover::before {
    opacity: 1;
  }

  .orange-gradient-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px -3px rgba(255, 69, 0, 0.4);
  }

  .subtle-gradient-button {
    @apply relative overflow-hidden bg-gradient-to-r from-dark-900 via-dark-800 to-dark-900 border border-fiery/30 transition-all duration-300 text-white shadow-[0_4px_15px_-5px_rgba(255,69,0,0.25)] hover:shadow-[0_8px_30px_-5px_rgba(255,69,0,0.6)] hover:border-fiery/60 translate-y-0 hover:translate-y-[-2px];
  }

  .subtle-gradient-button::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-r from-fiery/10 via-fiery/40 to-fiery/10 opacity-0 transition-all duration-300;
  }

  .subtle-gradient-button:hover::before {
    @apply opacity-100;
  }

  .subtle-gradient-button::after {
    @apply content-[''] absolute -inset-[1px] bg-gradient-to-r from-fiery/10 via-fiery/70 to-fiery/10 opacity-0 blur-md transition-opacity duration-500;
  }

  .subtle-gradient-button:hover::after {
    @apply opacity-50;
  }

  .subtle-gradient-button:hover {
    @apply text-white shadow-[0_0_10px_rgba(255,255,255,0.1)];
    text-shadow: 0 0 8px rgba(255, 69, 0, 0.5);
  }

  .subtle-gradient-button:active {
    @apply translate-y-0 scale-[0.98] shadow-[0_0_20px_-2px_rgba(255,69,0,0.5)];
  }

  .glass-card {
    @apply bg-dark-800/50 backdrop-blur-md border border-white/10 rounded-lg p-4 transition-all duration-300 ease-in-out;
  }

  /* Modern Card Design - Global card styling for consistent UI across the application */
  .firenest-card {
    @apply bg-gradient-to-br from-[#1A1A28] to-[#252536] rounded-lg p-4 shadow-md transition-all duration-300 ease-in-out border border-white/5;
    position: relative;
    overflow: hidden;
  }

  .firenest-card:hover {
    @apply shadow-lg border-white/10;
    box-shadow: 0 12px 20px -5px rgba(0, 0, 0, 0.3), 0 8px 16px -8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  /* Card with fiery accent - for highlighted or important cards */
  .firenest-card-accent {
    @apply bg-gradient-to-br from-[#1A1A28] to-[#252536] rounded-lg p-4 shadow-md transition-all duration-300 ease-in-out border border-white/5 border-l-2 border-l-fiery;
    position: relative;
    overflow: hidden;
  }

  .firenest-card-accent:hover {
    @apply shadow-lg border-white/10;
    box-shadow: 0 12px 20px -5px rgba(255, 69, 0, 0.15), 0 8px 16px -8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  /* Nested card - for cards inside other cards */
  .firenest-nested-card {
    @apply bg-[#272742] rounded-lg p-3 shadow-sm transition-all duration-300 ease-in-out border border-white/10;
    position: relative;
    overflow: hidden;
  }

  .firenest-nested-card:hover {
    @apply shadow-md border-white/20;
    box-shadow: 0 6px 16px -4px rgba(0, 0, 0, 0.4);
  }

  /* Card icon container */
  .firenest-card-icon {
    @apply w-5 h-5 rounded-full flex items-center justify-center mr-2;
  }

  /* Card title styling */
  .firenest-card-title {
    @apply text-white/70 text-sm mb-1;
  }

  /* Card value styling */
  .firenest-card-value {
    @apply text-3xl font-bold text-white mt-2;
  }

  /* Card footer info styling */
  .firenest-card-info {
    @apply flex items-center mt-1 text-xs text-white/70;
  }

  /* Card badge styling */
  .firenest-card-badge {
    @apply text-xs px-2 py-0.5 rounded-full;
  }

  /* Card badge variants */
  .firenest-card-badge-red {
    @apply bg-red-500/20 text-red-400;
  }

  .firenest-card-badge-green {
    @apply bg-green-500/20 text-green-400;
  }

  .firenest-card-badge-blue {
    @apply bg-blue-500/20 text-blue-400;
  }

  .firenest-card-badge-purple {
    @apply bg-purple-500/20 text-purple-400;
  }

  /* Card icon variants */
  .firenest-card-icon-red {
    @apply bg-red-500;
  }

  .firenest-card-icon-green {
    @apply bg-green-500;
  }

  .firenest-card-icon-blue {
    @apply bg-blue-500;
  }

  .firenest-card-icon-purple {
    @apply bg-purple-500;
  }

  /* Input fields within cards */
  .firenest-card input,
  .firenest-card textarea,
  .firenest-card select {
    @apply bg-dark-700/70 border-white/10 text-white focus:border-fiery/50 focus:ring-fiery/20;
  }

  /* Card headers and footers */
  .firenest-card-header {
    @apply border-b border-white/10 pb-4 mb-4;
  }

  .firenest-card-footer {
    @apply border-t border-white/10 pt-4 mt-4;
  }

  /* Additional card variants */

  /* Interactive card - for clickable cards */
  .firenest-card-interactive {
    @apply firenest-card cursor-pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .firenest-card-interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 20px -5px rgba(0, 0, 0, 0.2), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }

  /* Interactive nested card - for clickable nested cards */
  .firenest-nested-card-interactive {
    @apply firenest-nested-card cursor-pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .firenest-nested-card-interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.3), 0 4px 8px -4px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* Success card - for positive messages or successful operations */
  .firenest-card-success {
    @apply bg-dark-800/50 border-l-4 border-green-500 border-t border-r border-b border-white/10 rounded-lg;
  }

  /* Warning card - for warnings or cautions */
  .firenest-card-warning {
    @apply bg-dark-800/50 border-l-4 border-yellow-500 border-t border-r border-b border-white/10 rounded-lg;
  }

  /* Error card - for errors or critical information */
  .firenest-card-error {
    @apply bg-dark-800/50 border-l-4 border-red-500 border-t border-r border-b border-white/10 rounded-lg;
  }

  /* Info card - for informational content */
  .firenest-card-info {
    @apply bg-dark-800/50 border-l-4 border-blue-500 border-t border-r border-b border-white/10 rounded-lg;
  }

  /* New animations and effects */
  @keyframes float-slow {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
  }

  @keyframes pulse-glow {
    0%, 100% {
      filter: drop-shadow(0 0 5px rgba(255, 69, 0, 0.5));
      transform: scale(1);
    }
    50% {
      filter: drop-shadow(0 0 15px rgba(255, 69, 0, 0.8));
      transform: scale(1.02);
    }
  }

  @keyframes shimmer {
    0% { background-position: -100% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes rotate-gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes geometric-float {
    0% { transform: rotate(0deg) translateY(0); }
    25% { transform: rotate(3deg) translateY(-10px); }
    75% { transform: rotate(-3deg) translateY(10px); }
    100% { transform: rotate(0deg) translateY(0); }
  }

  @keyframes geometric-pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
  }

  /* Geometric animated background */
  .geometric-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
  }

  .geometric-shape {
    position: absolute;
    opacity: 0.2;
    filter: blur(40px);
    transform-origin: center;
  }

  .geometric-shape-1 {
    width: 500px;
    height: 500px;
    border-radius: 43% 57% 70% 30% / 30% 40% 60% 70%;
    background: radial-gradient(circle at 30% 40%, #FF4500, transparent 70%);
    top: 10%;
    left: -10%;
    animation: geometric-float 20s ease-in-out infinite, geometric-pulse 15s ease-in-out infinite;
  }

  .geometric-shape-2 {
    width: 600px;
    height: 600px;
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    background: radial-gradient(circle at 70% 30%, #1E90FF, transparent 70%);
    top: 40%;
    right: -15%;
    animation: geometric-float 25s ease-in-out infinite reverse, geometric-pulse 20s ease-in-out infinite 5s;
  }

  .geometric-shape-3 {
    width: 300px;
    height: 300px;
    border-radius: 33% 67% 70% 30% / 30% 30% 70% 70%;
    background: radial-gradient(circle at 50% 50%, rgba(255, 165, 0, 0.8), transparent 70%);
    bottom: 10%;
    left: 20%;
    animation: geometric-float 15s ease-in-out infinite 2s, geometric-pulse 12s ease-in-out infinite 2s;
  }

  .geometric-shape-4 {
    width: 400px;
    height: 400px;
    border-radius: 53% 47% 30% 70% / 50% 50% 50% 50%;
    background: radial-gradient(circle at 50% 50%, rgba(142, 45, 226, 0.5), transparent 70%);
    top: 30%;
    left: 30%;
    animation: geometric-float 18s ease-in-out infinite 7s, geometric-pulse 13s ease-in-out infinite 3s;
  }

  .animate-float-slow {
    animation: float-slow 7s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 4s ease-in-out infinite;
  }

  .hover-scale {
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.03);
    box-shadow: 0 10px 30px -10px rgba(255, 69, 0, 0.3);
  }

  .shimmer-effect {
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.0) 100%);
    background-size: 200% 100%;
    animation: shimmer 3s infinite;
  }

  .rotating-gradient-border {
    position: relative;
  }

  .rotating-gradient-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #FF4500, #FF8F00, #1E90FF, #FF4500);
    background-size: 400% 400%;
    animation: rotate-gradient 10s ease infinite;
    border-radius: inherit;
    z-index: -1;
  }

  .glass-card-interactive {
    @apply firenest-card hover-scale;
    transition: all 0.3s ease;
  }

  .glass-card-interactive:hover {
    @apply bg-dark-800/60 border-fiery/30;
  }

  .interactive-badge {
    @apply px-3 py-1 rounded-full text-sm font-medium;
    background: linear-gradient(45deg, rgba(255, 69, 0, 0.2), rgba(30, 144, 255, 0.2));
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .interactive-badge:hover {
    background: linear-gradient(45deg, rgba(255, 69, 0, 0.3), rgba(30, 144, 255, 0.3));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px -5px rgba(255, 69, 0, 0.5);
  }

  .magic-text {
    position: relative;
    display: inline-block;
    background: linear-gradient(90deg, #FF4500, #FF8F00, #1E90FF);
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: rotate-gradient 5s linear infinite;
  }

  .parallax-layer {
    will-change: transform;
    transition: transform 0.1s cubic-bezier(0, 0, 0.2, 1);
  }

  .darker-bg {
    background: linear-gradient(to bottom, #141322 0%, #0F101A 100%);
  }

  .darker-section {
    background: linear-gradient(to bottom, #151926 0%, #0F101A 100%);
  }

  /* Enhanced toast styling - Dynamic Island style */
  .toast-glow {
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
  }

  .toast-glow::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.03));
    pointer-events: none;
    z-index: 10;
    opacity: 0.3;
    transition: opacity 0.3s ease;
  }

  .toast-glow:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
    transform: translateY(-1px);
  }

  .toast-glow:hover::after {
    opacity: 0.5;
  }

  /* Toast animations */
  @keyframes toast-slide-in-right {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes toast-slide-in-top {
    from {
      transform: translateY(-20px);
      opacity: 0;
      scale: 0.9;
    }
    to {
      transform: translateY(0);
      opacity: 1;
      scale: 1;
    }
  }

  @keyframes toast-border-pulse {
    0%, 100% {
      border-color: rgba(255, 69, 0, 0.2);
    }
    50% {
      border-color: rgba(255, 69, 0, 0.5);
    }
  }

  /* Success toast animation */
  @keyframes toast-success-pulse {
    0%, 100% {
      border-left-color: rgba(255, 69, 0, 0.7);
    }
    50% {
      border-left-color: rgba(255, 69, 0, 1);
    }
  }

  /* Error toast animation */
  @keyframes toast-error-pulse {
    0%, 100% {
      border-left-color: rgba(239, 68, 68, 0.7);
    }
    50% {
      border-left-color: rgba(239, 68, 68, 1);
    }
  }

  /* Warning toast animation */
  @keyframes toast-warning-pulse {
    0%, 100% {
      border-left-color: rgba(245, 158, 11, 0.7);
    }
    50% {
      border-left-color: rgba(245, 158, 11, 1);
    }
  }

  /* Info toast animation */
  @keyframes toast-info-pulse {
    0%, 100% {
      border-left-color: rgba(30, 144, 255, 0.7);
    }
    50% {
      border-left-color: rgba(30, 144, 255, 1);
    }
  }

  /* Apply animations to toasts */
  [data-sonner-toast] {
    animation: toast-slide-in-top 0.3s ease forwards !important;
    width: 500px !important;
    margin-bottom: 0.75rem !important;
    z-index: 100 !important;
    opacity: 1 !important;
    transform: none !important;
    transition: transform 0.3s ease, opacity 0.3s ease !important;
    pointer-events: auto !important;
  }

  /* Ensure notifications don't disappear when new ones appear */
  .firenest-notification {
    opacity: 1 !important;
    transform: none !important;
    transition: transform 0.3s ease, opacity 0.3s ease !important;
  }

  /* Fix for description text disappearing */
  [data-sonner-toast] [data-description],
  [data-sonner-toast] [data-title] {
    opacity: 1 !important;
    height: auto !important;
    transform: none !important;
    overflow: visible !important;
    max-height: none !important;
    margin: 0 !important;
  }

  /* Target Sonner's internal elements */
  [data-sonner-toast] > div,
  [data-sonner-toast] > div > div {
    opacity: 1 !important;
    height: auto !important;
    transform: none !important;
    overflow: visible !important;
    max-height: none !important;
  }

  /* Ensure toast content is always visible */
  [data-sonner-toast]:not(:hover) [data-description],
  [data-sonner-toast]:not(:hover) [data-title],
  [data-sonner-toast] [data-content] {
    opacity: 1 !important;
    height: auto !important;
    max-height: none !important;
    transform: none !important;
    overflow: visible !important;
    visibility: visible !important;
  }

  /* Override Sonner's default behavior */
  [data-sonner-toaster] [data-collapsed="true"] {
    --y-offset: 0 !important;
    --overlay-opacity: 0 !important;
  }

  /* Force all toast content to be visible */
  [data-sonner-toast] * {
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Custom scrollbar styling */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  /* Hide scrollbar but maintain scrolling functionality */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;             /* Chrome, Safari and Opera */
    width: 0;
    height: 0;
  }

  /* Responsive animations for mobile */
  @media (max-width: 768px) {
    [data-sonner-toast] {
      animation: toast-slide-in-top 0.3s ease forwards !important;
      width: 90% !important;
      max-width: 500px !important;
    }

    /* Ensure toast is centered on mobile */
    [data-sonner-toaster][data-position="top-center"] {
      width: 100% !important;
      left: 0 !important;
      right: 0 !important;
      transform: none !important;
    }
  }

  /* Type-specific animations */
  .toast-success {
    animation: toast-success-pulse 3s ease infinite !important;
  }

  .toast-error {
    animation: toast-error-pulse 3s ease infinite !important;
  }

  .toast-warning {
    animation: toast-warning-pulse 3s ease infinite !important;
  }

  .toast-info {
    animation: toast-info-pulse 3s ease infinite !important;
  }

  /* Toast container positioning - centered below header */
  [data-sonner-toaster][data-position="top-center"] {
    position: fixed !important;
    left: 0 !important;
    right: 0 !important;
    top: 64px !important; /* Position exactly below the header (h-16 = 64px) */
    max-height: calc(100vh - 64px) !important;
    overflow-y: visible !important;
    padding: 0 !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-start !important;
    pointer-events: none !important;
    margin: 0 auto !important;
    z-index: 9999 !important;
  }

  /* Ensure each toast is centered */
  [data-sonner-toaster][data-position="top-center"] > div {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
  }

  /* Ensure toasts don't overlap with header or other content */
  .firenest-toaster {
    z-index: 9999 !important;
    pointer-events: auto !important;
  }

  /* Dynamic Island style for toasts - centered below header with Firenest theme */
  .firenest-notification {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important; /* Center the content */
    min-height: 45px !important;
    height: 45px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 15px rgba(255, 69, 0, 0.3) !important; /* Add fiery glow */
    margin: 8px auto !important; /* Add some margin from the header */
    width: 375px !important;
  }

  /* Adjust toast content for Dynamic Island style - centered with Firenest theme */
  [data-sonner-toast] [data-content] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  /* Make toast icons smaller and centered */
  [data-sonner-toast] svg {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    flex-shrink: 0 !important;
  }

  /* Adjust text size and spacing for Dynamic Island style */
  [data-sonner-toast] [data-title] {
    font-size: 14px !important;
    font-weight: 500 !important;
    line-height: 1.2 !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  [data-sonner-toast] [data-description] {
    font-size: 12px !important;
    line-height: 1.2 !important;
    margin-top: 2px !important;
    opacity: 0.9 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  /* Additional animations for dashboard preview */
  @keyframes shine {
    0% {
      transform: translateX(-100%) translateY(-100%);
    }
    50%, 100% {
      transform: translateX(100%) translateY(100%);
    }
  }

  .animate-shine {
    animation: shine 3s infinite linear;
  }

  @keyframes pulse-slow {
    0%, 100% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
  }

  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
}
