import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { notify } from '@/components/ui/notification-system';
import { PartnerAccount, PartnerTool } from '@/lib/partner-portal/types';
import { getPartnerAccount, getPartnerTools } from '@/lib/partner-portal/api';
import { usePageVisibility } from '@/hooks/usePageVisibility';

interface PartnerContextType {
  partner: PartnerAccount | null;
  tools: PartnerTool[];
  isLoading: boolean;
  refreshPartnerData: () => Promise<void>;
  refreshToolsData: () => Promise<void>;
}

const PartnerContext = createContext<PartnerContextType>({
  partner: null,
  tools: [],
  isLoading: true,
  refreshPartnerData: async () => {},
  refreshToolsData: async () => {}
});

export const usePartner = () => useContext(PartnerContext);

export const PartnerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [partner, setPartner] = useState<PartnerAccount | null>(null);
  const [tools, setTools] = useState<PartnerTool[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [sessionCheckCount, setSessionCheckCount] = useState(0);

  // Track page visibility to prevent unnecessary data fetches when switching tabs
  const isVisible = usePageVisibility();
  const lastVisibleTime = useRef<number>(Date.now());
  const sessionCheckedOnce = useRef<boolean>(false);
  const partnerDataRef = useRef<{partnerId: string, toolsData: PartnerTool[]} | null>(null);

  // Function to check if we have cached partner data
  const getCachedPartnerData = () => {
    try {
      // Try localStorage first for persistence across tabs
      const cachedData = localStorage.getItem('firenest_partner_context_data') ||
                         sessionStorage.getItem('firenest_partner_context_data');

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        const cacheTime = parsedData.timestamp || 0;
        // Use cached data if it's less than 60 minutes old
        if (Date.now() - cacheTime < 60 * 60 * 1000) {
          console.log('Using cached partner context data from storage');
          return parsedData;
        }
      }
    } catch (error) {
      console.error('Error reading cached partner context data:', error);
    }
    return null;
  };

  // Function to save partner data to both session and local storage for persistence
  const savePartnerContextData = (partnerData: PartnerAccount, toolsData: PartnerTool[]) => {
    try {
      const dataToCache = {
        partner: partnerData,
        tools: toolsData,
        timestamp: Date.now()
      };

      // Save to both storage types for maximum persistence
      const dataString = JSON.stringify(dataToCache);
      localStorage.setItem('firenest_partner_context_data', dataString);
      sessionStorage.setItem('firenest_partner_context_data', dataString);

      partnerDataRef.current = { partnerId: partnerData.id, toolsData };
      console.log('Partner data cached successfully');
    } catch (error) {
      console.error('Error saving partner context data to cache:', error);
    }
  };

  // Update last visible time when page becomes visible
  useEffect(() => {
    if (isVisible) {
      lastVisibleTime.current = Date.now();
    }
  }, [isVisible]);

  useEffect(() => {
    // Always try to load cached data first, even if page is not visible
    const loadCachedData = () => {
      const cachedData = getCachedPartnerData();
      if (cachedData && cachedData.partner && cachedData.tools) {
        console.log('Using cached partner context data:', cachedData.partner.name);
        setPartner(cachedData.partner);
        setTools(cachedData.tools);
        setIsLoading(false);
        return true;
      }
      return false;
    };

    // Try to load cached data immediately
    const hasCachedData = loadCachedData();

    // Skip session check if page is not visible and we've already checked once or have cached data
    if (!isVisible && (sessionCheckedOnce.current || hasCachedData)) {
      return;
    }

    const checkSession = async () => {
      setIsLoading(true);
      try {
        console.log(`Checking partner session (attempt ${sessionCheckCount + 1})...`);

        // Skip fetching if we already loaded cached data
        if (hasCachedData) {
          sessionCheckedOnce.current = true;
          return;
        }

        // Add a timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout fetching partner data')), 8000)
        );

        const fetchDataPromise = async () => {
          try {
            // Get the session directly without a race condition
            const { data: { session } } = await supabase.auth.getSession();

            if (session?.user) {
              console.log('Session found, fetching partner data for user:', session.user.id);

              try {
                // Get partner data directly without a race condition
                const partnerData = await getPartnerAccount(session.user.id);
                setPartner(partnerData);

                if (partnerData) {
                  console.log('Partner data found, fetching tools for partner:', partnerData.id);

                  try {
                    // Get tools directly without a race condition
                    const toolsData = await getPartnerTools(partnerData.id);
                    setTools(toolsData);

                    // Cache the partner context data for future use
                    savePartnerContextData(partnerData, toolsData);
                  } catch (toolsError) {
                    console.error('Error fetching tools:', toolsError);
                    setTools([]);
                  }
                } else {
                  console.log('No partner data found for user:', session.user.id);
                  setTools([]);
                }
              } catch (partnerError) {
                console.error('Error fetching partner data:', partnerError);
                setPartner(null);
                setTools([]);
              }
            } else {
              console.log('No session found, clearing partner data');
              setPartner(null);
              setTools([]);
            }
          } catch (sessionError) {
            console.error('Error getting session:', sessionError);
            setPartner(null);
            setTools([]);
          }
        };

        // Race the data fetch against the timeout
        await Promise.race([fetchDataPromise(), timeoutPromise]);
      } catch (error) {
        console.error('Error checking partner session:', error);
        if (error.message === 'Timeout fetching partner data') {
          notify.error('Connection timed out. Please check your internet connection and try again.');
        } else {
          notify.error('Failed to load partner data. Please refresh the page.');
        }
        // Even on error, we should clear the loading state
        setPartner(null);
        setTools([]);
      } finally {
        setIsLoading(false);
        sessionCheckedOnce.current = true;
      }
    };

    checkSession();

    // Set up a timer to re-check the session if loading takes too long
    const sessionCheckTimer = setTimeout(() => {
      if (isLoading) {
        console.log('Partner session check is taking too long, forcing a re-check...');
        setSessionCheckCount(prev => prev + 1);
      }
    }, 10000); // 10 seconds timeout

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change detected:', event);

        // Critical events that always need processing
        const criticalEvents = ['SIGNED_IN', 'SIGNED_OUT', 'USER_DELETED', 'TOKEN_REFRESHED'];
        const isCriticalEvent = criticalEvents.includes(event);

        // Only process if the page is visible or this is a critical event
        if (!isVisible && !isCriticalEvent) {
          console.log('Page not visible, skipping non-critical auth state change');
          return;
        }

        // For non-critical events, try to use cached data first
        if (!isCriticalEvent) {
          const cachedData = getCachedPartnerData();
          if (cachedData && cachedData.partner && cachedData.tools) {
            console.log('Using cached data for non-critical auth event:', event);
            setPartner(cachedData.partner);
            setTools(cachedData.tools);
            return;
          }
        }

        console.log('Processing auth state change:', event);
        setIsLoading(true);

        try {
          // For sign-in events, always try to get fresh data
          if (event === 'SIGNED_IN') {
            if (session?.user) {
              console.log('User signed in, fetching fresh partner data');
              // Continue with fresh data fetch
            } else {
              console.log('Sign in event but no user in session');
              setIsLoading(false);
              return;
            }
          } else {
            // For other events, try cached data first
            const cachedData = getCachedPartnerData();
            if (session?.user && cachedData && cachedData.partner && cachedData.tools) {
              console.log('Using cached partner context data during auth state change:', cachedData.partner.name);
              setPartner(cachedData.partner);
              setTools(cachedData.tools);
              setIsLoading(false);
              return;
            }
          }

          if (session?.user) {
            console.log('Auth state changed, fetching partner data for user:', session.user.id);

            try {
              // Use a direct approach without race to avoid the race condition
              const partnerData = await getPartnerAccount(session.user.id);
              setPartner(partnerData);

              if (partnerData) {
                console.log('Partner data found, fetching tools for partner:', partnerData.id);

                try {
                  // Fetch tools directly
                  const toolsData = await getPartnerTools(partnerData.id);
                  setTools(toolsData);

                  // Cache the partner context data for future use
                  savePartnerContextData(partnerData, toolsData);
                } catch (toolsError) {
                  console.error('Error fetching tools during auth state change:', toolsError);
                  setTools([]);
                }
              } else {
                console.log('No partner data found for user:', session.user.id);
                setTools([]);
              }
            } catch (partnerError) {
              console.error('Error fetching partner data during auth state change:', partnerError);

              // Check if we have cached data as a fallback
              if (cachedData && cachedData.partner) {
                console.log('Using cached partner data as fallback after error:', cachedData.partner.name);
                setPartner(cachedData.partner);
                setTools(cachedData.tools || []);
              } else {
                setPartner(null);
                setTools([]);
              }
            }
          } else {
            console.log('No session found during auth state change, clearing partner data');
            setPartner(null);
            setTools([]);
          }
        } catch (error) {
          console.error('Error during auth state change:', error);

          // Try to use cached data as a fallback
          const cachedData = getCachedPartnerData();
          if (cachedData && cachedData.partner) {
            console.log('Using cached partner data as fallback after error:', cachedData.partner.name);
            setPartner(cachedData.partner);
            setTools(cachedData.tools || []);
          } else {
            setPartner(null);
            setTools([]);
            notify.error('Failed to load partner data. Please refresh the page.');
          }
        } finally {
          setIsLoading(false);
        }
      }
    );

    return () => {
      clearTimeout(sessionCheckTimer);
      subscription.unsubscribe();
    };
  }, [isVisible, sessionCheckCount, isLoading]);

  const refreshPartnerData = async () => {
    if (!partner) return;

    setIsLoading(true);
    try {
      // Add a timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout refreshing partner data')), 8000)
      );

      const fetchDataPromise = async () => {
        console.log('Refreshing partner data for partner:', partner.id);

        // Fetch partner data with timeout
        const partnerPromise = getPartnerAccount(partner.id);
        const partnerTimeoutPromise = new Promise<null>((resolve) => {
          setTimeout(() => {
            console.warn('Partner refresh timed out');
            resolve(null);
          }, 5000); // 5 second timeout
        });

        const partnerData = await Promise.race([partnerPromise, partnerTimeoutPromise]);

        if (partnerData) {
          setPartner(partnerData);

          // If we have tools data, update the cache
          if (tools.length > 0) {
            savePartnerContextData(partnerData, tools);
          }
        }
      };

      // Race the data fetch against the timeout
      await Promise.race([fetchDataPromise(), timeoutPromise]);
    } catch (error) {
      console.error('Error refreshing partner data:', error);
      if (error.message === 'Timeout refreshing partner data') {
        notify.error('Connection timed out. Please check your internet connection and try again.');
      } else {
        notify.error('Failed to refresh partner data. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToolsData = async () => {
    if (!partner) return;

    setIsLoading(true);
    try {
      // Add a timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout refreshing tools data')), 8000)
      );

      const fetchDataPromise = async () => {
        console.log('Refreshing tools data for partner:', partner.id);

        // Fetch tools with timeout
        const toolsPromise = getPartnerTools(partner.id);
        const toolsTimeoutPromise = new Promise<PartnerTool[]>((resolve) => {
          setTimeout(() => {
            console.warn('Tools refresh timed out');
            resolve([]);
          }, 5000); // 5 second timeout
        });

        const toolsData = await Promise.race([toolsPromise, toolsTimeoutPromise]);

        if (toolsData.length > 0) {
          setTools(toolsData);

          // Update the cache with the new tools data
          savePartnerContextData(partner, toolsData);
        }
      };

      // Race the data fetch against the timeout
      await Promise.race([fetchDataPromise(), timeoutPromise]);
    } catch (error) {
      console.error('Error refreshing tools data:', error);
      if (error.message === 'Timeout refreshing tools data') {
        notify.error('Connection timed out. Please check your internet connection and try again.');
      } else {
        notify.error('Failed to refresh tools data. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PartnerContext.Provider
      value={{
        partner,
        tools,
        isLoading,
        refreshPartnerData,
        refreshToolsData
      }}
    >
      {children}
    </PartnerContext.Provider>
  );
};
