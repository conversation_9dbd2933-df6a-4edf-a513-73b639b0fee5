import React, { useState } from 'react';
import { <PERSON>, Bell, CreditCard, Check, AlertTriangle, Info, Trash2, RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { notify } from '@/components/ui/notification-system';

// Mock notification data
const initialNotifications = [
  {
    id: '1',
    title: 'Credits Added',
    message: '100 credits have been added to your account.',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    read: false,
    type: 'success',
  },
  {
    id: '2',
    title: 'New Feature Available',
    message: 'Check out our new AI tool integration.',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    read: true,
    type: 'info',
  },
  {
    id: '3',
    title: 'Payment Successful',
    message: 'Your credit purchase was successful.',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    read: true,
    type: 'success',
  },
  {
    id: '4',
    title: 'Low Credits Warning',
    message: 'You have less than 10 credits remaining.',
    timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
    read: false,
    type: 'warning',
  },
  {
    id: '5',
    title: 'Account Security',
    message: 'We recommend enabling two-factor authentication for better security.',
    timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    read: false,
    type: 'info',
  }
];

const Notifications = () => {
  const [notifications, setNotifications] = useState(initialNotifications);
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'read'
  const [isLoading, setIsLoading] = useState(false);

  const unreadCount = notifications.filter(n => !n.read).length;

  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <Flame className="h-5 w-5 text-fiery" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-cool-500" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Bell className="h-5 w-5 text-fiery" />;
    }
  };

  const getIconBackground = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-fiery/20';
      case 'warning':
        return 'bg-amber-500/20';
      case 'info':
        return 'bg-cool-500/20';
      case 'error':
        return 'bg-red-500/20';
      default:
        return 'bg-fiery/20';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMins > 0) {
      return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const markAllAsRead = () => {
    setIsLoading(true);
    setTimeout(() => {
      setNotifications(notifications.map(n => ({ ...n, read: true })));
      setIsLoading(false);
      notify.success('All notifications marked as read', {
        title: 'Notifications Updated',
        duration: 3000
      });
    }, 500);
  };

  const markAsRead = (id: string) => {
    setNotifications(notifications.map(n =>
      n.id === id ? { ...n, read: true } : n
    ));
  };

  const clearAllNotifications = () => {
    setIsLoading(true);
    setTimeout(() => {
      setNotifications([]);
      setIsLoading(false);
      notify.info('All notifications cleared', {
        title: 'Notifications',
        duration: 3000
      });
    }, 500);
  };

  const refreshNotifications = () => {
    setIsLoading(true);
    setTimeout(() => {
      // In a real app, this would fetch from an API
      setNotifications(initialNotifications);
      setIsLoading(false);
      notify.success('Notifications refreshed', {
        title: 'Updated',
        duration: 3000
      });
    }, 800);
  };

  const filteredNotifications = notifications.filter(n => {
    if (filter === 'unread') return !n.read;
    if (filter === 'read') return n.read;
    return true;
  });

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">Notifications</h1>
            <p className="text-white/70 mt-1">
              {unreadCount > 0
                ? `You have ${unreadCount} unread notification${unreadCount > 1 ? 's' : ''}`
                : 'No unread notifications'}
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={markAllAsRead}
              disabled={unreadCount === 0 || isLoading}
              className="border-white/10 hover:bg-white/5"
            >
              <Check className="mr-1 h-4 w-4" />
              Mark all as read
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={refreshNotifications}
              disabled={isLoading}
              className="border-white/10 hover:bg-white/5"
            >
              <RefreshCw className={`mr-1 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={clearAllNotifications}
              disabled={notifications.length === 0 || isLoading}
              className="border-white/10 hover:bg-white/5 text-red-400 hover:text-red-300 hover:border-red-500/30"
            >
              <Trash2 className="mr-1 h-4 w-4" />
              Clear all
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex space-x-2 border-b border-white/10 pb-2">
          <button
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              filter === 'all'
                ? 'bg-white/10 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              filter === 'unread'
                ? 'bg-white/10 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
            onClick={() => setFilter('unread')}
          >
            Unread
          </button>
          <button
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              filter === 'read'
                ? 'bg-white/10 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
            onClick={() => setFilter('read')}
          >
            Read
          </button>
        </div>

        {/* Notifications list */}
        <div className="space-y-3">
          {filteredNotifications.length === 0 ? (
            <div className="firenest-card p-8 text-center">
              <Bell className="h-12 w-12 mx-auto text-white/30 mb-4" />
              <h3 className="text-lg font-medium text-white/90">No notifications</h3>
              <p className="text-white/60 mt-1">
                {filter === 'all'
                  ? "You don't have any notifications yet"
                  : filter === 'unread'
                    ? "You don't have any unread notifications"
                    : "You don't have any read notifications"}
              </p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`firenest-card p-4 ${
                  notification.read
                    ? 'opacity-80'
                    : 'shadow-md'
                }`}
                onClick={() => !notification.read && markAsRead(notification.id)}
              >
                <div className="flex gap-4">
                  <div className={`h-10 w-10 rounded-full ${getIconBackground(notification.type)} flex items-center justify-center flex-shrink-0`}>
                    {getIcon(notification.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <h3 className={`font-medium ${notification.read ? 'text-white/80' : 'text-white'}`}>
                        {notification.title}
                        {!notification.read && (
                          <span className="ml-2 inline-block h-2 w-2 rounded-full bg-fiery"></span>
                        )}
                      </h3>
                      <span className="text-xs text-white/50">{formatTimestamp(notification.timestamp)}</span>
                    </div>
                    <p className={`mt-1 text-sm ${notification.read ? 'text-white/60' : 'text-white/80'}`}>
                      {notification.message}
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default Notifications;
