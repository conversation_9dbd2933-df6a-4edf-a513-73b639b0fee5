import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { toast as sonnerToast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { PartnerStatus, ToolStatus } from '@/lib/partner-portal/types';
import { PlusCircle, Settings, Key, Code, ExternalLink, AlertTriangle, CheckCircle, Clock, Wrench, Zap, BookOpen as BookOpenIcon, FileText, HelpCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { AddToolModal } from '@/components/partner/AddToolModal';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { partner, tools, isLoading, refreshToolsData } = usePartner();
  const [isAddToolModalOpen, setIsAddToolModalOpen] = useState(false);

  const handleLogout = async () => {
    try {
      const loadingId = notify.loading('Logging out...', { duration: 2000 });
      await supabase.auth.signOut();

      // Dismiss the loading notification
      if (loadingId) {
        sonnerToast.dismiss(loadingId);
      }

      notify.success('Logged out successfully', {
        title: 'Goodbye',
        duration: 4000
      });
      navigate('/partner');
    } catch (error) {
      console.error('Error logging out:', error);
      notify.error('Failed to log out', {
        title: 'Logout Error',
        duration: 5000
      });
    }
  };

  const handleCreateTool = () => {
    setIsAddToolModalOpen(true);
  };

  const getStatusBadge = (status: PartnerStatus | ToolStatus) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500/20 text-green-500 border border-green-500/30">Active</Badge>;
      case 'pending':
      case 'pending_review':
        return <Badge className="bg-yellow-500/20 text-yellow-500 border border-yellow-500/30">Pending</Badge>;
      case 'suspended':
        return <Badge className="bg-red-500/20 text-red-500 border border-red-500/30">Suspended</Badge>;
      case 'draft':
        return <Badge className="bg-blue-500/20 text-blue-500 border border-blue-500/30">Draft</Badge>;
      default:
        return <Badge className="bg-white/10 text-white/70 border border-white/20">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: PartnerStatus | ToolStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'pending':
      case 'pending_review':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'suspended':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'draft':
        return <Wrench className="w-5 h-5 text-blue-500" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-950 flex items-center justify-center">
        <Loading size="lg" />
      </div>
    );
  }

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Add Tool Modal */}
      <AddToolModal
        isOpen={isAddToolModalOpen}
        onClose={() => setIsAddToolModalOpen(false)}
      />

      {/* Header */}
      <header className="bg-gradient-to-r from-dark-900 to-dark-950 border-b border-white/10 py-4 relative">
        {/* Top gradient overlay */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-fiery to-cool"></div>

        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="mr-3">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-fiery to-cool bg-clip-text text-transparent">Firenest</h1>
                <p className="text-white/60 text-sm">Partner Portal</p>
              </div>
              <div className="h-8 w-px bg-white/10 mx-4 hidden md:block"></div>
              <div className="hidden md:block">
                <p className="text-white/60 text-sm">Welcome,</p>
                <p className="text-white font-medium">{partner.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="text-white/70 hover:text-white hover:bg-white/5"
                onClick={() => navigate('/partner/settings')}
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-white/10 hover:bg-white/5"
                onClick={handleLogout}
              >
                Log Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-1">
        {/* Account Status Card */}
        <Card className="firenest-card mb-8 border-0 shadow-lg overflow-hidden relative">
          {/* Decorative gradient */}
          <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-fiery/10 to-cool/5 rounded-full filter blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-gradient-to-tr from-cool/10 to-transparent rounded-full filter blur-xl"></div>

          <CardContent className="p-8 relative">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Account Status</h2>
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-10 h-10 rounded-full bg-dark-800 flex items-center justify-center">
                    {getStatusIcon(partner.status)}
                  </div>
                  <div>
                    <span className="text-white/90 font-medium">Your account is {getStatusBadge(partner.status)}</span>
                    {partner.status === 'pending' && (
                      <p className="text-yellow-500 mt-1 text-sm">
                        Your account is pending approval. You can still set up your tools, but they won't be visible to users until approved.
                      </p>
                    )}
                    {partner.status === 'active' && (
                      <p className="text-green-500 mt-1 text-sm">
                        Your account is active. Your tools are visible to Firenest users.
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                <Button
                  variant="outline"
                  className="flex items-center space-x-2 border-white/10 hover:bg-white/5"
                  onClick={() => navigate('/partner/settings')}
                >
                  <Settings className="w-4 h-4" />
                  <span>Account Settings</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tools Section */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center">
                <h2 className="text-2xl font-bold text-white">Your AI Tools</h2>
                <div className="ml-3 px-2 py-1 bg-dark-800 rounded-full text-xs text-white/60">
                  {tools.length} {tools.length === 1 ? 'tool' : 'tools'}
                </div>
              </div>
              <p className="text-white/60 mt-1">Manage your AI tools and integrations</p>
            </div>
            <Button
              className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white shadow-md hover:shadow-lg transition-all duration-200 flex items-center"
              onClick={handleCreateTool}
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              <span>Add New Tool</span>
            </Button>
          </div>

          {tools.length === 0 ? (
            <Card className="firenest-card border-0 shadow-lg overflow-hidden">
              <CardContent className="p-12 text-center relative">
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-fiery/5 rounded-full filter blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-64 h-64 bg-cool/5 rounded-full filter blur-3xl"></div>

                <div className="relative">
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-fiery/20 to-cool/20 flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Zap className="w-10 h-10 text-white/80" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3">No Tools Yet</h3>
                  <p className="text-white/70 mb-8 max-w-md mx-auto">
                    You haven't added any AI tools to your Firenest partner account yet. Get started by adding your first tool.
                  </p>
                  <div className="flex flex-col items-center">
                    <Button
                      className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white shadow-md hover:shadow-lg transition-all duration-200 flex items-center px-6 py-5 h-auto"
                      onClick={handleCreateTool}
                    >
                      <PlusCircle className="w-5 h-5 mr-2" />
                      <span className="text-base">Add Your First Tool</span>
                    </Button>
                    <p className="text-white/50 text-sm mt-4">
                      Need help? Check our <Button variant="link" className="text-fiery p-0 h-auto text-sm">integration guide</Button>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tools.map((tool) => (
                <Card key={tool.id} className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-fiery/50 to-cool/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-xl text-white group-hover:text-fiery transition-colors duration-300">{tool.name}</CardTitle>
                      {getStatusBadge(tool.status)}
                    </div>
                    <CardDescription className="line-clamp-2 h-10">
                      {tool.description || 'No description provided'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-dark-800/30 p-3 rounded-lg">
                        <p className="text-xs text-white/50 mb-1">Category</p>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-fiery/30 mr-2"></div>
                          <p className="text-white/90 text-sm font-medium">{tool.category || 'Uncategorized'}</p>
                        </div>
                      </div>
                      <div className="bg-dark-800/30 p-3 rounded-lg">
                        <p className="text-xs text-white/50 mb-1">Created</p>
                        <p className="text-white/90 text-sm font-medium">{new Date(tool.createdAt).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-2 border-t border-white/5">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white/70 hover:text-white hover:bg-white/5 flex items-center"
                      onClick={() => window.open(tool.websiteUrl, '_blank')}
                      disabled={!tool.websiteUrl}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      <span>Visit</span>
                    </Button>
                    <Button
                      className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                      size="sm"
                      onClick={() => navigate(`/partner/tools/${tool.id}`)}
                    >
                      Manage
                    </Button>
                  </CardFooter>
                </Card>
              ))}

              {/* Add New Tool Card */}
              <Card
                className="firenest-card border-2 border-dashed border-white/10 bg-dark-900/30 hover:bg-dark-800/30 hover:border-fiery/20 transition-all duration-300 cursor-pointer hover:shadow-lg hover:translate-y-[-4px] group"
                onClick={handleCreateTool}
              >
                <CardContent className="p-8 flex flex-col items-center justify-center h-full">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-fiery/10 to-cool/10 group-hover:from-fiery/20 group-hover:to-cool/20 flex items-center justify-center mb-4 transition-all duration-300">
                    <PlusCircle className="w-8 h-8 text-white/60 group-hover:text-white/90 transition-all duration-300" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-fiery transition-colors duration-300">Add New Tool</h3>
                  <p className="text-white/60 text-center group-hover:text-white/80 transition-colors duration-300">
                    Register a new AI tool to integrate with Firenest
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Quick Links */}
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <h2 className="text-2xl font-bold text-white">Quick Links</h2>
            <div className="h-px bg-gradient-to-r from-fiery/50 to-transparent flex-grow ml-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-fiery/5 rounded-full filter blur-xl"></div>
              <CardContent className="p-8">
                <div className="flex flex-col">
                  <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-fiery/20 to-fiery/5 flex items-center justify-center mb-5 group-hover:from-fiery/30 group-hover:to-fiery/10 transition-all duration-300">
                    <Key className="w-7 h-7 text-fiery" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-fiery transition-colors duration-300">Authentication</h3>
                  <p className="text-white/60 mb-6 group-hover:text-white/80 transition-colors duration-300">
                    Set up OAuth 2.0, OIDC, or API key authentication for your tools
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-white/10 hover:bg-white/5 group-hover:border-fiery/20 transition-colors duration-300"
                    onClick={() => navigate('/partner/authentication')}
                  >
                    Configure
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-green-500/5 rounded-full filter blur-xl"></div>
              <CardContent className="p-8">
                <div className="flex flex-col">
                  <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-green-500/20 to-green-500/5 flex items-center justify-center mb-5 group-hover:from-green-500/30 group-hover:to-green-500/10 transition-all duration-300">
                    <Zap className="w-7 h-7 text-green-500" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-green-500 transition-colors duration-300">Metered Features</h3>
                  <p className="text-white/60 mb-6 group-hover:text-white/80 transition-colors duration-300">
                    Define premium features that will consume Firenest credits
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-white/10 hover:bg-white/5 group-hover:border-green-500/20 transition-colors duration-300"
                    onClick={() => navigate('/partner/features')}
                  >
                    Manage Features
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-blue-500/5 rounded-full filter blur-xl"></div>
              <CardContent className="p-8">
                <div className="flex flex-col">
                  <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center mb-5 group-hover:from-blue-500/30 group-hover:to-blue-500/10 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                      <path d="M10.1 2.182a10 10 0 0 1 3.8 0"></path>
                      <path d="M13.9 21.818a10 10 0 0 1-3.8 0"></path>
                      <path d="M17.609 3.721a10 10 0 0 1 2.69 2.7"></path>
                      <path d="M3.701 17.579a10 10 0 0 1-2.7-2.69"></path>
                      <path d="M20.29 6.422a10 10 0 0 1 1.49 3.6"></path>
                      <path d="M2.22 13.978a10 10 0 0 1-1.49-3.6"></path>
                      <path d="M21.779 10.022a10 10 0 0 1-1.49 3.6"></path>
                      <path d="M3.7 6.422a10 10 0 0 1 2.7-2.7"></path>
                      <path d="M17.609 20.279a10 10 0 0 1-2.7 2.7"></path>
                      <path d="M6.4 20.279a10 10 0 0 1-2.7-2.7"></path>
                      <path d="M2.22 10.022a10 10 0 0 1 1.49-3.6"></path>
                      <path d="M14 12l-4-4v8l4-4z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-500 transition-colors duration-300">Webhooks</h3>
                  <p className="text-white/60 mb-6 group-hover:text-white/80 transition-colors duration-300">
                    Configure webhooks to receive real-time notifications about events
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-white/10 hover:bg-white/5 group-hover:border-blue-500/20 transition-colors duration-300"
                    onClick={() => navigate('/partner/webhooks')}
                  >
                    Configure
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-cool/5 rounded-full filter blur-xl"></div>
              <CardContent className="p-8">
                <div className="flex flex-col">
                  <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-cool/20 to-cool/5 flex items-center justify-center mb-5 group-hover:from-cool/30 group-hover:to-cool/10 transition-all duration-300">
                    <Code className="w-7 h-7 text-cool" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cool transition-colors duration-300">Integration Code</h3>
                  <p className="text-white/60 mb-6 group-hover:text-white/80 transition-colors duration-300">
                    Get code snippets to integrate Firenest with your platform
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-white/10 hover:bg-white/5 group-hover:border-cool/20 transition-colors duration-300"
                    onClick={() => navigate('/partner/integration')}
                  >
                    View Code
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-purple-500/5 rounded-full filter blur-xl"></div>
              <CardContent className="p-8">
                <div className="flex flex-col">
                  <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-500/5 flex items-center justify-center mb-5 group-hover:from-purple-500/30 group-hover:to-purple-500/10 transition-all duration-300">
                    <ExternalLink className="w-7 h-7 text-purple-500" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-purple-500 transition-colors duration-300">Connection Manager</h3>
                  <p className="text-white/60 mb-6 group-hover:text-white/80 transition-colors duration-300">
                    Manage user connections to your AI tools
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-white/10 hover:bg-white/5 group-hover:border-purple-500/20 transition-colors duration-300"
                    onClick={() => navigate('/dashboard/connections')}
                  >
                    Manage
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="firenest-card border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-4px] group overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-amber-500/5 rounded-full filter blur-xl"></div>
              <CardContent className="p-8">
                <div className="flex flex-col">
                  <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-amber-500/20 to-amber-500/5 flex items-center justify-center mb-5 group-hover:from-amber-500/30 group-hover:to-amber-500/10 transition-all duration-300">
                    <Settings className="w-7 h-7 text-amber-500" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-amber-500 transition-colors duration-300">Account Settings</h3>
                  <p className="text-white/60 mb-6 group-hover:text-white/80 transition-colors duration-300">
                    Manage your partner account settings and preferences
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-white/10 hover:bg-white/5 group-hover:border-amber-500/20 transition-colors duration-300"
                    onClick={() => navigate('/partner/settings')}
                  >
                    Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Documentation */}
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <h2 className="text-2xl font-bold text-white">Documentation</h2>
            <div className="h-px bg-gradient-to-r from-fiery/50 to-transparent flex-grow ml-4"></div>
          </div>

          <Card className="firenest-card border-0 shadow-lg overflow-hidden relative">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-fiery/5 to-cool/5 rounded-full filter blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-cool/5 to-transparent rounded-full filter blur-2xl"></div>

            <CardContent className="p-8 relative">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-fiery/20 to-cool/20 flex items-center justify-center mr-3">
                        <BookOpenIcon className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Getting Started</h3>
                    </div>
                    <p className="text-white/70 mb-4">
                      Learn how to integrate your AI tools with Firenest and reach thousands of users.
                    </p>
                    <Button
                      variant="outline"
                      className="w-full border-white/10 hover:bg-white/5 flex items-center justify-center"
                      onClick={() => window.open('https://docs.firenest.com/partners/getting-started', '_blank')}
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      <span>View Guide</span>
                    </Button>
                  </div>

                  <div className="pt-4">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-500/10 flex items-center justify-center mr-3">
                        <Code className="w-5 h-5 text-purple-400" />
                      </div>
                      <h3 className="text-xl font-bold text-white">API Reference</h3>
                    </div>
                    <p className="text-white/70 mb-4">
                      Explore our API documentation for advanced integration options.
                    </p>
                    <Button
                      variant="outline"
                      className="w-full border-white/10 hover:bg-white/5 flex items-center justify-center"
                      onClick={() => window.open('https://docs.firenest.com/partners/api', '_blank')}
                    >
                      <Code className="w-4 h-4 mr-2" />
                      <span>View API Docs</span>
                    </Button>
                  </div>
                </div>

                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500/20 to-green-500/10 flex items-center justify-center mr-3">
                        <FileText className="w-5 h-5 text-green-400" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Integration Guide</h3>
                    </div>
                    <p className="text-white/70 mb-4">
                      Step-by-step instructions for integrating your tools with the Firenest platform.
                    </p>
                    <Button
                      variant="outline"
                      className="w-full border-white/10 hover:bg-white/5 flex items-center justify-center"
                      onClick={() => window.open('https://docs.firenest.com/partners/integration', '_blank')}
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      <span>View Integration Guide</span>
                    </Button>
                  </div>

                  <div className="pt-4">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/10 flex items-center justify-center mr-3">
                        <HelpCircle className="w-5 h-5 text-blue-400" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Support Resources</h3>
                    </div>
                    <p className="text-white/70 mb-4">
                      Get help with your integration or find answers to common questions.
                    </p>
                    <Button
                      variant="outline"
                      className="w-full border-white/10 hover:bg-white/5 flex items-center justify-center"
                      onClick={() => window.open('https://docs.firenest.com/partners/support', '_blank')}
                    >
                      <HelpCircle className="w-4 h-4 mr-2" />
                      <span>View Support Resources</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerDashboard;
