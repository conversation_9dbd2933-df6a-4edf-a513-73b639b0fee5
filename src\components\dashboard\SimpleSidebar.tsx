import { useState, useEffect } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import {
  Home,
  Layers,
  CreditCard,
  Settings,
  HelpCircle,
  ChevronRight,
  X,
  Zap,
  BarChart3,
  Users,
  LogOut
} from 'lucide-react';
import { cn } from '@/lib/utils';
// Progress import removed

interface SimpleSidebarProps {
  isMobile: boolean;
  sidebarOpen: boolean;
  toggleSidebar: () => void;
}

/**
 * Simple, clean sidebar component built from scratch
 */
const SimpleSidebar = ({ isMobile, sidebarOpen, toggleSidebar }: SimpleSidebarProps) => {
  const navigate = useNavigate();
  const { logout } = useAuth(); // Credits removed
  const [isHovered, setIsHovered] = useState(false);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  // Only expand on hover if not mobile
  const isExpanded = isMobile ? sidebarOpen : isHovered;

  // Handle sidebar expansion effect on main content
  useEffect(() => {
    if (isMobile) return;

    const mainContent = document.getElementById('main-content');
    if (!mainContent) return;

    // Set margin based on sidebar state with smooth transition
    mainContent.style.transition = 'margin-left 0.3s ease-in-out';
    mainContent.style.marginLeft = isExpanded ? '180px' : '64px';

    // Add a small delay to ensure the transition is smooth
    const transitionTimeout = setTimeout(() => {
      mainContent.style.transitionTimingFunction = 'cubic-bezier(0.4, 0, 0.2, 1)';
    }, 50);

    // Set initial margin
    return () => {
      mainContent.style.marginLeft = '';
      clearTimeout(transitionTimeout);
    };
  }, [isExpanded, isMobile]);

  // Set initial margin on mount
  useEffect(() => {
    if (isMobile) return;

    const mainContent = document.getElementById('main-content');
    if (!mainContent) return;

    // Set initial margin without transition
    mainContent.style.marginLeft = '64px';
  }, []);

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  // Handle hover for section expansion
  const handleSectionHover = (section: string) => {
    if (isExpanded && !isMobile) {
      setExpandedSection(section);
    }
  };

  // Handle mouse leave for section
  const handleSectionLeave = () => {
    if (!isMobile) {
      setExpandedSection(null);
    }
  };

  return (
    <aside
      className={cn(
        "fixed top-0 left-0 z-40 h-full bg-dark-950 border-r border-white/5",
        "transition-all duration-300 ease-in-out",
        isExpanded ? "w-44" : "w-16",
        isMobile && !sidebarOpen && "transform -translate-x-full"
      )}
      style={{ transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)' }}
      onMouseEnter={() => !isMobile && setIsHovered(true)}
      onMouseLeave={() => !isMobile && setIsHovered(false)}
    >
      {/* Mobile close button */}
      {isMobile && sidebarOpen && (
        <button
          className="absolute top-3 right-3 text-white/70 hover:text-white"
          onClick={toggleSidebar}
        >
          <X className="h-5 w-5" />
        </button>
      )}

      {/* Logo */}
      <div className="flex items-center h-16 px-3 border-b border-white/5">
        <div className="flex items-center w-full">
          <div className="flex-shrink-0 h-9 w-9 rounded-md bg-gradient-to-br from-fiery to-fiery-600 flex items-center justify-center shadow-md">
            <Zap className="h-5 w-5 text-white" />
          </div>
          <span className={cn(
            "ml-3 font-semibold text-white text-lg transition-all duration-300 whitespace-nowrap overflow-hidden",
            isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
          )}>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-fiery via-fiery to-cool-500 bg-size-200">
              <span className="text-fiery">Fir</span>
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
              <span className="text-cool">est</span>
            </span>
          </span>
        </div>
      </div>

      {/* Removed credits display from top */}

      {/* Navigation */}
      <div className="py-4 px-3 flex-1 overflow-y-auto hide-scrollbar">
        <div className="space-y-1.5">
          {/* Main section - Using absolute positioning to prevent layout shifts */}
          <div className="relative h-6">
            <div className={cn(
              "absolute left-0 right-0 px-3 text-xs font-medium text-white/40 uppercase tracking-wider transition-all duration-300",
              isExpanded ? "opacity-100 translate-x-0" : "opacity-0 translate-x-4 pointer-events-none"
            )}>
              Main
            </div>
          </div>

          {/* Dashboard */}
          <NavLink
            to="."
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <Home className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Dashboard</span>
            </div>
          </NavLink>

          {/* Notifications section removed */}

          {/* Workbench */}
          <NavLink
            to="workbench"
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <Layers className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Workbench</span>
            </div>
          </NavLink>

          {/* Analytics section */}
          <div
            className={cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all cursor-pointer",
              expandedSection === 'analytics' ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
            onClick={() => isExpanded && toggleSection('analytics')}
            onMouseEnter={() => handleSectionHover('analytics')}
            onMouseLeave={handleSectionLeave}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-5 h-5">
                  <BarChart3 className="h-5 w-5" />
                </div>
                <span className={cn(
                  "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                  isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
                )}>Analytics</span>
              </div>
              {isExpanded && (
                <ChevronRight
                  className={cn(
                    "h-4 w-4 transition-transform duration-300 flex-shrink-0",
                    expandedSection === 'analytics' && "rotate-90"
                  )}
                />
              )}
            </div>
          </div>

          {/* Analytics submenu */}
          <div
            className={cn(
              "overflow-hidden transition-all duration-300 ml-8 pl-2 border-l border-white/10 space-y-1 py-1",
              isExpanded && expandedSection === 'analytics' ? "max-h-40 opacity-100" : "max-h-0 opacity-0"
            )}
            onMouseEnter={() => handleSectionHover('analytics')}
            onMouseLeave={handleSectionLeave}
          >
            <NavLink
              to="analytics/usage"
              className={({ isActive }) => cn(
                "block py-1.5 px-2 text-xs rounded-md transition-colors",
                isActive ? "text-fiery bg-fiery/10" : "text-white/70 hover:text-white hover:bg-white/5"
              )}
            >
              Usage Metrics
            </NavLink>
            <NavLink
              to="analytics/performance"
              className={({ isActive }) => cn(
                "block py-1.5 px-2 text-xs rounded-md transition-colors",
                isActive ? "text-fiery bg-fiery/5" : "text-white/70 hover:text-white hover:bg-white/5"
              )}
            >
              Performance
            </NavLink>
          </div>

          {/* Credits */}
          <NavLink
            to="credits"
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <CreditCard className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Credits</span>
            </div>
          </NavLink>

          {/* Credit Usage Demo */}
          <NavLink
            to="credit-usage-demo"
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <Zap className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Credit Demo</span>
            </div>
          </NavLink>

          {/* Team section */}
          <div
            className={cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all cursor-pointer",
              expandedSection === 'team' ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
            onClick={() => isExpanded && toggleSection('team')}
            onMouseEnter={() => handleSectionHover('team')}
            onMouseLeave={handleSectionLeave}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-5 h-5">
                  <Users className="h-5 w-5" />
                </div>
                <span className={cn(
                  "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                  isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
                )}>Team</span>
              </div>
              {isExpanded && (
                <ChevronRight
                  className={cn(
                    "h-4 w-4 transition-transform duration-300 flex-shrink-0",
                    expandedSection === 'team' && "rotate-90"
                  )}
                />
              )}
            </div>
          </div>

          {/* Team submenu */}
          <div
            className={cn(
              "overflow-hidden transition-all duration-300 ml-8 pl-2 border-l border-white/10 space-y-1 py-1",
              isExpanded && expandedSection === 'team' ? "max-h-40 opacity-100" : "max-h-0 opacity-0"
            )}
            onMouseEnter={() => handleSectionHover('team')}
            onMouseLeave={handleSectionLeave}
          >
            <NavLink
              to="team/members"
              className={({ isActive }) => cn(
                "block py-1.5 px-2 text-xs rounded-md transition-colors",
                isActive ? "text-fiery bg-fiery/10" : "text-white/70 hover:text-white hover:bg-white/5"
              )}
            >
              Members
            </NavLink>
            <NavLink
              to="team/invites"
              className={({ isActive }) => cn(
                "block py-1.5 px-2 text-xs rounded-md transition-colors",
                isActive ? "text-fiery bg-fiery/10" : "text-white/70 hover:text-white hover:bg-white/5"
              )}
            >
              Invitations
            </NavLink>
          </div>

          {/* Settings section - Using absolute positioning to prevent layout shifts */}
          <div className="relative h-6 mt-6">
            <div className={cn(
              "absolute left-0 right-0 px-3 text-xs font-medium text-white/40 uppercase tracking-wider transition-all duration-300",
              isExpanded ? "opacity-100 translate-x-0" : "opacity-0 translate-x-4 pointer-events-none"
            )}>
              Settings
            </div>
          </div>

          {/* Profile Settings */}
          <NavLink
            to="settings"
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <Settings className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Settings</span>
            </div>
          </NavLink>

          {/* Components Demo */}
          <NavLink
            to="components"
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <Zap className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Components</span>
            </div>
          </NavLink>

          {/* Help & Support */}
          <NavLink
            to="support"
            end
            className={({ isActive }) => cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all",
              isActive ? "text-fiery bg-fiery/10 border-l-2 border-fiery" : "text-white/70 hover:text-white hover:bg-white/5 border-l-2 border-transparent",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <HelpCircle className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Help</span>
            </div>
          </NavLink>

          {/* Logout */}
          <button
            onClick={handleLogout}
            className={cn(
              "flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-all w-full mt-4",
              "text-white/70 hover:text-red-400 hover:bg-red-500/10 border-l-2 border-transparent hover:border-red-500",
              !isExpanded && "justify-center"
            )}
          >
            <div className="flex items-center w-full">
              <div className="flex-shrink-0 w-5 h-5">
                <LogOut className="h-5 w-5" />
              </div>
              <span className={cn(
                "ml-3 transition-all duration-300 whitespace-nowrap overflow-hidden",
                isExpanded ? "opacity-100 max-w-[150px]" : "opacity-0 max-w-0"
              )}>Logout</span>
            </div>
          </button>
        </div>
      </div>

      {/* Collapse button for desktop - hidden since we're using hover */}
      {false && !isMobile && (
        <div className="p-3 mt-auto border-t border-white/5">
          <button
            onClick={toggleSidebar}
            className="w-full flex items-center justify-center p-1.5 rounded text-white/50 hover:text-white hover:bg-white/5 transition-colors"
          >
            <ChevronRight className={cn(
              "h-4 w-4 transition-transform duration-300",
              isExpanded && "rotate-180"
            )} />
          </button>
        </div>
      )}
    </aside>
  );
};

export default SimpleSidebar;
