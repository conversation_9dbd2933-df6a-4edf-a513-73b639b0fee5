@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px) translateX(-50%); opacity: 0; }
  to { transform: translateY(0) translateX(-50%); opacity: 1; }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(108, 99, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(108, 99, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(108, 99, 255, 0); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.5s ease-out forwards;
}

.animate-pulse-shadow {
  animation: pulse 2s infinite;
}

:root {
  --primary-color: #6C63FF;
  --secondary-color: #FF6584;
  --accent-color: #9892FF;
  --background-color: #121212;
  --surface-color: #1E1E1E;
  --text-color: #F8F9FA;
  --text-secondary: #BAC1C6;
  --border-color: #323232;
  --light-gray: #464646;
  --dark-gray: #5A5A5A;
}

@layer base {
  body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  a {
    @apply text-primary hover:text-primary-400 transition-colors;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300 inline-flex items-center justify-center;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-600 shadow-md hover:shadow-glow;
  }

  .btn-secondary {
    @apply bg-dark-600 text-white hover:bg-dark-500 border border-dark-500;
  }

  .btn-outline {
    @apply bg-transparent border border-primary text-primary hover:bg-primary hover:text-white;
  }

  .card {
    @apply bg-dark-700 rounded-xl shadow-lg p-6 border border-dark-600;
  }

  .input {
    @apply w-full px-4 py-3 bg-dark-800 border border-dark-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-light;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-900 text-primary-200;
  }

  .badge-secondary {
    @apply bg-secondary-900 text-secondary-200;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
  }

  .section {
    @apply py-16 md:py-24;
  }

  .pricing-card {
    @apply card border-dark-500 hover:border-primary transition-colors duration-300 flex flex-col h-full;
  }

  .pricing-card-popular {
    @apply pricing-card border-primary relative overflow-hidden;
  }

  .pricing-card-popular::before {
    content: "Popular";
    @apply absolute top-0 right-0 bg-primary text-white px-4 py-1 text-sm font-medium rounded-bl-lg;
  }

  .feature-list {
    @apply space-y-3 mt-6;
  }

  .feature-item {
    @apply flex items-start;
  }

  .feature-icon {
    @apply flex-shrink-0 h-5 w-5 text-primary mt-0.5;
  }

  .feature-text {
    @apply ml-3 text-light-900;
  }
}