-- Firenest Usage Tracking Tables
-- This file contains the SQL to create tables for tracking tool launches and usage sessions

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tool_launches table for tracking when tools are launched
CREATE TABLE IF NOT EXISTS tool_launches (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  launched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT NOT NULL, -- 'active', 'completed', 'failed'
  launch_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_sessions table for tracking usage sessions
CREATE TABLE IF NOT EXISTS usage_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  launch_id UUID REFERENCES tool_launches(id) ON DELETE CASCADE,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  status TEXT NOT NULL, -- 'active', 'completed', 'terminated'
  metrics JSONB, -- Store various metrics like API calls, resources consumed, etc.
  estimated_credits INTEGER DEFAULT 0,
  actual_credits_used INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_events table for tracking detailed usage events
CREATE TABLE IF NOT EXISTS usage_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES usage_sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  event_type TEXT NOT NULL, -- 'api_call', 'resource_consumption', 'time_update', etc.
  event_data JSONB,
  credits_used INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE tool_launches ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;

-- Create policies for tool_launches table
CREATE POLICY "Users can view their own tool launches" 
  ON tool_launches FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tool launches" 
  ON tool_launches FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tool launches" 
  ON tool_launches FOR UPDATE 
  USING (auth.uid() = user_id);

-- Create policies for usage_sessions table
CREATE POLICY "Users can view their own usage sessions" 
  ON usage_sessions FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own usage sessions" 
  ON usage_sessions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own usage sessions" 
  ON usage_sessions FOR UPDATE 
  USING (auth.uid() = user_id);

-- Create policies for usage_events table
CREATE POLICY "Users can view their own usage events" 
  ON usage_events FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own usage events" 
  ON usage_events FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS tool_launches_user_id_idx ON tool_launches(user_id);
CREATE INDEX IF NOT EXISTS tool_launches_tool_id_idx ON tool_launches(tool_id);
CREATE INDEX IF NOT EXISTS tool_launches_status_idx ON tool_launches(status);

CREATE INDEX IF NOT EXISTS usage_sessions_user_id_idx ON usage_sessions(user_id);
CREATE INDEX IF NOT EXISTS usage_sessions_tool_id_idx ON usage_sessions(tool_id);
CREATE INDEX IF NOT EXISTS usage_sessions_launch_id_idx ON usage_sessions(launch_id);
CREATE INDEX IF NOT EXISTS usage_sessions_status_idx ON usage_sessions(status);

CREATE INDEX IF NOT EXISTS usage_events_session_id_idx ON usage_events(session_id);
CREATE INDEX IF NOT EXISTS usage_events_user_id_idx ON usage_events(user_id);
CREATE INDEX IF NOT EXISTS usage_events_tool_id_idx ON usage_events(tool_id);
CREATE INDEX IF NOT EXISTS usage_events_event_type_idx ON usage_events(event_type);

-- Create function to end a usage session
CREATE OR REPLACE FUNCTION end_usage_session(
  session_id UUID,
  end_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'completed',
  metrics JSONB DEFAULT NULL,
  actual_credits_used INTEGER DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  session_record usage_sessions%ROWTYPE;
  duration INTEGER;
BEGIN
  -- Get the session record
  SELECT * INTO session_record
  FROM usage_sessions
  WHERE id = session_id;
  
  -- If session not found, return false
  IF session_record IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Calculate duration in seconds
  duration := EXTRACT(EPOCH FROM (end_time - session_record.start_time))::INTEGER;
  
  -- Update the session
  UPDATE usage_sessions
  SET 
    end_time = end_usage_session.end_time,
    duration_seconds = duration,
    status = end_usage_session.status,
    metrics = COALESCE(end_usage_session.metrics, session_record.metrics),
    actual_credits_used = COALESCE(end_usage_session.actual_credits_used, session_record.estimated_credits),
    updated_at = NOW()
  WHERE id = session_id;
  
  -- Update the tool launch status if this is the only session for this launch
  UPDATE tool_launches
  SET 
    status = end_usage_session.status,
    updated_at = NOW()
  WHERE id = session_record.launch_id
  AND NOT EXISTS (
    SELECT 1 FROM usage_sessions 
    WHERE launch_id = session_record.launch_id 
    AND id != session_id 
    AND status = 'active'
  );
  
  -- If actual_credits_used is provided, deduct credits
  IF actual_credits_used IS NOT NULL AND actual_credits_used > 0 THEN
    -- Call the use_credits function (this assumes the function exists)
    PERFORM use_credits(
      session_record.user_id, 
      actual_credits_used, 
      session_record.tool_id, 
      format('Used %s credits for %s (Session ID: %s)', actual_credits_used, session_record.tool_id, session_id)
    );
  END IF;
  
  RETURN TRUE;
EXCEPTION WHEN OTHERS THEN
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update session metrics
CREATE OR REPLACE FUNCTION update_session_metrics(
  session_id UUID,
  new_metrics JSONB,
  estimated_credits INTEGER DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE usage_sessions
  SET 
    metrics = new_metrics,
    estimated_credits = COALESCE(update_session_metrics.estimated_credits, estimated_credits),
    updated_at = NOW()
  WHERE id = session_id;
  
  RETURN FOUND;
EXCEPTION WHEN OTHERS THEN
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to record a usage event
CREATE OR REPLACE FUNCTION record_usage_event(
  session_id UUID,
  user_id UUID,
  tool_id TEXT,
  event_type TEXT,
  event_data JSONB DEFAULT NULL,
  credits_used INTEGER DEFAULT 0
)
RETURNS UUID AS $$
DECLARE
  event_id UUID;
BEGIN
  -- Insert the event
  INSERT INTO usage_events (
    id,
    session_id,
    user_id,
    tool_id,
    event_type,
    event_data,
    credits_used,
    created_at
  )
  VALUES (
    uuid_generate_v4(),
    session_id,
    user_id,
    tool_id,
    event_type,
    event_data,
    credits_used,
    NOW()
  )
  RETURNING id INTO event_id;
  
  -- Update the session metrics if credits_used > 0
  IF credits_used > 0 THEN
    UPDATE usage_sessions
    SET 
      estimated_credits = estimated_credits + credits_used,
      updated_at = NOW()
    WHERE id = session_id;
  END IF;
  
  RETURN event_id;
EXCEPTION WHEN OTHERS THEN
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION end_usage_session TO authenticated;
GRANT EXECUTE ON FUNCTION update_session_metrics TO authenticated;
GRANT EXECUTE ON FUNCTION record_usage_event TO authenticated;
