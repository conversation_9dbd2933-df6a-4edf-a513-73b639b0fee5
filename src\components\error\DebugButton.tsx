import React from 'react';
import { Bug } from 'lucide-react';
import { useDebug } from '@/contexts/DebugContext';

interface DebugButtonProps {
  className?: string;
}

const DebugButton: React.FC<DebugButtonProps> = ({ className = '' }) => {
  const { isDebugMode, openDebugConsole } = useDebug();

  if (!isDebugMode) return null;

  return (
    <button
      onClick={openDebugConsole}
      className={`fixed bottom-4 right-4 firenest-card rounded-full p-3 text-fiery hover:text-fiery-400 transition-all shadow-lg z-50 ${className}`}
      title="Open Debug Console"
    >
      <Bug className="w-5 h-5" />
    </button>
  );
};

export default DebugButton;
