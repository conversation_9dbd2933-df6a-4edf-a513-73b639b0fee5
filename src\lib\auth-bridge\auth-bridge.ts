/**
 * Authentication Bridge Service
 *
 * This service handles authentication with third-party services through various methods:
 * - OAuth 2.0
 * - API Key
 * - Credentials
 * - IP-based
 */

import { v4 as uuidv4 } from 'uuid';
import { notify } from '@/components/ui/notification-system';
import {
  AuthMethod,
  ServiceConfig,
  ServiceSession,
  AuthResult,
  UsageUpdateEvent
} from './types';
import { serviceConfigs } from './service-configs';
import { secureStorage } from './secure-storage';

class AuthBridgeService {
  private activeSessions: Map<string, ServiceSession> = new Map();
  private serviceConfigs: Map<string, ServiceConfig> = new Map();
  private usageUpdateCallbacks: ((event: UsageUpdateEvent) => void)[] = [];

  constructor() {
    // Load service configurations
    serviceConfigs.forEach(config => {
      this.serviceConfigs.set(config.id, config);
    });

    // Load active sessions from storage
    this.loadSessions();

    // Set up interval to periodically save sessions and update usage
    setInterval(() => this.saveAndUpdateSessions(), 30000); // Every 30 seconds
  }

  /**
   * Initialize authentication with a service
   */
  public async initializeAuth(serviceId: string, userId: string): Promise<AuthResult> {
    try {
      const serviceConfig = this.serviceConfigs.get(serviceId);

      if (!serviceConfig) {
        return {
          success: false,
          error: {
            code: 'service_not_found',
            message: `Service with ID ${serviceId} not found`
          }
        };
      }

      // Check if there's already an active session
      const existingSession = this.findSession(serviceId, userId);
      if (existingSession && existingSession.status === 'active') {
        return {
          success: true,
          session: existingSession
        };
      }

      // Handle authentication based on the method
      switch (serviceConfig.authMethod) {
        case 'oauth':
          return this.initializeOAuth(serviceConfig, userId);
        case 'api_key':
          return this.initializeApiKey(serviceConfig, userId);
        case 'credentials':
          return this.initializeCredentials(serviceConfig, userId);
        case 'ip_based':
          return this.initializeIpBased(serviceConfig, userId);
        default:
          return {
            success: false,
            error: {
              code: 'unsupported_auth_method',
              message: `Authentication method not supported`
            }
          };
      }
    } catch (error) {
      console.error('Error initializing authentication:', error);
      return {
        success: false,
        error: {
          code: 'auth_initialization_failed',
          message: 'Failed to initialize authentication',
          details: error
        }
      };
    }
  }

  /**
   * Complete the authentication process (e.g., after OAuth redirect)
   */
  public async completeAuth(serviceId: string, userId: string, authData: any): Promise<AuthResult> {
    try {
      const serviceConfig = this.serviceConfigs.get(serviceId);

      if (!serviceConfig) {
        return {
          success: false,
          error: {
            code: 'service_not_found',
            message: `Service with ID ${serviceId} not found`
          }
        };
      }

      // Handle completion based on the method
      switch (serviceConfig.authMethod) {
        case 'oauth':
          return this.completeOAuth(serviceConfig, userId, authData);
        case 'api_key':
          return this.completeApiKey(serviceConfig, userId, authData);
        case 'credentials':
          return this.completeCredentials(serviceConfig, userId, authData);
        case 'ip_based':
          return this.completeIpBased(serviceConfig, userId, authData);
        default:
          return {
            success: false,
            error: {
              code: 'unsupported_auth_method',
              message: `Authentication method not supported`
            }
          };
      }
    } catch (error) {
      console.error('Error completing authentication:', error);
      return {
        success: false,
        error: {
          code: 'auth_completion_failed',
          message: 'Failed to complete authentication',
          details: error
        }
      };
    }
  }

  /**
   * End a session with a service
   */
  public async endSession(serviceId: string, userId: string): Promise<boolean> {
    try {
      const session = this.findSession(serviceId, userId);

      if (!session) {
        return false;
      }

      // Update session status
      session.status = 'completed';
      session.lastActiveTime = new Date();

      // Calculate final usage
      this.calculateSessionUsage(session);

      // Save the updated session
      this.activeSessions.set(`${serviceId}:${userId}`, session);
      this.saveSessions();

      // Emit usage update event
      this.emitUsageUpdate({
        serviceId,
        userId,
        sessionId: `${serviceId}:${userId}`,
        timestamp: new Date(),
        metrics: {
          timeSpentSeconds: session.usageMetrics.timeSpentSeconds,
          apiCallsMade: session.usageMetrics.apiCallsMade,
          resourcesConsumed: session.usageMetrics.resourcesConsumed
        }
      });

      return true;
    } catch (error) {
      console.error('Error ending session:', error);
      return false;
    }
  }

  /**
   * Get all active sessions for a user
   */
  public getUserSessions(userId: string): ServiceSession[] {
    const userSessions: ServiceSession[] = [];

    this.activeSessions.forEach(session => {
      if (session.userId === userId && session.status !== 'completed') {
        userSessions.push(session);
      }
    });

    return userSessions;
  }

  /**
   * Get a specific session
   */
  public getSession(serviceId: string, userId: string): ServiceSession | null {
    return this.findSession(serviceId, userId);
  }

  /**
   * Update usage metrics for a session
   */
  public updateUsage(serviceId: string, userId: string, metrics: any): boolean {
    try {
      const session = this.findSession(serviceId, userId);

      if (!session || session.status === 'completed') {
        return false;
      }

      // Update last active time
      session.lastActiveTime = new Date();

      // Update metrics based on the service's tracking method
      const serviceConfig = this.serviceConfigs.get(serviceId);
      if (!serviceConfig) {
        return false;
      }

      switch (serviceConfig.usageTracking.metric) {
        case 'time':
          if (metrics.timeSpentSeconds) {
            session.usageMetrics.timeSpentSeconds += metrics.timeSpentSeconds;
          }
          break;
        case 'api_calls':
          if (metrics.apiCallsMade) {
            session.usageMetrics.apiCallsMade += metrics.apiCallsMade;
          }
          break;
        case 'resources':
          if (metrics.resourcesConsumed) {
            session.usageMetrics.resourcesConsumed += metrics.resourcesConsumed;
          }
          break;
        case 'custom':
          if (metrics.customMetric) {
            session.usageMetrics.customMetric =
              (session.usageMetrics.customMetric || 0) + metrics.customMetric;
          }
          break;
      }

      // Calculate estimated credits
      this.calculateSessionUsage(session);

      // Save the updated session
      this.activeSessions.set(`${serviceId}:${userId}`, session);

      // Emit usage update event
      this.emitUsageUpdate({
        serviceId,
        userId,
        sessionId: `${serviceId}:${userId}`,
        timestamp: new Date(),
        metrics
      });

      return true;
    } catch (error) {
      console.error('Error updating usage:', error);
      return false;
    }
  }

  /**
   * Register a callback for usage updates
   */
  public onUsageUpdate(callback: (event: UsageUpdateEvent) => void): () => void {
    this.usageUpdateCallbacks.push(callback);

    // Return a function to unregister the callback
    return () => {
      this.usageUpdateCallbacks = this.usageUpdateCallbacks.filter(cb => cb !== callback);
    };
  }

  /**
   * Get service configuration
   */
  public getServiceConfig(serviceId: string): ServiceConfig | undefined {
    return this.serviceConfigs.get(serviceId);
  }

  /**
   * Get all service configurations
   */
  public getAllServiceConfigs(): ServiceConfig[] {
    return Array.from(this.serviceConfigs.values());
  }

  // Private methods

  private findSession(serviceId: string, userId: string): ServiceSession | null {
    const session = this.activeSessions.get(`${serviceId}:${userId}`);
    return session || null;
  }

  private calculateSessionUsage(session: ServiceSession): void {
    const serviceConfig = this.serviceConfigs.get(session.serviceId);
    if (!serviceConfig) {
      return;
    }

    let units = 0;

    switch (serviceConfig.usageTracking.metric) {
      case 'time':
        // Convert seconds to minutes and round up
        units = Math.ceil(session.usageMetrics.timeSpentSeconds / 60);
        break;
      case 'api_calls':
        units = session.usageMetrics.apiCallsMade;
        break;
      case 'resources':
        units = session.usageMetrics.resourcesConsumed;
        break;
      case 'custom':
        units = session.usageMetrics.customMetric || 0;
        break;
    }

    // Apply minimum usage if specified
    if (serviceConfig.usageTracking.minimumUsage && units < serviceConfig.usageTracking.minimumUsage) {
      units = serviceConfig.usageTracking.minimumUsage;
    }

    // Calculate credits
    session.usageMetrics.estimatedCreditsUsed = units * serviceConfig.usageTracking.costPerUnit;
  }

  private saveAndUpdateSessions(): void {
    // Update all active sessions
    this.activeSessions.forEach(session => {
      if (session.status === 'active') {
        // Calculate time since last update
        const now = new Date();
        const elapsedSeconds = Math.floor((now.getTime() - session.lastActiveTime.getTime()) / 1000);

        if (elapsedSeconds > 0) {
          // Update time spent
          session.usageMetrics.timeSpentSeconds += elapsedSeconds;
          session.lastActiveTime = now;

          // Calculate estimated credits
          this.calculateSessionUsage(session);

          // Emit usage update event
          this.emitUsageUpdate({
            serviceId: session.serviceId,
            userId: session.userId,
            sessionId: `${session.serviceId}:${session.userId}`,
            timestamp: now,
            metrics: {
              timeSpentSeconds: elapsedSeconds
            }
          });
        }
      }
    });

    // Save sessions to storage
    this.saveSessions();
  }

  private saveSessions(): void {
    try {
      const sessionsArray = Array.from(this.activeSessions.values());
      secureStorage.setItem('firenest-sessions', JSON.stringify(sessionsArray));
    } catch (error) {
      console.error('Error saving sessions:', error);
    }
  }

  private loadSessions(): void {
    try {
      const sessionsJson = secureStorage.getItem('firenest-sessions');

      if (sessionsJson) {
        const sessionsArray = JSON.parse(sessionsJson) as ServiceSession[];

        sessionsArray.forEach(session => {
          // Convert string dates back to Date objects
          session.startTime = new Date(session.startTime);
          session.lastActiveTime = new Date(session.lastActiveTime);

          if (session.authData.expiresAt) {
            session.authData.expiresAt = new Date(session.authData.expiresAt);
          }

          this.activeSessions.set(`${session.serviceId}:${session.userId}`, session);
        });
      }
    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  }

  private emitUsageUpdate(event: UsageUpdateEvent): void {
    this.usageUpdateCallbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in usage update callback:', error);
      }
    });
  }

  // Authentication method implementations

  private async initializeOAuth(serviceConfig: ServiceConfig, userId: string): Promise<AuthResult> {
    try {
      if (!serviceConfig.oauth) {
        return {
          success: false,
          error: {
            code: 'missing_oauth_config',
            message: 'OAuth configuration is missing'
          }
        };
      }

      // Generate state parameter for security
      const state = uuidv4();

      // Store state in session storage for verification
      sessionStorage.setItem(`firenest-oauth-state-${serviceConfig.id}`, state);
      sessionStorage.setItem(`firenest-oauth-user-${state}`, userId);

      // Construct authorization URL
      const authUrl = new URL(serviceConfig.oauth.authorizationUrl);

      // Add query parameters
      authUrl.searchParams.append('client_id', serviceConfig.oauth.clientId);
      authUrl.searchParams.append('redirect_uri', serviceConfig.oauth.redirectUrl);
      authUrl.searchParams.append('response_type', serviceConfig.oauth.responseType);
      authUrl.searchParams.append('state', state);

      if (serviceConfig.oauth.scope && serviceConfig.oauth.scope.length > 0) {
        authUrl.searchParams.append('scope', serviceConfig.oauth.scope.join(' '));
      }

      // Return the authorization URL for redirection
      return {
        success: true,
        redirectUrl: authUrl.toString()
      };
    } catch (error) {
      console.error('Error initializing OAuth:', error);
      return {
        success: false,
        error: {
          code: 'oauth_initialization_failed',
          message: 'Failed to initialize OAuth',
          details: error
        }
      };
    }
  }

  private async completeOAuth(serviceConfig: ServiceConfig, userId: string, authData: any): Promise<AuthResult> {
    try {
      // In a real implementation, this would exchange the authorization code for tokens
      // For now, we'll simulate a successful authentication

      // Create a new session
      const session: ServiceSession = {
        serviceId: serviceConfig.id,
        userId,
        startTime: new Date(),
        lastActiveTime: new Date(),
        status: 'active',
        authData: {
          accessToken: authData.accessToken || 'simulated-access-token',
          refreshToken: authData.refreshToken || 'simulated-refresh-token',
          expiresAt: new Date(Date.now() + 3600 * 1000) // 1 hour from now
        },
        usageMetrics: {
          timeSpentSeconds: 0,
          apiCallsMade: 0,
          resourcesConsumed: 0,
          estimatedCreditsUsed: 0
        }
      };

      // Store the session
      this.activeSessions.set(`${serviceConfig.id}:${userId}`, session);
      this.saveSessions();

      return {
        success: true,
        session
      };
    } catch (error) {
      console.error('Error completing OAuth:', error);
      return {
        success: false,
        error: {
          code: 'oauth_completion_failed',
          message: 'Failed to complete OAuth',
          details: error
        }
      };
    }
  }

  private async initializeApiKey(serviceConfig: ServiceConfig, userId: string): Promise<AuthResult> {
    try {
      // For API key authentication, we need to prompt the user for their API key
      // In a real implementation, this would show a UI prompt
      // For now, we'll simulate a successful authentication

      return {
        success: true,
        // In a real implementation, this would be a redirect to an API key entry form
        redirectUrl: `/api-key-entry?serviceId=${serviceConfig.id}&userId=${userId}`
      };
    } catch (error) {
      console.error('Error initializing API key auth:', error);
      return {
        success: false,
        error: {
          code: 'api_key_initialization_failed',
          message: 'Failed to initialize API key authentication',
          details: error
        }
      };
    }
  }

  private async completeApiKey(serviceConfig: ServiceConfig, userId: string, authData: any): Promise<AuthResult> {
    try {
      if (!authData.apiKey) {
        return {
          success: false,
          error: {
            code: 'missing_api_key',
            message: 'API key is required'
          }
        };
      }

      // Create a new session
      const session: ServiceSession = {
        serviceId: serviceConfig.id,
        userId,
        startTime: new Date(),
        lastActiveTime: new Date(),
        status: 'active',
        authData: {
          apiKey: authData.apiKey
        },
        usageMetrics: {
          timeSpentSeconds: 0,
          apiCallsMade: 0,
          resourcesConsumed: 0,
          estimatedCreditsUsed: 0
        }
      };

      // Store the session
      this.activeSessions.set(`${serviceConfig.id}:${userId}`, session);
      this.saveSessions();

      return {
        success: true,
        session
      };
    } catch (error) {
      console.error('Error completing API key auth:', error);
      return {
        success: false,
        error: {
          code: 'api_key_completion_failed',
          message: 'Failed to complete API key authentication',
          details: error
        }
      };
    }
  }

  private async initializeCredentials(serviceConfig: ServiceConfig, userId: string): Promise<AuthResult> {
    try {
      // For credentials authentication, we need to prompt the user for their username and password
      // In a real implementation, this would show a UI prompt
      // For now, we'll simulate a successful authentication

      return {
        success: true,
        // In a real implementation, this would be a redirect to a login form
        redirectUrl: `/credentials-entry?serviceId=${serviceConfig.id}&userId=${userId}`
      };
    } catch (error) {
      console.error('Error initializing credentials auth:', error);
      return {
        success: false,
        error: {
          code: 'credentials_initialization_failed',
          message: 'Failed to initialize credentials authentication',
          details: error
        }
      };
    }
  }

  private async completeCredentials(serviceConfig: ServiceConfig, userId: string, authData: any): Promise<AuthResult> {
    try {
      if (!authData.username || !authData.password) {
        return {
          success: false,
          error: {
            code: 'missing_credentials',
            message: 'Username and password are required'
          }
        };
      }

      // In a real implementation, this would perform a login request to the service
      // For now, we'll simulate a successful authentication

      // Create a new session
      const session: ServiceSession = {
        serviceId: serviceConfig.id,
        userId,
        startTime: new Date(),
        lastActiveTime: new Date(),
        status: 'active',
        authData: {
          cookies: {
            'session_id': 'simulated-session-id',
            'auth_token': 'simulated-auth-token'
          },
          sessionId: 'simulated-session-id'
        },
        usageMetrics: {
          timeSpentSeconds: 0,
          apiCallsMade: 0,
          resourcesConsumed: 0,
          estimatedCreditsUsed: 0
        }
      };

      // Store the session
      this.activeSessions.set(`${serviceConfig.id}:${userId}`, session);
      this.saveSessions();

      return {
        success: true,
        session
      };
    } catch (error) {
      console.error('Error completing credentials auth:', error);
      return {
        success: false,
        error: {
          code: 'credentials_completion_failed',
          message: 'Failed to complete credentials authentication',
          details: error
        }
      };
    }
  }

  private async initializeIpBased(serviceConfig: ServiceConfig, userId: string): Promise<AuthResult> {
    try {
      // For IP-based authentication, we need to verify the user's IP address
      // In a real implementation, this would check the user's IP against allowed IPs
      // For now, we'll simulate a successful authentication

      // Create a new session
      const session: ServiceSession = {
        serviceId: serviceConfig.id,
        userId,
        startTime: new Date(),
        lastActiveTime: new Date(),
        status: 'active',
        authData: {
          ipAddress: '127.0.0.1' // Simulated IP address
        },
        usageMetrics: {
          timeSpentSeconds: 0,
          apiCallsMade: 0,
          resourcesConsumed: 0,
          estimatedCreditsUsed: 0
        }
      };

      // Store the session
      this.activeSessions.set(`${serviceConfig.id}:${userId}`, session);
      this.saveSessions();

      return {
        success: true,
        session,
        // In a real implementation, this would be a redirect to the service
        redirectUrl: serviceConfig.endpoints.baseUrl
      };
    } catch (error) {
      console.error('Error initializing IP-based auth:', error);
      return {
        success: false,
        error: {
          code: 'ip_based_initialization_failed',
          message: 'Failed to initialize IP-based authentication',
          details: error
        }
      };
    }
  }

  private async completeIpBased(serviceConfig: ServiceConfig, userId: string, authData: any): Promise<AuthResult> {
    // IP-based authentication doesn't typically have a completion step
    // The initialization step should handle everything
    return {
      success: true,
      session: this.findSession(serviceConfig.id, userId)
    };
  }
}

// Create and export a singleton instance
export const authBridge = new AuthBridgeService();
