# Firenest Credit System

This document explains how the Firenest credit system works and how to use it in your application.

## Overview

The Firenest credit system allows users to purchase and use credits for various AI tools. The system consists of:

1. **Database Tables**:
   - `user_credits`: Stores the total and used credits for each user
   - `credit_transactions`: Records all credit transactions (purchases, usage, refunds, bonuses)

2. **SQL Functions**:
   - `purchase_credits`: Adds credits to a user's account and records a purchase transaction
   - `use_credits`: Uses credits for a service and records a usage transaction
   - `add_bonus_credits`: Adds bonus credits to a user's account
   - `refund_credits`: Refunds used credits back to a user's available balance

3. **Client Functions**:
   - `purchaseCredits`: Client-side function to purchase credits
   - `useCredits`: Client-side function to use credits for a service
   - `addBonusCredits`: Client-side function to add bonus credits
   - `refundCredits`: Client-side function to refund credits

## Production-Ready Features

The credit system includes several production-ready features that make it suitable for real-world financial transactions:

1. **Idempotency**: Prevents duplicate transactions if the same operation is retried
2. **Comprehensive Audit Trail**: Tracks who made changes and when
3. **Detailed Payment Information**: Stores payment method, amount, currency, and reference
4. **Robust Error Handling**: Provides detailed error information for troubleshooting
5. **Security**: Uses Row Level Security (RLS) to ensure users can only access their own data

## Database Schema

### user_credits Table

```sql
CREATE TABLE IF NOT EXISTS user_credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  total_credits INTEGER DEFAULT 0,
  used_credits INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### credit_transactions Table

```sql
CREATE TABLE IF NOT EXISTS credit_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  description TEXT,
  transaction_type TEXT NOT NULL, -- 'purchase', 'usage', 'refund', 'bonus'
  service_id TEXT,
  payment_method TEXT,
  payment_reference TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## How to Use

### Purchasing Credits

```typescript
import { purchaseCredits } from '@/lib/credits';

// Purchase 500 credits
const result = await purchaseCredits({
  userId: user.id,
  amount: 500,
  paymentMethod: 'card',
  description: 'Purchased 500 credits',
  price: 199
});

if (result.success) {
  console.log('Credits purchased successfully!');
  console.log('New total credits:', result.data.newTotalCredits);
  console.log('New available credits:', result.data.newAvailableCredits);
} else {
  console.error('Failed to purchase credits:', result.error.message);
}
```

### Using Credits

```typescript
import { useCredits } from '@/lib/credits';

// Use 10 credits for ChatGPT
const result = await useCredits({
  userId: user.id,
  amount: 10,
  serviceId: 'chatgpt',
  description: 'Used 10 credits for ChatGPT'
});

if (result.success) {
  console.log('Credits used successfully!');
  console.log('New available credits:', result.data.newAvailableCredits);
} else {
  console.error('Failed to use credits:', result.error.message);
}
```

### Adding Bonus Credits

```typescript
import { addBonusCredits } from '@/lib/credits';

// Add 100 bonus credits
const result = await addBonusCredits(
  user.id,
  100,
  'Welcome bonus credits'
);

if (result.success) {
  console.log('Bonus credits added successfully!');
  console.log('New total credits:', result.data.newTotalCredits);
} else {
  console.error('Failed to add bonus credits:', result.error.message);
}
```

### Refunding Credits

```typescript
import { refundCredits } from '@/lib/credits';

// Refund 5 credits
const result = await refundCredits(
  user.id,
  5,
  'Refund for failed service'
);

if (result.success) {
  console.log('Credits refunded successfully!');
  console.log('New available credits:', result.data.newAvailableCredits);
} else {
  console.error('Failed to refund credits:', result.error.message);
}
```

## Credit Usage Demo

The Credit Usage Demo page (`/dashboard/credit-usage-demo`) demonstrates how to use the credit system in a real application. It allows you to:

1. Select an AI service
2. Specify the number of units to use
3. See the cost in credits
4. Use the credits
5. View your usage history

## Implementation Details

### Security

The credit system uses Supabase Row Level Security (RLS) policies to ensure that users can only access and modify their own credits. The SQL functions are defined with `SECURITY DEFINER` to ensure they run with the necessary permissions.

### Error Handling

All credit functions return a consistent result object with the following structure:

```typescript
{
  success: boolean;
  data?: {
    transactionId: string;
    newTotalCredits: number;
    newAvailableCredits: number;
    idempotent?: boolean; // Indicates if this was a duplicate transaction
  };
  error?: {
    message: string;
    details?: any;
    error_detail?: string; // PostgreSQL error code
  };
}
```

This makes it easy to handle errors and display appropriate messages to the user.

### Transaction Types

The credit system supports the following transaction types:

- `purchase`: Credits purchased by the user
- `usage`: Credits used for a service
- `refund`: Credits refunded to the user
- `bonus`: Bonus credits added to the user's account

### Idempotency

All credit functions support idempotency keys to prevent duplicate transactions. If you provide the same idempotency key for multiple calls, only the first one will be processed, and subsequent calls will return the result of the first call.

```typescript
// First call - will be processed
const result1 = await addBonusCredits(
  user.id,
  100,
  'Welcome bonus',
  'welcome-bonus-123'
);

// Second call with the same idempotency key - will not be processed again
const result2 = await addBonusCredits(
  user.id,
  100,
  'Welcome bonus',
  'welcome-bonus-123'
);

// result2.data.idempotent will be true
```

## Best Practices

1. **Always check if a user has enough credits before using them**:
   ```typescript
   if (credits.availableCredits < cost) {
     notify.error(`Not enough credits. You need ${cost} credits but only have ${credits.availableCredits} available.`);
     return;
   }
   ```

2. **Refresh user data after credit transactions**:
   ```typescript
   await refreshUserData();
   ```

3. **Use descriptive transaction descriptions**:
   ```typescript
   description: `Used ${units} ${serviceId} units`
   ```

4. **Handle errors gracefully**:
   ```typescript
   try {
     // Use credits
   } catch (error) {
     notify.error('Failed to use credits. Please try again.');
   }
   ```

## Troubleshooting

If you encounter issues with the credit system, check the following:

1. **Supabase Connection**: Ensure your application is properly connected to Supabase.
2. **SQL Functions**: Make sure the SQL functions are properly installed in your Supabase project.
3. **RLS Policies**: Verify that the RLS policies are correctly configured.
4. **User Authentication**: Ensure the user is properly authenticated before attempting credit operations.
5. **Error Details**: Check the `error_detail` field in the error response for specific PostgreSQL error codes.

## Installation

To install the credit system in your Supabase project:

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Create a new query
4. Copy and paste the contents of `supabase-credit-functions-final.sql`
5. Run the script

This will create all the necessary functions and policies for the credit system.

For more information, refer to the Supabase documentation or contact the Firenest development team.
