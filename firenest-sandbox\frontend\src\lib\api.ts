import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance
export const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const { response } = error

    if (response) {
      const { status, data } = response

      // Handle specific error cases
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          if (window.location.pathname !== '/login') {
            toast.error('Session expired. Please login again.')
            // Clear auth state and redirect
            localStorage.removeItem('firenest-sandbox-auth')
            window.location.href = '/login'
          }
          break

        case 403:
          toast.error('Access denied')
          break

        case 404:
          toast.error('Resource not found')
          break

        case 409:
          toast.error(data.message || 'Conflict error')
          break

        case 422:
          // Validation errors
          if (data.details && Array.isArray(data.details)) {
            const errorMessages = data.details.map((detail: any) => detail.message).join(', ')
            toast.error(`Validation error: ${errorMessages}`)
          } else {
            toast.error(data.message || 'Validation error')
          }
          break

        case 429:
          toast.error('Too many requests. Please try again later.')
          break

        case 500:
          toast.error('Server error. Please try again later.')
          break

        default:
          if (status >= 500) {
            toast.error('Server error. Please try again later.')
          } else {
            toast.error(data.message || 'An error occurred')
          }
      }
    } else if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please try again.')
    } else if (error.message === 'Network Error') {
      toast.error('Network error. Please check your connection.')
    } else {
      toast.error('An unexpected error occurred')
    }

    return Promise.reject(error)
  }
)

// API response types
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
}

export interface PaginatedResponse<T = any> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface ApiError {
  error: string
  message: string
  code?: string
  details?: any[]
  errorId?: string
  timestamp?: string
}

// Auth API
export const authApi = {
  login: (idToken: string, provider = 'auth0') =>
    api.post<ApiResponse<{ token: string; user: any }>>('/auth/login', { idToken, provider }),
  
  logout: () =>
    api.post<ApiResponse>('/auth/logout'),
  
  refresh: (token: string) =>
    api.post<ApiResponse<{ token: string; user: any }>>('/auth/refresh', { token }),
  
  profile: () =>
    api.get<ApiResponse<any>>('/auth/profile'),
}

// Workspaces API
export const workspacesApi = {
  list: (params?: { page?: number; limit?: number; sortBy?: string; sortOrder?: string }) =>
    api.get<PaginatedResponse<any>>('/workspaces', { params }),
  
  get: (id: string) =>
    api.get<ApiResponse<any>>(`/workspaces/${id}`),
  
  create: (data: { name: string; description?: string; settings?: any }) =>
    api.post<ApiResponse<any>>('/workspaces', data),
  
  update: (id: string, data: { name?: string; description?: string; settings?: any }) =>
    api.put<ApiResponse<any>>(`/workspaces/${id}`, data),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/workspaces/${id}`),
  
  addMember: (id: string, data: { email: string; role?: string }) =>
    api.post<ApiResponse<any>>(`/workspaces/${id}/members`, data),
}

// Projects API
export const projectsApi = {
  list: (params?: { 
    page?: number; 
    limit?: number; 
    sortBy?: string; 
    sortOrder?: string;
    workspaceId?: string;
    status?: string;
    search?: string;
  }) =>
    api.get<PaginatedResponse<any>>('/projects', { params }),
  
  get: (id: string) =>
    api.get<ApiResponse<any>>(`/projects/${id}`),
  
  create: (data: { name: string; description?: string; workspaceId: string }) =>
    api.post<ApiResponse<any>>('/projects', data),
  
  update: (id: string, data: { name?: string; description?: string; status?: string }) =>
    api.put<ApiResponse<any>>(`/projects/${id}`, data),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/projects/${id}`),
}

// Uploads API
export const uploadsApi = {
  initiate: (data: { fileName: string; fileSize: number; fileType: string; projectId: string }) =>
    api.post<ApiResponse<{ uploadId: string; uploadUrl: string; key: string; expiresIn: number }>>('/uploads/initiate', data),
  
  finalize: (data: { key: string; fileName: string; fileType: string }) =>
    api.post<ApiResponse<any>>('/uploads/finalize', data),
  
  get: (id: string) =>
    api.get<ApiResponse<any>>(`/uploads/${id}`),
  
  listByProject: (projectId: string, params?: { page?: number; limit?: number; sortBy?: string; sortOrder?: string }) =>
    api.get<PaginatedResponse<any>>(`/uploads/project/${projectId}`, { params }),
  
  delete: (id: string) =>
    api.delete<ApiResponse>(`/uploads/${id}`),
}

// Pricing Models API
export const modelsApi = {
  listByProject: (projectId: string, params?: { page?: number; limit?: number; sortBy?: string; sortOrder?: string }) =>
    api.get<PaginatedResponse<any>>(`/models/project/${projectId}`, { params }),

  get: (id: string) =>
    api.get<ApiResponse<any>>(`/models/${id}`),

  create: (data: { name: string; description?: string; modelType: string; projectId: string }) =>
    api.post<ApiResponse<any>>('/models', data),

  update: (id: string, data: { name?: string; description?: string; modelType?: string }) =>
    api.put<ApiResponse<any>>(`/models/${id}`, data),

  delete: (id: string) =>
    api.delete<ApiResponse>(`/models/${id}`),

  // Model components
  addComponent: (modelId: string, data: { componentType: string; config: any; sortOrder?: number }) =>
    api.post<ApiResponse<any>>(`/models/${modelId}/components`, data),

  updateComponent: (modelId: string, componentId: string, data: { config: any; sortOrder?: number }) =>
    api.put<ApiResponse<any>>(`/models/${modelId}/components/${componentId}`, data),

  deleteComponent: (modelId: string, componentId: string) =>
    api.delete<ApiResponse>(`/models/${modelId}/components/${componentId}`),
}

// Simulations API
export const simulationsApi = {
  listByProject: (projectId: string, params?: { page?: number; limit?: number; sortBy?: string; sortOrder?: string }) =>
    api.get<PaginatedResponse<any>>(`/simulations/project/${projectId}`, { params }),

  get: (id: string) =>
    api.get<ApiResponse<any>>(`/simulations/${id}`),

  create: (data: { name?: string; modelIds: string[]; projectId: string; scenarioConfig?: any }) =>
    api.post<ApiResponse<any>>('/simulations', data),

  getStatus: (id: string) =>
    api.get<ApiResponse<any>>(`/simulations/${id}/status`),

  cancel: (id: string) =>
    api.post<ApiResponse>(`/simulations/${id}/cancel`),

  delete: (id: string) =>
    api.delete<ApiResponse>(`/simulations/${id}`),

  // Results and analytics
  getResults: (id: string) =>
    api.get<ApiResponse<any>>(`/simulations/${id}/results`),

  getAnalytics: (id: string) =>
    api.get<ApiResponse<any>>(`/simulations/${id}/analytics`),

  exportResults: (id: string, format: 'csv' | 'xlsx' | 'pdf') =>
    api.get(`/simulations/${id}/export?format=${format}`, { responseType: 'blob' }),
}

// File upload helper
export const uploadFile = async (file: File, uploadUrl: string): Promise<void> => {
  await axios.put(uploadUrl, file, {
    headers: {
      'Content-Type': 'application/octet-stream',
    },
    timeout: 300000, // 5 minutes for large files
  })
}

export default api
