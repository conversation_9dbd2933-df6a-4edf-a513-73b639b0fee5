import { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { checkEmailVerification } from '@/lib/auth';
import { FullScreenLoading } from '@/components/ui/loading';
import { usePageVisibility } from '@/hooks/usePageVisibility';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const [redirected, setRedirected] = useState(false);
  const isVisible = usePageVisibility();
  const initialLoadComplete = useRef(false);
  const [showLoading, setShowLoading] = useState(isLoading);

  useEffect(() => {
    console.log('ProtectedRoute - user:', user ? 'exists' : 'null', 'isLoading:', isLoading, 'redirected:', redirected);

    // Set a timeout to prevent infinite loading
    const timer = setTimeout(() => {
      if (isLoading) {
        console.log('Loading timeout reached, showing timeout message');
        setLoadingTimeout(true);
      }
    }, 5000); // 5 seconds timeout

    // Only redirect if we're not loading, user is null, and we haven't redirected yet
    if (!isLoading && user === null && !redirected) {
      console.log('No user and not loading, redirecting to login');
      setRedirected(true); // Prevent multiple redirects
      navigate('/login', {
        replace: true,
        state: { message: 'Please log in to access this page' }
      });
    }

    // Check for session in localStorage to detect unverified email cases
    const checkForUnverifiedSession = async () => {
      try {
        // If we're still loading after 3 seconds, check if there might be an unverified email issue
        if (isLoading && !loadingTimeout) {
          const { data } = await supabase.auth.getSession();
          if (data.session?.user) {
            // First check if email is not confirmed in the session
            if (!data.session.user.email_confirmed_at) {
              console.warn('Found session with unverified email');
              setRedirected(true);
              navigate('/verify-email', {
                replace: true,
                state: { email: data.session.user.email }
              });
              return;
            }

            // Double-check with our verification function to be sure
            const email = data.session.user.email;
            if (email) {
              const { verified } = await checkEmailVerification(email);
              if (!verified) {
                console.warn('Email verification check failed for:', email);
                setRedirected(true);
                navigate('/verify-email', {
                  replace: true,
                  state: { email: email }
                });
              }
            }
          }
        }
      } catch (error) {
        console.error('Error checking for unverified session:', error);
      }
    };

    // Set a timeout to check for unverified email after 3 seconds of loading
    const unverifiedCheckTimer = setTimeout(checkForUnverifiedSession, 3000);

    // If user exists, reset the redirected flag
    if (user !== null) {
      setRedirected(false);
    }

    return () => {
      clearTimeout(timer);
      clearTimeout(unverifiedCheckTimer);
    };
  }, [user, isLoading, navigate, redirected, loadingTimeout]);

  // Only show loading on initial load, not when switching tabs
  useEffect(() => {
    // If this is the initial load and we're loading, show the loading screen
    if (isLoading && !initialLoadComplete.current && !loadingTimeout) {
      setShowLoading(true);
    } else if (!isLoading) {
      // Once loading is complete, mark initial load as done and hide loading
      initialLoadComplete.current = true;
      setShowLoading(false);
    }
  }, [isLoading, loadingTimeout]);

  // When page becomes visible again (tab switch), don't show loading
  useEffect(() => {
    if (isVisible && initialLoadComplete.current) {
      setShowLoading(false);
    }
  }, [isVisible]);

  if (showLoading && !loadingTimeout) {
    return <FullScreenLoading text="Loading your dashboard..." variant="spinner" />;
  }

  // If loading takes too long, show a message and provide a way to go back to login
  if (isLoading && loadingTimeout) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-dark-950 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Top gradient */}
          <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent z-10" />

          {/* Animated background shapes */}
          <div className="geometric-shape geometric-shape-1"></div>
          <div className="geometric-shape geometric-shape-2"></div>
        </div>

        {/* Content */}
        <div className="z-10 flex flex-col items-center justify-center p-8">
          <div className="glass-card p-8 flex flex-col items-center justify-center relative max-w-md">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-cool-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>

            <div className="relative">
              <div className="h-16 w-16 border-4 border-fiery/30 border-t-fiery rounded-full animate-spin"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-8 w-8 bg-fiery rounded-full opacity-80"></div>
              </div>
            </div>

            <h3 className="mt-6 text-xl font-semibold text-white">Taking longer than expected</h3>
            <p className="mt-2 text-white/70 text-center">We're having trouble loading your dashboard.</p>
            <p className="mt-2 text-white/50 text-sm text-center">This could be due to authentication issues or network problems.</p>

            <div className="mt-6 space-y-3 w-full">
              <button
                onClick={() => {
                  navigate('/login', {
                    replace: true,
                    state: { message: 'Authentication timed out. Please log in again.' }
                  });
                }}
                className="w-full px-4 py-2 bg-fiery text-white rounded-md hover:bg-fiery-600 transition-colors"
              >
                Return to login
              </button>
              <button
                onClick={() => {
                  setLoadingTimeout(false);
                  window.location.reload();
                }}
                className="w-full px-4 py-2 firenest-card text-white/80 rounded-md transition-colors"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Only render children if user is definitely logged in
  return user !== null ? <>{children}</> : null;
};

export default ProtectedRoute;
