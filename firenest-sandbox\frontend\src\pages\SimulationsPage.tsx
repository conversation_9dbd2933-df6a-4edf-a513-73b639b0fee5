/**
 * Simulations Page
 * Phase 3: Simulation management and execution
 */

import { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {
  Play,
  Plus,
  ArrowLeft,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Trash2
} from 'lucide-react'
import { simulationsApi, projectsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { SimulationBuilder } from '@/components/simulations/SimulationBuilder'
import { SimulationProgress } from '@/components/simulations/SimulationProgress'
import { SimulationResults } from '@/components/simulations/SimulationResults'
import { formatDate } from '@/lib/utils'

export function SimulationsPage() {
  const { projectId } = useParams<{ projectId: string }>()
  const navigate = useNavigate()
  const [showBuilder, setShowBuilder] = useState(false)
  const [selectedSimulation, setSelectedSimulation] = useState<any>(null)
  const [viewMode, setViewMode] = useState<'list' | 'progress' | 'results'>('list')

  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: ['projects', projectId],
    queryFn: () => projectsApi.get(projectId!),
    enabled: !!projectId
  })

  const { data: simulations, isLoading: simulationsLoading, refetch } = useQuery({
    queryKey: ['simulations', projectId],
    queryFn: () => simulationsApi.listByProject(projectId!, { sortBy: 'created_at', sortOrder: 'desc' }),
    enabled: !!projectId
  })

  if (projectLoading || simulationsLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!project?.data?.data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white mb-2">Project not found</h2>
          <Button variant="outline" onClick={() => navigate('/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  const projectData = project.data.data
  const simulationsList = simulations?.data?.data || []

  // Handle different view modes
  if (showBuilder) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container-responsive py-8">
          <SimulationBuilder
            projectId={projectId!}
            onSimulationCreated={(simulation) => {
              setSelectedSimulation(simulation)
              setShowBuilder(false)
              setViewMode('progress')
              refetch()
            }}
            onCancel={() => setShowBuilder(false)}
          />
        </div>
      </div>
    )
  }

  if (viewMode === 'progress' && selectedSimulation) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container-responsive py-8">
          <SimulationProgress
            simulationId={selectedSimulation.id}
            onComplete={() => setViewMode('results')}
            onCancel={() => {
              setViewMode('list')
              setSelectedSimulation(null)
            }}
          />
        </div>
      </div>
    )
  }

  if (viewMode === 'results' && selectedSimulation) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container-responsive py-8">
          <SimulationResults
            simulationId={selectedSimulation.id}
            onBack={() => {
              setViewMode('list')
              setSelectedSimulation(null)
            }}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur">
        <div className="container-responsive py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/projects/${projectId}`)}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Project
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Simulations</h1>
                <p className="text-gray-400">{projectData.name}</p>
              </div>
            </div>

            <Button onClick={() => setShowBuilder(true)} variant="fiery">
              <Plus className="w-4 h-4 mr-2" />
              New Simulation
            </Button>
          </div>
        </div>
      </div>

      <div className="container-responsive py-8">
        {/* Project Status Check */}
        {projectData.status !== 'READY' && projectData.status !== 'COMPLETE' && (
          <div className="firenest-card mb-8 border-l-4 border-yellow-500">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <Play className="w-4 h-4 text-yellow-400" />
              </div>
              <div>
                <h3 className="text-white font-medium">Data and Models Required</h3>
                <p className="text-gray-400 text-sm">
                  Upload data and create pricing models before running simulations.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Simulations List */}
        {simulationsList.length === 0 ? (
          <div className="firenest-card">
            <div className="text-center py-12">
              <Play className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No simulations yet</h3>
              <p className="text-gray-400 mb-6">
                Run your first simulation to analyze revenue impact across pricing models
              </p>
              <Button
                onClick={() => setShowBuilder(true)}
                variant="fiery"
                disabled={projectData.status !== 'READY' && projectData.status !== 'COMPLETE'}
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Simulation
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {simulationsList.map((simulation: any) => (
              <SimulationCard
                key={simulation.id}
                simulation={simulation}
                onView={(sim) => {
                  setSelectedSimulation(sim)
                  if (sim.status === 'COMPLETE') {
                    setViewMode('results')
                  } else if (sim.status === 'RUNNING' || sim.status === 'QUEUED') {
                    setViewMode('progress')
                  } else {
                    setViewMode('results') // Show results even for failed simulations
                  }
                }}
                onDelete={() => {
                  // TODO: Implement delete confirmation
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

interface SimulationCardProps {
  simulation: any
  onView: (simulation: any) => void
  onDelete: () => void
}

function SimulationCard({ simulation, onView, onDelete }: SimulationCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'QUEUED':
        return Clock
      case 'RUNNING':
        return Play
      case 'COMPLETE':
        return CheckCircle
      case 'FAILED':
      case 'CANCELLED':
        return XCircle
      default:
        return Clock
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'QUEUED':
        return 'text-yellow-400'
      case 'RUNNING':
        return 'text-blue-400'
      case 'COMPLETE':
        return 'text-green-400'
      case 'FAILED':
      case 'CANCELLED':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const StatusIcon = getStatusIcon(simulation.status)
  const statusColor = getStatusColor(simulation.status)

  return (
    <div className="firenest-card-interactive">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-fiery/20 rounded-lg flex items-center justify-center">
            <StatusIcon className={`w-5 h-5 ${statusColor}`} />
          </div>
          <div>
            <h3 className="text-white font-semibold">
              {simulation.name || `Simulation ${simulation.id.slice(0, 8)}`}
            </h3>
            <Badge
              variant={simulation.status === 'COMPLETE' ? 'success' :
                      simulation.status === 'FAILED' ? 'destructive' : 'secondary'}
              className="text-xs"
            >
              {simulation.status}
            </Badge>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" onClick={() => onView(simulation)}>
            <Eye className="w-3 h-3" />
          </Button>
          <Button variant="ghost" size="sm" onClick={onDelete} className="text-red-400">
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Progress Bar for Running Simulations */}
      {simulation.status === 'RUNNING' && (
        <div className="mb-4">
          <div className="flex justify-between text-xs mb-1">
            <span className="text-gray-400">Progress</span>
            <span className="text-white">{simulation.progress_percentage}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1">
            <div
              className="bg-blue-400 h-1 rounded-full transition-all duration-300"
              style={{ width: `${simulation.progress_percentage}%` }}
            />
          </div>
        </div>
      )}

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{simulation.model_count || 0}</div>
          <div className="text-xs text-gray-400">Models</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-white">{simulation.result_count || 0}</div>
          <div className="text-xs text-gray-400">Results</div>
        </div>
      </div>

      <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
        <span>Created {formatDate(simulation.created_at)}</span>
        {simulation.completed_at && (
          <span>Completed {formatDate(simulation.completed_at)}</span>
        )}
      </div>

      <div className="flex space-x-2">
        <Button variant="outline" size="sm" onClick={() => onView(simulation)} className="flex-1">
          {simulation.status === 'COMPLETE' ? (
            <>
              <BarChart3 className="w-3 h-3 mr-1" />
              View Results
            </>
          ) : simulation.status === 'RUNNING' || simulation.status === 'QUEUED' ? (
            <>
              <Eye className="w-3 h-3 mr-1" />
              View Progress
            </>
          ) : (
            <>
              <Eye className="w-3 h-3 mr-1" />
              View Details
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

export function SimulationDetailPage() {
  const { simulationId } = useParams<{ simulationId: string }>()
  const navigate = useNavigate()

  const { data: simulation, isLoading } = useQuery({
    queryKey: ['simulation', simulationId],
    queryFn: () => simulationsApi.get(simulationId!),
    enabled: !!simulationId
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!simulation?.data?.data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white mb-2">Simulation not found</h2>
          <Button variant="outline" onClick={() => navigate('/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  const simulationData = simulation.data.data

  if (simulationData.status === 'COMPLETE') {
    return (
      <div className="min-h-screen bg-background">
        <div className="container-responsive py-8">
          <SimulationResults
            simulationId={simulationId!}
            onBack={() => navigate(`/projects/${simulationData.project_id}/simulations`)}
          />
        </div>
      </div>
    )
  } else {
    return (
      <div className="min-h-screen bg-background">
        <div className="container-responsive py-8">
          <SimulationProgress
            simulationId={simulationId!}
            onComplete={() => window.location.reload()}
            onCancel={() => navigate(`/projects/${simulationData.project_id}/simulations`)}
          />
        </div>
      </div>
    )
  }
}
