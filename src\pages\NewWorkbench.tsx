import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSearchParams, Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuGroup } from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';

// Import custom components
import ToolDetails from '@/components/workbench/ToolDetails';
import ToolLaunch from '@/components/workbench/ToolLaunch';
import CategoryFilter from '@/components/workbench/CategoryFilter';
import ToolComparison from '@/components/workbench/ToolComparison';
import ToolCollections from '@/components/workbench/ToolCollections';
import ToolHistory from '@/components/workbench/ToolHistory';
import ToolRecommendations from '@/components/workbench/ToolRecommendations';
import {
  Search,
  Filter,
  Layers,
  Zap,
  Star,
  CheckCircle,
  RefreshCw,
  MoreHorizontal,
  Clock,
  Heart,
  Image,
  MessageSquare,
  Headphones,
  Code,
  FileText,
  Video,
  Bookmark,
  Info,
  ExternalLink,
  History,
  Settings,
  ChevronRight,
  ArrowUpRight,
  TrendingUp,
  Sparkles,
  CreditCard,
  Plus,
  X,
  ChevronDown,
  Tag,
  BarChart3
} from 'lucide-react';

import { notify } from '@/components/ui/notification-system';

/**
 * Enhanced Workbench Page with professional design patterns
 * Inspired by industry leaders like Zapier and HubSpot
 */
const NewWorkbench = () => {
  const { credits, user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Tools');
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [recentlyUsed, setRecentlyUsed] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedTool, setSelectedTool] = useState<any>(null);
  const [launchTool, setLaunchTool] = useState<any>(null);
  const [compareTools, setCompareTools] = useState<any[]>([]);
  const [selectedSection, setSelectedSection] = useState<'browse' | 'collections' | 'history' | 'recommendations'>('browse');

  // Mock data for tools
  const mockTools = [
    {
      id: 'chatgpt',
      name: 'ChatGPT',
      description: 'Advanced AI assistant for text generation and conversation',
      icon: 'Bot',
      category: 'Text Generation',
      tags: ['AI', 'Text', 'Assistant', 'OpenAI'],
      rating: 4.8,
      popular: true,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 5,
        currency: 'USD'
      }
    },
    {
      id: 'midjourney',
      name: 'Midjourney',
      description: 'AI image generation from text prompts',
      icon: 'Image',
      category: 'Image Generation',
      tags: ['AI', 'Image', 'Art', 'Creative'],
      rating: 4.7,
      popular: true,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 10,
        currency: 'USD'
      }
    },
    {
      id: 'claude',
      name: 'Claude',
      description: 'Anthropic\'s AI assistant for helpful, harmless, and honest conversations',
      icon: 'Bot',
      category: 'Text Generation',
      tags: ['AI', 'Text', 'Assistant', 'Anthropic'],
      rating: 4.6,
      popular: true,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 5,
        currency: 'USD'
      }
    },
    {
      id: 'dalle',
      name: 'DALL-E',
      description: 'OpenAI\'s image generation model',
      icon: 'Image',
      category: 'Image Generation',
      tags: ['AI', 'Image', 'Art', 'OpenAI'],
      rating: 4.5,
      popular: false,
      new: true,
      status: 'live',
      pricing: {
        costPerUnit: 8,
        currency: 'USD'
      }
    },
    {
      id: 'stable-diffusion',
      name: 'Stable Diffusion',
      description: 'Open-source image generation model',
      icon: 'Image',
      category: 'Image Generation',
      tags: ['AI', 'Image', 'Art', 'Open Source'],
      rating: 4.4,
      popular: false,
      new: false,
      status: 'live',
      pricing: {
        costPerUnit: 6,
        currency: 'USD'
      }
    },
    {
      id: 'whisper',
      name: 'Whisper',
      description: 'Speech-to-text transcription and translation',
      icon: 'Audio',
      category: 'Audio Processing',
      tags: ['AI', 'Audio', 'Transcription', 'OpenAI'],
      rating: 4.3,
      popular: false,
      new: true,
      status: 'live',
      pricing: {
        costPerUnit: 4,
        currency: 'USD'
      }
    }
  ];

  // Check for tool parameter in URL
  useEffect(() => {
    const toolParam = searchParams.get('tool');
    if (toolParam) {
      setSearchQuery(toolParam);
    }
  }, [searchParams]);

  // Simulate loading state and initialize favorites/recent
  useEffect(() => {
    // Mock data for favorites and recently used
    setFavorites(['chatgpt', 'midjourney']);
    setRecentlyUsed(['chatgpt', 'dalle', 'claude']);

    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Filter tools based on search and category
  const filteredTools = mockTools.filter(tool => {
    const matchesSearch =
      tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = selectedCategory === 'All Tools' || tool.category === selectedCategory;

    const matchesTab =
      (activeTab === 'all') ||
      (activeTab === 'popular' && tool.popular) ||
      (activeTab === 'new' && tool.new);

    return matchesSearch && matchesCategory && matchesTab;
  });

  // Get unique categories
  const categories = ['All Tools', ...new Set(mockTools.map(tool => tool.category))];

  // Toggle favorite status
  const toggleFavorite = (toolId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent card click

    setFavorites(prev => {
      if (prev.includes(toolId)) {
        notify.success(`Removed ${mockTools.find(t => t.id === toolId)?.name} from favorites`, {
          duration: 3000
        });
        return prev.filter(id => id !== toolId);
      } else {
        notify.success(`Added ${mockTools.find(t => t.id === toolId)?.name} to favorites`, {
          duration: 3000
        });
        return [...prev, toolId];
      }
    });
  };

  // View tool details
  const handleViewDetails = (toolId: string) => {
    const tool = mockTools.find(t => t.id === toolId);
    if (!tool) return;
    setSelectedTool(tool);
  };

  // Close tool details
  const handleCloseDetails = () => {
    setSelectedTool(null);
  };

  // Open tool launch modal
  const handleOpenLaunch = (toolId: string) => {
    const tool = mockTools.find(t => t.id === toolId);
    if (!tool) return;

    if (credits?.availableCredits < tool.pricing.costPerUnit) {
      notify.error('Not enough credits to launch this tool', {
        title: 'Insufficient Credits',
        duration: 4000
      });
      return;
    }

    setLaunchTool(tool);
  };

  // Close tool launch modal
  const handleCloseLaunch = () => {
    setLaunchTool(null);
  };

  // Handle launching a tool
  const handleLaunch = (toolId: string, options?: any) => {
    const tool = mockTools.find(t => t.id === toolId);
    if (!tool) return;

    // Get usage model and credit limit from options
    const usageModel = options?.usageModel || 'fixed';
    const creditLimit = options?.creditLimit || null;

    // Check if user has enough credits based on usage model
    if (usageModel === 'fixed' && credits?.availableCredits < tool.pricing.costPerUnit) {
      notify.error('Not enough credits to launch this tool', {
        title: 'Insufficient Credits',
        duration: 4000
      });
      return;
    }

    // For dynamic pricing with a limit, check if user has enough credits for the limit
    if (usageModel === 'dynamic' && creditLimit && credits?.availableCredits < creditLimit) {
      notify.error('Your credit limit exceeds your available credits', {
        title: 'Insufficient Credits',
        duration: 4000
      });
      return;
    }

    // Add to recently used
    setRecentlyUsed(prev => {
      const newRecent = prev.filter(id => id !== toolId);
      return [toolId, ...newRecent].slice(0, 5); // Keep only 5 most recent
    });

    // Close the launch modal if it's open
    setLaunchTool(null);

    // In a real implementation, this would redirect to the tool's platform
    // with authentication and tracking parameters
    const toolUrl = getToolRedirectUrl(tool, usageModel, creditLimit);

    // Open the tool in a new tab
    window.open(toolUrl, '_blank');

    // Show appropriate success message based on usage model
    if (usageModel === 'fixed') {
      notify.success(`Successfully redirected to ${tool.name} with fixed pricing (${tool.pricing.costPerUnit} credits)`, {
        title: 'Tool Launched',
        duration: 4000
      });
    } else {
      const limitMessage = creditLimit ? ` (Max: ${creditLimit} credits)` : '';
      notify.success(`Successfully redirected to ${tool.name} with pay-per-use pricing${limitMessage}`, {
        title: 'Tool Launched',
        duration: 4000
      });
    }
  };

  // Get the redirect URL for a tool
  const getToolRedirectUrl = (tool: any, usageModel: string = 'fixed', creditLimit: number | null = null) => {
    // In a real implementation, this would generate a URL with authentication tokens
    // and tracking parameters for the specific tool

    // Mock URLs for demonstration
    const mockUrls: Record<string, string> = {
      'chatgpt': 'https://chat.openai.com',
      'dalle': 'https://labs.openai.com',
      'claude': 'https://claude.ai',
      'midjourney': 'https://www.midjourney.com',
      'whisper': 'https://platform.openai.com/playground/audio',
    };

    // Get base URL
    const baseUrl = mockUrls[tool.id] || 'https://example.com/redirect';

    // Add tracking and authentication parameters
    let url = `${baseUrl}?tool=${tool.id}&tracking=true&auth=firenest_user`;

    // Add usage model parameters
    url += `&usageModel=${usageModel}`;

    // Add credit limit if applicable
    if (usageModel === 'dynamic' && creditLimit) {
      url += `&creditLimit=${creditLimit}`;
    }

    // Add timestamp for tracking
    url += `&timestamp=${Date.now()}`;

    return url;
  };

  // Get appropriate icon for tool category
  const getToolIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Get icon for category in dropdown menu
  const getCategoryIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-4 w-4 mr-2" />;
      case 'Image Generation':
        return <Image className="h-4 w-4 mr-2" />;
      case 'Audio Processing':
        return <Headphones className="h-4 w-4 mr-2" />;
      case 'Code Generation':
        return <Code className="h-4 w-4 mr-2" />;
      case 'Document Processing':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'All Tools':
        return <Layers className="h-4 w-4 mr-2" />;
      case 'Popular':
        return <Star className="h-4 w-4 mr-2" />;
      case 'New':
        return <Sparkles className="h-4 w-4 mr-2" />;
      case 'Recently Used':
        return <Clock className="h-4 w-4 mr-2" />;
      default:
        return <Zap className="h-4 w-4 mr-2" />;
    }
  };

  // Render enhanced tool card with improved visual hierarchy
  const renderToolCard = (tool: any) => {
    const isFavorite = favorites.includes(tool.id);
    const isRecent = recentlyUsed.includes(tool.id);

    // Determine status color for the top accent
    const getStatusColor = () => {
      if (tool.new) return "from-blue-500";
      if (tool.popular) return "from-purple-500";
      if (isRecent) return "from-teal-500";
      return "from-fiery";
    };

    return (
      <Card
        key={tool.id}
        className="firenest-card cursor-pointer group overflow-hidden relative border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
        onClick={() => handleViewDetails(tool.id)}
      >
        {/* Top accent gradient */}
        <div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r opacity-80 to-transparent w-full"
             style={{ backgroundImage: `linear-gradient(to right, var(--${getStatusColor().split('-')[1]}-500), transparent)` }}></div>

        {/* Favorite button - positioned absolutely in top right */}
        <div className="absolute top-3 right-3 z-10">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className={`flex items-center justify-center h-8 w-8 rounded-full backdrop-blur-sm transition-all duration-200 ${
                    isFavorite
                      ? 'bg-fiery/20 text-fiery shadow-md'
                      : 'bg-white/5 text-white/50 hover:text-white hover:bg-white/10'
                  }`}
                  onClick={(e) => toggleFavorite(tool.id, e)}
                >
                  <Heart className={`h-4 w-4 ${isFavorite ? 'fill-fiery' : ''} transition-all duration-300 ${isFavorite ? 'scale-110' : ''}`} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isFavorite ? 'Remove from favorites' : 'Add to favorites'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <CardContent className="p-5 pt-6">
          {/* Header with icon, name and badges */}
          <div className="flex items-start mb-4 mt-2">
            <div className="flex items-center gap-3 w-full">
              <div className="h-12 w-12 rounded-md bg-gradient-to-br from-fiery/30 to-fiery/10 flex items-center justify-center shadow-sm group-hover:shadow group-hover:from-fiery/40 group-hover:to-fiery/20 transition-all duration-300">
                {getToolIcon(tool.category)}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-white text-base group-hover:text-fiery transition-colors">{tool.name}</h3>
                  <div className="flex items-center gap-1.5">
                    {tool.new && (
                      <Badge className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 border-none text-xs">New</Badge>
                    )}
                    {tool.popular && (
                      <Badge className="bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 border-none text-xs">Popular</Badge>
                    )}
                    {isRecent && !tool.new && !tool.popular && (
                      <Badge className="bg-teal-500/20 text-teal-400 hover:bg-teal-500/30 border-none text-xs">Recent</Badge>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-3 mt-1">
                  <p className="text-xs text-white/70">{tool.category}</p>
                  <div className="flex items-center">
                    <div className="flex items-center bg-yellow-500/10 rounded-full px-1.5 py-0.5">
                      <Star className="h-3 w-3 text-yellow-400 mr-1" />
                      <span className="text-xs text-yellow-300">{tool.rating.toFixed(1)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="text-sm text-white/80 mb-5 line-clamp-2 min-h-[40px]">{tool.description}</p>

          {/* Features and tags */}
          <div className="flex flex-wrap gap-2 mb-5">
            {tool.tags.slice(0, 3).map((tag: string, index: number) => (
              <Badge key={index} className="bg-dark-700/50 text-white/70 hover:bg-dark-700 border-none text-xs px-2.5 py-0.5 rounded-full">
                {tag}
              </Badge>
            ))}
          </div>

          {/* Pricing and status */}
          <div className="flex items-center justify-between text-xs text-white/70 mb-4 border-t border-white/10 pt-4">
            <div className="flex items-center bg-fiery/10 rounded-full px-2 py-1">
              <Zap className="h-3.5 w-3.5 mr-1.5 text-fiery" />
              <span className="font-medium text-fiery">{tool.pricing.costPerUnit} credits/use</span>
            </div>
            <div className="flex items-center">
              <div className="flex items-center gap-1 bg-green-500/10 rounded-full px-2 py-1">
                <div className="h-2 w-2 rounded-full bg-green-400"></div>
                <span className="text-xs text-green-400">Ready</span>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              className="border-white/10 hover:bg-white/5 text-white/80 transition-all duration-300 hover:border-white/30"
              onClick={(e) => {
                e.stopPropagation();
                handleViewDetails(tool.id);
              }}
            >
              <Info className="h-3.5 w-3.5 mr-1.5" />
              Details
            </Button>

            <Button
              size="sm"
              className="bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-600 hover:to-fiery-700 shadow-sm hover:shadow transition-all duration-300"
              onClick={(e) => {
                e.stopPropagation();
                handleOpenLaunch(tool.id);
              }}
            >
              <Zap className="h-3.5 w-3.5 mr-1.5" />
              Launch
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render enhanced tool card in list view
  const renderToolCardList = (tool: any) => {
    const isFavorite = favorites.includes(tool.id);
    const isRecent = recentlyUsed.includes(tool.id);

    // Determine status color for the left accent
    const getStatusColor = () => {
      if (tool.new) return "from-blue-500";
      if (tool.popular) return "from-purple-500";
      if (isRecent) return "from-teal-500";
      return "from-fiery";
    };

    return (
      <Card
        key={tool.id}
        className="firenest-card cursor-pointer group overflow-hidden relative border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5"
        onClick={() => handleViewDetails(tool.id)}
      >
        {/* Left accent gradient */}
        <div className="absolute left-0 top-0 bottom-0 w-1.5 bg-gradient-to-b opacity-80 to-transparent h-full"
             style={{ backgroundImage: `linear-gradient(to bottom, var(--${getStatusColor().split('-')[1]}-500), transparent)` }}></div>

        {/* Subtle gradient effect */}
        <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-fiery/5 to-transparent rounded-full -translate-y-1/2 translate-x-1/2 blur-xl pointer-events-none"></div>

        <CardContent className="p-4 pl-6 flex items-center">
          <div className="flex items-center gap-4 flex-1">
            <div className="h-12 w-12 rounded-md bg-gradient-to-br from-fiery/30 to-fiery/10 flex items-center justify-center flex-shrink-0 shadow-sm group-hover:shadow group-hover:from-fiery/40 group-hover:to-fiery/20 transition-all duration-300">
              {getToolIcon(tool.category)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-medium text-white group-hover:text-fiery transition-colors">{tool.name}</h3>
                <div className="flex items-center gap-1.5">
                  {tool.new && (
                    <Badge className="bg-blue-500/20 text-blue-400 border-none text-xs">New</Badge>
                  )}
                  {tool.popular && (
                    <Badge className="bg-purple-500/20 text-purple-400 border-none text-xs">Popular</Badge>
                  )}
                  {isRecent && !tool.new && !tool.popular && (
                    <Badge className="bg-teal-500/20 text-teal-400 border-none text-xs">Recent</Badge>
                  )}
                </div>
              </div>
              <p className="text-sm text-white/80 line-clamp-1 mb-2">{tool.description}</p>
              <div className="flex items-center gap-4 text-xs">
                <div className="flex items-center bg-fiery/10 rounded-full px-2 py-0.5">
                  <Zap className="h-3 w-3 mr-1 text-fiery" />
                  <span className="text-fiery">{tool.pricing.costPerUnit} credits</span>
                </div>
                <div className="flex items-center bg-yellow-500/10 rounded-full px-2 py-0.5">
                  <Star className="h-3 w-3 mr-1 text-yellow-400" />
                  <span className="text-yellow-300">{tool.rating.toFixed(1)}</span>
                </div>
                <div className="flex items-center bg-green-500/10 rounded-full px-2 py-0.5">
                  <div className="h-2 w-2 rounded-full mr-1 bg-green-400" />
                  <span className="text-green-400">Ready</span>
                </div>
                <div className="flex items-center text-white/60">
                  <span className="text-xs">{tool.category}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 flex-shrink-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className={`flex items-center justify-center h-8 w-8 rounded-full backdrop-blur-sm transition-all duration-200 ${
                      isFavorite
                        ? 'bg-fiery/20 text-fiery shadow-md'
                        : 'bg-white/5 text-white/50 hover:text-white hover:bg-white/10'
                    }`}
                    onClick={(e) => toggleFavorite(tool.id, e)}
                  >
                    <Heart className={`h-4 w-4 ${isFavorite ? 'fill-fiery' : ''} transition-all duration-300 ${isFavorite ? 'scale-110' : ''}`} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isFavorite ? 'Remove from favorites' : 'Add to favorites'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              variant="outline"
              size="sm"
              className="border-white/10 hover:bg-white/5 text-white/80 transition-all duration-300 hover:border-white/30"
              onClick={(e) => {
                e.stopPropagation();
                handleViewDetails(tool.id);
              }}
            >
              <Info className="h-3.5 w-3.5 mr-1.5" />
              Details
            </Button>

            <Button
              size="sm"
              className="bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-600 hover:to-fiery-700 shadow-sm hover:shadow transition-all duration-300"
              onClick={(e) => {
                e.stopPropagation();
                handleOpenLaunch(tool.id);
              }}
            >
              <Zap className="h-3.5 w-3.5 mr-1.5" />
              Launch
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Tool details modal */}
      {selectedTool && (
        <ToolDetails
          tool={selectedTool}
          onBack={handleCloseDetails}
          onLaunch={handleOpenLaunch}
          onToggleFavorite={toggleFavorite}
          isFavorite={favorites.includes(selectedTool.id)}
        />
      )}

      {/* Tool launch modal */}
      {launchTool && (
        <ToolLaunch
          tool={launchTool}
          onClose={handleCloseLaunch}
          onLaunch={handleLaunch}
          availableCredits={credits?.availableCredits || 0}
        />
      )}

      {/* Tool comparison modal */}
      {compareTools.length > 0 && (
        <ToolComparison
          tools={compareTools}
          onClose={() => setCompareTools([])}
          onLaunch={handleOpenLaunch}
        />
      )}

      {!selectedTool && (<>
      {/* Enhanced Header with Breadcrumbs and Stats */}
      <div className="space-y-4">
        {/* Breadcrumbs */}
        <div className="flex items-center text-sm text-white/50 mb-2">
          <Link to="/dashboard" className="hover:text-white transition-colors">Dashboard</Link>
          <ChevronRight className="h-3 w-3 mx-1" />
          <span className="text-white">Workbench</span>
        </div>

        {/* Main Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-white">AI Tools Workbench</h1>
            <p className="text-white/70">Access and launch all your AI tools in one place</p>
          </div>

          <div className="flex items-center gap-4">
            {/* Connection Status Indicator */}
            <div className="hidden md:flex items-center">
              <HoverCard openDelay={200} closeDelay={100}>
                <HoverCardTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-white/10 bg-dark-800/50 hover:bg-white/5 rounded-full transition-all duration-300 hover:border-white/30"
                      >
                        <div className="flex items-center">
                          <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-white/70">Connected</span>
                        </div>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64 firenest-card border border-white/10 text-white p-0">
                      <div className="p-4 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-transparent">
                        <h4 className="font-medium">Connected Services</h4>
                      </div>
                      <div className="p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                            <span>OpenAI</span>
                          </div>
                          <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30 border-none text-xs">OAuth</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                            <span>Anthropic</span>
                          </div>
                          <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30 border-none text-xs">OAuth</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></div>
                            <span>Midjourney</span>
                          </div>
                          <Badge className="bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30 border-none text-xs">API Key</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="h-2 w-2 rounded-full bg-red-500 mr-2"></div>
                            <span>GitHub</span>
                          </div>
                          <Badge className="bg-red-500/20 text-red-400 hover:bg-red-500/30 border-none text-xs">Not Connected</Badge>
                        </div>
                        <div className="pt-3 mt-2 border-t border-white/10">
                          <Button
                            size="sm"
                            className="w-full bg-blue-500 hover:bg-blue-600"
                            onClick={() => navigate('/dashboard/connections')}
                          >
                            Manage Connections
                          </Button>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </HoverCardTrigger>
                <HoverCardContent className="w-64 firenest-card border border-white/10 text-white p-0">
                  <div className="p-4 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-transparent">
                    <h4 className="font-medium">Connected Services</h4>
                  </div>
                  <div className="p-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                        <span>OpenAI</span>
                      </div>
                      <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30 border-none text-xs">OAuth</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                        <span>Anthropic</span>
                      </div>
                      <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30 border-none text-xs">OAuth</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></div>
                        <span>Midjourney</span>
                      </div>
                      <Badge className="bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30 border-none text-xs">API Key</Badge>
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            </div>

            {/* Usage Analytics Button */}
            <div className="hidden md:flex items-center">
              <HoverCard openDelay={200} closeDelay={100}>
                <HoverCardTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-white/10 bg-dark-800/50 hover:bg-white/5 rounded-full transition-all duration-300 hover:border-white/30"
                      >
                        <BarChart3 className="h-3.5 w-3.5 mr-1.5 text-teal-400" />
                        <span className="text-white/70">Usage</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64 firenest-card border border-white/10 text-white p-0">
                      <div className="p-4 border-b border-white/10 bg-gradient-to-r from-teal-500/10 to-transparent">
                        <h4 className="font-medium">Usage Analytics</h4>
                      </div>
                      <div className="p-4 space-y-3">
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-white/70">Used today:</span>
                            <span className="font-medium">{Math.ceil(Math.random() * 20)} credits</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-white/70">Used this week:</span>
                            <span className="font-medium">{Math.ceil(Math.random() * 100)} credits</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-white/70">Active sessions:</span>
                            <span className="font-medium">{Math.floor(Math.random() * 3)}</span>
                          </div>
                        </div>
                        <div className="pt-3 border-t border-white/10">
                          <Button
                            size="sm"
                            className="w-full bg-teal-500 hover:bg-teal-600"
                            onClick={() => window.location.href = '/dashboard/analytics/usage'}
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </HoverCardTrigger>
                <HoverCardContent className="w-64 firenest-card border border-white/10 text-white p-0">
                  <div className="p-4 border-b border-white/10 bg-gradient-to-r from-teal-500/10 to-transparent">
                    <h4 className="font-medium">Usage Analytics</h4>
                  </div>
                  <div className="p-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-white/70">Used today:</span>
                      <span className="font-medium">{Math.ceil(Math.random() * 20)} credits</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-white/70">Used this week:</span>
                      <span className="font-medium">{Math.ceil(Math.random() * 100)} credits</span>
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            </div>

            {/* Quick Stats */}
            <div className="flex items-center gap-4 bg-dark-800/50 rounded-lg px-4 py-2 border border-white/5">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-fiery" />
                <div>
                  <div className="text-xs text-white/50">Available Credits</div>
                  <div className="text-sm font-medium text-white">{credits?.availableCredits || 0}</div>
                </div>
              </div>
              <div className="h-8 w-px bg-white/10"></div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-400" />
                <div>
                  <div className="text-xs text-white/50">Recent Usage</div>
                  <div className="text-sm font-medium text-white">{recentlyUsed.length} tools</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {/* Empty space where the action buttons were */}
      </div>

      {/* Enhanced Section Navigation */}
      <div className="flex items-center border-b border-white/10 pb-2 mb-6 overflow-x-auto mt-8">
        <Button
          variant="ghost"
          className={`rounded-none border-b-2 ${selectedSection === 'browse' ? 'border-fiery text-white' : 'border-transparent text-white/70'} hover:text-white hover:bg-transparent px-4`}
          onClick={() => setSelectedSection('browse')}
        >
          <Layers className="h-4 w-4 mr-2" />
          Browse Tools
        </Button>
        <Button
          variant="ghost"
          className={`rounded-none border-b-2 ${selectedSection === 'recommendations' ? 'border-fiery text-white' : 'border-transparent text-white/70'} hover:text-white hover:bg-transparent px-4`}
          onClick={() => setSelectedSection('recommendations')}
        >
          <Sparkles className="h-4 w-4 mr-2" />
          Recommendations
        </Button>
        <Button
          variant="ghost"
          className={`rounded-none border-b-2 ${selectedSection === 'collections' ? 'border-fiery text-white' : 'border-transparent text-white/70'} hover:text-white hover:bg-transparent px-4`}
          onClick={() => setSelectedSection('collections')}
        >
          <Bookmark className="h-4 w-4 mr-2" />
          Collections & Workflows
        </Button>
        <Button
          variant="ghost"
          className={`rounded-none border-b-2 ${selectedSection === 'history' ? 'border-fiery text-white' : 'border-transparent text-white/70'} hover:text-white hover:bg-transparent px-4`}
          onClick={() => setSelectedSection('history')}
        >
          <Clock className="h-4 w-4 mr-2" />
          Usage History
        </Button>
      </div>

      {selectedSection === 'browse' && (
        <>
          {/* Enhanced Search and filters */}
          <div className="space-y-6">
            {/* Quick Access Section - Recently Used and Favorites */}
            <Card className="firenest-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg text-white flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-fiery" />
                  Quick Access
                </CardTitle>
                <CardDescription>
                  Your recently used and favorite tools
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {recentlyUsed.length > 0 ? (
                    recentlyUsed.slice(0, 6).map(id => {
                      const tool = mockTools.find(t => t.id === id);
                      if (!tool) return null;
                      return (
                        <div
                          key={id}
                          className="firenest-nested-card-interactive flex flex-col items-center text-center"
                          onClick={() => handleViewDetails(tool.id)}
                        >
                          <div className="h-10 w-10 rounded-full bg-fiery/20 flex items-center justify-center mb-2">
                            {getToolIcon(tool.category)}
                          </div>
                          <div className="text-sm font-medium text-white">{tool.name}</div>
                          <div className="text-xs text-white/50 mt-1">{tool.pricing.costPerUnit} credits</div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="col-span-full text-center py-4 text-white/50">
                      No recently used tools. Start exploring below!
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Search with Suggested Searches */}
            <div className="flex flex-col gap-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1 group">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50 group-focus-within:text-fiery transition-colors duration-300">
                    <Search className="h-4 w-4" />
                  </div>
                  <Input
                    type="search"
                    placeholder="Search for AI tools by name, category, or capability..."
                    className="pl-10 firenest-card focus:border-fiery/50 focus:ring-fiery/20 w-full transition-all duration-300 bg-dark-800/50"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  {searchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 text-white/50 hover:text-white transition-colors duration-200"
                      onClick={() => setSearchQuery('')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </Button>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Badge className="bg-dark-800/70 text-white/80 px-3 py-1.5 rounded-full border border-white/10">
                    <span className="font-semibold text-fiery mr-1">{filteredTools.length}</span> tools
                  </Badge>

                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/10 hover:bg-white/5 text-white/80 rounded-full transition-all duration-300 hover:border-white/30"
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedCategory('All Tools');
                      setActiveTab('all');
                    }}
                  >
                    <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                    Reset
                  </Button>
                </div>
              </div>

              {/* Suggested/Trending Searches */}
              <div className="flex flex-wrap items-center gap-2 text-sm">
                <span className="text-white/60 font-medium">Trending:</span>
                {['image generation', 'text', 'audio', 'code', 'chatbots'].map((term) => (
                  <Button
                    key={term}
                    variant="ghost"
                    size="sm"
                    className={`h-7 px-3 py-0.5 text-white/70 hover:text-white rounded-full transition-all duration-300 ${
                      searchQuery.includes(term) ? 'bg-fiery/10 text-fiery' : 'hover:bg-white/5'
                    }`}
                    onClick={() => setSearchQuery(term)}
                  >
                    {term}
                  </Button>
                ))}
              </div>

              {/* Advanced filters (conditionally shown) - industry standard implementation */}
              {showFilters && (
                <Card className="firenest-card border border-white/10 overflow-hidden">
                  {/* Subtle gradient effect at top */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-fiery/50 to-transparent"></div>

                  <CardHeader className="pb-2 border-b border-white/5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base text-white flex items-center">
                        <Filter className="h-4 w-4 mr-2 text-fiery" />
                        Advanced Filters
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full hover:bg-white/5"
                        onClick={() => setShowFilters(false)}
                      >
                        <X className="h-4 w-4 text-white/70" />
                      </Button>
                    </div>
                  </CardHeader>

                  <CardContent className="p-4">
                    {/* Two-column layout for better organization */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Left column - Categories and Features */}
                      <div className="space-y-6">
                        {/* Category filters with improved visual hierarchy */}
                        <div>
                          <h3 className="text-sm font-medium text-white mb-3 flex items-center">
                            <Layers className="h-3.5 w-3.5 mr-1.5 text-fiery" />
                            Categories
                          </h3>
                          <div className="grid grid-cols-2 gap-2">
                            {categories.map((category) => {
                              const isSelected = selectedCategory === category;
                              return (
                                <Button
                                  key={category}
                                  variant={isSelected ? "default" : "outline"}
                                  size="sm"
                                  className={`justify-start h-9 px-3 ${
                                    isSelected
                                      ? 'bg-fiery/20 text-fiery border-fiery/30'
                                      : 'border-white/10 hover:bg-white/5 text-white/80'
                                  } rounded-lg transition-all duration-200`}
                                  onClick={() => setSelectedCategory(category)}
                                >
                                  <div className="flex items-center w-full">
                                    {getCategoryIcon(category)}
                                    <span className="truncate">{category}</span>
                                    {isSelected && <CheckCircle className="h-3 w-3 ml-auto" />}
                                  </div>
                                </Button>
                              );
                            })}
                          </div>
                        </div>

                        {/* Features/Tags filter - new section */}
                        <div>
                          <h3 className="text-sm font-medium text-white mb-3 flex items-center">
                            <Tag className="h-3.5 w-3.5 mr-1.5 text-blue-400" />
                            Features
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            {['AI', 'Text', 'Image', 'Audio', 'Code', 'Assistant', 'Creative', 'OpenAI', 'Anthropic', 'Open Source'].map((tag) => (
                              <Button
                                key={tag}
                                variant="outline"
                                size="sm"
                                className="border-white/10 hover:bg-white/5 text-white/80 rounded-full transition-all duration-300 hover:border-blue-400/30 hover:text-white h-7 px-3 py-0 text-xs"
                              >
                                {tag}
                              </Button>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Right column - Pricing, Rating, and Sort */}
                      <div className="space-y-6">
                        {/* Credit Cost with range slider */}
                        <div>
                          <h3 className="text-sm font-medium text-white mb-3 flex items-center justify-between">
                            <div className="flex items-center">
                              <CreditCard className="h-3.5 w-3.5 mr-1.5 text-fiery" />
                              Credit Cost
                            </div>
                            <span className="text-xs text-white/60">Any</span>
                          </h3>
                          <div className="space-y-4">
                            <div className="relative pt-1">
                              <div className="h-1 bg-dark-700 rounded-full">
                                <div className="absolute h-1 rounded-full bg-gradient-to-r from-fiery to-fiery-600" style={{ width: '100%' }}></div>
                              </div>
                              <div className="flex items-center justify-between mt-2">
                                <div className="text-xs text-white/60">1</div>
                                <div className="text-xs text-white/60">5</div>
                                <div className="text-xs text-white/60">10</div>
                                <div className="text-xs text-white/60">15+</div>
                              </div>
                            </div>
                            <div className="flex flex-wrap items-center gap-1.5">
                              {['Any', '1-5', '6-10', '10+'].map((range) => (
                                <Button
                                  key={range}
                                  variant="outline"
                                  size="sm"
                                  className="border-white/10 hover:bg-white/5 text-white/80 rounded-full transition-all duration-300 hover:border-fiery/30 hover:text-white h-7 px-2.5 py-0 text-xs"
                                >
                                  {range}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Rating with star indicators */}
                        <div>
                          <h3 className="text-sm font-medium text-white mb-3 flex items-center">
                            <Star className="h-3.5 w-3.5 mr-1.5 text-yellow-400" />
                            Rating
                          </h3>
                          <div className="space-y-2">
                            {[
                              { label: 'Any Rating', value: 0, stars: 0 },
                              { label: '4.5 and above', value: 4.5, stars: 5 },
                              { label: '4.0 and above', value: 4.0, stars: 4 },
                              { label: '3.5 and above', value: 3.5, stars: 3 }
                            ].map((rating) => (
                              <Button
                                key={rating.label}
                                variant="outline"
                                size="sm"
                                className="w-full justify-start border-white/10 hover:bg-white/5 text-white/80 rounded-lg transition-all duration-300 hover:border-yellow-400/30 hover:text-white h-8 px-3 py-0 text-xs"
                              >
                                <div className="flex items-center">
                                  {rating.stars > 0 ? (
                                    <div className="flex mr-2">
                                      {[...Array(5)].map((_, i) => (
                                        <Star key={i} className={`h-3 w-3 ${i < rating.stars ? 'text-yellow-400' : 'text-white/20'}`} />
                                      ))}
                                    </div>
                                  ) : (
                                    <span>Any</span>
                                  )}
                                </div>
                              </Button>
                            ))}
                          </div>
                        </div>

                        {/* Sort By with radio-style selection */}
                        <div>
                          <h3 className="text-sm font-medium text-white mb-3 flex items-center">
                            <ArrowUpRight className="h-3.5 w-3.5 mr-1.5 text-blue-400" />
                            Sort By
                          </h3>
                          <div className="grid grid-cols-2 gap-2">
                            {[
                              { label: 'Popular', icon: <TrendingUp className="h-3 w-3 mr-1.5 text-purple-400" /> },
                              { label: 'Newest', icon: <Clock className="h-3 w-3 mr-1.5 text-teal-400" /> },
                              { label: 'Rating', icon: <Star className="h-3 w-3 mr-1.5 text-yellow-400" /> },
                              { label: 'Credits (Low to High)', icon: <Zap className="h-3 w-3 mr-1.5 text-fiery" /> }
                            ].map((sort) => (
                              <Button
                                key={sort.label}
                                variant="outline"
                                size="sm"
                                className="justify-start border-white/10 hover:bg-white/5 text-white/80 rounded-lg transition-all duration-300 hover:border-white/30 hover:text-white h-8 px-3 py-0 text-xs"
                              >
                                {sort.icon}
                                <span className="truncate">{sort.label}</span>
                              </Button>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Apply/Reset buttons with improved styling */}
                    <div className="mt-6 pt-4 border-t border-white/10 flex justify-between items-center">
                      <div className="text-xs text-white/60">
                        <span className="font-semibold text-fiery">{filteredTools.length}</span> tools match your filters
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-white/10 hover:bg-white/5 text-white/80 h-9 px-4"
                          onClick={() => {
                            setSearchQuery('');
                            setSelectedCategory('All Tools');
                            setActiveTab('all');
                            setShowFilters(false);
                          }}
                        >
                          <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                          Reset All
                        </Button>
                        <Button
                          size="sm"
                          className="bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-600 hover:to-fiery-700 h-9 px-4"
                          onClick={() => setShowFilters(false)}
                        >
                          Apply Filters
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </>
      )}

      {/* Enhanced Main content */}
      {selectedSection === 'browse' && (
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <TabsList className="firenest-card bg-dark-800/50 p-1 rounded-full border border-white/10">
              <TabsTrigger
                value="all"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-fiery data-[state=active]:to-fiery-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full transition-all duration-300"
              >
                <Layers className="h-3.5 w-3.5 mr-1.5" />
                All Tools
              </TabsTrigger>
              <TabsTrigger
                value="popular"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full transition-all duration-300"
              >
                <TrendingUp className="h-3.5 w-3.5 mr-1.5" />
                Popular
              </TabsTrigger>
              <TabsTrigger
                value="new"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full transition-all duration-300"
              >
                <Sparkles className="h-3.5 w-3.5 mr-1.5" />
                New
              </TabsTrigger>
              <TabsTrigger
                value="favorites"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full transition-all duration-300"
              >
                <Heart className="h-3.5 w-3.5 mr-1.5" />
                Favorites
              </TabsTrigger>
              <TabsTrigger
                value="recent"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-teal-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full transition-all duration-300"
              >
                <History className="h-3.5 w-3.5 mr-1.5" />
                Recent
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-3 flex-wrap">




              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-white/10 hover:bg-white/5 rounded-full transition-all duration-300 hover:border-white/30"
                      onClick={() => {
                        // Select two tools for comparison
                        const toolsToCompare = mockTools.slice(0, 2);
                        setCompareTools(toolsToCompare);
                      }}
                    >
                      <ArrowUpRight className="h-3.5 w-3.5 mr-1.5" />
                      Compare
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="firenest-card border border-white/10 text-white p-0">
                    <div className="p-4">
                      <p>Compare selected tools side by side</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <div className="flex items-center bg-dark-800/50 p-1 rounded-full border border-white/10">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`rounded-full px-3 ${viewMode === 'grid' ? 'bg-white/10 text-white' : 'text-white/70'}`}
                        onClick={() => setViewMode('grid')}
                      >
                        <Layers className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Grid view</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`rounded-full px-3 ${viewMode === 'list' ? 'bg-white/10 text-white' : 'text-white/70'}`}
                        onClick={() => setViewMode('list')}
                      >
                        <FileText className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>List view</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={showFilters ? "default" : "outline"}
                    size="sm"
                    className={`rounded-full transition-all duration-300 ${
                      showFilters
                        ? 'bg-fiery hover:bg-fiery-600'
                        : 'border-white/10 hover:bg-white/5 hover:border-white/30'
                    }`}
                  >
                    <Filter className="h-3.5 w-3.5 mr-1.5" />
                    Filters {selectedCategory !== 'All Tools' || showFilters ? '(on)' : ''}
                    <ChevronDown className="h-3.5 w-3.5 ml-1.5 opacity-70" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="firenest-card border border-white/10 p-0 shadow-xl rounded-xl w-[280px] overflow-hidden"
                >
                  {/* Categories Section */}
                  <div className="p-3 border-b border-white/10">
                    <div className="text-xs font-medium text-white/60 mb-2 flex items-center">
                      <Layers className="h-3 w-3 mr-1.5 text-fiery" />
                      CATEGORIES
                    </div>
                    <div className="grid grid-cols-2 gap-1.5">
                      {categories.slice(0, 6).map((category) => {
                        const isSelected = selectedCategory === category;
                        return (
                          <DropdownMenuItem
                            key={category}
                            className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer transition-all duration-200 ${
                              isSelected
                                ? 'bg-fiery/20 text-fiery'
                                : 'hover:bg-white/5 text-white/80'
                            }`}
                            onClick={() => setSelectedCategory(category)}
                          >
                            <div className="flex items-center w-full">
                              {getCategoryIcon(category)}
                              <span className="truncate">{category}</span>
                              {isSelected && <CheckCircle className="h-3 w-3 ml-auto" />}
                            </div>
                          </DropdownMenuItem>
                        );
                      })}
                    </div>
                    {categories.length > 6 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full mt-1 text-xs text-white/60 hover:text-white hover:bg-white/5"
                        onClick={() => setShowFilters(!showFilters)}
                      >
                        Show all categories
                        <ChevronRight className="h-3 w-3 ml-1" />
                      </Button>
                    )}
                  </div>

                  {/* Quick Filters */}
                  <div className="p-3 border-b border-white/10">
                    <div className="text-xs font-medium text-white/60 mb-2 flex items-center">
                      <Star className="h-3 w-3 mr-1.5 text-yellow-400" />
                      RATING
                    </div>
                    <div className="flex flex-wrap gap-1.5">
                      {[
                        { label: 'Any', value: 0 },
                        { label: '4.5+', value: 4.5 },
                        { label: '4.0+', value: 4.0 },
                        { label: '3.5+', value: 3.5 }
                      ].map((rating) => (
                        <Button
                          key={rating.label}
                          variant="outline"
                          size="sm"
                          className="px-2.5 py-0.5 h-7 text-xs border-white/10 hover:bg-white/5 text-white/80 rounded-full"
                        >
                          {rating.value > 0 && <Star className="h-2.5 w-2.5 mr-1 text-yellow-400" />}
                          {rating.label}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Credits Filter */}
                  <div className="p-3 border-b border-white/10">
                    <div className="text-xs font-medium text-white/60 mb-2 flex items-center">
                      <Zap className="h-3 w-3 mr-1.5 text-fiery" />
                      CREDITS
                    </div>
                    <div className="flex flex-wrap gap-1.5">
                      {['Any', '1-5', '6-10', '10+'].map((range) => (
                        <Button
                          key={range}
                          variant="outline"
                          size="sm"
                          className="px-2.5 py-0.5 h-7 text-xs border-white/10 hover:bg-white/5 text-white/80 rounded-full"
                        >
                          {range}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Sort By */}
                  <div className="p-3">
                    <div className="text-xs font-medium text-white/60 mb-2 flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1.5 text-blue-400" />
                      SORT BY
                    </div>
                    <div className="grid grid-cols-2 gap-1.5">
                      {[
                        { label: 'Popular', icon: <TrendingUp className="h-3 w-3 mr-1 text-purple-400" /> },
                        { label: 'Newest', icon: <Clock className="h-3 w-3 mr-1 text-teal-400" /> },
                        { label: 'Rating', icon: <Star className="h-3 w-3 mr-1 text-yellow-400" /> },
                        { label: 'Credits', icon: <Zap className="h-3 w-3 mr-1 text-fiery" /> }
                      ].map((sort) => (
                        <Button
                          key={sort.label}
                          variant="outline"
                          size="sm"
                          className="px-2.5 py-1 h-7 text-xs border-white/10 hover:bg-white/5 text-white/80 rounded-lg"
                        >
                          {sort.icon}
                          {sort.label}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Advanced Filters Button */}
                  <div className="p-3 pt-0 flex justify-end">
                    <Button
                      size="sm"
                      className="text-xs bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-600 hover:to-fiery-700 rounded-full"
                      onClick={() => setShowFilters(!showFilters)}
                    >
                      {showFilters ? 'Hide Advanced Filters' : 'Show Advanced Filters'}
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

        <TabsContent value="all" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="bg-dark-800 border-white/10">
                  <CardContent className="p-5">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-10 w-10 rounded-md bg-white/5" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32 bg-white/5" />
                        <Skeleton className="h-3 w-20 bg-white/5" />
                      </div>
                    </div>
                    <Skeleton className="h-10 w-full bg-white/5 mb-4" />
                    <div className="flex justify-between mb-4">
                      <Skeleton className="h-3 w-20 bg-white/5" />
                      <Skeleton className="h-3 w-16 bg-white/5" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-3 w-24 bg-white/5" />
                      <Skeleton className="h-8 w-20 bg-white/5 rounded-md" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredTools.length > 0 ? (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTools.map(renderToolCard)}
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                {filteredTools.map(renderToolCardList)}
              </div>
            )
          ) : (
            <Card className="firenest-card border border-white/10 overflow-hidden">
              {/* Subtle gradient effect */}
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-fiery/30 to-transparent"></div>

              <CardContent className="p-10 flex flex-col items-center justify-center">
                <div className="relative mb-6">
                  <div className="h-20 w-20 rounded-full bg-dark-800/80 flex items-center justify-center">
                    <Search className="h-10 w-10 text-white/30" />
                  </div>
                  <div className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-fiery/10 flex items-center justify-center border border-white/10">
                    <X className="h-4 w-4 text-fiery/70" />
                  </div>
                </div>

                <h3 className="text-xl font-medium text-white mb-3">No tools found</h3>
                <p className="text-white/70 text-center max-w-md mb-6">
                  We couldn't find any tools matching your search criteria. Try adjusting your filters or search query.
                </p>

                <div className="flex flex-wrap gap-3 justify-center">
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5 rounded-full transition-all duration-300 hover:border-white/30"
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedCategory('All Tools');
                      setActiveTab('all');
                    }}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Clear All Filters
                  </Button>

                  <Button
                    className="bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-600 hover:to-fiery-700 rounded-full transition-all duration-300"
                    onClick={() => setActiveTab('popular')}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Popular Tools
                  </Button>
                </div>

                <div className="mt-8 pt-6 border-t border-white/10 w-full max-w-md">
                  <p className="text-sm text-white/50 text-center mb-3">Try one of these popular searches:</p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {['image generation', 'text', 'chatbots', 'code'].map(term => (
                      <Button
                        key={term}
                        variant="ghost"
                        size="sm"
                        className="rounded-full bg-white/5 hover:bg-white/10 text-white/70 hover:text-white"
                        onClick={() => setSearchQuery(term)}
                      >
                        {term}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="popular" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="bg-dark-800 border-white/10">
                  <CardContent className="p-5">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-10 w-10 rounded-md bg-white/5" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32 bg-white/5" />
                        <Skeleton className="h-3 w-20 bg-white/5" />
                      </div>
                    </div>
                    <Skeleton className="h-10 w-full bg-white/5 mb-4" />
                    <div className="flex justify-between mb-4">
                      <Skeleton className="h-3 w-20 bg-white/5" />
                      <Skeleton className="h-3 w-16 bg-white/5" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-3 w-24 bg-white/5" />
                      <Skeleton className="h-8 w-20 bg-white/5 rounded-md" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* AI-Powered Recommendations */}
              <Card className="firenest-card border border-white/10 overflow-hidden">
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500/30 to-transparent"></div>
                <CardHeader className="pb-2 border-b border-white/5">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base text-white flex items-center">
                      <Sparkles className="h-4 w-4 mr-2 text-purple-400" />
                      AI-Powered Recommendations
                    </CardTitle>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full hover:bg-white/5"
                          >
                            <Info className="h-4 w-4 text-white/70" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">Recommendations based on your usage patterns, preferences, and industry trends</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="bg-dark-800/50 border-white/10 hover:border-purple-400/30 transition-all">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="h-8 w-8 rounded-md bg-purple-500/10 flex items-center justify-center">
                            <MessageSquare className="h-4 w-4 text-purple-400" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-white">Based on your usage</h4>
                            <p className="text-xs text-white/70">You use text generation tools frequently</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-white/70 mb-3">
                          <Clock className="h-3 w-3" />
                          <span>Last used 2 hours ago</span>
                        </div>
                        <Button size="sm" className="w-full bg-purple-500/20 hover:bg-purple-500/30 text-purple-200">
                          Try Claude 3 Opus
                        </Button>
                      </CardContent>
                    </Card>

                    <Card className="bg-dark-800/50 border-white/10 hover:border-blue-400/30 transition-all">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="h-8 w-8 rounded-md bg-blue-500/10 flex items-center justify-center">
                            <Code className="h-4 w-4 text-blue-400" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-white">For your workflow</h4>
                            <p className="text-xs text-white/70">Complement your coding tools</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-white/70 mb-3">
                          <TrendingUp className="h-3 w-3" />
                          <span>Popular with developers</span>
                        </div>
                        <Button size="sm" className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-200">
                          Try GitHub Copilot
                        </Button>
                      </CardContent>
                    </Card>

                    <Card className="bg-dark-800/50 border-white/10 hover:border-teal-400/30 transition-all">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="h-8 w-8 rounded-md bg-teal-500/10 flex items-center justify-center">
                            <Image className="h-4 w-4 text-teal-400" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-white">New for you</h4>
                            <p className="text-xs text-white/70">Based on your interests</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-white/70 mb-3">
                          <Sparkles className="h-3 w-3" />
                          <span>Just released</span>
                        </div>
                        <Button size="sm" className="w-full bg-teal-500/20 hover:bg-teal-500/30 text-teal-200">
                          Try Midjourney v6
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>

              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredTools.map(renderToolCard)}
                </div>
              ) : (
                <div className="flex flex-col gap-2">
                  {filteredTools.map(renderToolCardList)}
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="new" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="bg-dark-800 border-white/10">
                  <CardContent className="p-5">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-10 w-10 rounded-md bg-white/5" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32 bg-white/5" />
                        <Skeleton className="h-3 w-20 bg-white/5" />
                      </div>
                    </div>
                    <Skeleton className="h-10 w-full bg-white/5 mb-4" />
                    <div className="flex justify-between mb-4">
                      <Skeleton className="h-3 w-20 bg-white/5" />
                      <Skeleton className="h-3 w-16 bg-white/5" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-3 w-24 bg-white/5" />
                      <Skeleton className="h-8 w-20 bg-white/5 rounded-md" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTools.map(renderToolCard)}
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                {filteredTools.map(renderToolCardList)}
              </div>
            )
          )}
        </TabsContent>

        <TabsContent value="favorites" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2].map((i) => (
                <Card key={i} className="bg-dark-800 border-white/10">
                  <CardContent className="p-5">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-10 w-10 rounded-md bg-white/5" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32 bg-white/5" />
                        <Skeleton className="h-3 w-20 bg-white/5" />
                      </div>
                    </div>
                    <Skeleton className="h-10 w-full bg-white/5 mb-4" />
                    <div className="flex justify-between mb-4">
                      <Skeleton className="h-3 w-20 bg-white/5" />
                      <Skeleton className="h-3 w-16 bg-white/5" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-3 w-24 bg-white/5" />
                      <Skeleton className="h-8 w-20 bg-white/5 rounded-md" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : favorites.length > 0 ? (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mockTools.filter(tool => favorites.includes(tool.id)).map(renderToolCard)}
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                {mockTools.filter(tool => favorites.includes(tool.id)).map(renderToolCardList)}
              </div>
            )
          ) : (
            <Card className="firenest-card border border-white/10 overflow-hidden">
              {/* Subtle gradient effect */}
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-pink-500/30 to-transparent"></div>

              <CardContent className="p-10 flex flex-col items-center justify-center">
                <div className="relative mb-6">
                  <div className="h-20 w-20 rounded-full bg-dark-800/80 flex items-center justify-center">
                    <Heart className="h-10 w-10 text-white/30" />
                  </div>
                  <div className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-pink-500/10 flex items-center justify-center border border-white/10">
                    <Plus className="h-4 w-4 text-pink-400/70" />
                  </div>
                </div>

                <h3 className="text-xl font-medium text-white mb-3">No favorites yet</h3>
                <p className="text-white/70 text-center max-w-md mb-6">
                  Click the heart icon on any tool card to add it to your favorites for quick access.
                  Favorite tools will appear here for easy access.
                </p>

                <div className="flex flex-wrap gap-3 justify-center">
                  <Button
                    className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 rounded-full transition-all duration-300"
                    onClick={() => setActiveTab('popular')}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Popular Tools
                  </Button>

                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5 rounded-full transition-all duration-300 hover:border-white/30"
                    onClick={() => setActiveTab('all')}
                  >
                    <Layers className="h-4 w-4 mr-2" />
                    Browse All Tools
                  </Button>
                </div>

                <div className="mt-8 pt-6 border-t border-white/10 w-full max-w-md">
                  <p className="text-sm text-white/50 text-center mb-3">How to use favorites:</p>
                  <div className="flex flex-col gap-2 text-sm text-white/70">
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-white/5 flex items-center justify-center">
                        <Heart className="h-3 w-3 text-pink-400" />
                      </div>
                      <span>Click the heart icon on any tool to add it to favorites</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-white/5 flex items-center justify-center">
                        <Star className="h-3 w-3 text-yellow-400" />
                      </div>
                      <span>Favorite tools are always available in this tab</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="recent" className="space-y-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2].map((i) => (
                <Card key={i} className="bg-dark-800 border-white/10">
                  <CardContent className="p-5">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="h-10 w-10 rounded-md bg-white/5" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32 bg-white/5" />
                        <Skeleton className="h-3 w-20 bg-white/5" />
                      </div>
                    </div>
                    <Skeleton className="h-10 w-full bg-white/5 mb-4" />
                    <div className="flex justify-between mb-4">
                      <Skeleton className="h-3 w-20 bg-white/5" />
                      <Skeleton className="h-3 w-16 bg-white/5" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-3 w-24 bg-white/5" />
                      <Skeleton className="h-8 w-20 bg-white/5 rounded-md" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : recentlyUsed.length > 0 ? (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mockTools.filter(tool => recentlyUsed.includes(tool.id)).map(renderToolCard)}
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                {mockTools.filter(tool => recentlyUsed.includes(tool.id)).map(renderToolCardList)}
              </div>
            )
          ) : (
            <Card className="firenest-card border border-white/10 overflow-hidden">
              {/* Subtle gradient effect */}
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500/30 to-transparent"></div>

              <CardContent className="p-10 flex flex-col items-center justify-center">
                <div className="relative mb-6">
                  <div className="h-20 w-20 rounded-full bg-dark-800/80 flex items-center justify-center">
                    <History className="h-10 w-10 text-white/30" />
                  </div>
                  <div className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-teal-500/10 flex items-center justify-center border border-white/10">
                    <Clock className="h-4 w-4 text-teal-400/70" />
                  </div>
                </div>

                <h3 className="text-xl font-medium text-white mb-3">No recent tools</h3>
                <p className="text-white/70 text-center max-w-md mb-6">
                  Your recently used tools will appear here for quick access.
                  Start exploring and using tools to build your history.
                </p>

                <div className="flex flex-wrap gap-3 justify-center">
                  <Button
                    className="bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 rounded-full transition-all duration-300"
                    onClick={() => setActiveTab('popular')}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Popular Tools
                  </Button>

                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5 rounded-full transition-all duration-300 hover:border-white/30"
                    onClick={() => setActiveTab('all')}
                  >
                    <Layers className="h-4 w-4 mr-2" />
                    Browse All Tools
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
      )}

      {selectedSection === 'recommendations' && (
        <ToolRecommendations
          tools={mockTools}
          recentlyUsed={recentlyUsed}
          onViewDetails={handleViewDetails}
          onLaunchTool={handleOpenLaunch}
        />
      )}

      {selectedSection === 'collections' && (
        <ToolCollections
          tools={mockTools}
          onLaunchTool={handleOpenLaunch}
        />
      )}

      {selectedSection === 'history' && (
        <ToolHistory
          tools={mockTools}
          onLaunchTool={handleOpenLaunch}
        />
      )}
      </>)}
    </div>
  );
};

export default NewWorkbench;
