import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, X, Maximize, Volume2, VolumeX } from 'lucide-react';

interface InlineVideoPlayerProps {
  expanded: boolean;
  onToggleExpand: () => void;
}

const InlineVideoPlayer: React.FC<InlineVideoPlayerProps> = ({ expanded, onToggleExpand }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // Use ref instead of getElementById for better React practices
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      video.muted = isMuted;
    }
  }, [isMuted]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;
    
    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    
    setIsPlaying(!isPlaying);
  };
  
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  // Common button style for video controls
  const controlButtonClass = "bg-black/40 hover:bg-black/60 rounded-full p-2 text-white/80 hover:text-white transition-all";

  return (
    <div className={`relative overflow-hidden rounded-xl border border-white/10 bg-dark-900 shadow-2xl transition-all duration-500 ${
      expanded ? 'fixed inset-0 z-50 m-4 md:m-8 flex items-center justify-center' : 'w-full'
    }`}>
      {expanded && (
        <div className="absolute inset-0 bg-black/80 backdrop-blur-sm -z-10"></div>
      )}
      
      <div className={`relative ${expanded ? 'w-full max-w-3xl' : 'w-full'}`}>
        {/* Video Controls Overlay */}
        <div className="absolute inset-0 flex flex-col justify-between p-3 bg-gradient-to-b from-black/40 via-transparent to-black/40 opacity-0 hover:opacity-100 transition-opacity duration-300">
          <div className="flex justify-end">
            {expanded && (
              <button 
                onClick={onToggleExpand}
                className={controlButtonClass}
                aria-label="Close fullscreen"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          
          <div className="flex justify-between items-center">
            <button 
              onClick={togglePlay}
              className={controlButtonClass}
              aria-label={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </button>
            
            <div className="flex gap-2">
              <button 
                onClick={toggleMute}
                className={controlButtonClass}
                aria-label={isMuted ? "Unmute" : "Mute"}
              >
                {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </button>
              
              {!expanded && (
                <button 
                  onClick={onToggleExpand}
                  className={controlButtonClass}
                  aria-label="Expand to fullscreen"
                >
                  <Maximize className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </div>
        
        {/* Video Element */}
        <div className={`aspect-video w-full ${expanded ? 'rounded-xl overflow-hidden' : ''}`}>
          <video 
            ref={videoRef}
            className="w-full h-full object-cover"
            poster="/demo-thumbnail.jpg"
            muted={isMuted}
            loop
            playsInline
            onClick={togglePlay}
            aria-label="Firenest Platform Demo Video"
          >
            <source src="https://player.vimeo.com/external/470438639.hd.mp4?s=f08312a14db8b71dfc7b6c2e3d2a8d735850bc1f&profile_id=175" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
        
        {/* Video info when expanded */}
        {expanded && (
          <div className="p-3 bg-gradient-to-b from-dark-900/90 to-dark-900">
            <h3 className="text-lg font-bold mb-1">Firenest Platform Demo</h3>
            <p className="text-white/70 text-sm">
              See how Firenest can transform your startup's AI capabilities while reducing costs.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InlineVideoPlayer;
