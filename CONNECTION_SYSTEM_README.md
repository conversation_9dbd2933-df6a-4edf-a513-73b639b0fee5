# Firenest Connection Management System

This document explains how the Firenest connection management system works and how to set it up in your Supabase database.

## Overview

The Firenest connection management system allows users to connect to various AI tools and services through different authentication methods (OAuth, API Key, Credentials, IP-based). The system consists of:

1. **Database Tables**:
   - `user_connections`: Stores connections between users and tools
   - `connection_logs`: Records connection events (create, update, delete, status changes)
   - `tool_launches`: Tracks when tools are launched
   - `usage_sessions`: Tracks usage sessions for credit calculation
   - `usage_events`: Records detailed usage events

2. **SQL Functions**:
   - `end_usage_session`: Ends a usage session and calculates credits used
   - `update_session_metrics`: Updates metrics for a usage session
   - `record_usage_event`: Records a usage event and updates session metrics

## Setup Instructions

To set up the connection management system, you need to run the following SQL scripts in your Supabase database:

1. First, run the `supabase-auth-tables.sql` script to create the basic user tables
2. Then, run the `supabase-connection-tables.sql` script to create the connection tables
3. Finally, run the `supabase-usage-tracking-tables.sql` script to create the usage tracking tables
4. (Optional) Run the `supabase-credit-functions-final.sql` script to set up the credit system

### Step 1: Run the Auth Tables Script

This script creates the basic user tables required for authentication.

```sql
-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table for storing additional user information
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table for storing additional profile information
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  company TEXT,
  job_title TEXT,
  bio TEXT,
  website TEXT,
  phone TEXT,
  country TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_settings table for storing user preferences
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  email_notifications BOOLEAN DEFAULT TRUE,
  theme TEXT DEFAULT 'dark',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_credits table for tracking credit usage
CREATE TABLE IF NOT EXISTS user_credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  total_credits INTEGER DEFAULT 0,
  used_credits INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create credit_transactions table for tracking credit history
CREATE TABLE IF NOT EXISTS credit_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  description TEXT,
  transaction_type TEXT NOT NULL, -- 'purchase', 'usage', 'refund', 'bonus'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Step 2: Run the Connection Tables Script

This script creates the tables for managing connections to AI tools.

```sql
-- Create user_connections table for tracking tool connections
CREATE TABLE IF NOT EXISTS user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  auth_method TEXT NOT NULL,
  status TEXT NOT NULL,
  connection_data JSONB,
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tool_id)
);

-- Create connection_logs table for tracking connection events
CREATE TABLE IF NOT EXISTS connection_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  event_type TEXT NOT NULL,
  event_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE user_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE connection_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for user_connections table
CREATE POLICY "Users can view their own connections" 
  ON user_connections FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connections" 
  ON user_connections FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own connections" 
  ON user_connections FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own connections" 
  ON user_connections FOR DELETE 
  USING (auth.uid() = user_id);

-- Create policies for connection_logs table
CREATE POLICY "Users can view their own connection logs" 
  ON connection_logs FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own connection logs" 
  ON connection_logs FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS user_connections_user_id_idx ON user_connections(user_id);
CREATE INDEX IF NOT EXISTS user_connections_tool_id_idx ON user_connections(tool_id);
CREATE INDEX IF NOT EXISTS connection_logs_user_id_idx ON connection_logs(user_id);
CREATE INDEX IF NOT EXISTS connection_logs_tool_id_idx ON connection_logs(tool_id);
```

### Step 3: Run the Usage Tracking Tables Script

This script creates the tables for tracking tool launches and usage sessions.

```sql
-- Create tool_launches table for tracking when tools are launched
CREATE TABLE IF NOT EXISTS tool_launches (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  launched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT NOT NULL, -- 'active', 'completed', 'failed'
  launch_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_sessions table for tracking usage sessions
CREATE TABLE IF NOT EXISTS usage_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  launch_id UUID REFERENCES tool_launches(id) ON DELETE CASCADE,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  status TEXT NOT NULL, -- 'active', 'completed', 'terminated'
  metrics JSONB, -- Store various metrics like API calls, resources consumed, etc.
  estimated_credits INTEGER DEFAULT 0,
  actual_credits_used INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_events table for tracking detailed usage events
CREATE TABLE IF NOT EXISTS usage_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES usage_sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  tool_id TEXT NOT NULL,
  event_type TEXT NOT NULL, -- 'api_call', 'resource_consumption', 'time_update', etc.
  event_data JSONB,
  credits_used INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE tool_launches ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;

-- Create policies for tool_launches table
CREATE POLICY "Users can view their own tool launches" 
  ON tool_launches FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tool launches" 
  ON tool_launches FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tool launches" 
  ON tool_launches FOR UPDATE 
  USING (auth.uid() = user_id);

-- Create policies for usage_sessions table
CREATE POLICY "Users can view their own usage sessions" 
  ON usage_sessions FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own usage sessions" 
  ON usage_sessions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own usage sessions" 
  ON usage_sessions FOR UPDATE 
  USING (auth.uid() = user_id);

-- Create policies for usage_events table
CREATE POLICY "Users can view their own usage events" 
  ON usage_events FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own usage events" 
  ON usage_events FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS tool_launches_user_id_idx ON tool_launches(user_id);
CREATE INDEX IF NOT EXISTS tool_launches_tool_id_idx ON tool_launches(tool_id);
CREATE INDEX IF NOT EXISTS tool_launches_status_idx ON tool_launches(status);

CREATE INDEX IF NOT EXISTS usage_sessions_user_id_idx ON usage_sessions(user_id);
CREATE INDEX IF NOT EXISTS usage_sessions_tool_id_idx ON usage_sessions(tool_id);
CREATE INDEX IF NOT EXISTS usage_sessions_launch_id_idx ON usage_sessions(launch_id);
CREATE INDEX IF NOT EXISTS usage_sessions_status_idx ON usage_sessions(status);

CREATE INDEX IF NOT EXISTS usage_events_session_id_idx ON usage_events(session_id);
CREATE INDEX IF NOT EXISTS usage_events_user_id_idx ON usage_events(user_id);
CREATE INDEX IF NOT EXISTS usage_events_tool_id_idx ON usage_events(tool_id);
CREATE INDEX IF NOT EXISTS usage_events_event_type_idx ON usage_events(event_type);
```

## Usage

### Connecting to Tools

The connection management system supports four authentication methods:

1. **OAuth**: For services that use OAuth 2.0 authentication
2. **API Key**: For services that use API keys
3. **Credentials**: For services that use username/password authentication
4. **IP-based**: For services that authenticate based on IP address

### Tracking Tool Usage

When a user launches a tool, the system:

1. Records the launch in the `tool_launches` table
2. Creates a usage session in the `usage_sessions` table
3. Tracks usage events in the `usage_events` table
4. Calculates credits used based on the usage metrics
5. Deducts credits from the user's account

### Managing Connections

Users can:

1. Connect to tools using various authentication methods
2. View their connection status and details
3. Disconnect from tools
4. Launch tools and track usage

## API Reference

### Connection Management

- `getUserConnections(userId)`: Get all connections for a user
- `getUserConnection(userId, toolId)`: Get a specific connection
- `saveUserConnection(userId, toolId, authMethod, status, connectionData)`: Save a connection
- `updateConnectionStatus(userId, toolId, status)`: Update connection status
- `deleteUserConnection(userId, toolId)`: Delete a connection
- `updateConnectionLastUsed(userId, toolId)`: Update last used timestamp
- `getConnectionLogs(userId, toolId)`: Get connection logs
- `logConnectionEvent(userId, toolId, eventType, eventData)`: Log a connection event

### Usage Tracking

- `trackToolLaunch(userId, toolId)`: Track a tool launch
- `endUsageSession(sessionId, endTime, status, metrics, actualCreditsUsed)`: End a usage session
- `updateSessionMetrics(sessionId, newMetrics, estimatedCredits)`: Update session metrics
- `recordUsageEvent(sessionId, userId, toolId, eventType, eventData, creditsUsed)`: Record a usage event

## Troubleshooting

If you encounter issues with the connection management system, check the following:

1. Make sure all required tables exist in your Supabase database
2. Check that Row Level Security (RLS) policies are properly configured
3. Verify that the user has sufficient credits for tool usage
4. Check the browser console for any JavaScript errors
5. Look for error messages in the Supabase logs

## Security Considerations

The connection management system implements several security measures:

1. **Row Level Security**: Users can only access their own connections and usage data
2. **Secure Storage**: Sensitive authentication data is stored securely
3. **Minimal Permissions**: Functions have the minimum permissions required
4. **Input Validation**: All inputs are validated before processing

## Future Enhancements

Planned enhancements for the connection management system include:

1. **Better Analytics**: More detailed usage analytics and reporting
2. **Automatic Reconnection**: Automatically reconnect when tokens expire
3. **Connection Health Monitoring**: Monitor connection health and alert users
4. **Usage Quotas**: Set usage quotas for different tools
5. **Team Sharing**: Share connections with team members
