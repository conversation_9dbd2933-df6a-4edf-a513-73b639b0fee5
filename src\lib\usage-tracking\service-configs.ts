/**
 * Service Usage Configurations
 * 
 * This file contains the usage tracking configuration for all supported services.
 */

import { ServiceUsageConfig } from './types';

// Service usage configurations
export const serviceUsageConfigs: ServiceUsageConfig[] = [
  // OpenAI (ChatGPT)
  {
    serviceId: 'chatgpt',
    metricType: 'time',
    unitName: 'minute',
    costPerUnit: 5,
    minimumUsage: 1
  },
  
  // Midjourney
  {
    serviceId: 'midjourney',
    metricType: 'resources',
    unitName: 'image',
    costPerUnit: 10
  },
  
  // GitHub Copilot
  {
    serviceId: 'github-copilot',
    metricType: 'time',
    unitName: 'hour',
    costPerUnit: 15,
    minimumUsage: 1
  },
  
  // Descript
  {
    serviceId: 'descript',
    metricType: 'time',
    unitName: 'minute',
    costPerUnit: 8
  },
  
  // Jasper
  {
    serviceId: 'jasper',
    metricType: 'resources',
    unitName: 'word',
    costPerUnit: 0.1
  },
  
  // Runway
  {
    serviceId: 'runway',
    metricType: 'time',
    unitName: 'minute',
    costPerUnit: 12
  },
  
  // Notion AI
  {
    serviceId: 'notion-ai',
    metricType: 'resources',
    unitName: 'page',
    costPerUnit: 5
  },
  
  // Claude
  {
    serviceId: 'claude',
    metricType: 'time',
    unitName: 'minute',
    costPerUnit: 6
  },
  
  // DALL-E
  {
    serviceId: 'dalle',
    metricType: 'resources',
    unitName: 'image',
    costPerUnit: 8
  },
  
  // Stable Diffusion
  {
    serviceId: 'stable-diffusion',
    metricType: 'resources',
    unitName: 'image',
    costPerUnit: 5
  },
  
  // Grammarly
  {
    serviceId: 'grammarly',
    metricType: 'time',
    unitName: 'month',
    costPerUnit: 30,
    minimumUsage: 1
  },
  
  // Synthesia
  {
    serviceId: 'synthesia',
    metricType: 'resources',
    unitName: 'video',
    costPerUnit: 25
  }
];
