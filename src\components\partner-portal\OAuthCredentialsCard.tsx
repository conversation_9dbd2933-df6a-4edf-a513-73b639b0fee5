import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Co<PERSON>, Eye, EyeOff, RefreshCw, AlertTriangle } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { regenerateClientId, regenerateClientSecret, generateOAuthCredentials } from '@/lib/partner-portal/api';
import { getPartnerAccount } from '@/lib/partner-portal/api';
import { usePartner } from '@/contexts/PartnerContext';

interface OAuthCredentialsCardProps {
  toolId: string;
  clientId: string;
  clientSecret: string;
  onCredentialsChange: (clientId: string, clientSecret: string) => void;
}

const OAuthCredentialsCard: React.FC<OAuthCredentialsCardProps> = ({
  toolId,
  clientId,
  clientSecret,
  onCredentialsChange
}) => {
  const [showClientSecret, setShowClientSecret] = useState(false);
  const [isRegeneratingId, setIsRegeneratingId] = useState(false);
  const [isRegeneratingSecret, setIsRegeneratingSecret] = useState(false);
  const [isGeneratingCredentials, setIsGeneratingCredentials] = useState(false);
  const { partner } = usePartner();

  const credentialsExist = clientId && clientSecret;

  const handleCopyClientId = () => {
    if (!clientId) {
      notify.error('No Client ID available to copy');
      return;
    }
    navigator.clipboard.writeText(clientId);
    notify.success('Client ID copied to clipboard');
  };

  const handleCopyClientSecret = () => {
    if (!clientSecret) {
      notify.error('No Client Secret available to copy');
      return;
    }
    navigator.clipboard.writeText(clientSecret);
    notify.success('Client Secret copied to clipboard');
  };

  const handleRegenerateClientId = async () => {
    if (!window.confirm('Are you sure you want to regenerate the Client ID? This will invalidate the current ID and may break existing integrations.')) {
      return;
    }

    setIsRegeneratingId(true);
    try {
      const newClientId = await regenerateClientId(toolId);
      if (newClientId) {
        onCredentialsChange(newClientId, clientSecret);
        notify.success('Client ID regenerated successfully');
      } else {
        throw new Error('Failed to regenerate Client ID');
      }
    } catch (error) {
      console.error('Error regenerating Client ID:', error);
      notify.error('Failed to regenerate Client ID');
    } finally {
      setIsRegeneratingId(false);
    }
  };

  const handleRegenerateClientSecret = async () => {
    if (!window.confirm('Are you sure you want to regenerate the Client Secret? This will invalidate the current secret and may break existing integrations.')) {
      return;
    }

    setIsRegeneratingSecret(true);
    try {
      const newClientSecret = await regenerateClientSecret(toolId);
      if (newClientSecret) {
        onCredentialsChange(clientId, newClientSecret);
        notify.success('Client Secret regenerated successfully');
        // Automatically show the new secret
        setShowClientSecret(true);
      } else {
        throw new Error('Failed to regenerate Client Secret');
      }
    } catch (error) {
      console.error('Error regenerating Client Secret:', error);
      notify.error('Failed to regenerate Client Secret');
    } finally {
      setIsRegeneratingSecret(false);
    }
  };

  const handleGenerateCredentials = async () => {
    if (!partner) {
      notify.error('Partner information not available');
      return;
    }

    setIsGeneratingCredentials(true);
    try {
      const partnerAccount = await getPartnerAccount(partner.id);
      if (!partnerAccount) {
        throw new Error('Partner account not found');
      }

      const credentials = await generateOAuthCredentials(toolId, partnerAccount.name);
      if (credentials) {
        onCredentialsChange(credentials.clientId, credentials.clientSecret);
        notify.success('OAuth credentials generated successfully');
        // Automatically show the new secret
        setShowClientSecret(true);
      } else {
        throw new Error('Failed to generate OAuth credentials');
      }
    } catch (error) {
      console.error('Error generating OAuth credentials:', error);
      if (error instanceof Error) {
        notify.error(`Failed to generate OAuth credentials: ${error.message}`);
      } else {
        notify.error('Failed to generate OAuth credentials');
      }
    } finally {
      setIsGeneratingCredentials(false);
    }
  };

  return (
    <Card className="firenest-card border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="text-lg text-white">OAuth Credentials</CardTitle>
        <CardDescription>
          Use these credentials to authenticate your tool with Firenest
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!credentialsExist ? (
          // No credentials exist yet
          <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
            <div className="flex items-start mb-4">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500/20 to-yellow-500/5 flex items-center justify-center mr-4 flex-shrink-0">
                <AlertTriangle className="w-5 h-5 text-yellow-400" />
              </div>
              <div>
                <h3 className="text-white font-medium mb-1">OAuth Credentials Not Found</h3>
                <p className="text-white/70 text-sm">
                  No OAuth credentials have been generated for this tool yet. Generate credentials to enable OAuth authentication.
                </p>
              </div>
            </div>

            <div className="mt-6 flex justify-center">
              <Button
                className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                onClick={handleGenerateCredentials}
                disabled={isGeneratingCredentials}
              >
                {isGeneratingCredentials ? (
                  <>
                    <div className="mr-2 w-4 h-4 rounded-full border-2 border-white/10 border-t-white animate-spin"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Generate OAuth Credentials
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          // Credentials exist
          <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
            <div className="flex items-start mb-4">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center mr-4 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              </div>
              <div>
                <h3 className="text-white font-medium mb-1">OAuth 2.0 Credentials</h3>
                <p className="text-white/70 text-sm">
                  These credentials are used to authenticate your tool with Firenest. Keep them secure and never share them publicly.
                </p>
              </div>
            </div>

            <div className="space-y-4 mt-6">
              <div className="space-y-2">
                <Label htmlFor="clientId" className="text-white/90">Client ID</Label>
                <div className="relative">
                  <Input
                    id="clientId"
                    value={clientId}
                    readOnly
                    className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10 pr-20"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-1">
                    <button
                      className="text-white/50 hover:text-white p-1"
                      onClick={handleCopyClientId}
                      title="Copy Client ID"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    <button
                      className="text-white/50 hover:text-white p-1"
                      onClick={handleRegenerateClientId}
                      disabled={isRegeneratingId}
                      title="Regenerate Client ID"
                    >
                      <RefreshCw className={`w-4 h-4 ${isRegeneratingId ? 'animate-spin' : ''}`} />
                    </button>
                  </div>
                </div>
                <p className="text-white/50 text-sm">Your unique client identifier</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientSecret" className="text-white/90">Client Secret</Label>
                <div className="relative">
                  <Input
                    id="clientSecret"
                    type={showClientSecret ? "text" : "password"}
                    value={clientSecret}
                    readOnly
                    className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10 pr-20"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-1">
                    <button
                      className="text-white/50 hover:text-white p-1"
                      onClick={() => setShowClientSecret(!showClientSecret)}
                      title={showClientSecret ? "Hide Client Secret" : "Show Client Secret"}
                    >
                      {showClientSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                    <button
                      className="text-white/50 hover:text-white p-1"
                      onClick={handleCopyClientSecret}
                      title="Copy Client Secret"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    <button
                      className="text-white/50 hover:text-white p-1"
                      onClick={handleRegenerateClientSecret}
                      disabled={isRegeneratingSecret}
                      title="Regenerate Client Secret"
                    >
                      <RefreshCw className={`w-4 h-4 ${isRegeneratingSecret ? 'animate-spin' : ''}`} />
                    </button>
                  </div>
                </div>
                <p className="text-white/50 text-sm">Keep this secret secure and never expose it in client-side code</p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
          <div className="flex items-start">
            <div className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </div>
            <div>
              <h4 className="text-yellow-500 font-medium mb-1">Security Warning</h4>
              <p className="text-white/70 text-sm">
                {credentialsExist
                  ? "Regenerating credentials will invalidate the previous ones and may break existing integrations. Only regenerate credentials if you suspect they have been compromised or if you're setting up a new integration."
                  : "OAuth credentials are required for secure authentication. Generate them before publishing your tool."}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OAuthCredentialsCard;
