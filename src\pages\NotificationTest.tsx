import React from 'react';
import { Button } from '@/components/ui/button';
import { notify } from '@/components/ui/notification-system';

const NotificationTest = () => {
  const showSuccessNotification = () => {
    notify.success('Operation completed successfully');
  };

  const showErrorNotification = () => {
    notify.error('Something went wrong');
  };

  const showWarningNotification = () => {
    notify.warning('Be careful with this action');
  };

  const showInfoNotification = () => {
    notify.info('Here is some information');
  };

  const showFirenestNotification = () => {
    notify.firenest('Welcome to Firenest');
  };

  const showPromiseNotification = () => {
    const promise = new Promise((resolve) => {
      setTimeout(() => resolve('Data loaded'), 2000);
    });

    notify.promise(promise, {
      loading: 'Loading data...',
      success: 'Data loaded successfully',
      error: 'Failed to load data',
    });
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Notification Test</h1>
      <div className="flex flex-col gap-4 max-w-md mx-auto">
        <Button onClick={showSuccessNotification} className="w-full">
          Show Success Notification
        </Button>
        <Button onClick={showErrorNotification} className="w-full" variant="destructive">
          Show Error Notification
        </Button>
        <Button onClick={showWarningNotification} className="w-full" variant="outline">
          Show Warning Notification
        </Button>
        <Button onClick={showInfoNotification} className="w-full" variant="secondary">
          Show Info Notification
        </Button>
        <Button onClick={showFirenestNotification} className="w-full bg-gradient-to-r from-fiery to-fiery-600">
          Show Firenest Notification
        </Button>
        <Button onClick={showPromiseNotification} className="w-full" variant="default">
          Show Promise Notification
        </Button>
      </div>
    </div>
  );
};

export default NotificationTest;
