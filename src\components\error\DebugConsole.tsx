import React, { useState, useEffect } from 'react';
import { X, Download, Trash, Search, Info, AlertCircle, AlertTriangle, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { notify } from '@/components/ui/notification-system';
import { getStoredErrors, clearStoredErrors } from '@/lib/error-utils';

interface DebugConsoleProps {
  isOpen: boolean;
  onClose: () => void;
}

const DebugConsole: React.FC<DebugConsoleProps> = ({ isOpen, onClose }) => {
  const [errors, setErrors] = useState<any[]>([]);
  const [filteredErrors, setFilteredErrors] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState<'errors' | 'system' | 'network'>('errors');
  const [systemInfo, setSystemInfo] = useState<Record<string, any>>({});
  const [networkInfo, setNetworkInfo] = useState<Record<string, any>>({});

  useEffect(() => {
    if (isOpen) {
      loadErrors();
      collectSystemInfo();
      collectNetworkInfo();
    }
  }, [isOpen]);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredErrors(errors);
    } else {
      const term = searchTerm.toLowerCase();
      setFilteredErrors(errors.filter(error =>
        (error.message && error.message.toLowerCase().includes(term)) ||
        (error.category && error.category.toLowerCase().includes(term)) ||
        (error.severity && error.severity.toLowerCase().includes(term))
      ));
    }
  }, [searchTerm, errors]);

  const loadErrors = () => {
    const storedErrors = getStoredErrors();
    setErrors(storedErrors);
    setFilteredErrors(storedErrors);
  };

  const handleClearErrors = () => {
    clearStoredErrors();
    setErrors([]);
    setFilteredErrors([]);
    notify.success('Debug logs cleared', {
      title: 'Logs Cleared',
      duration: 3000
    });
  };

  const handleDownloadLogs = () => {
    // Prepare log content based on selected tab
    let logContent = '';
    let filename = '';

    if (selectedTab === 'errors') {
      logContent = JSON.stringify(errors, null, 2);
      filename = 'firenest-error-logs.json';
    } else if (selectedTab === 'system') {
      logContent = JSON.stringify(systemInfo, null, 2);
      filename = 'firenest-system-info.json';
    } else if (selectedTab === 'network') {
      logContent = JSON.stringify(networkInfo, null, 2);
      filename = 'firenest-network-info.json';
    }

    // Create and download file
    const blob = new Blob([logContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    notify.success('Logs downloaded', {
      title: 'Download Complete',
      duration: 3000
    });
  };

  const collectSystemInfo = () => {
    const info: Record<string, any> = {
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        cookiesEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack,
        platform: navigator.platform,
        vendor: navigator.vendor,
      },
      screen: {
        width: window.screen.width,
        height: window.screen.height,
        availWidth: window.screen.availWidth,
        availHeight: window.screen.availHeight,
        colorDepth: window.screen.colorDepth,
        pixelDepth: window.screen.pixelDepth,
        devicePixelRatio: window.devicePixelRatio,
      },
      window: {
        innerWidth: window.innerWidth,
        innerHeight: window.innerHeight,
        outerWidth: window.outerWidth,
        outerHeight: window.outerHeight,
      },
      time: {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: Intl.DateTimeFormat().resolvedOptions().locale,
        currentTime: new Date().toISOString(),
      },
      localStorage: {
        available: (() => {
          try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return true;
          } catch (e) {
            return false;
          }
        })(),
        size: (() => {
          try {
            let total = 0;
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i) || '';
              const value = localStorage.getItem(key) || '';
              total += key.length + value.length;
            }
            return `${(total / 1024).toFixed(2)} KB`;
          } catch (e) {
            return 'Unknown';
          }
        })(),
      },
      performance: window.performance ? {
        memory: (window.performance as any).memory ? {
          jsHeapSizeLimit: ((window.performance as any).memory.jsHeapSizeLimit / (1024 * 1024)).toFixed(2) + ' MB',
          totalJSHeapSize: ((window.performance as any).memory.totalJSHeapSize / (1024 * 1024)).toFixed(2) + ' MB',
          usedJSHeapSize: ((window.performance as any).memory.usedJSHeapSize / (1024 * 1024)).toFixed(2) + ' MB',
        } : 'Not available',
        navigation: window.performance.navigation ? {
          redirectCount: window.performance.navigation.redirectCount,
          type: (() => {
            switch (window.performance.navigation.type) {
              case 0: return 'Navigate';
              case 1: return 'Reload';
              case 2: return 'Back/Forward';
              default: return 'Other';
            }
          })(),
        } : 'Not available',
        timing: window.performance.timing ? {
          loadTime: window.performance.timing.loadEventEnd - window.performance.timing.navigationStart,
          domReadyTime: window.performance.timing.domComplete - window.performance.timing.domLoading,
          readyStart: window.performance.timing.fetchStart - window.performance.timing.navigationStart,
          redirectTime: window.performance.timing.redirectEnd - window.performance.timing.redirectStart,
          appcacheTime: window.performance.timing.domainLookupStart - window.performance.timing.fetchStart,
          unloadEventTime: window.performance.timing.unloadEventEnd - window.performance.timing.unloadEventStart,
          lookupDomainTime: window.performance.timing.domainLookupEnd - window.performance.timing.domainLookupStart,
          connectTime: window.performance.timing.connectEnd - window.performance.timing.connectStart,
          requestTime: window.performance.timing.responseEnd - window.performance.timing.requestStart,
          initDomTreeTime: window.performance.timing.domInteractive - window.performance.timing.responseEnd,
          loadEventTime: window.performance.timing.loadEventEnd - window.performance.timing.loadEventStart,
        } : 'Not available',
      } : 'Not available',
    };

    setSystemInfo(info);
  };

  const collectNetworkInfo = () => {
    const info: Record<string, any> = {
      online: navigator.onLine,
      connection: (navigator as any).connection ? {
        effectiveType: (navigator as any).connection.effectiveType,
        downlink: (navigator as any).connection.downlink,
        rtt: (navigator as any).connection.rtt,
        saveData: (navigator as any).connection.saveData,
      } : 'Not available',
      url: {
        href: window.location.href,
        origin: window.location.origin,
        protocol: window.location.protocol,
        host: window.location.host,
        hostname: window.location.hostname,
        port: window.location.port,
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash,
      },
      referrer: document.referrer,
      lastNavigationTime: window.performance && window.performance.timing ?
        new Date(window.performance.timing.navigationStart).toISOString() : 'Not available',
    };

    // Add performance entries if available
    if (window.performance && window.performance.getEntriesByType) {
      try {
        const resources = window.performance.getEntriesByType('resource');
        const resourceStats = resources.map(resource => ({
          name: resource.name,
          entryType: resource.entryType,
          startTime: resource.startTime,
          duration: resource.duration,
          initiatorType: resource.initiatorType,
        }));

        info.resources = {
          count: resourceStats.length,
          totalDuration: resourceStats.reduce((sum, r) => sum + r.duration, 0),
          slowest: resourceStats.sort((a, b) => b.duration - a.duration).slice(0, 5),
        };
      } catch (e) {
        info.resources = { error: 'Failed to collect resource timing' };
      }
    }

    setNetworkInfo(info);
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-cool-500" />;
      default:
        return <Bug className="h-4 w-4 text-white/70" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in overflow-y-auto">
      <div className="bg-dark-900 border border-white/10 rounded-xl w-full max-w-5xl my-4 md:my-6 relative max-h-[90vh] flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center">
            <Bug className="h-5 w-5 text-fiery mr-2" />
            <h2 className="text-lg font-semibold text-white">Firenest Debug Console</h2>
          </div>
          <button
            onClick={onClose}
            className="text-white/70 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex border-b border-white/10">
          <button
            className={`px-4 py-2 text-sm font-medium ${selectedTab === 'errors' ? 'text-fiery border-b-2 border-fiery' : 'text-white/70 hover:text-white'}`}
            onClick={() => setSelectedTab('errors')}
          >
            Error Logs
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${selectedTab === 'system' ? 'text-fiery border-b-2 border-fiery' : 'text-white/70 hover:text-white'}`}
            onClick={() => setSelectedTab('system')}
          >
            System Info
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${selectedTab === 'network' ? 'text-fiery border-b-2 border-fiery' : 'text-white/70 hover:text-white'}`}
            onClick={() => setSelectedTab('network')}
          >
            Network Info
          </button>
        </div>

        <div className="p-4 flex items-center justify-between gap-4">
          {selectedTab === 'errors' && (
            <>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
                <Input
                  type="text"
                  placeholder="Search errors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-dark-800 border-white/10"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearErrors}
                className="border-white/20"
              >
                <Trash className="h-4 w-4 mr-2" />
                Clear Logs
              </Button>
            </>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadLogs}
            className="border-white/20"
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {selectedTab === 'errors' && (
            <>
              {filteredErrors.length === 0 ? (
                <div className="text-center py-12 text-white/50">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p>No error logs found</p>
                  <p className="text-sm mt-2">Errors will appear here when they occur</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredErrors.map((error, index) => (
                    <div key={index} className="firenest-card p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center">
                          {getSeverityIcon(error.severity)}
                          <span className="ml-2 font-medium text-white/90">{error.message}</span>
                        </div>
                        <div className="text-xs text-white/50">
                          {new Date(error.timestamp).toLocaleString()}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div className="text-white/70">
                          <span className="text-white/50">Category:</span> {error.category || 'Unknown'}
                        </div>
                        <div className="text-white/70">
                          <span className="text-white/50">URL:</span> {error.context?.url || 'N/A'}
                        </div>
                      </div>

                      {error.stack && (
                        <div className="mt-2">
                          <details className="text-xs">
                            <summary className="cursor-pointer text-white/50 hover:text-white/70 transition-colors">
                              Stack Trace
                            </summary>
                            <pre className="mt-2 p-2 bg-dark-900/70 rounded border border-white/5 overflow-x-auto text-white/70">
                              {error.stack}
                            </pre>
                          </details>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </>
          )}

          {selectedTab === 'system' && (
            <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
              <pre className="text-xs text-white/70 overflow-x-auto">
                {JSON.stringify(systemInfo, null, 2)}
              </pre>
            </div>
          )}

          {selectedTab === 'network' && (
            <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
              <pre className="text-xs text-white/70 overflow-x-auto">
                {JSON.stringify(networkInfo, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DebugConsole;
