/**
 * <PERSON><PERSON><PERSON> to update the redirect URI for the test partner in the database
 */

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://wjwguxccykrbarehqgpq.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.6P_W7tcaTpt3nCXv-AnuQrbwKD_IMvn0zIrsSPgq4hw';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Test partner client ID
const testPartnerClientId = 'test-partner-client';

// New redirect URI
const newRedirectUri = 'http://localhost:3002/auth/callback';

async function updateRedirectUri() {
  try {
    console.log(`Updating redirect URI for client ID: ${testPartnerClientId}`);
    console.log(`New redirect URI: ${newRedirectUri}`);

    // Get the partner account
    const { data: partner, error: fetchError } = await supabase
      .from('partner_accounts')
      .select('id, name, redirect_uris')
      .eq('client_id', testPartnerClientId)
      .single();

    if (fetchError) {
      throw new Error(`Error fetching partner: ${fetchError.message}`);
    }

    if (!partner) {
      throw new Error(`Partner with client ID ${testPartnerClientId} not found`);
    }

    console.log('Found partner:', {
      id: partner.id,
      name: partner.name,
      currentRedirectUris: partner.redirect_uris
    });

    // Update the redirect URIs
    const { error: updateError } = await supabase
      .from('partner_accounts')
      .update({
        redirect_uris: [newRedirectUri],
        updated_at: new Date().toISOString()
      })
      .eq('id', partner.id);

    if (updateError) {
      throw new Error(`Error updating partner: ${updateError.message}`);
    }

    console.log('Successfully updated redirect URI');

    // Check if partners table exists and update it too for backward compatibility
    const { count, error: tableError } = await supabase
      .from('partners')
      .select('*', { count: 'exact', head: true });

    if (tableError && !tableError.message.includes('does not exist')) {
      throw new Error(`Error checking partners table: ${tableError.message}`);
    }

    if (count !== undefined) {
      console.log('Partners table exists, updating it too');

      const { error: partnersUpdateError } = await supabase
        .from('partners')
        .update({
          redirect_uris: [newRedirectUri],
          updated_at: new Date().toISOString()
        })
        .eq('client_id', testPartnerClientId);

      if (partnersUpdateError) {
        throw new Error(`Error updating partners table: ${partnersUpdateError.message}`);
      }

      console.log('Successfully updated redirect URI in partners table');
    } else {
      console.log('Partners table does not exist, skipping');
    }

    console.log('Redirect URI update completed successfully');
  } catch (error) {
    console.error('Error updating redirect URI:', error.message);
    process.exit(1);
  }
}

// Run the update function
updateRedirectUri()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
