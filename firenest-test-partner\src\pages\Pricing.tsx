import Header from '../components/Header';
import Footer from '../components/Footer';
import PricingTable from '../components/PricingTable';
import FeatureComparison from '../components/FeatureComparison';
import FAQ from '../components/FAQ';
import CTA from '../components/CTA';

const Pricing = () => {
  return (
    <div className="min-h-screen flex flex-col bg-dark-900">
      <Header />
      
      <main className="flex-1">
        <section className="py-20 md:py-32 bg-dark-900 relative overflow-hidden">
          {/* Background gradient */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute -top-24 -left-24 w-96 h-96 bg-primary rounded-full filter blur-3xl opacity-20"></div>
            <div className="absolute -bottom-24 -right-24 w-96 h-96 bg-secondary rounded-full filter blur-3xl opacity-20"></div>
          </div>
          
          <div className="container relative z-10">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Simple, <span className="gradient-text">Transparent Pricing</span>
              </h1>
              <p className="text-xl text-light-600 mb-8 max-w-2xl mx-auto">
                Choose the plan that's right for your team. All plans include a 14-day free trial with no credit card required.
              </p>
            </div>
          </div>
        </section>
        
        <PricingTable />
        <FeatureComparison />
        <FAQ />
        <CTA />
      </main>
      
      <Footer />
    </div>
  );
};

export default Pricing;
