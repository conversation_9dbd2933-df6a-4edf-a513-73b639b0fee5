@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  /* Firenest Card System - Consistent with main application */
  .firenest-card {
    @apply bg-gradient-to-br from-[#1A1A28] to-[#252536] rounded-lg p-4 shadow-md transition-all duration-300 ease-in-out border border-white/5;
    position: relative;
    overflow: hidden;
  }

  .firenest-card:hover {
    @apply shadow-lg border-white/10;
    box-shadow: 0 12px 20px -5px rgba(0, 0, 0, 0.3), 0 8px 16px -8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .firenest-card-accent {
    @apply bg-gradient-to-br from-[#1A1A28] to-[#252536] rounded-lg p-4 shadow-md transition-all duration-300 ease-in-out border border-white/5 border-l-2 border-l-fiery;
    position: relative;
    overflow: hidden;
  }

  .firenest-card-accent:hover {
    @apply shadow-lg border-white/10;
    box-shadow: 0 12px 20px -5px rgba(255, 69, 0, 0.15), 0 8px 16px -8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .firenest-nested-card {
    @apply bg-[#272742] rounded-lg p-3 shadow-sm transition-all duration-300 ease-in-out border border-white/10;
    position: relative;
    overflow: hidden;
  }

  .firenest-nested-card:hover {
    @apply shadow-md border-white/20;
    box-shadow: 0 6px 16px -4px rgba(0, 0, 0, 0.4);
  }

  .firenest-card-interactive {
    @apply firenest-card cursor-pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .firenest-card-interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 20px -5px rgba(0, 0, 0, 0.2), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }

  /* Status indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-created {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
  }

  .status-uploading {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }

  .status-validating {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }

  .status-ready {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  .status-simulating {
    @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
  }

  .status-complete {
    @apply bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200;
  }

  .status-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }

  .status-failed {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-fiery text-white hover:bg-fiery-600 focus:ring-fiery/20;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-outline {
    @apply border border-white/20 bg-transparent text-white hover:bg-white/10;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  .loading-shimmer {
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.0) 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Form elements */
  .form-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  /* Upload area */
  .upload-area {
    @apply border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center transition-colors;
  }

  .upload-area.dragover {
    @apply border-fiery bg-fiery/5;
  }

  /* Progress bars */
  .progress-bar {
    @apply w-full bg-secondary rounded-full h-2;
  }

  .progress-fill {
    @apply bg-fiery h-2 rounded-full transition-all duration-300;
  }

  /* Metrics cards */
  .metric-card {
    @apply firenest-card p-6;
  }

  .metric-value {
    @apply text-3xl font-bold text-white;
  }

  .metric-label {
    @apply text-sm text-muted-foreground;
  }

  .metric-change {
    @apply text-xs font-medium;
  }

  .metric-change.positive {
    @apply text-green-400;
  }

  .metric-change.negative {
    @apply text-red-400;
  }

  /* Data table */
  .data-table {
    @apply w-full border-collapse;
  }

  .data-table th {
    @apply border-b border-border px-4 py-3 text-left text-sm font-medium text-muted-foreground;
  }

  .data-table td {
    @apply border-b border-border px-4 py-3 text-sm;
  }

  .data-table tr:hover {
    @apply bg-muted/50;
  }

  /* Sidebar */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-card border-r border-border transition-transform duration-300;
  }

  .sidebar.collapsed {
    @apply -translate-x-full;
  }

  /* Header */
  .header {
    @apply sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60;
  }

  /* Content area */
  .main-content {
    @apply flex-1 space-y-4 p-8 pt-6;
  }

  /* Responsive utilities */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Animation utilities */
  .fade-in {
    @apply animate-fade-in;
  }

  .slide-in {
    @apply animate-slide-in;
  }

  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
}
