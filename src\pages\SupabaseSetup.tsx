import { useState } from 'react';
import { checkSupabaseAuth } from '@/lib/checkSupabaseAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle, XCircle, Copy, ExternalLink } from 'lucide-react';
import { Link } from 'react-router-dom';

const SupabaseSetup = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [checkResult, setCheckResult] = useState<{ success: boolean; message: string } | null>(null);
  const [showSql, setShowSql] = useState(false);

  const handleCheck = async () => {
    setIsChecking(true);
    try {
      const result = await checkSupabaseAuth();
      setCheckResult(result);
    } finally {
      setIsChecking(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('SQL copied to clipboard!');
  };

  return (
    <div className="min-h-screen darker-bg text-white p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-2">Supabase Authentication Setup</h1>
        <p className="text-white/70 mb-8">
          This page helps you check and fix your Supabase authentication setup.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle>Authentication Status</CardTitle>
                <CardDescription>Check if your Supabase authentication is properly set up</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button 
                    onClick={handleCheck} 
                    disabled={isChecking}
                    className="pop-button"
                  >
                    {isChecking ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Checking...
                      </>
                    ) : (
                      'Check Authentication Setup'
                    )}
                  </Button>

                  {checkResult && (
                    <div className={`p-4 rounded-md ${checkResult.success ? 'bg-green-500/20 border border-green-500/50' : 'bg-red-500/20 border border-red-500/50'}`}>
                      <div className="flex items-start gap-3">
                        {checkResult.success ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                        )}
                        <div>
                          <p className={`font-medium ${checkResult.success ? 'text-green-400' : 'text-red-400'}`}>
                            {checkResult.success ? 'Success!' : 'Setup Incomplete'}
                          </p>
                          <p className="text-white/80 mt-1">{checkResult.message}</p>
                          
                          {!checkResult.success && (
                            <div className="mt-4">
                              <p className="text-white/80 mb-2">To fix this issue:</p>
                              <ol className="list-decimal list-inside space-y-2 text-white/70">
                                <li>Go to your <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer" className="text-fiery hover:underline inline-flex items-center">Supabase Dashboard <ExternalLink className="h-3 w-3 ml-1" /></a></li>
                                <li>Select your project</li>
                                <li>Go to the SQL Editor</li>
                                <li>Create a new query</li>
                                <li>Paste the SQL script below and run it</li>
                                <li>Come back here and check again</li>
                              </ol>
                              
                              <Button 
                                variant="outline" 
                                className="mt-4 border-white/10 hover:bg-white/5"
                                onClick={() => setShowSql(!showSql)}
                              >
                                {showSql ? 'Hide SQL Script' : 'Show SQL Script'}
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {showSql && (
                    <div className="mt-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-lg font-medium">SQL Script</h3>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => copyToClipboard(sqlScript)}
                          className="text-white/70 hover:text-white hover:bg-white/10"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </Button>
                      </div>
                      <div className="bg-black/50 p-4 rounded-md overflow-x-auto">
                        <pre className="text-white/80 text-sm whitespace-pre-wrap">{sqlScript}</pre>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle>Navigation</CardTitle>
                <CardDescription>Go to other pages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button asChild className="w-full" variant="outline">
                    <Link to="/">Home</Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link to="/login">Login</Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link to="/signup">Signup</Link>
                  </Button>
                  <Button asChild className="w-full pop-button">
                    <Link to="/dashboard">Dashboard</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

// SQL script for setting up authentication tables
const sqlScript = `-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table for storing additional user information
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table for storing additional profile information
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  company TEXT,
  job_title TEXT,
  bio TEXT,
  website TEXT,
  phone TEXT,
  country TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_settings table for storing user preferences
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  email_notifications BOOLEAN DEFAULT TRUE,
  theme TEXT DEFAULT 'dark',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_credits table for tracking credit usage
CREATE TABLE IF NOT EXISTS user_credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  total_credits INTEGER DEFAULT 0,
  used_credits INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create credit_transactions table for tracking credit history
CREATE TABLE IF NOT EXISTS credit_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  description TEXT,
  transaction_type TEXT NOT NULL, -- 'purchase', 'usage', 'refund', 'bonus'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
CREATE POLICY "Users can view their own data" 
  ON users FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" 
  ON users FOR UPDATE 
  USING (auth.uid() = id);

-- Create policies for user_profiles table
CREATE POLICY "Users can view their own profile" 
  ON user_profiles FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
  ON user_profiles FOR UPDATE 
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" 
  ON user_profiles FOR INSERT 
  WITH CHECK (auth.uid() = id);

-- Create policies for user_settings table
CREATE POLICY "Users can view their own settings" 
  ON user_settings FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" 
  ON user_settings FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" 
  ON user_settings FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create policies for user_credits table
CREATE POLICY "Users can view their own credits" 
  ON user_credits FOR SELECT 
  USING (auth.uid() = user_id);

-- Create policies for credit_transactions table
CREATE POLICY "Users can view their own transactions" 
  ON credit_transactions FOR SELECT 
  USING (auth.uid() = user_id);

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into users table
  INSERT INTO public.users (id, email, name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  
  -- Insert into user_profiles table
  INSERT INTO public.user_profiles (id, user_id)
  VALUES (NEW.id, NEW.id);
  
  -- Insert into user_settings table
  INSERT INTO public.user_settings (user_id)
  VALUES (NEW.id);
  
  -- Insert into user_credits table with initial credits
  INSERT INTO public.user_credits (user_id, total_credits, used_credits)
  VALUES (NEW.id, 500, 0);
  
  -- Record initial credit transaction
  INSERT INTO public.credit_transactions (user_id, amount, description, transaction_type)
  VALUES (NEW.id, 500, 'Welcome bonus credits', 'bonus');
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to handle user updates
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Update users table when auth.users is updated
  UPDATE public.users
  SET 
    email = NEW.email,
    name = NEW.raw_user_meta_data->>'name',
    avatar_url = NEW.raw_user_meta_data->>'avatar_url',
    updated_at = NOW()
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user updates
CREATE OR REPLACE TRIGGER on_auth_user_updated
  AFTER UPDATE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_user_update();`;

export default SupabaseSetup;
