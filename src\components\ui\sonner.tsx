import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme="dark"
      className="toaster group"
      position="top-right"
      offset="80px"
      toastOptions={{
        classNames: {
          toast:
            "group toast toast-glow group-[.toaster]:bg-dark-900/70 group-[.toaster]:text-white group-[.toaster]:border-fiery/20 group-[.toaster]:shadow-[0_8px_20px_-6px_rgba(255,69,0,0.4)] backdrop-blur-xl",
          description: "group-[.toast]:text-white/80",
          actionButton:
            "group-[.toast]:bg-fiery group-[.toast]:text-white group-[.toast]:hover:bg-fiery-500",
          cancelButton:
            "group-[.toast]:bg-dark-700 group-[.toast]:text-white/90 group-[.toast]:hover:bg-dark-600",
          success: "group-[.toast]:border-l-4 group-[.toast]:border-l-fiery",
          error: "group-[.toast]:border-l-4 group-[.toast]:border-l-red-500",
          warning: "group-[.toast]:border-l-4 group-[.toast]:border-l-yellow-500",
          info: "group-[.toast]:border-l-4 group-[.toast]:border-l-cool-500",
        },
      }}
      {...props}
    />
  )
}

export { Toaster }
