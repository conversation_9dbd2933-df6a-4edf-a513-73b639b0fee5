import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import {
  Zap,
  CreditCard,
  BarChart3,
  Clock,
  ArrowRight,
  TrendingUp,
  TrendingDown,
  Sparkles,
  Lightbulb,
  Rocket,
  Plus,
  ChevronRight,
  ExternalLink,
  Layers,
  Settings,
  Users,
  HelpCircle,
  AlertCircle,
  Calendar,
  DollarSign,
  Target,
  PieChart,
  Activity,
  Star,
  CheckCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import ModernCard from '@/components/ui/modern-card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// Mock data for the dashboard
const mockTools = [
  {
    id: 'chatgpt',
    name: 'ChatGPT',
    description: 'AI chat assistant',
    icon: 'chatgpt',
    usageToday: 12,
    usageTotal: 145,
    efficiency: 92,
    costPerUse: 0.35,
    trend: 'up',
    growthRate: 8.5
  },
  {
    id: 'midjourney',
    name: 'Midjourney',
    description: 'AI image generation',
    icon: 'midjourney',
    usageToday: 5,
    usageTotal: 78,
    efficiency: 85,
    costPerUse: 1.2,
    trend: 'up',
    growthRate: 12.3
  },
  {
    id: 'claude',
    name: 'Claude',
    description: 'Advanced AI assistant',
    icon: 'claude',
    usageToday: 8,
    usageTotal: 92,
    efficiency: 89,
    costPerUse: 0.45,
    trend: 'down',
    growthRate: -2.1
  },
  {
    id: 'dalle',
    name: 'DALL-E',
    description: 'OpenAI image generation',
    icon: 'dalle',
    usageToday: 3,
    usageTotal: 56,
    efficiency: 78,
    costPerUse: 0.95,
    trend: 'up',
    growthRate: 5.7
  }
];

const mockActivity = [
  { id: 1, tool: 'ChatGPT', action: 'Generated response', time: '10 minutes ago', credits: 5, status: 'success', duration: '2.3s' },
  { id: 2, tool: 'Midjourney', action: 'Created image', time: '1 hour ago', credits: 10, status: 'success', duration: '5.7s' },
  { id: 3, tool: 'Claude', action: 'Analyzed document', time: '3 hours ago', credits: 15, status: 'success', duration: '4.1s' },
  { id: 4, tool: 'DALL-E', action: 'Generated artwork', time: 'Yesterday', credits: 12, status: 'warning', duration: '8.2s' },
  { id: 5, tool: 'ChatGPT', action: 'Summarized text', time: 'Yesterday', credits: 3, status: 'success', duration: '1.5s' }
];

// Daily usage data for the past 15 days
const mockUsageTrend = [25, 30, 45, 35, 55, 40, 60, 45, 50, 55, 70, 65, 75, 70, 90];

// Tool usage distribution
const mockToolDistribution = [
  { tool: 'ChatGPT', percentage: 42, color: 'from-fiery to-fiery-600' },
  { tool: 'Midjourney', percentage: 28, color: 'from-blue-500 to-blue-600' },
  { tool: 'Claude', percentage: 18, color: 'from-purple-500 to-purple-600' },
  { tool: 'DALL-E', percentage: 12, color: 'from-teal-500 to-teal-600' }
];

// Credit usage forecast
const mockCreditForecast = {
  currentRate: 15,  // credits per day
  daysRemaining: 18,
  projectedDepletion: 'Nov 28, 2023',
  recommendedPurchase: 300
};

// Performance metrics
const mockPerformanceMetrics = {
  averageResponseTime: '3.2s',
  uptime: '99.8%',
  successRate: '97.5%',
  errorRate: '2.5%',
  costEfficiency: '92%',
  trendsWeekOverWeek: '+8.3%'
};

/**
 * Enterprise Dashboard Home Page
 * Inspired by industry leaders like Stripe, Vercel, and Linear
 */
const EnterpriseDashboard = () => {
  const { user, credits } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [usageData, setUsageData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');
  // Default theme is darker for compatibility with SimpleDashboardLayout
  const theme = 'darker';

  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setUsageData({
        totalUsage: 345,
        monthlyUsage: 120,
        dailyAverage: 15,
        trend: mockUsageTrend,
        weekOverWeekChange: 8.3,
        monthOverMonthChange: 12.5,
        peakUsageTime: '2-4 PM',
        mostEfficientTool: 'ChatGPT',
        leastEfficientTool: 'DALL-E',
        toolDistribution: mockToolDistribution,
        creditForecast: mockCreditForecast,
        performanceMetrics: mockPerformanceMetrics
      });
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Format large numbers with commas
  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Calculate credit usage percentage
  const calculateUsagePercentage = () => {
    if (!credits) return 0;
    const total = credits.totalCredits;
    const used = credits.usedCredits;
    return total > 0 ? Math.round((used / total) * 100) : 0;
  };

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">
            Welcome back, {user?.name?.split(' ')[0] || 'User'}
          </h1>
          <p className="text-white/70 mt-1">
            Here's what's happening with your AI tools today.
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="border-white/10 hover:bg-white/5">
            <Settings className="h-4 w-4 mr-2" />
            Customize
          </Button>
          <Button className="bg-fiery hover:bg-fiery-600">
            <Zap className="h-4 w-4 mr-2" />
            Launch Workbench
          </Button>
        </div>
      </div>

      {/* Quick stats - Using ModernCard Component */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <ModernCard
          title="Total Credits Used"
          value={isLoading ? <Skeleton className="h-8 w-16 bg-white/5" /> : "225"}
          icon={<CreditCard />}
          iconColor="red"
          trend={{
            value: "15% vs last week",
            isPositive: true
          }}
          badge="Weekly"
          badgeColor="red"
        />

        <ModernCard
          title="Average Daily Usage"
          value={isLoading ? <Skeleton className="h-8 w-16 bg-white/5" /> : "32"}
          icon={<BarChart3 />}
          iconColor="blue"
          description="credits per day"
          footer={
            <div className="flex items-center text-white/70 text-xs">
              <Clock className="h-3 w-3 mr-1" />
              <span>Peak: 2-4 PM</span>
            </div>
          }
        />

        <ModernCard
          title="Success Rate"
          value={isLoading ? <Skeleton className="h-8 w-16 bg-white/5" /> : "98%"}
          icon={<CheckCircle />}
          iconColor="purple"
          footer={
            <div className="flex flex-col">
              <div className="text-white/70 text-xs mb-1">
                Requests completed successfully
              </div>
              <Progress value={98} className="h-1.5 bg-white/10" indicatorClassName="bg-purple-500" />
            </div>
          }
        />

        <ModernCard
          title="Cost Efficiency"
          value={isLoading ? <Skeleton className="h-8 w-16 bg-white/5" /> : "$0.12"}
          icon={<DollarSign />}
          iconColor="green"
          description="avg. cost per credit"
          badge="Good"
          badgeColor="green"
        />
      </div>

      {/* Main content tabs */}
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="bg-dark-800 border-white/10">
          <TabsTrigger value="overview" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="tools" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            AI Tools
          </TabsTrigger>
          <TabsTrigger value="activity" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Recent Activity
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Usage chart */}
            <div className="firenest-card overflow-hidden lg:col-span-2">
              <div className="p-4">
                <div className="flex items-center mb-2">
                  <div className="firenest-card-icon firenest-card-icon-green">
                    <BarChart3 className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-lg text-white font-medium">Usage Trends</span>
                </div>
                <p className="text-sm text-white/70 ml-7 mb-4">Your AI tool usage over the past 15 days</p>

                {isLoading ? (
                  <Skeleton className="h-64 w-full bg-white/5" />
                ) : (
                  <div className="h-64 w-full">
                    {/* Simple chart visualization */}
                    <div className="h-full w-full flex items-end justify-between gap-1">
                      {usageData?.trend.map((value: number, index: number) => (
                        <div key={index} className="h-full flex-1 flex flex-col justify-end">
                          <div
                            className="bg-fiery rounded-t-sm"
                            style={{ height: `${value}%` }}
                          ></div>
                          <div className="mt-2 text-xs text-white/50 text-center">
                            {index + 1}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mt-6">
                  <Button variant="outline" size="sm" className="border-white/10 hover:bg-white/5" asChild>
                    <Link to="/dashboard/analytics/usage">
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Detailed Analytics
                    </Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Recent activity */}
            <div className="firenest-card overflow-hidden">
              <div className="p-4">
                <div className="flex items-center mb-2">
                  <div className="firenest-card-icon firenest-card-icon-red">
                    <Zap className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-lg text-white font-medium">Recent Activity</span>
                </div>
                <p className="text-sm text-white/70 ml-7 mb-4">Your latest interactions</p>

                {isLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center gap-3">
                        <Skeleton className="h-8 w-8 rounded-full bg-white/5" />
                        <div className="space-y-1.5 flex-1">
                          <Skeleton className="h-4 w-full bg-white/5" />
                          <Skeleton className="h-3 w-24 bg-white/5" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {mockActivity.slice(0, 3).map((activity) => (
                      <div key={activity.id} className="firenest-nested-card flex items-start gap-3 p-2 hover:shadow-md transition-all">
                        <div className="h-8 w-8 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Zap className="h-4 w-4 text-fiery" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-white">{activity.action} with {activity.tool}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Clock className="h-3 w-3 text-white/50 mr-1" />
                            <p className="text-xs text-white/50">{activity.time}</p>
                            <span className="text-white/30">•</span>
                            <p className="text-xs text-fiery">{activity.credits} credits</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="mt-6">
                  <Button variant="ghost" size="sm" className="text-white/70 hover:text-white hover:bg-white/5" asChild>
                    <Link to="#activity" onClick={() => setActiveTab('activity')}>
                      View All Activity
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>



          {/* Quick access tools */}
          <div className="firenest-card overflow-hidden">
            <div className="p-4">
              <div className="flex items-center mb-2">
                <div className="firenest-card-icon firenest-card-icon-blue">
                  <Sparkles className="h-3 w-3 text-white" />
                </div>
                <span className="text-lg text-white font-medium">Quick Access</span>
              </div>
              <p className="text-sm text-white/70 ml-7 mb-4">Your most used AI tools</p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {isLoading ? (
                  Array(4).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-32 rounded-lg bg-white/5" />
                  ))
                ) : (
                  mockTools.map((tool) => (
                    <Link
                      key={tool.id}
                      to={`/dashboard/workbench?tool=${tool.id}`}
                      className="group"
                    >
                      <div className="firenest-nested-card-interactive h-full">
                        <div className="flex items-center justify-between">
                          <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                            <Sparkles className="h-5 w-5 text-fiery" />
                          </div>
                          <span className="firenest-card-badge firenest-card-badge-blue">
                            {tool.usageToday} today
                          </span>
                        </div>
                        <h3 className="mt-3 font-medium text-white">{tool.name}</h3>
                        <p className="text-sm text-white/70 mt-1">{tool.description}</p>
                      </div>
                    </Link>
                  ))
                )}
              </div>

              <div className="mt-6">
                <Button asChild>
                  <Link to="/dashboard/workbench">
                    <Layers className="h-4 w-4 mr-2" />
                    Open Workbench
                  </Link>
                </Button>
              </div>
            </div>
          </div>


        </TabsContent>

        {/* Tools Tab */}
        <TabsContent value="tools" className="space-y-4">
          <div className="firenest-card overflow-hidden">
            <div className="p-4">
              <div className="flex items-center mb-2">
                <div className="firenest-card-icon firenest-card-icon-purple">
                  <Layers className="h-3 w-3 text-white" />
                </div>
                <span className="text-lg text-white font-medium">AI Tools</span>
              </div>
              <p className="text-sm text-white/70 ml-7 mb-4">All available AI tools in your subscription</p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {isLoading ? (
                  Array(6).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-40 rounded-lg bg-white/5" />
                  ))
                ) : (
                  [...mockTools, ...mockTools.slice(0, 2)].map((tool, index) => (
                    <Link
                      key={`${tool.id}-${index}`}
                      to={`/dashboard/workbench?tool=${tool.id}`}
                      className="group"
                    >
                      <div className="firenest-nested-card-interactive h-full">
                        <div className="flex items-center justify-between">
                          <div className="h-12 w-12 rounded-md bg-fiery/20 flex items-center justify-center">
                            <Sparkles className="h-6 w-6 text-fiery" />
                          </div>
                          <span className="firenest-card-badge firenest-card-badge-blue">
                            {tool.usageTotal} uses
                          </span>
                        </div>
                        <h3 className="mt-4 font-medium text-white text-lg">{tool.name}</h3>
                        <p className="text-sm text-white/70 mt-1">{tool.description}</p>
                        <div className="mt-4 flex items-center text-fiery text-sm font-medium">
                          Launch Tool
                          <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </Link>
                  ))
                )}
              </div>

              <div className="mt-6">
                <Button asChild>
                  <Link to="/dashboard/workbench">
                    <Layers className="h-4 w-4 mr-2" />
                    Open Workbench
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4">
          <div className="firenest-card overflow-hidden">
            <div className="p-4">
              <div className="flex items-center mb-2">
                <div className="firenest-card-icon firenest-card-icon-red">
                  <Zap className="h-3 w-3 text-white" />
                </div>
                <span className="text-lg text-white font-medium">Recent Activity</span>
              </div>
              <p className="text-sm text-white/70 ml-7 mb-4">Your recent interactions with AI tools</p>

              {isLoading ? (
                <div className="space-y-4">
                  {Array(5).fill(0).map((_, i) => (
                    <div key={i} className="flex items-center gap-4">
                      <Skeleton className="h-10 w-10 rounded-full bg-white/5" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-5 w-full bg-white/5" />
                        <Skeleton className="h-4 w-32 bg-white/5" />
                      </div>
                      <Skeleton className="h-8 w-16 bg-white/5" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {[...mockActivity, ...mockActivity].map((activity, index) => (
                    <div key={`${activity.id}-${index}`} className="firenest-nested-card flex items-start gap-4 p-3 hover:shadow-md transition-all">
                      <div className="h-10 w-10 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Zap className="h-5 w-5 text-fiery" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-white">{activity.action} with {activity.tool}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="h-3 w-3 text-white/50" />
                          <p className="text-xs text-white/50">{activity.time}</p>
                        </div>
                      </div>
                      <span className="firenest-card-badge firenest-card-badge-red">
                        {activity.credits} credits
                      </span>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex justify-between mt-6">
                <Button variant="outline" className="border-white/10 hover:bg-white/5">
                  Export Activity
                </Button>
                <Button variant="outline" className="border-white/10 hover:bg-white/5">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnterpriseDashboard;
