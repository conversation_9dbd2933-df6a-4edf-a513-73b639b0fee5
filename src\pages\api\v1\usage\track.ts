/**
 * Usage Tracking API Endpoint
 *
 * This endpoint allows partners to report usage of their tools by Firenest users.
 * It handles tracking different types of usage metrics and deducts credits accordingly.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import {
  withApiAuth,
  StatusCodes,
  ErrorType,
  ErrorMessages,
  checkUserCredits,
  deductCredits,
  logApiRequest
} from '../utils';

// Usage event types
export enum UsageEventType {
  SESSION_START = 'session_start',
  SESSION_END = 'session_end',
  SESSION_HEARTBEAT = 'session_heartbeat',
  API_CALL = 'api_call',
  RESOURCE_CONSUMPTION = 'resource_consumption',
  CHAT_MESSAGE = 'chat_message',
  CUSTOM = 'custom',
}

// Usage metrics types
export enum MetricType {
  TIME = 'time',
  API_CALLS = 'api_calls',
  RESOURCES = 'resources',
  CUSTOM = 'custom',
}

// Handler for the usage tracking endpoint
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
  context: { userId: string; partnerId: string }
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(StatusCodes.BAD_REQUEST).json({
      success: false,
      error: ErrorType.INVALID_REQUEST,
      message: 'Only POST requests are allowed'
    });
  }

  try {
    const { userId, partnerId } = context;

    // Extract usage data from request body
    const {
      userId: requestUserId,
      featureId,
      units = 1,
      timestamp = new Date().toISOString(),
      metadata = {},
      sessionId = uuidv4() // Generate a session ID if not provided
    } = req.body;

    // For backward compatibility
    const action = req.body.action || 'feature_usage';

    // Validate required parameters
    if (!featureId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing required parameter: featureId is required'
      });
    }

    // If requestUserId is provided, validate it matches the authenticated user
    if (requestUserId && requestUserId !== userId) {
      console.warn(`User ID mismatch: authenticated=${userId}, requested=${requestUserId}`);
      // Continue with the authenticated user ID for security
    }

    // Determine event type
    const eventType = Object.values(UsageEventType).includes(action as UsageEventType)
      ? action
      : UsageEventType.CUSTOM;

    // Skip credit deduction for session_start, session_heartbeat, and session_end events
    const skipCreditDeduction = [
      UsageEventType.SESSION_START,
      UsageEventType.SESSION_HEARTBEAT,
      UsageEventType.SESSION_END
    ].includes(action as UsageEventType);

    // Calculate credits to deduct based on feature and units
    let creditsToDeduct = 0;
    if (!skipCreditDeduction) {
      // Get feature configuration for credit calculation
      const { data: featureConfig, error: featureConfigError } = await supabase
        .from('partner_tools')
        .select('credit_cost_per_unit, metric_type')
        .eq('partner_id', partnerId)
        .eq('feature_id', featureId)
        .maybeSingle();

      if (featureConfigError) {
        console.error('Error fetching feature configuration:', featureConfigError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error fetching feature configuration'
        });
      }

      if (!featureConfig) {
        console.error(`Feature not found: partnerId=${partnerId}, featureId=${featureId}`);
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          error: ErrorType.NOT_FOUND,
          message: 'Feature not found'
        });
      }

      // Use the configured credit cost
      const creditCostPerUnit = featureConfig.credit_cost_per_unit || 1;
      creditsToDeduct = units * creditCostPerUnit;
    }

    // Check if user has sufficient credits (skip for session events)
    if (!skipCreditDeduction && creditsToDeduct > 0) {
      const hasSufficientCredits = await checkUserCredits(userId, creditsToDeduct);
      if (!hasSufficientCredits) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          error: ErrorType.INSUFFICIENT_CREDITS,
          message: ErrorMessages[ErrorType.INSUFFICIENT_CREDITS]
        });
      }
    }

    // Get or create usage session
    let usageSessionId: string;

    if (action === UsageEventType.SESSION_START) {
      // Create a new usage session
      const { data: sessionData, error: sessionError } = await supabase
        .from('usage_sessions')
        .insert({
          id: sessionId,
          user_id: userId,
          tool_id: partnerId,
          start_time: timestamp,
          status: 'active',
          metrics: {},
          estimated_credits: 0
        })
        .select('id')
        .single();

      if (sessionError) {
        console.error('Error creating usage session:', sessionError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error creating usage session'
        });
      }

      usageSessionId = sessionData.id;
    } else if (action === UsageEventType.SESSION_END) {
      // End the usage session
      const { error: sessionError } = await supabase
        .rpc('end_usage_session', {
          session_id: sessionId,
          end_time: timestamp,
          status: 'completed',
          metrics: metadata,
          actual_credits_used: null // Let the function calculate based on estimated
        });

      if (sessionError) {
        console.error('Error ending usage session:', sessionError);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error ending usage session'
        });
      }

      usageSessionId = sessionId;
    } else {
      // Check if session exists
      const { data: sessionData, error: sessionError } = await supabase
        .from('usage_sessions')
        .select('id')
        .eq('id', sessionId)
        .maybeSingle();

      if (sessionError || !sessionData) {
        console.error('Error fetching usage session:', sessionError);
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          error: ErrorType.NOT_FOUND,
          message: 'Usage session not found'
        });
      }

      usageSessionId = sessionData.id;

      // Update session metrics for non-session events
      if (!skipCreditDeduction) {
        const { error: updateError } = await supabase
          .rpc('update_session_metrics', {
            session_id: usageSessionId,
            new_metrics: {
              ...metadata,
              last_activity: timestamp
            },
            estimated_credits: creditsToDeduct
          });

        if (updateError) {
          console.error('Error updating session metrics:', updateError);
        }
      }
    }

    // Record usage event
    const { data: eventData, error: eventError } = await supabase
      .rpc('record_usage_event', {
        session_id: usageSessionId,
        user_id: userId,
        tool_id: partnerId,
        event_type: eventType,
        event_data: {
          action,
          units,
          featureId,
          timestamp,
          metadata
        },
        credits_used: creditsToDeduct
      });

    if (eventError) {
      console.error('Error recording usage event:', eventError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error recording usage event'
      });
    }

    // Deduct credits if needed
    let creditDeductionSuccess = true;
    if (!skipCreditDeduction && creditsToDeduct > 0) {
      creditDeductionSuccess = await deductCredits(
        userId,
        creditsToDeduct,
        `Used ${creditsToDeduct} credits for ${partnerId} (${action})`
      );

      if (!creditDeductionSuccess) {
        console.error('Error deducting credits');
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error deducting credits'
        });
      }
    }

    // Prepare response
    const response = {
      success: true,
      data: {
        eventId: eventData,
        sessionId: usageSessionId,
        creditsDeducted: skipCreditDeduction ? 0 : creditsToDeduct,
        timestamp: new Date().toISOString()
      }
    };

    // Log the API request
    await logApiRequest(
      '/api/v1/usage/track',
      'POST',
      partnerId,
      userId,
      req.body,
      StatusCodes.OK,
      response
    );

    // Return success response
    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error tracking usage:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
}

export default withApiAuth(handler);
