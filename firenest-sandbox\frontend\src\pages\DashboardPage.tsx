/**
 * Dashboard Page - Main analytics and overview dashboard
 * SOC 2 Alignment: CC6.1 (Logical Access), CC7.1 (System Operations)
 */

import React, { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {
  Plus,
  TrendingUp,
  Database,
  Calculator,
  Play,
  FileText,
  Building2,
  ArrowRight,
  Users,
  FolderOpen,
  Upload,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Target,
  Activity,
  BarChart3
} from 'lucide-react'
import { workspacesApi, projectsApi, simulationsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate, formatCurrency, formatNumber } from '@/lib/utils'

// Metric Card Component
interface MetricCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<any>
  color: string
  trend?: string
  onClick?: () => void
}

function MetricCard({ title, value, icon: Icon, color, trend, onClick }: MetricCardProps) {
  return (
    <div
      className={`metric-card ${onClick ? 'cursor-pointer hover:bg-white/5 transition-colors' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="metric-label">{title}</p>
          <p className="metric-value">{value}</p>
          {trend && (
            <div className="flex items-center mt-1">
              <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
              <span className="text-xs text-green-400">{trend}</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 ${color}/20 rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 ${color.replace('bg-', 'text-')}`} />
        </div>
      </div>
    </div>
  )
}

export function DashboardPage() {
  const navigate = useNavigate()
  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d'>('30d')

  // Fetch dashboard data
  const { data: workspaces, isLoading: workspacesLoading } = useQuery({
    queryKey: ['workspaces'],
    queryFn: () => workspacesApi.list({ limit: 10 })
  })

  const { data: projects, isLoading: projectsLoading } = useQuery({
    queryKey: ['projects', 'recent'],
    queryFn: () => projectsApi.list({ limit: 5, sortBy: 'updated_at', sortOrder: 'desc' })
  })

  const { data: recentSimulations, isLoading: simulationsLoading } = useQuery({
    queryKey: ['simulations', 'recent'],
    queryFn: async () => {
      // Get recent simulations across all projects
      const projectsData = await projectsApi.list({ limit: 50 })
      if (!projectsData?.data?.data?.length) return { data: { data: [] } }

      // For now, return mock data since we need project-specific simulation calls
      return { data: { data: [] } }
    }
  })

  const isLoading = workspacesLoading || projectsLoading || simulationsLoading

  // Calculate metrics from real data
  const metrics = useMemo(() => {
    const workspaceCount = workspaces?.data?.data?.length || 0
    const projectCount = projects?.data?.data?.length || 0
    const activeProjects = projects?.data?.data?.filter(p => p.status !== 'COMPLETE')?.length || 0
    const totalSimulations = recentSimulations?.data?.data?.length || 0
    const completedSimulations = recentSimulations?.data?.data?.filter(s => s.status === 'COMPLETE')?.length || 0

    return {
      workspaces: workspaceCount,
      projects: projectCount,
      activeProjects,
      simulations: totalSimulations,
      completedSimulations,
      successRate: totalSimulations > 0 ? Math.round((completedSimulations / totalSimulations) * 100) : 95 // Default for demo
    }
  }, [workspaces, projects, recentSimulations])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <p className="text-gray-400 mt-1">
            Welcome back! Here's what's happening with your pricing intelligence platform.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value as any)}
            className="bg-sidebar-background border border-white/10 text-white rounded-lg px-3 py-2 text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <Button onClick={() => navigate('/projects')} className="bg-fiery hover:bg-fiery/90">
            <Plus className="w-4 h-4 mr-2" />
            New Project
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Active Workspaces"
          value={metrics.workspaces}
          icon={Building2}
          color="bg-blue-500"
          trend="+12%"
          onClick={() => navigate('/workspaces')}
        />
        <MetricCard
          title="Total Projects"
          value={metrics.projects}
          icon={FolderOpen}
          color="bg-green-500"
          trend="+8%"
          onClick={() => navigate('/projects')}
        />
        <MetricCard
          title="Active Projects"
          value={metrics.activeProjects}
          icon={Activity}
          color="bg-purple-500"
          trend="+24%"
        />
        <MetricCard
          title="Success Rate"
          value={`${metrics.successRate}%`}
          icon={Target}
          color="bg-fiery"
          trend="+5%"
        />
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigate('/projects')}
            >
              <Plus className="w-4 h-4 mr-3" />
              Create New Project
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigate('/upload')}
            >
              <Database className="w-4 h-4 mr-3" />
              Upload Data Files
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigate('/models')}
            >
              <Calculator className="w-4 h-4 mr-3" />
              Build Pricing Model
            </Button>
          </div>
        </div>

        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Getting Started</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-fiery rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">1</span>
              </div>
              <div>
                <p className="text-white font-medium">Upload Your Data</p>
                <p className="text-sm text-gray-400">
                  Start by uploading customer usage and billing data
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">2</span>
              </div>
              <div>
                <p className="text-gray-400 font-medium">Create Pricing Models</p>
                <p className="text-sm text-gray-500">
                  Design and test different pricing strategies
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">3</span>
              </div>
              <div>
                <p className="text-gray-400 font-medium">Run Simulations</p>
                <p className="text-sm text-gray-500">
                  Analyze revenue impact and optimize pricing
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Projects & Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Projects */}
        <div className="firenest-card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Recent Projects</h3>
            <Button variant="ghost" size="sm" onClick={() => navigate('/projects')}>
              View All
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>

          <div className="space-y-4">
            {projects?.data?.data?.length > 0 ? (
              projects.data.data.slice(0, 3).map((project: any) => (
                <div
                  key={project.id}
                  className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors cursor-pointer"
                  onClick={() => navigate(`/projects/${project.id}`)}
                >
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <FolderOpen className="w-5 h-5 text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-white font-medium">{project.name}</p>
                    <p className="text-sm text-gray-400">
                      {project.workspace_name} • {formatDate(project.updated_at)}
                    </p>
                  </div>
                  <Badge variant={project.status === 'COMPLETE' ? 'success' : 'info'}>
                    {project.status}
                  </Badge>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FolderOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400">No projects yet</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-3"
                  onClick={() => navigate('/projects')}
                >
                  Create Your First Project
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="firenest-card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
            <Button variant="ghost" size="sm">
              View All
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>

          <div className="space-y-4">
            {/* Mock activity data - in real implementation, this would come from audit logs */}
            <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
              <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-400" />
              </div>
              <div className="flex-1">
                <p className="text-white font-medium">Project created</p>
                <p className="text-sm text-gray-400">New pricing analysis • 2 hours ago</p>
              </div>
              <Badge variant="success">Created</Badge>
            </div>

            <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
              <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Upload className="w-5 h-5 text-blue-400" />
              </div>
              <div className="flex-1">
                <p className="text-white font-medium">Data uploaded</p>
                <p className="text-sm text-gray-400">customer_data.csv • 4 hours ago</p>
              </div>
              <Badge variant="info">Processed</Badge>
            </div>

            <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
              <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-purple-400" />
              </div>
              <div className="flex-1">
                <p className="text-white font-medium">Simulation completed</p>
                <p className="text-sm text-gray-400">Revenue analysis • 6 hours ago</p>
              </div>
              <Badge variant="success">Complete</Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
