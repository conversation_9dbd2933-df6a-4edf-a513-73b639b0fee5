 **Firenest Sandbox**.

This guide is structured as a series of phases and steps, designed to be discrete and logical enough for a team of developers (human or AI) to execute. We will build this for security, scalability, and auditability from the ground up.

### **Guiding Principles (The Developer's Creed)**

1.  **Zero-Trust Architecture:** No component trusts another by default. Every action is authenticated and authorized.
2.  **Data Isolation is Absolute:** A customer's sensitive data must *never* be accessible by another customer or unauthorized internal personnel.
3.  **Asynchronous Everything:** Heavy processing (data ingestion, simulation) must happen in the background. The user experience must remain responsive.
4.  **Immutability and Auditability:** We log everything. Critical data is never deleted, only marked as invalid or superseded. Every action creates an immutable audit trail.
5.  **Infrastructure as Code (IaC):** The entire infrastructure will be defined in code (Terraform/Pulumi) for repeatability, peer review, and auditability.

---

### **Phase 0: Foundation & Secure Scaffolding**

**Objective:** Lay the secure, compliant groundwork before writing a single line of feature code.

**Step 0.1: Infrastructure Setup (IaC)**
*   **Action:** Using Terraform, define the core cloud infrastructure.
    *   A dedicated Virtual Private Cloud (VPC) with private and public subnets.
    *   An identity provider (e.g., configure Auth0, Okta, or Supabase Auth).
    *   A managed PostgreSQL database (e.g., AWS RDS) in a private subnet.
    *   An object storage bucket (e.g., AWS S3) with Server-Side Encryption (SSE-S3) enabled by default, versioning turned on, and public access blocked at the account level.
    *   A message queue (e.g., AWS SQS).
    *   A container orchestration service (e.g., AWS ECS on Fargate) for running our backend services.
    *   A logging and monitoring stack (e.g., AWS CloudWatch, Datadog).
*   **SOC 2 Alignment:** `CC6.1` (Infrastructure Security), `CC6.6` (Logical Access Security).

**Step 0.2: Identity & Access Management (IAM)**
*   **Action:** Configure the identity provider with strict policies.
    *   Enforce Multi-Factor Authentication (MFA) for all users.
    *   Set up Role-Based Access Control (RBAC). Define initial roles: `user`, `admin`.
    *   Integrate the chosen identity provider with our frontend application.
*   **SOC 2 Alignment:** `CC6.2` (Access Control), `CC6.3` (Authentication).

**Step 0.3: Database Schema - Core Application**
*   **Action:** Define and apply the initial database schema for application logic (not the sensitive customer data).
    *   `users`: `id` (UUID, primary key), `auth_provider_id` (text, unique), `email` (text), `role` (text), `created_at`, `updated_at`.
    *   `workspaces`: `id`, `name`, `owner_id` (foreign key to `users.id`), `created_at`.
    *   `sandbox_projects`: `id`, `name`, `workspace_id` (foreign key), `status` (e.g., 'UPLOADING', 'READY', 'SIMULATING', 'COMPLETE'), `created_at`.
    *   `audit_logs`: `id`, `user_id`, `action` (text), `target_resource` (text), `payload` (JSONB), `timestamp`.
*   **SOC 2 Alignment:** `CC7.2` (Change Management - schema changes via migration scripts), `CC3.2` (Audit Trails).

**Step 0.4: API & Frontend Shell**
*   **Action:** Set up a boilerplate Next.js application for the frontend and a Node.js/Express (or preferred language) application for the backend API.
    *   Configure the backend with a middleware that validates the JWT from the identity provider on every incoming request.
    *   Implement basic login, logout, and signup flows that redirect to the identity provider.
    *   Create a secure "scaffold" for the user dashboard, workspace, and project views.
*   **SOC 2 Alignment:** `CC6.8` (Secure Software Development).

---

### **Phase 1: Secure Data Ingestion**

**Objective:** Allow users to securely upload their sensitive usage and billing data for a specific project.

**Step 1.1: Data Upload Initiation API**
*   **Action:** Create a new API endpoint `POST /api/v1/projects/{projectId}/upload/initiate`.
    *   **Auth:** Requires authenticated `user` role with access to the specified `{projectId}`.
    *   **Logic:**
        1.  Validate user permissions.
        2.  Generate a pre-signed S3 URL for a `PUT` operation. The S3 key (path) must be unique and non-guessable, e.g., `uploads/{workspace_id}/{projectId}/{random_uuid}.csv`.
        3.  The pre-signed URL should have a short expiry (e.g., 5 minutes).
        4.  Log this initiation event in `audit_logs`.
    *   **Response:** `200 OK` with `{ "uploadUrl": "...", "key": "..." }`.
*   **SOC 2 Alignment:** `CC6.7` (Data Encryption in Transit), `CC6.2` (Access Control).

**Step 1.2: Frontend Upload Component**
*   **Action:** Build a React component for file upload.
    *   It calls the `initiate` endpoint to get the pre-signed URL.
    *   It performs a direct `PUT` request from the browser to the S3 `uploadUrl` with the selected file.
    *   Upon successful upload (HTTP 200 from S3), it calls a second API endpoint to finalize the process.
*   **SOC 2 Alignment:** `CC6.8` (Secure Software Development - client-side logic).

**Step 1.3: Data Upload Finalization & Validation Job**
*   **Action:** Create an API endpoint `POST /api/v1/projects/{projectId}/upload/finalize`.
    *   **Request Body:** `{ "key": "uploads/...", "fileName": "original_name.csv", "fileType": "customer_usage_data" }`.
    *   **Logic:**
        1.  Validate permissions.
        2.  Create a record in a new `data_uploads` table: `id`, `project_id`, `s3_key`, `status` ('UPLOADED'), `original_filename`.
        3.  Push a message to the SQS queue: `{ "jobType": "VALIDATE_UPLOAD", "uploadId": "..." }`.
        4.  Update `sandbox_projects.status` to 'VALIDATING'.
*   **Action:** Create a background worker service that listens to the SQS queue for `VALIDATE_UPLOAD` jobs.
    *   **Logic:**
        1.  Fetch the `data_uploads` record.
        2.  Download the file from S3 to the worker's ephemeral storage.
        3.  Validate the file (e.g., parse CSV headers, check for required columns like `customer_id`, `timestamp`, `metric_value`).
        4.  If valid, update `data_uploads.status` to 'VALIDATED'. If invalid, update to 'INVALID' and store validation errors.
        5.  Delete the file from ephemeral storage.
*   **SOC 2 Alignment:** `CC7.1` (System Operations - Asynchronous Processing), `A1.2` (Data Integrity).

---

### **Phase 2: The Pricing Model Builder**

**Objective:** Create a flexible UI for users to define the new pricing models they want to simulate.

**Step 2.1: Pricing Model Schema**
*   **Action:** Define the database schema for storing complex pricing models.
    *   `pricing_models`: `id`, `project_id`, `name`, `model_type` ('USAGE_BASED', 'HYBRID').
    *   `model_components`: `id`, `model_id`, `component_type` ('BASE_FEE', 'TIERED_RATE', 'PER_UNIT_RATE'), `config` (JSONB).
        *   `config` for `BASE_FEE`: `{ "amount": 5000, "currency": "USD", "period": "monthly" }`
        *   `config` for `PER_UNIT_RATE`: `{ "metric_name": "api_calls", "unit_rate": 0.01 }`
        *   `config` for `TIERED_RATE`: `{ "metric_name": "storage_gb", "tiers": [{"up_to": 10, "unit_rate": 5}, {"up_to": 100, "unit_rate": 3}, {"up_to": "infinity", "unit_rate": 2}] }`
*   **SOC 2 Alignment:** `CC7.2` (Schema Management).

**Step 2.2: Pricing Model CRUD APIs**
*   **Action:** Build standard CRUD API endpoints (`POST`, `GET`, `PUT`, `DELETE`) for `/api/v1/projects/{projectId}/models`.
    *   Ensure all endpoints perform strict permission checks.
    *   All write operations (`POST`, `PUT`, `DELETE`) must create an `audit_logs` entry.
*   **SOC 2 Alignment:** `CC6.2` (Access Control), `CC3.2` (Audit Trails).

**Step 2.3: Frontend Model Builder UI**
*   **Action:** Develop a dynamic React component.
    *   Allows users to create a new pricing model for a project.
    *   Users can add/remove components (Base Fee, Per-Unit, Tiered).
    *   Render different forms based on the `component_type`.
    *   Perform client-side validation (e.g., tiers must not overlap).
    *   The component will interact with the CRUD APIs from Step 2.2.
*   **SOC 2 Alignment:** `CC6.8` (Secure Software Development).

---

### **Phase 3: The Simulation Engine Backend**

**Objective:** The core asynchronous engine that runs the simulation against the uploaded data using the defined models.

**Step 3.1: Simulation Initiation**
*   **Action:** Create API endpoint `POST /api/v1/projects/{projectId}/simulations`.
    *   **Request Body:** `{ "modelIds": ["model_id_1", "model_id_2"] }`.
    *   **Logic:**
        1.  Validate that the project status is 'READY' (data is validated) and the models belong to the project.
        2.  Create a record in a new `simulations` table: `id`, `project_id`, `status` ('QUEUED').
        3.  Create records in `simulation_model_runs` linking the simulation to the models.
        4.  Push a message to the SQS queue: `{ "jobType": "RUN_SIMULATION", "simulationId": "..." }`.
        5.  Update `sandbox_projects.status` to 'SIMULATING'.
*   **SOC 2 Alignment:** `CC7.1` (System Operations).

**Step 3.2: The Simulation Worker**
*   **Action:** Create a dedicated background worker service for `RUN_SIMULATION` jobs. This is the most critical logic.
    *   **Logic:**
        1.  Fetch simulation details. Update status to 'RUNNING'.
        2.  Fetch all associated `pricing_models` and their `model_components`.
        3.  Fetch the validated `data_uploads` records for the project.
        4.  **STREAMING PROCESSING (CRITICAL):** Do NOT load the entire multi-gigabyte CSV into memory. Stream the data row by row from S3.
        5.  **For each row in the data file:**
            *   Parse the customer ID, timestamp, and metric values.
            *   **For each pricing model being simulated:**
                *   Apply the model's logic to this single data point. For example, if the model has a rate for "api_calls," calculate the cost for this row's `api_calls` value.
                *   Store the calculated micro-cost in an in-memory hash map, aggregating by `customer_id` and `model_id`. (e.g., `results[model_id][customer_id] += calculated_cost`).
        6.  **After processing all rows:**
            *   The in-memory `results` map now holds the total simulated bill for each customer under each model.
            *   Persist these aggregated results to a new `simulation_results` table: `id`, `simulation_id`, `model_id`, `customer_id`, `simulated_total_cost`.
        7.  Update `simulations.status` to 'COMPLETE'.
        8.  Update `sandbox_projects.status` to 'COMPLETE'.
*   **SOC 2 Alignment:** `A1.2` (Data Integrity), `CC7.1` (System Operations), `CC6.6` (Resource Management).

---

### **Phase 4: Results Visualization & Reporting**

**Objective:** Present the complex simulation results in a clear, interactive, and insightful dashboard.

**Step 4.1: Results API**
*   **Action:** Create a new API endpoint `GET /api/v1/simulations/{simulationId}/results`.
    *   **Logic:** Fetch data from the `simulation_results` table.
    *   Implement pagination, sorting (e.g., by highest cost difference), and filtering (e.g., by customer ID).
    *   Provide aggregated summary statistics (e.g., total revenue per model, number of customers whose bill increased/decreased).
*   **SOC 2 Alignment:** `CC6.2` (Access Control).

**Step 4.2: Frontend Dashboard**
*   **Action:** Build a new dashboard view in the frontend.
    *   Display high-level summary cards (Total Revenue, Avg. Customer Bill, etc.) for each simulated model.
    *   Use a charting library (e.g., Recharts, Chart.js) to visualize distributions (e.g., a histogram of bill changes).
    *   Display a paginated, sortable table of the detailed per-customer results.
    *   Allow users to compare two models side-by-side.
*   **SOC 2 Alignment:** `CC6.8` (Secure Software Development).

This detailed, phased plan provides a realistic path to building the Firenest Sandbox. It prioritizes security and compliance from day one, ensuring that the platform is built on a foundation of trust, which is non-negotiable for a product handling such sensitive data.