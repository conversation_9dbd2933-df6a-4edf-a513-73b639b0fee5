import { supabase } from '@/lib/supabase';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'method_not_allowed', error_description: 'Only POST requests are allowed' });
  }

  try {
    // Parse the request body
    const { grant_type, code, redirect_uri, client_id, client_secret, refresh_token } = req.body;

    // Validate required parameters
    if (!grant_type) {
      return res.status(400).json({
        error: 'invalid_request',
        error_description: 'Missing required parameter: grant_type'
      });
    }

    // Handle different grant types
    if (grant_type === 'authorization_code') {
      if (!code || !redirect_uri || !client_id) {
        return res.status(400).json({
          error: 'invalid_request',
          error_description: 'Missing required parameters for authorization_code grant'
        });
      }

      // Verify the authorization code
      const { data: authCode, error: authCodeError } = await supabase
        .from('auth_codes')
        .select('*')
        .eq('code', code)
        .eq('client_id', client_id)
        .eq('redirect_uri', redirect_uri)
        .eq('used', false)
        .maybeSingle();

      if (authCodeError) {
        console.error('Error verifying authorization code:', authCodeError);
        return res.status(500).json({
          error: 'server_error',
          error_description: 'An error occurred while verifying the authorization code'
        });
      }

      if (!authCode) {
        return res.status(400).json({
          error: 'invalid_grant',
          error_description: 'Invalid authorization code'
        });
      }

      // Check if the code has expired
      if (new Date(authCode.expires_at) < new Date()) {
        return res.status(400).json({
          error: 'invalid_grant',
          error_description: 'Authorization code has expired'
        });
      }

      // Verify client credentials if client_secret is provided
      if (client_secret) {
        // In a real implementation, you would verify the client_secret against the stored hash
        // For now, we'll skip this step
      }

      // Mark the authorization code as used
      await supabase
        .from('auth_codes')
        .update({ used: true })
        .eq('code', code);

      // Generate tokens
      const access_token = generateToken('access');
      const refresh_token = generateToken('refresh');

      // Store the tokens in the database
      const { error: tokenError } = await supabase
        .from('access_tokens')
        .insert({
          access_token,
          refresh_token,
          user_id: authCode.user_id,
          client_id,
          partner_id: authCode.partner_id,
          scope: authCode.scope,
          expires_at: new Date(Date.now() + 3600 * 1000).toISOString() // 1 hour expiration
        });

      if (tokenError) {
        console.error('Error storing tokens:', tokenError);
        return res.status(500).json({
          error: 'server_error',
          error_description: 'An error occurred while generating tokens'
        });
      }

      // Return the tokens
      return res.status(200).json({
        access_token,
        token_type: 'Bearer',
        expires_in: 3600,
        refresh_token,
        scope: authCode.scope
      });
    } else if (grant_type === 'refresh_token') {
      if (!refresh_token || !client_id) {
        return res.status(400).json({
          error: 'invalid_request',
          error_description: 'Missing required parameters for refresh_token grant'
        });
      }

      // Verify the refresh token
      const { data: tokenData, error: tokenError } = await supabase
        .from('access_tokens')
        .select('*')
        .eq('refresh_token', refresh_token)
        .eq('client_id', client_id)
        .maybeSingle();

      if (tokenError) {
        console.error('Error verifying refresh token:', tokenError);
        return res.status(500).json({
          error: 'server_error',
          error_description: 'An error occurred while verifying the refresh token'
        });
      }

      if (!tokenData) {
        return res.status(400).json({
          error: 'invalid_grant',
          error_description: 'Invalid refresh token'
        });
      }

      // Generate new tokens
      const access_token = generateToken('access');
      const new_refresh_token = generateToken('refresh');

      // Update the tokens in the database
      const { error: updateError } = await supabase
        .from('access_tokens')
        .update({
          access_token,
          refresh_token: new_refresh_token,
          expires_at: new Date(Date.now() + 3600 * 1000).toISOString() // 1 hour expiration
        })
        .eq('refresh_token', refresh_token);

      if (updateError) {
        console.error('Error updating tokens:', updateError);
        return res.status(500).json({
          error: 'server_error',
          error_description: 'An error occurred while refreshing tokens'
        });
      }

      // Return the new tokens
      return res.status(200).json({
        access_token,
        token_type: 'Bearer',
        expires_in: 3600,
        refresh_token: new_refresh_token,
        scope: tokenData.scope
      });
    } else {
      return res.status(400).json({
        error: 'unsupported_grant_type',
        error_description: `Unsupported grant type: ${grant_type}`
      });
    }
  } catch (error) {
    console.error('Error processing token request:', error);
    return res.status(500).json({
      error: 'server_error',
      error_description: 'An unexpected error occurred'
    });
  }
}

function generateToken(type: 'access' | 'refresh'): string {
  // Generate a random string for the token
  const randomBytes = new Uint8Array(32);
  crypto.getRandomValues(randomBytes);
  const randomString = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  
  // Add a prefix to distinguish between access and refresh tokens
  return type === 'access' ? `at_${randomString}` : `rt_${randomString}`;
}
