#!/usr/bin/env ts-node

/**
 * Database Initialization Script
 * Sets up the database schema and sample data for development
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { Pool } from 'pg';
import { config } from '../config/environment';
import { logger } from '../utils/logger';

async function initializeDatabase() {
  const pool = new Pool({
    host: config.database.host,
    port: config.database.port,
    database: config.database.name,
    user: config.database.username,
    password: config.database.password,
    ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
    max: 5,
    connectionTimeoutMillis: config.database.connectionTimeout,
  });

  try {
    logger.info('Starting database initialization...');

    // Read the SQL file
    const sqlPath = join(__dirname, 'init-database.sql');
    const sql = readFileSync(sqlPath, 'utf8');

    // Execute the SQL
    await pool.query(sql);

    logger.info('Database initialization completed successfully!');

    // Verify tables were created
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    logger.info('Created tables:', {
      tables: tablesResult.rows.map(row => row.table_name)
    });

    // Verify sample data
    const usersResult = await pool.query('SELECT auth_provider_id, email, role FROM users');
    logger.info('Sample users created:', {
      users: usersResult.rows
    });

    const workspacesResult = await pool.query('SELECT id, name FROM workspaces');
    logger.info('Sample workspaces created:', {
      workspaces: workspacesResult.rows
    });

    const projectsResult = await pool.query('SELECT id, name, status FROM projects');
    logger.info('Sample projects created:', {
      projects: projectsResult.rows
    });

  } catch (error) {
    logger.error('Database initialization failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase().catch(error => {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  });
}

export { initializeDatabase };
