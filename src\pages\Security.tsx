import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Shield, Lock, Server, Database, UserCheck, AlertTriangle } from 'lucide-react';

const Security = () => {
  const lastUpdated = "April 1, 2025";
  
  return (
    <div className="min-h-screen darker-bg text-white">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <Link to="/" className="inline-flex items-center gap-2 text-white/70 hover:text-white mb-8 transition-colors">
          <ArrowLeft size={16} />
          <span>Back to Home</span>
        </Link>
        
        <h1 className="text-3xl font-bold mb-2">Security Policy</h1>
        <p className="text-white/60 mb-8">Last Updated: {lastUpdated}</p>
        
        <div className="prose prose-invert max-w-none">
          <div className="flex items-center gap-3 mb-6">
            <Shield className="text-fiery" size={32} />
            <p className="text-lg font-medium">
              At Firenest, we prioritize the security and confidentiality of your data. This Security Policy outlines the measures we take to protect your information.
            </p>
          </div>
          
          <h2>1. Data Protection Commitment</h2>
          <p>
            Firenest is committed to implementing and maintaining the strongest level of security measures to protect the data you entrust to us. We understand the critical importance of data security in the financial analytics industry and have designed our infrastructure with security as a foundational principle.
          </p>
          
          <h2>2. Infrastructure Security</h2>
          <div className="flex items-start gap-3 mb-4">
            <Server className="text-fiery mt-1" size={20} />
            <div>
              <h3 className="mt-0">Secure Cloud Infrastructure</h3>
              <p className="mt-1">
                Our services are hosted in enterprise-grade cloud environments that maintain compliance with SOC 2, ISO 27001, and other industry-standard certifications. We utilize isolated network environments, virtual private clouds, and multiple security layers to protect our infrastructure.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3 mb-4">
            <Database className="text-fiery mt-1" size={20} />
            <div>
              <h3 className="mt-0">Data Encryption</h3>
              <p className="mt-1">
                All data is encrypted both in transit and at rest using industry-standard encryption protocols:
              </p>
              <ul>
                <li>TLS 1.3 for all data in transit</li>
                <li>AES-256 encryption for all data at rest</li>
                <li>Secure key management with regular rotation</li>
              </ul>
            </div>
          </div>
          
          <h2>3. Application Security</h2>
          <div className="flex items-start gap-3 mb-4">
            <Lock className="text-fiery mt-1" size={20} />
            <div>
              <h3 className="mt-0">Authentication & Access Control</h3>
              <p className="mt-1">
                We implement robust authentication mechanisms, including:
              </p>
              <ul>
                <li>Multi-factor authentication (MFA)</li>
                <li>Role-based access control (RBAC)</li>
                <li>Strong password policies</li>
                <li>Session management with automatic timeouts</li>
                <li>IP-based access restrictions (available for enterprise plans)</li>
              </ul>
            </div>
          </div>
          
          <div className="flex items-start gap-3 mb-4">
            <AlertTriangle className="text-fiery mt-1" size={20} />
            <div>
              <h3 className="mt-0">Vulnerability Management</h3>
              <p className="mt-1">
                Our security team conducts regular security assessments, including:
              </p>
              <ul>
                <li>Automated vulnerability scanning</li>
                <li>Regular penetration testing by third-party security experts</li>
                <li>Code security reviews</li>
                <li>Dependency vulnerability monitoring</li>
              </ul>
            </div>
          </div>
          
          <h2>4. Operational Security</h2>
          <div className="flex items-start gap-3 mb-4">
            <UserCheck className="text-fiery mt-1" size={20} />
            <div>
              <h3 className="mt-0">Security Team & Processes</h3>
              <p className="mt-1">
                Our dedicated security team implements comprehensive security processes:
              </p>
              <ul>
                <li>24/7 monitoring and incident response</li>
                <li>Regular security training for all employees</li>
                <li>Background checks for all staff</li>
                <li>Strict access controls based on the principle of least privilege</li>
                <li>Formal change management procedures</li>
              </ul>
            </div>
          </div>
          
          <h2>5. Compliance</h2>
          <p>
            Firenest maintains compliance with relevant industry standards and regulations:
          </p>
          <ul>
            <li>SOC 2 Type II</li>
            <li>GDPR</li>
            <li>CCPA</li>
            <li>ISO 27001</li>
            <li>PCI DSS (for payment processing)</li>
          </ul>
          <p>
            We undergo regular third-party audits to verify our compliance with these standards. Audit reports are available to enterprise customers upon request under NDA.
          </p>
          
          <h2>6. Data Breach Response</h2>
          <p>
            In the unlikely event of a data breach, we have established procedures to:
          </p>
          <ul>
            <li>Promptly identify and contain the breach</li>
            <li>Investigate the cause and impact</li>
            <li>Notify affected customers in accordance with applicable laws and regulations</li>
            <li>Implement measures to prevent similar incidents in the future</li>
          </ul>
          
          <h2>7. Business Continuity & Disaster Recovery</h2>
          <p>
            We maintain comprehensive business continuity and disaster recovery plans to ensure the availability of our services:
          </p>
          <ul>
            <li>Redundant infrastructure across multiple geographic regions</li>
            <li>Regular backup procedures with encryption</li>
            <li>Automated failover mechanisms</li>
            <li>Regular disaster recovery testing</li>
          </ul>
          <p>
            Our systems are designed to maintain a Recovery Point Objective (RPO) of less than 1 hour and a Recovery Time Objective (RTO) of less than 4 hours for critical systems.
          </p>
          
          <h2>8. Third-Party Risk Management</h2>
          <p>
            We carefully evaluate and monitor the security practices of our third-party service providers:
          </p>
          <ul>
            <li>Comprehensive security assessment before engagement</li>
            <li>Regular review of security practices and compliance</li>
            <li>Contractual security requirements</li>
            <li>Limited access to customer data on a need-to-know basis</li>
          </ul>
          
          <h2>9. Customer Responsibilities</h2>
          <p>
            While we implement robust security measures, security is a shared responsibility. We recommend that customers:
          </p>
          <ul>
            <li>Enable multi-factor authentication for all user accounts</li>
            <li>Regularly review user access and remove unnecessary permissions</li>
            <li>Use strong, unique passwords</li>
            <li>Keep devices and browsers updated</li>
            <li>Report any suspected security incidents promptly</li>
          </ul>
          
          <h2>10. Security Updates</h2>
          <p>
            We continuously improve our security measures to address evolving threats. This Security Policy will be updated periodically to reflect significant changes in our security practices. We encourage you to review this policy regularly.
          </p>
          
          <h2>11. Contact Information</h2>
          <p>
            If you have any questions about our security practices or to report a security concern, please contact our security team at:
          </p>
          <p>
            Email: <EMAIL><br />
            Phone: +****************
          </p>
          <p>
            For urgent security matters, please include "URGENT" in the subject line of your email.
          </p>
          
          <div className="mt-10 p-4 border border-fiery/30 rounded-lg bg-fiery/5">
            <h3 className="text-lg font-semibold mb-2">Security Certifications</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 flex items-center justify-center bg-white/10 rounded-full mb-2">
                  <span className="text-white font-bold">SOC2</span>
                </div>
                <span className="text-xs text-center">SOC 2 Type II</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 flex items-center justify-center bg-white/10 rounded-full mb-2">
                  <span className="text-white font-bold">ISO</span>
                </div>
                <span className="text-xs text-center">ISO 27001</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 flex items-center justify-center bg-white/10 rounded-full mb-2">
                  <span className="text-white font-bold">GDPR</span>
                </div>
                <span className="text-xs text-center">GDPR Compliant</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 flex items-center justify-center bg-white/10 rounded-full mb-2">
                  <span className="text-white font-bold">PCI</span>
                </div>
                <span className="text-xs text-center">PCI DSS</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Security;
