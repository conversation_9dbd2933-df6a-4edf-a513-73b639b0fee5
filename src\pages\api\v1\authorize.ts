/**
 * Authorization Check API Endpoint
 *
 * This endpoint allows partners to check if a user has access to a specific feature.
 * It verifies the user's credit balance and returns whether access is allowed.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';
import {
  withApiAuth,
  StatusCodes,
  ErrorType,
  ErrorMessages,
  logApiRequest
} from './utils';

// Handler for the authorization check endpoint
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
  context: { partnerId: string }
) {
  const { partnerId } = context;

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(StatusCodes.METHOD_NOT_ALLOWED).json({
      success: false,
      error: ErrorType.METHOD_NOT_ALLOWED,
      message: ErrorMessages[ErrorType.METHOD_NOT_ALLOWED]
    });
  }

  try {
    const { userId, featureId } = req.body;

    // Validate required parameters
    if (!userId || !featureId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Missing required parameters: userId and featureId are required'
      });
    }

    // Get the feature configuration from the partner's tools
    const { data: featureData, error: featureError } = await supabase
      .from('partner_tools')
      .select('id, name, credit_cost')
      .eq('partner_id', partnerId)
      .eq('feature_id', featureId)
      .maybeSingle();

    if (featureError) {
      console.error('Error fetching feature data:', featureError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error fetching feature data'
      });
    }

    if (!featureData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: ErrorType.NOT_FOUND,
        message: 'Feature not found',
        allowed: false,
        reason: 'FEATURE_NOT_CONFIGURED'
      });
    }

    // Get the user's credit balance
    const { data: creditData, error: creditError } = await supabase
      .from('user_credits')
      .select('balance')
      .eq('user_id', userId)
      .maybeSingle();

    if (creditError) {
      console.error('Error fetching user credits:', creditError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error fetching user credits'
      });
    }

    if (!creditData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: ErrorType.NOT_FOUND,
        message: 'User not found',
        allowed: false,
        reason: 'USER_NOT_FOUND'
      });
    }

    // Check if the user has enough credits
    const hasEnoughCredits = creditData.balance >= featureData.credit_cost;

    // Log the API request
    await logApiRequest(
      '/api/v1/authorize',
      'POST',
      partnerId,
      userId,
      {
        userId,
        featureId
      },
      StatusCodes.OK,
      {
        allowed: hasEnoughCredits,
        reason: hasEnoughCredits ? null : 'INSUFFICIENT_CREDITS',
        estimatedCost: featureData.credit_cost
      }
    );

    // Return the authorization result
    return res.status(StatusCodes.OK).json({
      success: true,
      allowed: hasEnoughCredits,
      reason: hasEnoughCredits ? null : 'INSUFFICIENT_CREDITS',
      estimatedCost: featureData.credit_cost
    });
  } catch (error) {
    console.error('Error checking authorization:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
}

// Export the handler with API authentication
export default withApiAuth(handler);
