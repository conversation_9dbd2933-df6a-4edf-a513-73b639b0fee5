import React, { useState, useEffect } from 'react';
import {
  X,
  Zap,
  CreditCard,
  ExternalLink,
  MessageSquare,
  Image,
  Headphones,
  Code,
  ArrowRight,
  Clock,
  Shield,
  BarChart,
  KeyRound,
  Lock,
  Fingerprint,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { notify } from '@/components/ui/notification-system';
import LaunchConfirmation from './LaunchConfirmation';

interface ToolLaunchProps {
  tool: any;
  onClose: () => void;
  onLaunch: (toolId: string, options?: any) => void;
  availableCredits: number;
}

const ToolLaunch: React.FC<ToolLaunchProps> = ({
  tool,
  onClose,
  onLaunch,
  availableCredits
}) => {
  const [trackUsage, setTrackUsage] = useState(true);
  const [redirectCountdown, setRedirectCountdown] = useState(0);
  const [usageModel, setUsageModel] = useState<'fixed' | 'dynamic'>('dynamic');
  const [customCreditLimit, setCustomCreditLimit] = useState<number | null>(null);
  const [isLaunched, setIsLaunched] = useState(false);
  const [launchTime, setLaunchTime] = useState(0);
  const [authMethod, setAuthMethod] = useState<'oauth' | 'api_key' | 'credentials' | 'firenest'>('oauth');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Get appropriate icon for tool category
  const getToolIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Start countdown when component mounts
  React.useEffect(() => {
    if (redirectCountdown > 0) {
      const timer = setTimeout(() => {
        setRedirectCountdown(redirectCountdown - 1);

        // When countdown reaches 0, launch the tool
        if (redirectCountdown === 1) {
          // Record launch time
          setLaunchTime(Date.now());

          // Set launched state to true to show confirmation
          setIsLaunched(true);

          // Launch the tool
          handleLaunch();
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [redirectCountdown]);

  const handleLaunch = () => {
    // For fixed pricing, check if user has enough credits
    if (usageModel === 'fixed' && availableCredits < tool.pricing.costPerUnit) {
      notify.error('Not enough credits to launch this tool', {
        title: 'Insufficient Credits',
        duration: 4000
      });
      return;
    }

    // For dynamic pricing with a limit, check if user has enough credits for the limit
    if (usageModel === 'dynamic' && customCreditLimit && availableCredits < customCreditLimit) {
      notify.error('Your credit limit exceeds your available credits', {
        title: 'Insufficient Credits',
        duration: 4000
      });
      return;
    }

    // Record launch time
    setLaunchTime(Date.now());

    // Set launched state to true to show confirmation
    setIsLaunched(true);

    // Pass usage model and credit limit to the launch handler
    onLaunch(tool.id, { usageModel, creditLimit: customCreditLimit, trackUsage });
  };

  const startRedirect = () => {
    setRedirectCountdown(3); // 3 second countdown
  };

  // If the tool is launched, show the confirmation component
  if (isLaunched && redirectCountdown === 0) {
    return (
      <LaunchConfirmation
        tool={tool}
        usageModel={usageModel}
        creditLimit={customCreditLimit}
        trackUsage={trackUsage}
        onClose={onClose}
        launchTime={launchTime}
      />
    );
  }

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 overflow-hidden">
      <div className="bg-dark-900 border border-white/10 rounded-lg w-full max-w-2xl flex flex-col max-h-[90vh] my-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
              {getToolIcon(tool.category)}
            </div>
            <div>
              <h2 className="text-lg font-medium text-white">{tool.name}</h2>
              <p className="text-sm text-white/70">{tool.category}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-white/70 hover:text-white hover:bg-white/5"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
          {redirectCountdown > 0 ? (
            <div className="text-center space-y-6">
              <div className="h-20 w-20 rounded-full bg-fiery/20 flex items-center justify-center mx-auto">
                <Clock className="h-10 w-10 text-fiery" />
              </div>
              <h3 className="text-xl font-medium text-white">Redirecting to {tool.name}...</h3>
              <p className="text-white/70">You will be redirected in {redirectCountdown} seconds</p>
              <div className="w-full bg-dark-800 h-2 rounded-full overflow-hidden">
                <div
                  className="bg-fiery h-full transition-all duration-1000"
                  style={{ width: `${(3 - redirectCountdown) / 3 * 100}%` }}
                ></div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-medium text-white mb-2">Launch {tool.name}</h3>
                <p className="text-white/70">You're about to be redirected to {tool.name}'s platform</p>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium text-white">Credit Usage Model</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card
                    className={`bg-dark-800 border-white/10 cursor-pointer transition-all ${usageModel === 'dynamic' ? 'border-fiery/50 ring-1 ring-fiery/30' : 'hover:border-white/20'}`}
                    onClick={() => setUsageModel('dynamic')}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="h-10 w-10 rounded-full bg-fiery/10 flex items-center justify-center flex-shrink-0">
                          <BarChart className="h-5 w-5 text-fiery" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-white">Dynamic Usage</h4>
                          <p className="text-xs text-white/70">Pay only for what you use</p>
                        </div>
                      </div>
                      <div className="pl-12 space-y-2">
                        <p className="text-xs text-white/70">• Credits deducted based on actual usage</p>
                        <p className="text-xs text-white/70">• Charged per token/request/operation</p>
                        <p className="text-xs text-white/70">• Real-time tracking and billing</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card
                    className={`bg-dark-800 border-white/10 cursor-pointer transition-all ${usageModel === 'fixed' ? 'border-fiery/50 ring-1 ring-fiery/30' : 'hover:border-white/20'}`}
                    onClick={() => setUsageModel('fixed')}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="h-10 w-10 rounded-full bg-fiery/10 flex items-center justify-center flex-shrink-0">
                          <CreditCard className="h-5 w-5 text-fiery" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-white">Fixed Cost</h4>
                          <p className="text-xs text-white/70">Predictable pricing</p>
                        </div>
                      </div>
                      <div className="pl-12 space-y-2">
                        <p className="text-xs text-white/70">• {tool.pricing.costPerUnit} credits per session</p>
                        <p className="text-xs text-white/70">• Unlimited usage during session</p>
                        <p className="text-xs text-white/70">• Best for heavy usage</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {usageModel === 'dynamic' && (
                  <div className="bg-dark-800 border border-white/10 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="text-sm font-medium text-white">Set Credit Limit (Optional)</h4>
                        <p className="text-xs text-white/70">Maximum credits to spend on this session</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="number"
                          className="w-20 bg-dark-700 border border-white/10 rounded px-2 py-1 text-white text-sm"
                          placeholder="No limit"
                          min="1"
                          max={availableCredits}
                          onChange={(e) => setCustomCreditLimit(e.target.value ? parseInt(e.target.value) : null)}
                        />
                        <span className="text-xs text-white/70">credits</span>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-white/70 flex items-center gap-1">
                      <Shield className="h-3.5 w-3.5 text-fiery" />
                      <span>Your session will automatically end when you reach this limit</span>
                    </div>
                  </div>
                )}

                <Card className="bg-dark-800 border-white/10">
                  <CardContent className="p-4 flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-fiery/10 flex items-center justify-center flex-shrink-0">
                      <BarChart className="h-5 w-5 text-fiery" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-white">Usage Tracking</h4>
                      <p className="text-xs text-white/70">Your activity will be tracked for analytics</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="bg-dark-800 border border-white/10 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-fiery" />
                    <h4 className="text-sm font-medium text-white">Track Usage & Analytics</h4>
                  </div>
                  <Switch
                    checked={trackUsage}
                    onCheckedChange={setTrackUsage}
                  />
                </div>
                <p className="text-xs text-white/70">Allow Firenest to track your usage of this tool for better recommendations and analytics</p>
              </div>

              {/* Authentication Method Selection */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-white">Authentication Method</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-white/70 hover:text-white"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  >
                    {showAdvancedOptions ? 'Hide Advanced' : 'Show Advanced'}
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Card
                    className={`bg-dark-800 border-white/10 cursor-pointer transition-all ${authMethod === 'oauth' ? 'border-fiery/50 ring-1 ring-fiery/30' : 'hover:border-white/20'}`}
                    onClick={() => setAuthMethod('oauth')}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-blue-500/10 flex items-center justify-center flex-shrink-0">
                          <Fingerprint className="h-4 w-4 text-blue-400" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-white">OAuth 2.0</h4>
                          <p className="text-xs text-white/70">Secure token-based authentication</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card
                    className={`bg-dark-800 border-white/10 cursor-pointer transition-all ${authMethod === 'firenest' ? 'border-fiery/50 ring-1 ring-fiery/30' : 'hover:border-white/20'}`}
                    onClick={() => setAuthMethod('firenest')}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-fiery/10 flex items-center justify-center flex-shrink-0">
                          <Zap className="h-4 w-4 text-fiery" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-white">Firenest Connect</h4>
                          <p className="text-xs text-white/70">Seamless one-click access</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {showAdvancedOptions && (
                    <>
                      <Card
                        className={`bg-dark-800 border-white/10 cursor-pointer transition-all ${authMethod === 'api_key' ? 'border-fiery/50 ring-1 ring-fiery/30' : 'hover:border-white/20'}`}
                        onClick={() => setAuthMethod('api_key')}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-8 rounded-full bg-purple-500/10 flex items-center justify-center flex-shrink-0">
                              <KeyRound className="h-4 w-4 text-purple-400" />
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-white">API Key</h4>
                              <p className="text-xs text-white/70">Use your existing API key</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card
                        className={`bg-dark-800 border-white/10 cursor-pointer transition-all ${authMethod === 'credentials' ? 'border-fiery/50 ring-1 ring-fiery/30' : 'hover:border-white/20'}`}
                        onClick={() => setAuthMethod('credentials')}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-8 rounded-full bg-green-500/10 flex items-center justify-center flex-shrink-0">
                              <Lock className="h-4 w-4 text-green-400" />
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-white">Credentials</h4>
                              <p className="text-xs text-white/70">Use your existing account</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  )}
                </div>
              </div>

              <div className="bg-dark-700/50 border border-white/5 rounded-lg p-4">
                <h4 className="text-sm font-medium text-white mb-2">How it works:</h4>
                <ul className="space-y-2 text-xs text-white/70">
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-3.5 w-3.5 text-fiery mt-0.5 flex-shrink-0" />
                    <span>You'll be redirected to {tool.name}'s platform</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-3.5 w-3.5 text-fiery mt-0.5 flex-shrink-0" />
                    <span>Firenest will automatically sign you in using {
                      authMethod === 'oauth' ? 'secure OAuth tokens' :
                      authMethod === 'api_key' ? 'your API key' :
                      authMethod === 'credentials' ? 'your saved credentials' :
                      'Firenest Connect'
                    }</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-3.5 w-3.5 text-fiery mt-0.5 flex-shrink-0" />
                    <span>Your usage will be tracked and credits will be deducted accordingly</span>
                  </li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-white/10 p-4 flex-shrink-0">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 text-fiery mr-2" />
                <span className="text-sm text-white/70">Available: <span className="text-white font-medium">{availableCredits} credits</span></span>
              </div>
              <div className="flex items-center">
                <Zap className="h-4 w-4 text-fiery mr-2" />
                <span className="text-sm text-white/70">Cost: <span className="text-white font-medium">
                  {usageModel === 'fixed' ? `${tool.pricing.costPerUnit} credits` : 'Pay per use'}
                  {usageModel === 'dynamic' && customCreditLimit ? ` (Max: ${customCreditLimit})` : ''}
                </span></span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="border-white/10 hover:bg-white/5"
                onClick={onClose}
              >
                Cancel
              </Button>
              {redirectCountdown > 0 ? (
                <Button
                  className="bg-fiery hover:bg-fiery-600"
                  onClick={handleLaunch}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Redirect Now
                </Button>
              ) : (
                <Button
                  className="bg-fiery hover:bg-fiery-600"
                  onClick={startRedirect}
                  disabled={availableCredits < tool.pricing.costPerUnit}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Launch {tool.name}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolLaunch;
