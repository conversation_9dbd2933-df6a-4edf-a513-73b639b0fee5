import React from 'react';
import {
  ArrowLeft,
  Star,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Heart,
  Share2,
  ExternalLink,
  MessageSquare,
  Image,
  Headphones,
  Code,
  FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { notify } from '@/components/ui/notification-system';

interface ToolDetailsProps {
  tool: any;
  onBack: () => void;
  onLaunch: (toolId: string) => void;
  onToggleFavorite: (toolId: string, event: React.MouseEvent) => void;
  isFavorite: boolean;
}

const ToolDetails: React.FC<ToolDetailsProps> = ({
  tool,
  onBack,
  onLaunch,
  onToggleFavorite,
  isFavorite
}) => {
  // Get appropriate icon for tool category
  const getToolIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Sample usage data (would come from props in real implementation)
  const usageData = {
    totalUses: 1243,
    successRate: 98.2,
    avgResponseTime: 1.8,
    lastUsed: '2 hours ago',
    usageHistory: [65, 72, 84, 78, 90, 85, 92],
    popularUseCase: 'Content creation',
    relatedTools: ['dalle', 'midjourney']
  };

  // Sample reviews (would come from props in real implementation)
  const reviews = [
    { id: 1, user: 'Alex M.', avatar: '', rating: 5, comment: 'Incredibly powerful tool that has transformed my workflow. Highly recommended!', date: '2 days ago' },
    { id: 2, user: 'Sarah K.', avatar: '', rating: 4, comment: 'Very useful for my daily tasks, though occasionally slow during peak hours.', date: '1 week ago' },
    { id: 3, user: 'Michael R.', avatar: '', rating: 5, comment: 'The quality of output is consistently impressive. Worth every credit.', date: '2 weeks ago' }
  ];

  // Sample examples (would come from props in real implementation)
  const examples = [
    { id: 1, title: 'Generate blog post outline', prompt: 'Create an outline for a blog post about sustainable living', output: 'I. Introduction to Sustainable Living\nII. Benefits of Sustainable Practices\nIII. Easy Ways to Start...' },
    { id: 2, title: 'Draft email response', prompt: 'Write a professional email declining a meeting invitation', output: 'Dear [Name],\n\nThank you for your invitation to the meeting scheduled for...' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="icon"
            className="h-9 w-9 border-white/10 hover:bg-white/5"
            onClick={onBack}
          >
            <ArrowLeft className="h-4 w-4 text-white/70" />
          </Button>
          <h1 className="text-2xl font-bold text-white">{tool.name}</h1>
          {tool.new && (
            <Badge className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 border-none">New</Badge>
          )}
          {tool.popular && (
            <Badge className="bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 border-none">Popular</Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            className={`h-9 w-9 border-white/10 ${isFavorite ? 'text-fiery hover:bg-fiery/10' : 'text-white/70 hover:bg-white/5'}`}
            onClick={(e) => onToggleFavorite(tool.id, e)}
          >
            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-fiery' : ''}`} />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-9 w-9 border-white/10 hover:bg-white/5"
          >
            <Share2 className="h-4 w-4 text-white/70" />
          </Button>
          <Button
            className="bg-fiery hover:bg-fiery-600"
            onClick={() => onLaunch(tool.id)}
          >
            <Zap className="h-4 w-4 mr-2" />
            Launch Tool
          </Button>
        </div>
      </div>

      {/* Tool info */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Main info card */}
          <Card className="firenest-card">
            <CardContent className="p-6">
              <div className="flex items-start gap-4 mb-6">
                <div className="h-16 w-16 rounded-md bg-fiery/20 flex items-center justify-center">
                  {getToolIcon(tool.category)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm text-white/70">{tool.category}</span>
                    <span className="text-white/30">•</span>
                    <div className="flex items-center">
                      <Star className="h-3.5 w-3.5 text-yellow-400 mr-1" />
                      <span className="text-sm text-white/70">{tool.rating.toFixed(1)}</span>
                    </div>
                    <span className="text-white/30">•</span>
                    <div className="flex items-center">
                      <Zap className="h-3.5 w-3.5 text-fiery mr-1" />
                      <span className="text-sm text-white/70">{tool.pricing.costPerUnit} credits/use</span>
                    </div>
                  </div>
                  <p className="text-white/80 mb-4">{tool.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {tool.tags && tool.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="bg-dark-700 text-white/70 border-white/10">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="border-t border-white/10 pt-6">
                <h3 className="text-lg font-medium text-white mb-4">About this tool</h3>
                <p className="text-white/70 mb-4">
                  {tool.longDescription || `${tool.name} is a powerful AI tool that helps you ${tool.description.toLowerCase()} It's designed to be easy to use while providing professional-quality results.`}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  <div className="firenest-card p-4">
                    <h4 className="text-sm font-medium text-white mb-2">Key Features</h4>
                    <ul className="space-y-2 text-sm text-white/70">
                      {tool.features ? tool.features.map((feature: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      )) : (
                        <>
                          <li className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>High-quality outputs with advanced AI</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>Fast processing and response times</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>Seamless integration with your workflow</span>
                          </li>
                        </>
                      )}
                    </ul>
                  </div>
                  <div className="firenest-card p-4">
                    <h4 className="text-sm font-medium text-white mb-2">Limitations</h4>
                    <ul className="space-y-2 text-sm text-white/70">
                      {tool.limitations ? tool.limitations.map((limitation: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <AlertCircle className="h-4 w-4 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
                          <span>{limitation}</span>
                        </li>
                      )) : (
                        <>
                          <li className="flex items-start">
                            <AlertCircle className="h-4 w-4 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>May have occasional delays during peak usage</span>
                          </li>
                          <li className="flex items-start">
                            <AlertCircle className="h-4 w-4 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>Limited to processing inputs under 4,000 tokens</span>
                          </li>
                          <li className="flex items-start">
                            <AlertCircle className="h-4 w-4 text-amber-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>Results may require minor adjustments</span>
                          </li>
                        </>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs for examples, reviews, etc. */}
          <Tabs defaultValue="examples" className="space-y-4">
            <TabsList className="firenest-card">
              <TabsTrigger value="examples" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                Examples
              </TabsTrigger>
              <TabsTrigger value="reviews" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                Reviews
              </TabsTrigger>
              <TabsTrigger value="related" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                Related Tools
              </TabsTrigger>
            </TabsList>

            {/* Examples Tab */}
            <TabsContent value="examples" className="space-y-4">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-white">Example Use Cases</CardTitle>
                  <CardDescription>See how others are using this tool</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {examples.map((example) => (
                    <div key={example.id} className="firenest-card p-4">
                      <h4 className="text-sm font-medium text-white mb-2">{example.title}</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs text-white/50 mb-1">Prompt:</div>
                          <div className="text-sm text-white/80 firenest-card p-3">
                            {example.prompt}
                          </div>
                        </div>
                        <div>
                          <div className="text-xs text-white/50 mb-1">Output:</div>
                          <div className="text-sm text-white/80 firenest-card p-3 whitespace-pre-line">
                            {example.output}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="border-white/10 hover:bg-white/5">
                    View More Examples
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Reviews Tab */}
            <TabsContent value="reviews" className="space-y-4">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-white">User Reviews</CardTitle>
                  <CardDescription>See what others think about this tool</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="firenest-card p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={review.avatar} alt={review.user} />
                            <AvatarFallback className="bg-fiery text-white text-xs">
                              {review.user.split(' ').map(name => name[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-white">{review.user}</div>
                            <div className="text-xs text-white/50">{review.date}</div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {Array(5).fill(0).map((_, i) => (
                            <Star key={i} className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-yellow-400' : 'text-white/20'}`} />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-white/70">{review.comment}</p>
                    </div>
                  ))}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" className="border-white/10 hover:bg-white/5">
                    View All Reviews
                  </Button>
                  <Button variant="outline" className="border-white/10 hover:bg-white/5">
                    Write a Review
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Related Tools Tab */}
            <TabsContent value="related" className="space-y-4">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-white">Related Tools</CardTitle>
                  <CardDescription>Other tools you might find useful</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {usageData.relatedTools.map((toolId) => {
                      const relatedTool = {
                        id: toolId,
                        name: toolId === 'dalle' ? 'DALL-E' : 'Midjourney',
                        category: 'Image Generation',
                        description: 'Generate stunning images from text descriptions',
                        rating: 4.8,
                        pricing: { costPerUnit: 5 }
                      };
                      return (
                        <div key={toolId} className="firenest-card p-4 cursor-pointer">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                              <Image className="h-5 w-5 text-fiery" />
                            </div>
                            <div>
                              <h4 className="font-medium text-white">{relatedTool.name}</h4>
                              <p className="text-xs text-white/70">{relatedTool.category}</p>
                            </div>
                          </div>
                          <p className="text-sm text-white/70 mb-3 line-clamp-2">{relatedTool.description}</p>
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center">
                              <Star className="h-3.5 w-3.5 text-yellow-400 mr-1" />
                              <span className="text-white/70">{relatedTool.rating.toFixed(1)}</span>
                            </div>
                            <div className="flex items-center">
                              <Zap className="h-3.5 w-3.5 text-fiery mr-1" />
                              <span className="text-white/70">{relatedTool.pricing.costPerUnit} credits/use</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Usage stats */}
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Usage Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-white/70">Total Uses</div>
                <div className="font-medium text-white">{usageData.totalUses.toLocaleString()}</div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Success Rate</span>
                  <span className="text-white">{usageData.successRate}%</span>
                </div>
                <Progress value={usageData.successRate} className="h-1.5 bg-white/10" indicatorClassName="bg-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm text-white/70">Avg. Response Time</div>
                <div className="font-medium text-white">{usageData.avgResponseTime}s</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm text-white/70">Last Used</div>
                <div className="font-medium text-white">{usageData.lastUsed}</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-sm text-white/70">Popular Use Case</div>
                <div className="font-medium text-white">{usageData.popularUseCase}</div>
              </div>
              <div className="pt-2">
                <div className="text-sm text-white/70 mb-2">Usage Trend</div>
                <div className="flex items-end h-24 gap-1">
                  {usageData.usageHistory.map((value, index) => (
                    <div
                      key={index}
                      className="flex-1 bg-fiery rounded-t-sm"
                      style={{ height: `${value}%` }}
                    ></div>
                  ))}
                </div>
                <div className="flex justify-between mt-1 text-xs text-white/50">
                  <span>7 days ago</span>
                  <span>Today</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="w-full border-white/10 hover:bg-white/5">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Detailed Analytics
              </Button>
            </CardFooter>
          </Card>

          {/* Pricing */}
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-white/70">Cost per use</div>
                <div className="font-medium text-white">{tool.pricing.costPerUnit} credits</div>
              </div>
              {tool.pricing.bulkDiscount && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-white/70">Bulk discount</div>
                  <div className="font-medium text-white">{tool.pricing.bulkDiscount}</div>
                </div>
              )}
              <div className="pt-2 border-t border-white/10">
                <div className="text-sm text-white/70 mb-2">Estimated cost per task</div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-white">Short task</span>
                    <Badge variant="outline" className="firenest-card text-white/70">
                      {tool.pricing.costPerUnit} credits
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-white">Medium task</span>
                    <Badge variant="outline" className="firenest-card text-white/70">
                      {tool.pricing.costPerUnit * 2} credits
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-white">Complex task</span>
                    <Badge variant="outline" className="firenest-card text-white/70">
                      {tool.pricing.costPerUnit * 4}+ credits
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              <Button className="w-full bg-fiery hover:bg-fiery-600" onClick={() => onLaunch(tool.id)}>
                <Zap className="h-4 w-4 mr-2" />
                Launch Tool
              </Button>
              <Button variant="outline" size="sm" className="w-full border-white/10 hover:bg-white/5">
                <ExternalLink className="h-4 w-4 mr-2" />
                Visit Provider Website
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ToolDetails;
