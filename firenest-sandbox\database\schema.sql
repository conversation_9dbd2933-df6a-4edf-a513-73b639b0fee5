-- Firenest Sandbox Database Schema
-- Phase 0: Foundation & Secure Scaffolding
-- SOC 2 Alignment: CC7.2 (Change Management), CC3.2 (Audit Trails)

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable Row Level Security
SET row_security = on;

-- Users table - Core application users (not sensitive customer data)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_provider_id TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin', 'workspace_admin')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Workspaces table - Multi-tenant organization
CREATE TABLE IF NOT EXISTS workspaces (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on workspaces table
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;

-- Workspace members table - For team collaboration
CREATE TABLE IF NOT EXISTS workspace_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('member', 'admin')),
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(workspace_id, user_id)
);

-- Enable RLS on workspace_members table
ALTER TABLE workspace_members ENABLE ROW LEVEL SECURITY;

-- Sandbox projects table - Project management within workspaces
CREATE TABLE IF NOT EXISTS sandbox_projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'CREATED' CHECK (status IN ('CREATED', 'UPLOADING', 'VALIDATING', 'READY', 'SIMULATING', 'COMPLETE', 'ERROR')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on sandbox_projects table
ALTER TABLE sandbox_projects ENABLE ROW LEVEL SECURITY;

-- Audit logs table - Compliance tracking for all actions
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    target_resource TEXT NOT NULL,
    target_id UUID,
    payload JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on audit_logs table
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Data uploads table - Track uploaded files
CREATE TABLE IF NOT EXISTS data_uploads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES sandbox_projects(id) ON DELETE CASCADE,
    s3_key TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_size BIGINT,
    file_type TEXT NOT NULL CHECK (file_type IN ('customer_usage_data', 'billing_data', 'customer_metadata')),
    status TEXT NOT NULL DEFAULT 'UPLOADED' CHECK (status IN ('UPLOADED', 'VALIDATING', 'VALIDATED', 'INVALID', 'PROCESSED')),
    validation_errors JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on data_uploads table
ALTER TABLE data_uploads ENABLE ROW LEVEL SECURITY;

-- Pricing models table - Store pricing model definitions
CREATE TABLE IF NOT EXISTS pricing_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES sandbox_projects(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    model_type TEXT NOT NULL CHECK (model_type IN ('USAGE_BASED', 'HYBRID', 'SUBSCRIPTION', 'TIERED')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on pricing_models table
ALTER TABLE pricing_models ENABLE ROW LEVEL SECURITY;

-- Model components table - Store pricing model components
CREATE TABLE IF NOT EXISTS model_components (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id UUID NOT NULL REFERENCES pricing_models(id) ON DELETE CASCADE,
    component_type TEXT NOT NULL CHECK (component_type IN ('BASE_FEE', 'TIERED_RATE', 'PER_UNIT_RATE', 'MINIMUM_FEE', 'MAXIMUM_FEE')),
    config JSONB NOT NULL DEFAULT '{}',
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on model_components table
ALTER TABLE model_components ENABLE ROW LEVEL SECURITY;

-- Simulations table - Track simulation runs
CREATE TABLE IF NOT EXISTS simulations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES sandbox_projects(id) ON DELETE CASCADE,
    name TEXT,
    status TEXT NOT NULL DEFAULT 'QUEUED' CHECK (status IN ('QUEUED', 'RUNNING', 'COMPLETE', 'FAILED', 'CANCELLED')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on simulations table
ALTER TABLE simulations ENABLE ROW LEVEL SECURITY;

-- Simulation model runs table - Link simulations to models
CREATE TABLE IF NOT EXISTS simulation_model_runs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES pricing_models(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'QUEUED' CHECK (status IN ('QUEUED', 'RUNNING', 'COMPLETE', 'FAILED', 'CANCELLED')),
    records_processed INTEGER DEFAULT 0,
    total_records INTEGER DEFAULT 0,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on simulation_model_runs table
ALTER TABLE simulation_model_runs ENABLE ROW LEVEL SECURITY;

-- Simulation results table - Store aggregated simulation results per model
CREATE TABLE IF NOT EXISTS simulation_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES pricing_models(id) ON DELETE CASCADE,
    total_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    customer_count INTEGER NOT NULL DEFAULT 0,
    avg_revenue_per_customer DECIMAL(15,2) NOT NULL DEFAULT 0,
    min_customer_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    max_customer_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    median_revenue DECIMAL(15,2),
    q1_revenue DECIMAL(15,2),
    q3_revenue DECIMAL(15,2),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Simulation customer results table - Store detailed per-customer results
CREATE TABLE IF NOT EXISTS simulation_customer_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    customer_id TEXT NOT NULL,
    model_id UUID NOT NULL REFERENCES pricing_models(id) ON DELETE CASCADE,
    calculated_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    component_breakdown JSONB NOT NULL DEFAULT '[]',
    billing_period TEXT NOT NULL DEFAULT 'monthly',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS on simulation_results table
ALTER TABLE simulation_results ENABLE ROW LEVEL SECURITY;

-- Enable RLS on simulation_customer_results table
ALTER TABLE simulation_customer_results ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies for Data Isolation
-- These policies ensure absolute data isolation between customers

-- Users table policies
CREATE POLICY "Users can view their own data" ON users
    FOR SELECT USING (auth_provider_id = current_setting('app.current_user_auth_id', true));

CREATE POLICY "Users can update their own data" ON users
    FOR UPDATE USING (auth_provider_id = current_setting('app.current_user_auth_id', true));

-- Workspaces table policies
CREATE POLICY "Users can view workspaces they own or are members of" ON workspaces
    FOR SELECT USING (
        owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
        OR id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
        )
    );

CREATE POLICY "Workspace owners can update their workspaces" ON workspaces
    FOR UPDATE USING (owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true)));

CREATE POLICY "Users can create workspaces" ON workspaces
    FOR INSERT WITH CHECK (owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true)));

-- Sandbox projects table policies
CREATE POLICY "Users can view projects in their workspaces" ON sandbox_projects
    FOR SELECT USING (
        workspace_id IN (
            SELECT id FROM workspaces WHERE owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            UNION
            SELECT workspace_id FROM workspace_members WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
        )
    );

CREATE POLICY "Users can manage projects in their workspaces" ON sandbox_projects
    FOR ALL USING (
        workspace_id IN (
            SELECT id FROM workspaces WHERE owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            UNION
            SELECT workspace_id FROM workspace_members WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
        )
    );

-- Audit logs policies
CREATE POLICY "Users can view their own audit logs" ON audit_logs
    FOR SELECT USING (user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true)));

CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (true);

-- Data uploads policies
CREATE POLICY "Users can view uploads in their projects" ON data_uploads
    FOR SELECT USING (
        project_id IN (
            SELECT sp.id FROM sandbox_projects sp
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

CREATE POLICY "Users can manage uploads in their projects" ON data_uploads
    FOR ALL USING (
        project_id IN (
            SELECT sp.id FROM sandbox_projects sp
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

-- Pricing models policies
CREATE POLICY "Users can view models in their projects" ON pricing_models
    FOR SELECT USING (
        project_id IN (
            SELECT sp.id FROM sandbox_projects sp
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

CREATE POLICY "Users can manage models in their projects" ON pricing_models
    FOR ALL USING (
        project_id IN (
            SELECT sp.id FROM sandbox_projects sp
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

-- Model components policies (inherit from pricing models)
CREATE POLICY "Users can view model components" ON model_components
    FOR SELECT USING (
        model_id IN (
            SELECT pm.id FROM pricing_models pm
            JOIN sandbox_projects sp ON pm.project_id = sp.id
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

CREATE POLICY "Users can manage model components" ON model_components
    FOR ALL USING (
        model_id IN (
            SELECT pm.id FROM pricing_models pm
            JOIN sandbox_projects sp ON pm.project_id = sp.id
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

-- Simulations policies
CREATE POLICY "Users can view simulations in their projects" ON simulations
    FOR SELECT USING (
        project_id IN (
            SELECT sp.id FROM sandbox_projects sp
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

CREATE POLICY "Users can manage simulations in their projects" ON simulations
    FOR ALL USING (
        project_id IN (
            SELECT sp.id FROM sandbox_projects sp
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

-- Simulation model runs and results policies (inherit from simulations)
CREATE POLICY "Users can view simulation model runs" ON simulation_model_runs
    FOR SELECT USING (
        simulation_id IN (
            SELECT s.id FROM simulations s
            JOIN sandbox_projects sp ON s.project_id = sp.id
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

CREATE POLICY "Users can view simulation results" ON simulation_results
    FOR SELECT USING (
        simulation_id IN (
            SELECT s.id FROM simulations s
            JOIN sandbox_projects sp ON s.project_id = sp.id
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

CREATE POLICY "Users can view simulation customer results" ON simulation_customer_results
    FOR SELECT USING (
        simulation_id IN (
            SELECT s.id FROM simulations s
            JOIN sandbox_projects sp ON s.project_id = sp.id
            JOIN workspaces w ON sp.workspace_id = w.id
            WHERE w.owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            OR sp.workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
            )
        )
    );

-- Workspace members policies
CREATE POLICY "Users can view workspace memberships" ON workspace_members
    FOR SELECT USING (
        user_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
        OR workspace_id IN (
            SELECT id FROM workspaces WHERE owner_id = (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true))
        )
    );

-- Helper functions for audit logging
CREATE OR REPLACE FUNCTION log_audit_event(
    p_user_id UUID,
    p_action TEXT,
    p_target_resource TEXT,
    p_target_id UUID DEFAULT NULL,
    p_payload JSONB DEFAULT '{}',
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO audit_logs (user_id, action, target_resource, target_id, payload, ip_address, user_agent)
    VALUES (p_user_id, p_action, p_target_resource, p_target_id, p_payload, p_ip_address, p_user_agent)
    RETURNING id INTO audit_id;

    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user ID from auth provider ID
CREATE OR REPLACE FUNCTION get_current_user_id() RETURNS UUID AS $$
BEGIN
    RETURN (SELECT id FROM users WHERE auth_provider_id = current_setting('app.current_user_auth_id', true));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_auth_provider_id ON users(auth_provider_id);
CREATE INDEX IF NOT EXISTS idx_workspaces_owner_id ON workspaces(owner_id);
CREATE INDEX IF NOT EXISTS idx_workspace_members_workspace_id ON workspace_members(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workspace_members_user_id ON workspace_members(user_id);
CREATE INDEX IF NOT EXISTS idx_sandbox_projects_workspace_id ON sandbox_projects(workspace_id);
CREATE INDEX IF NOT EXISTS idx_data_uploads_project_id ON data_uploads(project_id);
CREATE INDEX IF NOT EXISTS idx_pricing_models_project_id ON pricing_models(project_id);
CREATE INDEX IF NOT EXISTS idx_model_components_model_id ON model_components(model_id);
CREATE INDEX IF NOT EXISTS idx_simulations_project_id ON simulations(project_id);
CREATE INDEX IF NOT EXISTS idx_simulation_results_simulation_id ON simulation_results(simulation_id);
CREATE INDEX IF NOT EXISTS idx_simulation_results_model_id ON simulation_results(model_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);

-- Comments for documentation
COMMENT ON TABLE users IS 'Core application users with authentication provider integration';
COMMENT ON TABLE workspaces IS 'Multi-tenant workspaces for organizing projects';
COMMENT ON TABLE workspace_members IS 'Team collaboration and workspace access control';
COMMENT ON TABLE sandbox_projects IS 'Individual pricing simulation projects';
COMMENT ON TABLE audit_logs IS 'Immutable audit trail for compliance and security';
COMMENT ON TABLE data_uploads IS 'Tracking of uploaded customer data files';
COMMENT ON TABLE pricing_models IS 'Pricing model definitions for simulation';
COMMENT ON TABLE model_components IS 'Individual components of pricing models';
COMMENT ON TABLE simulations IS 'Simulation execution tracking';
COMMENT ON TABLE simulation_results IS 'Aggregated results from pricing simulations';
