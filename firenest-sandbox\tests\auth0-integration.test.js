/**
 * Auth0 Integration Tests
 * Comprehensive testing suite for Auth0 authentication flow
 * SOC 2 Alignment: CC6.1, CC6.2, CC6.3 (Access Controls)
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

// Test configuration
const TEST_CONFIG = {
  auth0Domain: process.env.AUTH0_DOMAIN || 'dev-22a3bgq8rrdgtwy3.au.auth0.com',
  clientId: process.env.AUTH0_CLIENT_ID || 'Jgi7Idu1z8y2tT6vSLxWVmZfmMNarvS3',
  audience: process.env.AUTH0_AUDIENCE || 'https://dev-22a3bgq8rrdgtwy3.au.auth0.com/api/v2/',
  apiUrl: process.env.API_URL || 'http://localhost:3001/api/v1',
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
};

// Test utilities
class Auth0TestUtils {
  static async getTestToken(username, password) {
    try {
      const response = await axios.post(`https://${TEST_CONFIG.auth0Domain}/oauth/token`, {
        grant_type: 'password',
        username,
        password,
        audience: TEST_CONFIG.audience,
        client_id: TEST_CONFIG.clientId,
        scope: 'openid profile email'
      });
      return response.data.access_token;
    } catch (error) {
      console.error('Failed to get test token:', error.response?.data || error.message);
      throw error;
    }
  }

  static async validateToken(token) {
    try {
      const response = await axios.get(`https://${TEST_CONFIG.auth0Domain}/.well-known/jwks.json`);
      const jwks = response.data;
      
      // Decode token header to get kid
      const decoded = jwt.decode(token, { complete: true });
      const kid = decoded.header.kid;
      
      // Find matching key
      const key = jwks.keys.find(k => k.kid === kid);
      if (!key) {
        throw new Error('No matching key found');
      }
      
      // Verify token (simplified - in real implementation use proper JWT verification)
      const payload = jwt.decode(token);
      return payload;
    } catch (error) {
      console.error('Token validation failed:', error.message);
      throw error;
    }
  }

  static async testApiEndpoint(endpoint, token, method = 'GET', data = null) {
    try {
      const config = {
        method,
        url: `${TEST_CONFIG.apiUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };
      
      if (data) {
        config.data = data;
      }
      
      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`API test failed for ${endpoint}:`, error.response?.data || error.message);
      throw error;
    }
  }
}

// Test suites
describe('Auth0 Integration Tests', () => {
  let testToken;
  let testUser;

  beforeAll(async () => {
    console.log('Setting up Auth0 integration tests...');
    console.log('Test Configuration:', {
      domain: TEST_CONFIG.auth0Domain,
      clientId: TEST_CONFIG.clientId,
      audience: TEST_CONFIG.audience,
      apiUrl: TEST_CONFIG.apiUrl
    });
  });

  describe('Authentication Flow', () => {
    test('should validate Auth0 configuration', async () => {
      // Test Auth0 well-known endpoints
      const wellKnownResponse = await axios.get(`https://${TEST_CONFIG.auth0Domain}/.well-known/openid_configuration`);
      expect(wellKnownResponse.status).toBe(200);
      expect(wellKnownResponse.data.issuer).toBe(`https://${TEST_CONFIG.auth0Domain}/`);
      
      const jwksResponse = await axios.get(`https://${TEST_CONFIG.auth0Domain}/.well-known/jwks.json`);
      expect(jwksResponse.status).toBe(200);
      expect(jwksResponse.data.keys).toBeDefined();
      expect(jwksResponse.data.keys.length).toBeGreaterThan(0);
    });

    test('should handle demo login in development', async () => {
      // Test demo login endpoint
      const response = await axios.post(`${TEST_CONFIG.apiUrl}/auth/login`, {
        idToken: 'demo-token',
        provider: 'auth0'
      });
      
      expect(response.status).toBe(200);
      expect(response.data.token).toBeDefined();
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe('<EMAIL>');
      
      testToken = response.data.token;
      testUser = response.data.user;
    });

    test('should validate JWT token structure', async () => {
      expect(testToken).toBeDefined();
      
      // Decode token without verification for structure check
      const decoded = jwt.decode(testToken, { complete: true });
      
      expect(decoded.header).toBeDefined();
      expect(decoded.header.alg).toBeDefined();
      expect(decoded.header.typ).toBe('JWT');
      
      expect(decoded.payload).toBeDefined();
      expect(decoded.payload.sub).toBeDefined();
      expect(decoded.payload.email).toBeDefined();
      expect(decoded.payload.iat).toBeDefined();
      expect(decoded.payload.exp).toBeDefined();
    });
  });

  describe('API Authorization', () => {
    test('should access protected endpoints with valid token', async () => {
      const response = await Auth0TestUtils.testApiEndpoint('/auth/me', testToken);
      
      expect(response).toBeDefined();
      expect(response.user).toBeDefined();
      expect(response.user.email).toBe(testUser.email);
    });

    test('should reject requests without token', async () => {
      try {
        await axios.get(`${TEST_CONFIG.apiUrl}/auth/me`);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });

    test('should reject requests with invalid token', async () => {
      try {
        await axios.get(`${TEST_CONFIG.apiUrl}/auth/me`, {
          headers: { 'Authorization': 'Bearer invalid-token' }
        });
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });

    test('should handle token refresh', async () => {
      const response = await Auth0TestUtils.testApiEndpoint('/auth/refresh', testToken, 'POST', {
        token: testToken
      });
      
      expect(response.token).toBeDefined();
      expect(response.user).toBeDefined();
      
      // New token should be different
      expect(response.token).not.toBe(testToken);
    });
  });

  describe('User Management', () => {
    test('should retrieve user profile', async () => {
      const response = await Auth0TestUtils.testApiEndpoint('/users/profile', testToken);
      
      expect(response.user).toBeDefined();
      expect(response.user.id).toBeDefined();
      expect(response.user.email).toBe(testUser.email);
      expect(response.user.role).toBeDefined();
    });

    test('should update user profile', async () => {
      const updateData = {
        name: 'Updated Demo User',
        preferences: {
          theme: 'dark',
          notifications: true
        }
      };
      
      const response = await Auth0TestUtils.testApiEndpoint('/users/profile', testToken, 'PUT', updateData);
      
      expect(response.user.name).toBe(updateData.name);
      expect(response.user.preferences).toEqual(updateData.preferences);
    });
  });

  describe('Security Controls', () => {
    test('should enforce rate limiting', async () => {
      const requests = [];
      
      // Make multiple rapid requests
      for (let i = 0; i < 20; i++) {
        requests.push(
          axios.get(`${TEST_CONFIG.apiUrl}/auth/me`, {
            headers: { 'Authorization': `Bearer ${testToken}` }
          }).catch(error => error.response)
        );
      }
      
      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      // Should have some rate limited responses
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    test('should log authentication events', async () => {
      // Test audit logging endpoint
      const response = await Auth0TestUtils.testApiEndpoint('/audit/events', testToken);
      
      expect(response.events).toBeDefined();
      expect(Array.isArray(response.events)).toBe(true);
      
      // Should have login event
      const loginEvent = response.events.find(e => e.action === 'user_login');
      expect(loginEvent).toBeDefined();
      expect(loginEvent.userId).toBe(testUser.id);
    });

    test('should handle logout and token invalidation', async () => {
      const logoutResponse = await Auth0TestUtils.testApiEndpoint('/auth/logout', testToken, 'POST');
      expect(logoutResponse.success).toBe(true);
      
      // Token should be invalid after logout
      try {
        await Auth0TestUtils.testApiEndpoint('/auth/me', testToken);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response?.status).toBe(401);
      }
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed tokens gracefully', async () => {
      const malformedTokens = [
        'not.a.token',
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid',
        'Bearer malformed-token',
        ''
      ];
      
      for (const token of malformedTokens) {
        try {
          await axios.get(`${TEST_CONFIG.apiUrl}/auth/me`, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          fail(`Should have rejected token: ${token}`);
        } catch (error) {
          expect(error.response.status).toBe(401);
        }
      }
    });

    test('should handle network failures gracefully', async () => {
      // Test with invalid API URL
      try {
        await axios.get('http://invalid-url/auth/me', {
          headers: { 'Authorization': `Bearer ${testToken}` },
          timeout: 1000
        });
        fail('Should have thrown a network error');
      } catch (error) {
        expect(error.code).toMatch(/ENOTFOUND|ECONNREFUSED|TIMEOUT/);
      }
    });
  });
});

// Performance tests
describe('Auth0 Performance Tests', () => {
  test('should handle concurrent authentication requests', async () => {
    const concurrentRequests = 10;
    const startTime = Date.now();
    
    const requests = Array(concurrentRequests).fill().map(() =>
      axios.post(`${TEST_CONFIG.apiUrl}/auth/login`, {
        idToken: 'demo-token',
        provider: 'auth0'
      })
    );
    
    const responses = await Promise.all(requests);
    const endTime = Date.now();
    
    // All requests should succeed
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.data.token).toBeDefined();
    });
    
    // Should complete within reasonable time (5 seconds)
    expect(endTime - startTime).toBeLessThan(5000);
  });

  test('should validate token performance', async () => {
    const iterations = 100;
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await axios.get(`${TEST_CONFIG.apiUrl}/auth/me`, {
        headers: { 'Authorization': `Bearer ${testToken}` }
      });
    }
    
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / iterations;
    
    // Average response time should be under 100ms
    expect(avgTime).toBeLessThan(100);
  });
});

// Export test utilities for use in other test files
module.exports = { Auth0TestUtils, TEST_CONFIG };
