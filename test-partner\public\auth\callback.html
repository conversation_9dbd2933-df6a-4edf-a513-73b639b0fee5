<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Authentication - Firenest Test Partner</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }
    .loading {
      border-top-color: #ff4500;
      animation: spinner 0.6s linear infinite;
    }
    @keyframes spinner {
      to {transform: rotate(360deg);}
    }
  </style>
</head>
<body class="gradient-bg min-h-screen text-white flex items-center justify-center">
  <div class="container mx-auto px-4 py-12 max-w-md">
    <div class="bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div class="p-8 text-center">
        <h2 class="text-2xl font-bold mb-6">Completing Authentication</h2>
        
        <div id="loading" class="flex flex-col items-center justify-center">
          <div class="loading h-12 w-12 rounded-full border-4 border-gray-300 mb-4"></div>
          <p class="text-gray-300">Processing your authentication...</p>
        </div>
        
        <div id="success" class="hidden">
          <svg class="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <p class="text-gray-300 mb-6">Authentication successful!</p>
          <p class="text-gray-400 text-sm mb-6">You'll be redirected to the dashboard in a moment...</p>
        </div>
        
        <div id="error" class="hidden">
          <svg class="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          <p id="error-message" class="text-red-400 mb-6">Authentication failed.</p>
          <a href="/" class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">Return to Home</a>
        </div>
      </div>
    </div>
  </div>

  <script>
    // This page is just for visual feedback
    // The actual processing happens on the server side in the /auth/callback route
    
    // Check for error parameters in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    
    if (error) {
      // Show error state
      document.getElementById('loading').classList.add('hidden');
      document.getElementById('error').classList.remove('hidden');
      
      let errorMessage = 'Authentication failed.';
      if (error === 'auth_error') {
        errorMessage = 'Authentication error. Please try again.';
      } else if (error === 'state_mismatch') {
        errorMessage = 'Security validation failed. Please try again.';
      } else if (error === 'token_error') {
        errorMessage = 'Error obtaining access token. Please try again.';
      }
      
      document.getElementById('error-message').textContent = errorMessage;
    } else {
      // The server will handle the redirect to the dashboard
      // This is just a fallback in case something goes wrong
      setTimeout(() => {
        document.getElementById('loading').classList.add('hidden');
        document.getElementById('success').classList.remove('hidden');
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 2000);
      }, 1500);
    }
  </script>
</body>
</html>
