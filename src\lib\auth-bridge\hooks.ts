/**
 * Authentication Bridge Hooks
 * 
 * This file provides React hooks for using the authentication bridge.
 */

import { useState, useEffect, useCallback } from 'react';
import { authBridge } from './auth-bridge';
import { ServiceConfig, ServiceSession, UsageUpdateEvent } from './types';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook for accessing service configurations
 */
export function useServiceConfigs() {
  const [configs, setConfigs] = useState<ServiceConfig[]>([]);
  
  useEffect(() => {
    setConfigs(authBridge.getAllServiceConfigs());
  }, []);
  
  return configs;
}

/**
 * Hook for accessing a specific service configuration
 */
export function useServiceConfig(serviceId: string) {
  const [config, setConfig] = useState<ServiceConfig | undefined>(undefined);
  
  useEffect(() => {
    setConfig(authBridge.getServiceConfig(serviceId));
  }, [serviceId]);
  
  return config;
}

/**
 * Hook for managing service sessions
 */
export function useServiceSessions() {
  const { user } = useAuth();
  const [sessions, setSessions] = useState<ServiceSession[]>([]);
  
  const refreshSessions = useCallback(() => {
    if (user) {
      setSessions(authBridge.getUserSessions(user.id));
    } else {
      setSessions([]);
    }
  }, [user]);
  
  useEffect(() => {
    refreshSessions();
    
    // Set up usage update listener
    const unsubscribe = authBridge.onUsageUpdate((event: UsageUpdateEvent) => {
      if (user && event.userId === user.id) {
        refreshSessions();
      }
    });
    
    return () => {
      unsubscribe();
    };
  }, [user, refreshSessions]);
  
  const endSession = useCallback(async (serviceId: string) => {
    if (user) {
      const success = await authBridge.endSession(serviceId, user.id);
      if (success) {
        refreshSessions();
      }
      return success;
    }
    return false;
  }, [user, refreshSessions]);
  
  return {
    sessions,
    refreshSessions,
    endSession
  };
}

/**
 * Hook for launching a service
 */
export function useLaunchService() {
  const { user } = useAuth();
  const [isLaunching, setIsLaunching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const launch = useCallback(async (serviceId: string) => {
    if (!user) {
      setError('You must be logged in to launch a service');
      return null;
    }
    
    setIsLaunching(true);
    setError(null);
    
    try {
      const result = await authBridge.initializeAuth(serviceId, user.id);
      
      if (!result.success) {
        setError(result.error?.message || 'Failed to launch service');
        return null;
      }
      
      if (result.redirectUrl) {
        // In a real implementation, this would handle the redirect properly
        // For now, we'll just open the URL in a new tab
        window.open(result.redirectUrl, '_blank');
      }
      
      return result.session || null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setIsLaunching(false);
    }
  }, [user]);
  
  return {
    launch,
    isLaunching,
    error
  };
}

/**
 * Hook for tracking service usage
 */
export function useServiceUsage(serviceId: string) {
  const { user } = useAuth();
  const [session, setSession] = useState<ServiceSession | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  
  useEffect(() => {
    if (!user || !serviceId) {
      setSession(null);
      return;
    }
    
    // Get initial session
    const currentSession = authBridge.getSession(serviceId, user.id);
    setSession(currentSession);
    
    if (currentSession && currentSession.status === 'active') {
      setIsTracking(true);
    }
    
    // Set up usage update listener
    const unsubscribe = authBridge.onUsageUpdate((event: UsageUpdateEvent) => {
      if (event.serviceId === serviceId && event.userId === user.id) {
        const updatedSession = authBridge.getSession(serviceId, user.id);
        setSession(updatedSession);
      }
    });
    
    return () => {
      unsubscribe();
    };
  }, [serviceId, user]);
  
  const updateUsage = useCallback((metrics: any) => {
    if (user && serviceId && isTracking) {
      return authBridge.updateUsage(serviceId, user.id, metrics);
    }
    return false;
  }, [serviceId, user, isTracking]);
  
  const endTracking = useCallback(async () => {
    if (user && serviceId && isTracking) {
      const success = await authBridge.endSession(serviceId, user.id);
      if (success) {
        setIsTracking(false);
        const updatedSession = authBridge.getSession(serviceId, user.id);
        setSession(updatedSession);
      }
      return success;
    }
    return false;
  }, [serviceId, user, isTracking]);
  
  return {
    session,
    isTracking,
    updateUsage,
    endTracking
  };
}
