/**
 * API Route for Auth Endpoints
 * 
 * This file serves as an entry point for auth-related API endpoints.
 */

import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Redirect to the appropriate endpoint based on the method
  if (req.method === 'GET') {
    // Redirect to the authorize endpoint
    const queryParams = new URLSearchParams(req.query as Record<string, string>).toString();
    res.redirect(`/api/v1/auth/authorize?${queryParams}`);
  } else if (req.method === 'POST') {
    // Redirect to the token endpoint
    res.redirect(307, '/api/v1/auth/token');
  } else {
    // Method not allowed
    res.status(405).json({
      success: false,
      error: 'method_not_allowed',
      message: 'Method not allowed'
    });
  }
}
