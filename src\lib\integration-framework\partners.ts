/**
 * Integration Partners
 * 
 * This file contains the data for integration partners.
 */

import { IntegrationPartner } from './types';

// Integration partners
export const partners: IntegrationPartner[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'Leading AI research lab focused on ensuring artificial general intelligence benefits all of humanity.',
    website: 'https://openai.com',
    logo: '🤖',
    status: 'live'
  },
  {
    id: 'midjourney',
    name: 'Midjourney',
    description: 'Independent research lab exploring new mediums of thought and expanding the imaginative powers of the human species.',
    website: 'https://www.midjourney.com',
    logo: '🎨',
    status: 'live'
  },
  {
    id: 'github',
    name: 'GitHub',
    description: 'Platform for software development and version control using Git, allowing developers to store and manage their code.',
    website: 'https://github.com',
    logo: '👨‍💻',
    status: 'live'
  },
  {
    id: 'descript',
    name: 'Descript',
    description: 'All-in-one audio and video editing platform that makes editing as easy as using a word processor.',
    website: 'https://www.descript.com',
    logo: '🎬',
    status: 'beta'
  },
  {
    id: 'jasper',
    name: '<PERSON>',
    description: 'AI content platform that helps teams create content that converts.',
    website: 'https://www.jasper.ai',
    logo: '✍️',
    status: 'live'
  },
  {
    id: 'runway',
    name: 'Runway',
    description: 'Applied AI research company building next-generation creation tools.',
    website: 'https://runwayml.com',
    logo: '📹',
    status: 'beta'
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'All-in-one workspace for notes, tasks, wikis, and databases.',
    website: 'https://www.notion.so',
    logo: '📝',
    status: 'live'
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    description: 'AI safety company working to build reliable, interpretable, and steerable AI systems.',
    website: 'https://www.anthropic.com',
    logo: '🧠',
    status: 'live'
  },
  {
    id: 'stability-ai',
    name: 'Stability AI',
    description: 'Company focused on building and deploying open AI models for image, language, audio, video, and 3D.',
    website: 'https://stability.ai',
    logo: '🌈',
    status: 'live'
  },
  {
    id: 'grammarly',
    name: 'Grammarly',
    description: 'Digital writing assistant that helps with grammar, clarity, engagement, and delivery mistakes.',
    website: 'https://www.grammarly.com',
    logo: '📄',
    status: 'live'
  },
  {
    id: 'synthesia',
    name: 'Synthesia',
    description: 'AI video generation platform that creates videos with virtual avatars.',
    website: 'https://www.synthesia.io',
    logo: '🎥',
    status: 'beta'
  }
];
