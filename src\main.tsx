import { createRoot } from 'react-dom/client'
import App from './App'
import './index.css'

// Import debug function for authentication troubleshooting
import './lib/debugAuth'

// Import service worker registration
import * as serviceWorkerRegistration from './utils/serviceWorkerRegistration';

// Render the app
createRoot(document.getElementById("root")!).render(<App />);

// Only register service worker in production
if (process.env.NODE_ENV === 'production') {
  // Register service worker for offline support and better caching
  try {
    serviceWorkerRegistration.register({
      onUpdate: (registration) => {
        // When a new service worker is available, show a notification
        const shouldUpdate = window.confirm(
          'New version of Firenest is available! Update now?'
        );

        if (shouldUpdate && registration.waiting) {
          // Send a message to the service worker to skip waiting
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });

          // Reload the page to activate the new service worker
          window.location.reload();
        }
      },
    });
    console.log('Service worker registration attempted');
  } catch (error) {
    console.error('Error registering service worker:', error);
  }
} else {
  console.log('Service worker not registered in development mode');
  // Unregister any existing service workers in development
  serviceWorkerRegistration.unregister();
}
