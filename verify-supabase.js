import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = 'https://wjwguxccykrbarehqgpq.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indqd2d1eGNjeWtyYmFyZWhxZ3BxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQwMjk0NTMsImV4cCI6MjA1OTYwNTQ1M30.woVFqaqa5oji3A9OOohYcUwzdZ3miBgOtexmdTxqLbU'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Test basic functionality
async function testSupabase() {
  try {
    // First, try to get the current timestamp
    const { data: timeData, error: timeError } = await supabase
      .from('_internal')
      .select('now()')
      .single()
    
    if (timeError) {
      console.error('Time query failed:', timeError.message)
    } else {
      console.log('Time query succeeded:', timeData)
    }

    // Then, try to create a test table
    const { error: createError } = await supabase
      .rpc('create_test_table')
    
    if (createError) {
      console.error('Create table failed:', createError.message)
    } else {
      console.log('Create table succeeded')
    }

  } catch (error) {
    console.error('Test failed:', error.message)
  }
}

testSupabase()