/**
 * Upload Status Component
 * Real-time status tracking with detailed progress information
 */

import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  RefreshCw, 
  TrendingUp,
  Database,
  Shield
} from 'lucide-react'
import { uploadsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Progress } from '@/components/ui/Progress'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatFileSize, formatDate, getStatusColor } from '@/lib/utils'

interface UploadStatusProps {
  uploadId: string
  onRetry?: () => void
}

export function UploadStatus({ uploadId, onRetry }: UploadStatusProps) {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['upload-status', uploadId],
    queryFn: () => uploadsApi.get(uploadId),
    refetchInterval: (data) => {
      // Poll every 2 seconds if still processing
      const status = data?.data?.data?.status
      return status === 'UPLOADED' || status === 'VALIDATING' ? 2000 : false
    },
    enabled: !!uploadId
  })

  if (isLoading) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <span className="ml-3 text-white">Loading upload status...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-400 mr-3" />
            <span className="text-white">Failed to load upload status</span>
          </div>
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const upload = data?.data?.data
  if (!upload) return null

  return (
    <div className="space-y-6">
      {/* Main Status Card */}
      <div className="firenest-card">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center">
            <FileText className="w-6 h-6 text-fiery mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-white">{upload.fileName}</h3>
              <p className="text-sm text-gray-400">
                {formatFileSize(upload.fileSize)} • {upload.fileType.replace('_', ' ')}
              </p>
            </div>
          </div>
          <Badge variant={getStatusVariant(upload.status)}>
            {upload.status}
          </Badge>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-300">Processing Progress</span>
            <span className="text-sm text-gray-300">{upload.progress}%</span>
          </div>
          <Progress value={upload.progress} className="h-3" />
        </div>

        {/* Status Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="firenest-nested-card">
            <div className="flex items-center mb-2">
              <Clock className="w-4 h-4 text-blue-400 mr-2" />
              <span className="text-sm font-medium text-white">Timeline</span>
            </div>
            <div className="space-y-1 text-xs text-gray-400">
              <div>Uploaded: {formatDate(upload.timestamps.uploaded)}</div>
              <div>Last Updated: {formatDate(upload.timestamps.lastUpdated)}</div>
            </div>
          </div>

          <div className="firenest-nested-card">
            <div className="flex items-center mb-2">
              <Shield className="w-4 h-4 text-green-400 mr-2" />
              <span className="text-sm font-medium text-white">Security</span>
            </div>
            <div className="space-y-1 text-xs text-gray-400">
              <div>✓ Encrypted in transit</div>
              <div>✓ Secure storage</div>
              <div>✓ Access controlled</div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        {upload.nextSteps && upload.nextSteps.length > 0 && (
          <div className="firenest-nested-card">
            <h4 className="text-sm font-medium text-white mb-3">Next Steps</h4>
            <ul className="space-y-2">
              {upload.nextSteps.map((step: string, index: number) => (
                <li key={index} className="flex items-center text-sm text-gray-300">
                  <div className="w-2 h-2 bg-fiery rounded-full mr-3 flex-shrink-0" />
                  {step}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          {upload.status === 'INVALID' && onRetry && (
            <Button variant="outline" onClick={onRetry}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry Validation
            </Button>
          )}
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh Status
          </Button>
        </div>
      </div>

      {/* Data Quality Metrics */}
      {upload.metadata && Object.keys(upload.metadata).length > 0 && (
        <DataQualityMetrics metadata={upload.metadata} />
      )}

      {/* Validation Errors */}
      {upload.validationErrors && upload.validationErrors.length > 0 && (
        <ValidationErrors errors={upload.validationErrors} />
      )}
    </div>
  )
}

interface DataQualityMetricsProps {
  metadata: any
}

function DataQualityMetrics({ metadata }: DataQualityMetricsProps) {
  if (!metadata.dataQuality) return null

  const { completeness, consistency, accuracy } = metadata.dataQuality

  return (
    <div className="firenest-card">
      <div className="flex items-center mb-4">
        <TrendingUp className="w-5 h-5 text-fiery mr-3" />
        <h3 className="text-lg font-semibold text-white">Data Quality Analysis</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <QualityMetric
          label="Completeness"
          value={completeness}
          description="Percentage of non-empty values"
        />
        <QualityMetric
          label="Consistency"
          value={consistency}
          description="Data format consistency"
        />
        <QualityMetric
          label="Accuracy"
          value={accuracy}
          description="Data validation accuracy"
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div className="firenest-nested-card">
          <Database className="w-6 h-6 text-blue-400 mx-auto mb-2" />
          <div className="text-lg font-semibold text-white">{metadata.rowCount?.toLocaleString()}</div>
          <div className="text-xs text-gray-400">Rows</div>
        </div>
        <div className="firenest-nested-card">
          <div className="text-lg font-semibold text-white">{metadata.columnCount}</div>
          <div className="text-xs text-gray-400">Columns</div>
        </div>
        <div className="firenest-nested-card">
          <div className="text-lg font-semibold text-white">{formatFileSize(metadata.fileSize)}</div>
          <div className="text-xs text-gray-400">File Size</div>
        </div>
        <div className="firenest-nested-card">
          <div className="text-lg font-semibold text-white">{(metadata.processingTime / 1000).toFixed(1)}s</div>
          <div className="text-xs text-gray-400">Processing Time</div>
        </div>
      </div>
    </div>
  )
}

interface QualityMetricProps {
  label: string
  value: number
  description: string
}

function QualityMetric({ label, value, description }: QualityMetricProps) {
  const getColor = (value: number) => {
    if (value >= 90) return 'text-green-400'
    if (value >= 70) return 'text-yellow-400'
    return 'text-red-400'
  }

  return (
    <div className="firenest-nested-card">
      <div className="text-center">
        <div className={`text-2xl font-bold ${getColor(value)}`}>
          {value.toFixed(1)}%
        </div>
        <div className="text-sm font-medium text-white mt-1">{label}</div>
        <div className="text-xs text-gray-400 mt-1">{description}</div>
      </div>
      <Progress value={value} className="mt-3 h-2" />
    </div>
  )
}

interface ValidationErrorsProps {
  errors: any[]
}

function ValidationErrors({ errors }: ValidationErrorsProps) {
  return (
    <div className="firenest-card">
      <div className="flex items-center mb-4">
        <AlertCircle className="w-5 h-5 text-red-400 mr-3" />
        <h3 className="text-lg font-semibold text-white">Validation Issues</h3>
        <Badge variant="destructive" className="ml-3">
          {errors.length} {errors.length === 1 ? 'issue' : 'issues'}
        </Badge>
      </div>

      <div className="space-y-3">
        {errors.map((error, index) => (
          <div key={index} className="firenest-nested-card border-l-4 border-red-500">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-1">
                  <Badge variant={error.severity === 'ERROR' ? 'destructive' : 'warning'} className="mr-2">
                    {error.severity}
                  </Badge>
                  <span className="text-sm font-medium text-white">{error.type.replace('_', ' ')}</span>
                </div>
                <p className="text-sm text-gray-300 mb-2">{error.message}</p>
                {error.row && (
                  <p className="text-xs text-gray-400">
                    Row {error.row}{error.column && `, Column: ${error.column}`}
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function getStatusVariant(status: string): 'default' | 'secondary' | 'destructive' | 'success' | 'warning' | 'info' {
  switch (status.toLowerCase()) {
    case 'validated':
    case 'complete':
      return 'success'
    case 'invalid':
    case 'error':
    case 'failed':
      return 'destructive'
    case 'validating':
    case 'uploading':
      return 'info'
    case 'uploaded':
      return 'warning'
    default:
      return 'secondary'
  }
}
