/**
 * ID Token Utilities
 * 
 * This file contains utilities for generating and validating ID tokens.
 */

import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

// Secret key for signing tokens (in production, use a secure key management system)
const JWT_SECRET = process.env.JWT_SECRET || 'firenest-jwt-secret';

// ID token interface
export interface IdTokenPayload {
  // OIDC standard claims
  iss: string;         // Issuer
  sub: string;         // Subject (user ID)
  aud: string;         // Audience (client ID)
  exp: number;         // Expiration time
  iat: number;         // Issued at time
  auth_time: number;   // Time when the authentication occurred
  nonce?: string;      // Nonce value
  
  // Optional standard claims
  name?: string;       // Full name
  email?: string;      // Email address
  email_verified?: boolean; // Whether the email is verified
  
  // Custom claims
  firenest_user_id?: string; // Firenest user ID (same as sub)
  firenest_partner_id?: string; // Partner ID
}

/**
 * Generate an ID token
 * 
 * @param userId User ID
 * @param clientId Client ID
 * @param partnerId Partner ID
 * @param userData Additional user data
 * @param nonce Optional nonce value
 * @returns The signed ID token
 */
export function generateIdToken(
  userId: string,
  clientId: string,
  partnerId: string,
  userData: {
    name?: string;
    email?: string;
    email_verified?: boolean;
  } = {},
  nonce?: string
): string {
  const now = Math.floor(Date.now() / 1000);
  
  // Create the ID token payload
  const payload: IdTokenPayload = {
    // OIDC standard claims
    iss: 'https://firenest.app', // Issuer
    sub: userId, // Subject (user ID)
    aud: clientId, // Audience (client ID)
    exp: now + 3600, // Expiration time (1 hour)
    iat: now, // Issued at time
    auth_time: now, // Time when the authentication occurred
    
    // Optional standard claims
    name: userData.name,
    email: userData.email,
    email_verified: userData.email_verified ?? true,
    
    // Custom claims
    firenest_user_id: userId,
    firenest_partner_id: partnerId,
  };
  
  // Add nonce if provided
  if (nonce) {
    payload.nonce = nonce;
  }
  
  // Sign the ID token
  return jwt.sign(payload, JWT_SECRET, {
    algorithm: 'HS256',
    jwtid: uuidv4(), // Add a unique ID to the token
  });
}

/**
 * Verify an ID token
 * 
 * @param idToken The ID token to verify
 * @param clientId The expected client ID (audience)
 * @returns The decoded token payload if valid, null otherwise
 */
export function verifyIdToken(
  idToken: string,
  clientId: string
): IdTokenPayload | null {
  try {
    // Verify the token signature and audience
    const decoded = jwt.verify(idToken, JWT_SECRET, {
      algorithms: ['HS256'],
      audience: clientId,
    }) as IdTokenPayload;
    
    // Check if the token has expired
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp < now) {
      console.error('ID token has expired');
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error verifying ID token:', error);
    return null;
  }
}

/**
 * Get user information from an ID token
 * 
 * @param idToken The ID token
 * @param clientId The expected client ID (audience)
 * @returns User information extracted from the ID token
 */
export function getUserInfoFromIdToken(
  idToken: string,
  clientId: string
): {
  userId: string;
  name?: string;
  email?: string;
  partnerId?: string;
} | null {
  const decoded = verifyIdToken(idToken, clientId);
  
  if (!decoded) {
    return null;
  }
  
  return {
    userId: decoded.sub,
    name: decoded.name,
    email: decoded.email,
    partnerId: decoded.firenest_partner_id,
  };
}
