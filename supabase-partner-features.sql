-- Partner Features Tables
-- This file contains the SQL to create tables for partner features and metering

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Alter partner_tools table to add feature_id and credit_cost_per_unit columns
ALTER TABLE partner_tools
ADD COLUMN IF NOT EXISTS feature_id TEXT,
ADD COLUMN IF NOT EXISTS display_name TEXT,
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS credit_cost_per_unit INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS metric_type TEXT DEFAULT 'custom';

-- Create a unique index on partner_id and feature_id
CREATE UNIQUE INDEX IF NOT EXISTS partner_tools_partner_id_feature_id_idx 
ON partner_tools(partner_id, feature_id);

-- Create partner_features table for storing feature definitions
CREATE TABLE IF NOT EXISTS partner_features (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  partner_id UUID REFERENCES partner_accounts(id) ON DELETE CASCADE,
  feature_id TEXT NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  default_credit_cost INTEGER NOT NULL DEFAULT 1,
  metric_type TEXT NOT NULL DEFAULT 'custom', -- 'time', 'api_calls', 'resources', 'custom'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(partner_id, feature_id)
);

-- Create feature_usage table for tracking feature usage
CREATE TABLE IF NOT EXISTS feature_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  partner_id UUID REFERENCES partner_accounts(id) ON DELETE CASCADE,
  feature_id TEXT NOT NULL,
  units_consumed INTEGER NOT NULL DEFAULT 1,
  credits_used INTEGER NOT NULL DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS partner_features_partner_id_idx ON partner_features(partner_id);
CREATE INDEX IF NOT EXISTS feature_usage_user_id_idx ON feature_usage(user_id);
CREATE INDEX IF NOT EXISTS feature_usage_partner_id_idx ON feature_usage(partner_id);
CREATE INDEX IF NOT EXISTS feature_usage_feature_id_idx ON feature_usage(feature_id);
CREATE INDEX IF NOT EXISTS feature_usage_created_at_idx ON feature_usage(created_at);

-- Enable Row Level Security
ALTER TABLE partner_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_usage ENABLE ROW LEVEL SECURITY;

-- Create policies for partner_features table
CREATE POLICY "Partners can view their own features"
  ON partner_features FOR SELECT
  USING (auth.uid() = partner_id);

CREATE POLICY "Partners can insert their own features"
  ON partner_features FOR INSERT
  WITH CHECK (auth.uid() = partner_id);

CREATE POLICY "Partners can update their own features"
  ON partner_features FOR UPDATE
  USING (auth.uid() = partner_id);

CREATE POLICY "Partners can delete their own features"
  ON partner_features FOR DELETE
  USING (auth.uid() = partner_id);

-- Create policies for feature_usage table
CREATE POLICY "Partners can view usage of their features"
  ON feature_usage FOR SELECT
  USING (auth.uid() = partner_id);

-- Create function to record feature usage
CREATE OR REPLACE FUNCTION record_feature_usage(
  user_id UUID,
  partner_id UUID,
  feature_id TEXT,
  units_consumed INTEGER DEFAULT 1,
  metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  feature_record RECORD;
  credits_used INTEGER;
  usage_id UUID;
BEGIN
  -- Get the feature configuration
  SELECT default_credit_cost INTO feature_record
  FROM partner_features
  WHERE partner_id = record_feature_usage.partner_id
  AND feature_id = record_feature_usage.feature_id;
  
  -- Calculate credits used
  credits_used := COALESCE(feature_record.default_credit_cost, 1) * units_consumed;
  
  -- Insert usage record
  INSERT INTO feature_usage (
    id,
    user_id,
    partner_id,
    feature_id,
    units_consumed,
    credits_used,
    metadata,
    created_at
  )
  VALUES (
    uuid_generate_v4(),
    user_id,
    partner_id,
    feature_id,
    units_consumed,
    credits_used,
    metadata,
    NOW()
  )
  RETURNING id INTO usage_id;
  
  -- Deduct credits from user's balance
  UPDATE user_credits
  SET 
    balance = balance - credits_used,
    last_updated = NOW()
  WHERE user_id = record_feature_usage.user_id;
  
  -- Record credit transaction
  INSERT INTO credit_transactions (
    user_id,
    amount,
    description,
    transaction_type
  )
  VALUES (
    user_id,
    -credits_used,
    format('Used %s credits for feature %s', credits_used, feature_id),
    'usage'
  );
  
  RETURN usage_id;
EXCEPTION WHEN OTHERS THEN
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
