import { Bo<PERSON>, Lightbulb, Workflow, Brain, Image, MessageSquare, CodeSquare, Video, Music, Search, Pen, Wand2, Headphones } from 'lucide-react';

interface AIToolIconProps {
  iconName: string;
  className?: string;
}

const AIToolIcon: React.FC<AIToolIconProps> = ({ iconName, className = '' }) => {
  switch (iconName) {
    case 'Bot':
      return <Bot className={className} />;
    case 'Lightbulb':
      return <Lightbulb className={className} />;
    case 'Workflow':
      return <Workflow className={className} />;
    case 'Brain':
      return <Brain className={className} />;
    case 'Image':
      return <Image className={className} />;
    case 'MessageSquare':
      return <MessageSquare className={className} />;
    case 'CodeSquare':
      return <CodeSquare className={className} />;
    case 'Video':
      return <Video className={className} />;
    case 'Music':
      return <Music className={className} />;
    case 'Search':
      return <Search className={className} />;
    case 'Pen':
      return <Pen className={className} />;
    case 'Wand2':
      return <Wand2 className={className} />;
    case 'Audio':
      return <Headphones className={className} />;
    default:
      return <Bot className={className} />;
  }
};

export default AIToolIcon;
