// Mock health monitoring system

// Mock hook for integration health status
export const useIntegrationHealth = () => {
  // Mock health status data
  const allHealthStatuses: Record<string, 'up' | 'degraded' | 'down' | 'unknown'> = {
    'chatgpt': 'up',
    'midjourney': 'up',
    'claude': 'up',
    'dalle': 'degraded',
    'stable-diffusion': 'up',
    'whisper': 'up'
  };

  return { allHealthStatuses };
};
