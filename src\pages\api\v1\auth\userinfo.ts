/**
 * UserInfo API Endpoint
 *
 * This endpoint implements the OpenID Connect UserInfo endpoint.
 * It returns information about the authenticated user based on the access token.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';
import {
  withTokenAuth,
  StatusCodes,
  ErrorType,
  ErrorMessages,
  logApiRequest
} from '../utils';

// Handler for the userinfo endpoint
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
  context: { userId: string; partnerId: string }
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(StatusCodes.METHOD_NOT_ALLOWED).json({
      success: false,
      error: ErrorType.METHOD_NOT_ALLOWED,
      message: ErrorMessages[ErrorType.METHOD_NOT_ALLOWED]
    });
  }

  try {
    const { userId, partnerId } = context;

    // Get user information from the database
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, display_name, created_at, updated_at')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error fetching user data'
      });
    }

    if (!userData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: ErrorType.NOT_FOUND,
        message: 'User not found'
      });
    }

    // Get user profile information
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .select('company, job_title, bio, website, phone, country')
      .eq('user_id', userId)
      .single();

    // Get user credit balance
    const { data: creditData, error: creditError } = await supabase
      .from('user_credits')
      .select('balance, last_updated')
      .eq('user_id', userId)
      .single();

    // Prepare the response following OIDC standards
    const userInfo = {
      // OIDC standard claims
      sub: userData.id, // Subject - Identifier for the user
      email: userData.email,
      email_verified: true, // Assuming email is verified
      name: userData.display_name,
      updated_at: Math.floor(new Date(userData.updated_at).getTime() / 1000),

      // Additional profile information if available
      ...(profileData && {
        company: profileData.company,
        job_title: profileData.job_title,
        bio: profileData.bio,
        website: profileData.website,
        phone: profileData.phone,
        country: profileData.country,
      }),

      // Credit information if available
      ...(creditData && {
        credit_balance: creditData.balance,
        credit_updated_at: creditData.last_updated,
      }),
    };

    // Log the API request (mask sensitive data)
    await logApiRequest(
      '/api/v1/auth/userinfo',
      'GET',
      partnerId,
      userId,
      {},
      StatusCodes.OK,
      {
        success: true,
        // Don't log the full user info
        sub: userInfo.sub,
        email_verified: userInfo.email_verified,
        updated_at: userInfo.updated_at,
      }
    );

    // Return the user information
    return res.status(StatusCodes.OK).json(userInfo);
  } catch (error) {
    console.error('Error fetching user info:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
}

// Export the handler with token authentication
export default withTokenAuth(handler);
