/**
 * Session Management API Endpoint
 * 
 * This endpoint allows partners to manage user sessions.
 * It handles creating, retrieving, and updating sessions.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import {
  withTokenAuth,
  StatusCodes,
  ErrorType,
  ErrorMessages,
  logApiRequest
} from '../utils';

// Handler for the session management endpoint
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
  context: { userId: string; partnerId: string }
) {
  const { userId, partnerId } = context;

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return handleGetSession(req, res, userId, partnerId);
    case 'POST':
      return handleCreateSession(req, res, userId, partnerId);
    case 'PUT':
      return handleUpdateSession(req, res, userId, partnerId);
    default:
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Method not allowed'
      });
  }
}

/**
 * Handle GET request to retrieve session information
 */
async function handleGetSession(
  req: NextApiRequest,
  res: NextApiResponse,
  userId: string,
  partnerId: string
) {
  try {
    const { sessionId } = req.query;

    // Validate session ID
    if (!sessionId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Session ID is required'
      });
    }

    // Get session from database
    const { data, error } = await supabase
      .from('usage_sessions')
      .select('*, usage_events(id, event_type, event_data, credits_used, created_at)')
      .eq('id', sessionId)
      .eq('user_id', userId)
      .eq('tool_id', partnerId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching session:', error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error fetching session'
      });
    }

    if (!data) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: ErrorType.NOT_FOUND,
        message: 'Session not found'
      });
    }

    // Get user credit balance
    const { data: creditData, error: creditError } = await supabase
      .from('user_credits')
      .select('balance')
      .eq('user_id', userId)
      .maybeSingle();

    if (creditError) {
      console.error('Error fetching user credits:', creditError);
    }

    // Prepare response
    const response = {
      success: true,
      data: {
        session: {
          id: data.id,
          startTime: data.start_time,
          endTime: data.end_time,
          status: data.status,
          metrics: data.metrics,
          estimatedCredits: data.estimated_credits,
          actualCreditsUsed: data.actual_credits_used
        },
        events: data.usage_events,
        user: {
          id: userId,
          creditBalance: creditData?.balance || 0
        }
      }
    };

    // Log the API request
    await logApiRequest(
      '/api/v1/session',
      'GET',
      partnerId,
      userId,
      { sessionId },
      StatusCodes.OK,
      response
    );

    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error getting session:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
}

/**
 * Handle POST request to create a new session
 */
async function handleCreateSession(
  req: NextApiRequest,
  res: NextApiResponse,
  userId: string,
  partnerId: string
) {
  try {
    const { metadata = {} } = req.body;

    // Generate a new session ID
    const sessionId = uuidv4();

    // Create a new session in the database
    const { data, error } = await supabase
      .from('usage_sessions')
      .insert({
        id: sessionId,
        user_id: userId,
        tool_id: partnerId,
        start_time: new Date().toISOString(),
        status: 'active',
        metrics: metadata,
        estimated_credits: 0
      })
      .select('id, start_time')
      .single();

    if (error) {
      console.error('Error creating session:', error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error creating session'
      });
    }

    // Record session start event
    const { error: eventError } = await supabase
      .rpc('record_usage_event', {
        session_id: sessionId,
        user_id: userId,
        tool_id: partnerId,
        event_type: 'session_start',
        event_data: {
          action: 'session_start',
          quantity: 1,
          timestamp: data.start_time,
          details: metadata
        },
        credits_used: 0
      });

    if (eventError) {
      console.error('Error recording session start event:', eventError);
    }

    // Get user credit balance
    const { data: creditData, error: creditError } = await supabase
      .from('user_credits')
      .select('balance')
      .eq('user_id', userId)
      .maybeSingle();

    if (creditError) {
      console.error('Error fetching user credits:', creditError);
    }

    // Prepare response
    const response = {
      success: true,
      data: {
        sessionId: data.id,
        startTime: data.start_time,
        userId,
        partnerId,
        creditBalance: creditData?.balance || 0
      }
    };

    // Log the API request
    await logApiRequest(
      '/api/v1/session',
      'POST',
      partnerId,
      userId,
      req.body,
      StatusCodes.CREATED,
      response
    );

    return res.status(StatusCodes.CREATED).json(response);
  } catch (error) {
    console.error('Error creating session:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
}

/**
 * Handle PUT request to update an existing session
 */
async function handleUpdateSession(
  req: NextApiRequest,
  res: NextApiResponse,
  userId: string,
  partnerId: string
) {
  try {
    const { sessionId } = req.query;
    const { status, metrics = {}, endTime } = req.body;

    // Validate session ID
    if (!sessionId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        error: ErrorType.INVALID_REQUEST,
        message: 'Session ID is required'
      });
    }

    // Check if session exists and belongs to the user and partner
    const { data: existingSession, error: checkError } = await supabase
      .from('usage_sessions')
      .select('id, status')
      .eq('id', sessionId)
      .eq('user_id', userId)
      .eq('tool_id', partnerId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking session:', checkError);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: 'Error checking session'
      });
    }

    if (!existingSession) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: ErrorType.NOT_FOUND,
        message: 'Session not found'
      });
    }

    // Prepare update data
    const updateData: any = {};
    
    if (status) {
      updateData.status = status;
    }
    
    if (endTime) {
      updateData.end_time = endTime;
    }
    
    // Update session in database
    let updatedSession;
    
    if (status === 'completed' || endTime) {
      // End the session using the stored procedure
      const { data, error } = await supabase
        .rpc('end_usage_session', {
          session_id: sessionId,
          end_time: endTime || new Date().toISOString(),
          status: status || 'completed',
          metrics: metrics,
          actual_credits_used: null // Let the function calculate based on estimated
        });

      if (error) {
        console.error('Error ending session:', error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error ending session'
        });
      }
      
      // Get the updated session
      const { data: sessionData, error: sessionError } = await supabase
        .from('usage_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();
        
      if (sessionError) {
        console.error('Error fetching updated session:', sessionError);
      } else {
        updatedSession = sessionData;
      }
      
      // Record session end event
      const { error: eventError } = await supabase
        .rpc('record_usage_event', {
          session_id: sessionId,
          user_id: userId,
          tool_id: partnerId,
          event_type: 'session_end',
          event_data: {
            action: 'session_end',
            quantity: 1,
            timestamp: endTime || new Date().toISOString(),
            details: metrics
          },
          credits_used: 0
        });

      if (eventError) {
        console.error('Error recording session end event:', eventError);
      }
    } else {
      // Just update metrics
      const { data, error } = await supabase
        .rpc('update_session_metrics', {
          session_id: sessionId,
          new_metrics: metrics,
          estimated_credits: null // Don't update estimated credits
        });

      if (error) {
        console.error('Error updating session metrics:', error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: ErrorType.SERVER_ERROR,
          message: 'Error updating session metrics'
        });
      }
      
      // Get the updated session
      const { data: sessionData, error: sessionError } = await supabase
        .from('usage_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();
        
      if (sessionError) {
        console.error('Error fetching updated session:', sessionError);
      } else {
        updatedSession = sessionData;
      }
    }

    // Prepare response
    const response = {
      success: true,
      data: {
        sessionId,
        status: updatedSession?.status || status || existingSession.status,
        metrics: updatedSession?.metrics || metrics,
        endTime: updatedSession?.end_time || endTime,
        actualCreditsUsed: updatedSession?.actual_credits_used
      }
    };

    // Log the API request
    await logApiRequest(
      '/api/v1/session',
      'PUT',
      partnerId,
      userId,
      { sessionId, ...req.body },
      StatusCodes.OK,
      response
    );

    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error updating session:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: ErrorType.SERVER_ERROR,
      message: ErrorMessages[ErrorType.SERVER_ERROR]
    });
  }
}

export default withTokenAuth(handler);
