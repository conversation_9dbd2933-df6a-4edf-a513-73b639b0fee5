import React, { Component, ErrorInfo, ReactNode } from 'react';
import { notify } from '@/components/ui/notification-system';
import ErrorPage from '@/pages/ErrorPage';
import ErrorDiagnosticPage from '@/pages/ErrorDiagnosticPage';
import { logError, ErrorCategory } from '@/lib/error-utils';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * ErrorBoundary component to catch JavaScript errors anywhere in the child component tree,
 * log those errors, and display a fallback UI instead of the component tree that crashed.
 */
class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to our error tracking service
    logError(error, errorInfo);

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Update state with error details
    this.setState({ errorInfo });

    // Show a toast notification
    notify.error('An unexpected error occurred', {
      title: 'Application Error',
      duration: 5000
    });

    // Determine error category based on error message or stack
    let category = ErrorCategory.UNKNOWN;
    if (error.stack?.includes('fetch') || error.message?.includes('network')) {
      category = ErrorCategory.NETWORK;
    } else if (error.stack?.includes('render') || error.stack?.includes('component')) {
      category = ErrorCategory.RENDERING;
    }

    // Get component name from error info if possible
    const componentMatch = errorInfo.componentStack.match(/\s+in\s+([A-Za-z0-9_]+)/);
    const componentName = componentMatch ? componentMatch[1] : undefined;

    // We'll show the error diagnostic page in the render method
  }

  public render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Determine error category based on error message or stack
      let category = ErrorCategory.UNKNOWN;
      if (this.state.error?.stack?.includes('fetch') || this.state.error?.message?.includes('network')) {
        category = ErrorCategory.NETWORK;
      } else if (this.state.error?.stack?.includes('render') || this.state.error?.stack?.includes('component')) {
        category = ErrorCategory.RENDERING;
      }

      // Get component name from error info if possible
      const componentMatch = this.state.errorInfo?.componentStack.match(/\s+in\s+([A-Za-z0-9_]+)/);
      const componentName = componentMatch ? componentMatch[1] : undefined;

      // Use the ErrorDiagnosticPage directly
      return (
        <ErrorDiagnosticPage
          message={this.state.error?.message}
          stack={this.state.error?.stack}
          component={componentName}
          category={category}
          path={window.location.pathname}
          onReset={() => this.setState({ hasError: false, error: null, errorInfo: null })}
        />
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
