# Firenest Sandbox - Local Development Setup

## Quick Start Guide

This guide will help you set up and run Firenest Sandbox locally for development and testing.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **PostgreSQL 14+** - [Download here](https://www.postgresql.org/download/)
- **Git** - [Download here](https://git-scm.com/)
- **AWS CLI** (optional, for S3/SQS features) - [Install guide](https://aws.amazon.com/cli/)

## Step 1: Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd firenest-sandbox

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

## Step 2: Database Setup

### Option A: Local PostgreSQL

1. **Create Database**
```bash
# Connect to PostgreSQL
psql -U postgres

# Create database and user
CREATE DATABASE firenest_sandbox;
CREATE USER sandbox_admin WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE firenest_sandbox TO sandbox_admin;
\q
```

2. **Run Schema**
```bash
# From the project root
psql -U sandbox_admin -d firenest_sandbox -f database/schema.sql
```

### Option B: Docker PostgreSQL

```bash
# Run PostgreSQL in Docker
docker run --name firenest-postgres \
  -e POSTGRES_DB=firenest_sandbox \
  -e POSTGRES_USER=sandbox_admin \
  -e POSTGRES_PASSWORD=your_password \
  -p 5432:5432 \
  -d postgres:14

# Wait for container to start, then run schema
sleep 10
docker exec -i firenest-postgres psql -U sandbox_admin -d firenest_sandbox < database/schema.sql
```

## Step 3: Environment Configuration

### Backend Configuration

Create `backend/.env` file:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=firenest_sandbox
DB_USERNAME=sandbox_admin
DB_PASSWORD=your_password
DB_SSL=false

# AWS Configuration (Optional - for file upload features)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_BUCKET_NAME=your_s3_bucket
SQS_QUEUE_URL=your_sqs_queue_url
SQS_DLQ_URL=your_dlq_url

# Authentication (Development)
JWT_SECRET=your-32-character-secret-key-here
AUTH_PROVIDER_TYPE=development
AUTH_PROVIDER_DOMAIN=localhost
AUTH_PROVIDER_CLIENT_ID=dev-client-id
AUTH_PROVIDER_CLIENT_SECRET=dev-client-secret

# Security
ENCRYPTION_KEY=your-32-character-encryption-key
HASH_SALT=12

# Application
NODE_ENV=development
PORT=3001
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Logging
LOG_LEVEL=debug
```

### Frontend Configuration

Create `frontend/.env` file:

```bash
VITE_API_URL=http://localhost:3001/api/v1
VITE_AUTH_DOMAIN=localhost
VITE_AUTH_CLIENT_ID=dev-client-id
VITE_ENVIRONMENT=development
```

## Step 4: Running the Application

### Option A: Development Mode (Recommended)

**Terminal 1 - Backend API:**
```bash
cd backend
npm run dev
```

**Terminal 2 - Background Workers:**
```bash
cd backend
npm run dev:worker
```

**Terminal 3 - Frontend:**
```bash
cd frontend
npm run dev
```

### Option B: Production Build

**Backend:**
```bash
cd backend
npm run build
npm start
```

**Frontend:**
```bash
cd frontend
npm run build
npm run preview
```

## Step 5: Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health
- **API Documentation**: http://localhost:3001/api/v1

## Step 6: Test the Setup

### 1. Health Check
```bash
curl http://localhost:3001/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "development"
}
```

### 2. Database Connection
```bash
# Check if tables were created
psql -U sandbox_admin -d firenest_sandbox -c "\dt"
```

### 3. Frontend Access
Open http://localhost:3000 in your browser. You should see the Firenest Sandbox login page.

## Development Workflow

### 1. Create a Workspace
1. Register/login at http://localhost:3000
2. Create a new workspace
3. Invite team members (optional)

### 2. Create a Project
1. Navigate to Projects
2. Click "Create Project"
3. Fill in project details

### 3. Upload Data
1. Go to your project
2. Click "Upload Data"
3. Upload CSV files with customer data

### 4. Create Pricing Models
1. Navigate to "Pricing Models"
2. Click "Create Model"
3. Use the visual builder to create pricing components

### 5. Run Simulations
1. Go to "Simulations"
2. Click "New Simulation"
3. Select models and configure scenarios
4. Monitor progress and view results

## Troubleshooting

### Common Issues

**1. Database Connection Error**
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # macOS

# Check connection
psql -U sandbox_admin -d firenest_sandbox -c "SELECT 1;"
```

**2. Port Already in Use**
```bash
# Find process using port 3001
lsof -i :3001  # macOS/Linux
netstat -ano | findstr :3001  # Windows

# Kill the process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

**3. Node Modules Issues**
```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

**4. TypeScript Compilation Errors**
```bash
# Clear TypeScript cache
rm -rf dist
npm run build
```

### Environment Variables

Make sure all required environment variables are set:

```bash
# Check backend environment
cd backend
node -e "console.log(process.env.DB_HOST)"

# Check frontend environment
cd frontend
npm run dev -- --debug
```

### Database Issues

**Reset Database:**
```bash
# Drop and recreate database
psql -U postgres -c "DROP DATABASE IF EXISTS firenest_sandbox;"
psql -U postgres -c "CREATE DATABASE firenest_sandbox;"
psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE firenest_sandbox TO sandbox_admin;"
psql -U sandbox_admin -d firenest_sandbox -f database/schema.sql
```

**Check Row Level Security:**
```sql
-- Connect to database
psql -U sandbox_admin -d firenest_sandbox

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies;
```

## Development Tips

### 1. Hot Reloading
Both frontend and backend support hot reloading. Changes will automatically restart the servers.

### 2. Debugging
- Backend: Use VS Code debugger or add `console.log` statements
- Frontend: Use browser developer tools
- Database: Use pgAdmin or command line tools

### 3. Testing
```bash
# Run backend tests
cd backend
npm test

# Run frontend tests
cd frontend
npm test
```

### 4. Linting
```bash
# Backend linting
cd backend
npm run lint
npm run lint:fix

# Frontend linting
cd frontend
npm run lint
npm run lint:fix
```

## Next Steps

1. **Explore Features**: Try uploading sample data and creating pricing models
2. **Read Documentation**: Check `/docs` folder for detailed guides
3. **Customize**: Modify the code to fit your specific needs
4. **Deploy**: Follow `DEPLOYMENT.md` for production deployment

## Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review the logs in the terminal
3. Check the browser console for frontend issues
4. Verify environment variables are set correctly
5. Ensure all prerequisites are installed

For additional help, refer to the documentation in the `/docs` folder or contact the development team.
