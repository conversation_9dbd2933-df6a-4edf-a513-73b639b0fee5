/**
 * Security Dashboard Page
 * SOC 2 Alignment: CC6.1 (Logical Access), CC7.1 (System Operations), CC7.2 (System Monitoring)
 */

import React, { useState } from 'react'
import { 
  Shield, 
  Lock, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  Activity,
  FileText,
  Download,
  RefreshCw,
  Clock,
  Users,
  Database,
  Key
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import SecurityMonitor from '@/components/security/SecurityMonitor'

export function SecurityDashboard() {
  const [activeTab, setActiveTab] = useState<'overview' | 'monitoring' | 'compliance' | 'reports'>('overview')

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Security Dashboard</h1>
          <p className="text-gray-400 mt-1">
            Monitor security status, compliance, and audit trails
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Security Status Banner */}
      <div className="firenest-card bg-green-500/10 border border-green-500/20">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
            <Shield className="w-6 h-6 text-green-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white">System Secure</h3>
            <p className="text-green-400">All security checks passed • Last scan: 2 minutes ago</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-400">Real-time Monitoring</span>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="firenest-card">
        <div className="flex space-x-1 p-1 bg-muted rounded-lg">
          <button
            onClick={() => setActiveTab('overview')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Shield className="w-4 h-4 mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('monitoring')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'monitoring'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Activity className="w-4 h-4 mr-2" />
            Monitoring
          </button>
          <button
            onClick={() => setActiveTab('compliance')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'compliance'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <FileText className="w-4 h-4 mr-2" />
            Compliance
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'reports'
                ? 'bg-fiery text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Download className="w-4 h-4 mr-2" />
            Reports
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && <SecurityOverview />}
      {activeTab === 'monitoring' && <SecurityMonitor />}
      {activeTab === 'compliance' && <ComplianceStatus />}
      {activeTab === 'reports' && <SecurityReports />}
    </div>
  )
}

function SecurityOverview() {
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">100%</p>
              <p className="text-gray-400">Security Score</p>
            </div>
          </div>
        </div>
        
        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">5</p>
              <p className="text-gray-400">Active Users</p>
            </div>
          </div>
        </div>
        
        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Database className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">256</p>
              <p className="text-gray-400">Encrypted Files</p>
            </div>
          </div>
        </div>
        
        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-fiery/20 rounded-lg flex items-center justify-center">
              <Key className="w-6 h-6 text-fiery" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">0</p>
              <p className="text-gray-400">Failed Logins</p>
            </div>
          </div>
        </div>
      </div>

      {/* Security Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Security Controls</h3>
          <div className="space-y-3">
            {[
              { name: 'Multi-Factor Authentication', status: 'enabled', icon: Lock },
              { name: 'Data Encryption', status: 'enabled', icon: Shield },
              { name: 'Access Logging', status: 'enabled', icon: Eye },
              { name: 'Intrusion Detection', status: 'enabled', icon: AlertTriangle }
            ].map((control) => (
              <div key={control.name} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <control.icon className="w-5 h-5 text-green-400" />
                  <span className="text-white">{control.name}</span>
                </div>
                <Badge variant="success">Enabled</Badge>
              </div>
            ))}
          </div>
        </div>

        <div className="firenest-card">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {[
              { action: 'User login', time: '2 minutes ago', status: 'success' },
              { action: 'Data export', time: '15 minutes ago', status: 'success' },
              { action: 'Permission change', time: '1 hour ago', status: 'success' },
              { action: 'Security scan', time: '2 hours ago', status: 'success' }
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-white">{activity.action}</span>
                </div>
                <span className="text-sm text-gray-400">{activity.time}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function ComplianceStatus() {
  return (
    <div className="space-y-6">
      {/* Compliance Overview */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-6">SOC 2 Type II Compliance</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            { control: 'CC6.1 - Logical Access', status: 'compliant', description: 'User access controls implemented' },
            { control: 'CC6.2 - Access Control', status: 'compliant', description: 'Role-based access in place' },
            { control: 'CC6.3 - Access Revocation', status: 'compliant', description: 'Automated access removal' },
            { control: 'CC7.1 - System Operations', status: 'compliant', description: 'Monitoring systems active' },
            { control: 'CC7.2 - System Monitoring', status: 'compliant', description: 'Real-time alerting enabled' },
            { control: 'CC8.1 - Change Management', status: 'compliant', description: 'Controlled deployment process' }
          ].map((item) => (
            <div key={item.control} className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <Badge variant="success">Compliant</Badge>
              </div>
              <h4 className="text-white font-medium mb-1">{item.control}</h4>
              <p className="text-sm text-gray-400">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function SecurityReports() {
  return (
    <div className="space-y-6">
      {/* Available Reports */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-6">Available Reports</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            { name: 'Security Audit Report', description: 'Comprehensive security assessment', lastGenerated: '2024-01-15' },
            { name: 'Compliance Report', description: 'SOC 2 compliance status', lastGenerated: '2024-01-15' },
            { name: 'Access Log Report', description: 'User access and activity logs', lastGenerated: '2024-01-15' },
            { name: 'Vulnerability Assessment', description: 'Security vulnerability scan results', lastGenerated: '2024-01-15' }
          ].map((report) => (
            <div key={report.name} className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">{report.name}</h4>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
              </div>
              <p className="text-sm text-gray-400 mb-2">{report.description}</p>
              <p className="text-xs text-gray-500">Last generated: {report.lastGenerated}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
