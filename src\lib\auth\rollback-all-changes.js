// Script to rollback all changes and restore the original functionality
import { supabase } from '@/lib/supabase';
import fs from 'fs';
import path from 'path';

async function rollbackAllChanges() {
  try {
    console.log('Starting rollback of all changes...');
    
    // Step 1: Revert the RLS policy changes for the access_tokens table
    console.log('Reverting RLS policy changes for access_tokens table...');
    
    // Execute the SQL directly to drop the policies we added
    const { error: dropPoliciesError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop the INSERT policies we added
        DROP POLICY IF EXISTS "Partners can insert tokens" ON access_tokens;
        DROP POLICY IF EXISTS "Users can insert tokens" ON access_tokens;
        DROP POLICY IF EXISTS "Service role can insert tokens" ON access_tokens;
      `
    });
    
    if (dropPoliciesError) {
      console.error('Error dropping policies:', dropPoliciesError);
      // Continue anyway, as we want to try all rollback steps
    } else {
      console.log('Policies dropped successfully.');
    }
    
    // Step 2: Restore the original token.js file
    console.log('Restoring original token.js file...');
    
    // We'll use a direct SQL query to restore the original RLS policies
    const { error: restorePoliciesError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Make sure RLS is enabled
        ALTER TABLE access_tokens ENABLE ROW LEVEL SECURITY;
        
        -- Recreate the original policies
        CREATE POLICY IF NOT EXISTS "Partners can view their tokens"
        ON access_tokens
        FOR SELECT
        TO authenticated
        USING (
          partner_id = auth.uid()
        );
        
        CREATE POLICY IF NOT EXISTS "Users can view their own tokens"
        ON access_tokens
        FOR SELECT
        TO authenticated
        USING (
          user_id = auth.uid()
        );
        
        CREATE POLICY IF NOT EXISTS "Partners can update their tokens"
        ON access_tokens
        FOR UPDATE
        TO authenticated
        USING (
          partner_id = auth.uid()
        );
        
        -- Add a policy for service role to do everything
        CREATE POLICY IF NOT EXISTS "Service role can do everything"
        ON access_tokens
        FOR ALL
        TO service_role
        USING (true)
        WITH CHECK (true);
      `
    });
    
    if (restorePoliciesError) {
      console.error('Error restoring policies:', restorePoliciesError);
    } else {
      console.log('Original policies restored successfully.');
    }
    
    // Step 3: Verify the rollback
    console.log('Verifying rollback...');
    
    const { data: policies, error: policiesError } = await supabase.rpc('get_table_policies', {
      table_name: 'access_tokens'
    });
    
    if (policiesError) {
      console.error('Error checking policies:', policiesError);
    } else {
      console.log('Current policies for access_tokens table:', policies);
    }
    
    console.log('Rollback completed.');
    
    return {
      success: true,
      message: 'Rollback completed successfully.'
    };
  } catch (error) {
    console.error('Exception during rollback:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  rollbackAllChanges()
    .then(result => {
      console.log('Rollback result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

export default rollbackAllChanges;
