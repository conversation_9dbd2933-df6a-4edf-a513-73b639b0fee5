import React from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { ArrowLeft, Key, Code, ExternalLink } from 'lucide-react';
import { Loading } from '@/components/ui/loading';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerOIDCDocumentation: React.FC = () => {
  const navigate = useNavigate();
  const { partner, isLoading } = usePartner();

  if (isLoading) {
    return <Loading />;
  }

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/documentation')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">OpenID Connect Documentation</h1>
                <p className="text-white/60">Comprehensive guide to implementing OpenID Connect with Firenest</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="firenest-card sticky top-24">
              <CardContent className="p-4">
                <nav className="space-y-1">
                  <Button
                    variant="default"
                    className="w-full justify-start bg-gradient-to-r from-fiery to-fiery/90 text-white"
                  >
                    Overview
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-white/70 hover:text-white hover:bg-white/5"
                    onClick={() => {
                      const element = document.getElementById('implementation');
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Implementation
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-white/70 hover:text-white hover:bg-white/5"
                    onClick={() => {
                      const element = document.getElementById('configuration');
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Configuration
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-white/70 hover:text-white hover:bg-white/5"
                    onClick={() => {
                      const element = document.getElementById('endpoints');
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Endpoints
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-white/70 hover:text-white hover:bg-white/5"
                    onClick={() => {
                      const element = document.getElementById('code-examples');
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Code Examples
                  </Button>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Overview */}
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-2xl text-white">OpenID Connect Overview</CardTitle>
                <CardDescription>
                  Understanding OpenID Connect and its benefits
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-white/80">
                  OpenID Connect (OIDC) is an authentication protocol built on top of OAuth 2.0 that allows clients to verify the identity of end-users. It extends OAuth 2.0 by providing a standardized way to obtain user profile information.
                </p>
                <p className="text-white/80">
                  When integrated with Firenest, OIDC provides a secure and standardized way for your users to authenticate with your tool while maintaining a seamless experience.
                </p>
                <div className="bg-dark-800 p-4 rounded-md border border-white/10 mt-4">
                  <h3 className="text-white font-medium mb-2">Key Benefits of OIDC</h3>
                  <ul className="space-y-2 text-white/70">
                    <li className="flex items-start">
                      <span className="text-fiery mr-2">•</span>
                      <span>Single Sign-On (SSO) capabilities across multiple applications</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-fiery mr-2">•</span>
                      <span>Standardized token format (JWT) with signature verification</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-fiery mr-2">•</span>
                      <span>Access to user profile information through standardized claims</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-fiery mr-2">•</span>
                      <span>Enhanced security with features like PKCE and nonce validation</span>
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Implementation Guide */}
            <Card className="firenest-card" id="implementation">
              <CardHeader>
                <CardTitle className="text-2xl text-white">Implementation Guide</CardTitle>
                <CardDescription>
                  Step-by-step guide to implementing OIDC with Firenest
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-xl text-white font-medium">Step 1: Configure OIDC in Partner Portal</h3>
                  <p className="text-white/80">
                    Navigate to the Authentication tab in your tool settings and select OpenID Connect as your authentication method. Configure your OIDC issuer URL, client ID, and client secret.
                  </p>
                  <div className="bg-dark-800 p-4 rounded-md border border-white/10">
                    <p className="text-white/70">
                      <span className="text-fiery font-medium">Note:</span> Firenest will automatically discover the necessary endpoints based on the issuer URL. Make sure your OIDC provider supports discovery.
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-xl text-white font-medium">Step 2: Configure Your OIDC Provider</h3>
                  <p className="text-white/80">
                    In your OIDC provider settings, add the following redirect URI:
                  </p>
                  <div className="bg-dark-800 p-3 rounded-md border border-white/10 font-mono text-sm text-white/90">
                    {window.location.origin}/auth/callback/partner
                  </div>
                  <p className="text-white/80">
                    Ensure your OIDC provider is configured to include the following scopes: <code className="bg-dark-800 px-2 py-1 rounded text-white/90">openid profile email</code>
                  </p>
                </div>

                <div className="space-y-4">
                  <h3 className="text-xl text-white font-medium">Step 3: Implement OIDC in Your Application</h3>
                  <p className="text-white/80">
                    Use the Firenest SDK to implement OIDC authentication in your application. The SDK handles the OIDC flow, token validation, and user session management.
                  </p>
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={() => {
                      const element = document.getElementById('code-examples');
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    <Code className="w-4 h-4 mr-2" />
                    View Code Examples
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Configuration Options */}
            <Card className="firenest-card" id="configuration">
              <CardHeader>
                <CardTitle className="text-2xl text-white">Configuration Options</CardTitle>
                <CardDescription>
                  Advanced configuration options for OIDC
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-dark-800 p-4 rounded-md border border-white/10">
                    <h3 className="text-white font-medium mb-2">PKCE Support</h3>
                    <p className="text-white/70 mb-3">
                      Proof Key for Code Exchange (PKCE) enhances security for public clients by preventing authorization code interception attacks.
                    </p>
                    <div className="flex items-center text-white/60 text-sm">
                      <Key className="w-4 h-4 mr-2 text-fiery" />
                      <span>Enable in Authentication settings</span>
                    </div>
                  </div>

                  <div className="bg-dark-800 p-4 rounded-md border border-white/10">
                    <h3 className="text-white font-medium mb-2">Nonce Validation</h3>
                    <p className="text-white/70 mb-3">
                      Nonce validation prevents replay attacks by ensuring that each authentication response can only be used once.
                    </p>
                    <div className="flex items-center text-white/60 text-sm">
                      <Key className="w-4 h-4 mr-2 text-fiery" />
                      <span>Enable in Authentication settings</span>
                    </div>
                  </div>
                </div>

                <div className="bg-dark-800 p-4 rounded-md border border-white/10">
                  <h3 className="text-white font-medium mb-2">Custom Scopes</h3>
                  <p className="text-white/70 mb-3">
                    You can request additional scopes beyond the default <code className="bg-dark-900 px-2 py-0.5 rounded">openid profile email</code> scopes.
                  </p>
                  <div className="font-mono text-sm text-white/80 bg-dark-900 p-3 rounded">
                    <div>// Example scope configuration</div>
                    <div>scope: "openid profile email custom_scope"</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Endpoints */}
            <Card className="firenest-card" id="endpoints">
              <CardHeader>
                <CardTitle className="text-2xl text-white">OIDC Endpoints</CardTitle>
                <CardDescription>
                  Reference for OIDC endpoints used by Firenest
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b border-white/10">
                        <th className="text-left py-3 px-4 text-white font-medium">Endpoint</th>
                        <th className="text-left py-3 px-4 text-white font-medium">Description</th>
                        <th className="text-left py-3 px-4 text-white font-medium">URL</th>
                      </tr>
                    </thead>
                    <tbody className="text-white/70">
                      <tr className="border-b border-white/5">
                        <td className="py-3 px-4">Authorization</td>
                        <td className="py-3 px-4">Initiates the authentication flow</td>
                        <td className="py-3 px-4 font-mono text-sm">/api/v1/auth/authorize</td>
                      </tr>
                      <tr className="border-b border-white/5">
                        <td className="py-3 px-4">Token</td>
                        <td className="py-3 px-4">Exchanges code for tokens</td>
                        <td className="py-3 px-4 font-mono text-sm">/api/v1/auth/token</td>
                      </tr>
                      <tr className="border-b border-white/5">
                        <td className="py-3 px-4">UserInfo</td>
                        <td className="py-3 px-4">Returns user profile information</td>
                        <td className="py-3 px-4 font-mono text-sm">/api/v1/auth/userinfo</td>
                      </tr>
                      <tr>
                        <td className="py-3 px-4">Callback</td>
                        <td className="py-3 px-4">Redirect URI for OIDC flow</td>
                        <td className="py-3 px-4 font-mono text-sm">/auth/callback/partner</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Code Examples */}
            <Card className="firenest-card" id="code-examples">
              <CardHeader>
                <CardTitle className="text-2xl text-white">Code Examples</CardTitle>
                <CardDescription>
                  Implementation examples for different frameworks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-dark-800 p-4 rounded-md border border-white/10">
                  <h3 className="text-white font-medium mb-3">Node.js Example</h3>
                  <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                    <pre>{`// Initialize the Firenest SDK with OIDC configuration
const firenest = require('firenest-sdk');

firenest.init({
  clientId: 'YOUR_CLIENT_ID',
  clientSecret: 'YOUR_CLIENT_SECRET',
  redirectUri: 'https://yourdomain.com/auth/callback',
  authMethod: 'oidc',
  oidc: {
    issuer: 'https://your-oidc-provider.com',
    scope: 'openid profile email',
    pkceEnabled: true,
    nonceValidationEnabled: true
  }
});

// Handle the callback
app.get('/auth/callback', async (req, res) => {
  try {
    const userData = await firenest.handleCallback(req.url);
    
    // Store user session
    req.session.user = userData;
    
    // Redirect to dashboard
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Authentication error:', error);
    res.redirect('/error');
  }
});`}</pre>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={() => window.open('https://docs.firenest.com/partners/oidc', '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Full Documentation
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerOIDCDocumentation;
