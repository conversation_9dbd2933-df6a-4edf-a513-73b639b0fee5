import React from 'react';
import {
  <PERSON><PERSON>les,
  Zap,
  Star,
  TrendingUp,
  MessageSquare,
  Image,
  Headphones,
  Code,
  FileText,
  ArrowRight
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ToolRecommendationsProps {
  tools: any[];
  recentlyUsed: string[];
  onViewDetails: (toolId: string) => void;
  onLaunchTool: (toolId: string) => void;
}

const ToolRecommendations: React.FC<ToolRecommendationsProps> = ({
  tools,
  recentlyUsed,
  onViewDetails,
  onLaunchTool
}) => {
  // Get appropriate icon for tool category
  const getToolIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Get recommended tools based on various factors
  const getRecommendedTools = () => {
    // In a real app, this would use more sophisticated recommendation algorithms
    // For now, we'll use a simple approach based on ratings and recent usage

    // Start with all tools
    let recommendedTools = [...tools];

    // Boost score for recently used tools
    recommendedTools = recommendedTools.map(tool => ({
      ...tool,
      recommendationScore: tool.rating + (recentlyUsed.includes(tool.id) ? 1 : 0)
    }));

    // Sort by recommendation score
    recommendedTools.sort((a, b) => b.recommendationScore - a.recommendationScore);

    // Return top 6
    return recommendedTools.slice(0, 6);
  };

  // Get tools for specific use cases
  const getToolsForUseCase = (useCase: string) => {
    // In a real app, this would be based on tags, categories, or ML-based recommendations
    // For now, we'll use a simple mapping
    const useCaseMap: Record<string, string[]> = {
      'content-creation': ['chatgpt', 'claude', 'dalle'],
      'code-assistance': ['chatgpt', 'claude'],
      'image-generation': ['dalle', 'midjourney'],
      'data-analysis': ['chatgpt', 'claude']
    };

    const toolIds = useCaseMap[useCase] || [];
    return tools.filter(tool => toolIds.includes(tool.id));
  };

  const recommendedTools = getRecommendedTools();
  const contentCreationTools = getToolsForUseCase('content-creation');
  const codeAssistanceTools = getToolsForUseCase('code-assistance');

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold text-white mb-4">Recommended for You</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendedTools.map(tool => (
            <Card
              key={tool.id}
              className="firenest-card cursor-pointer group"
              onClick={() => onViewDetails(tool.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                    {getToolIcon(tool.category)}
                  </div>
                  <div>
                    <h3 className="font-medium text-white group-hover:text-fiery transition-colors">{tool.name}</h3>
                    <div className="flex items-center gap-1.5">
                      <Star className="h-3 w-3 text-yellow-400" />
                      <span className="text-xs text-white/70">{tool.rating.toFixed(1)}</span>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-white/70 line-clamp-2 mb-3">{tool.description}</p>
                <div className="flex items-center justify-between">
                  <Badge className="bg-dark-700 text-white/70 border-white/10">
                    {tool.pricing.costPerUnit} credits
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-fiery hover:text-fiery-400 hover:bg-fiery/5 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      onLaunchTool(tool.id);
                    }}
                  >
                    Launch
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold text-white mb-4">Popular Use Cases</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Content Creation Use Case */}
          <Card className="firenest-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white">Content Creation</CardTitle>
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/20">Popular</Badge>
              </div>
              <CardDescription>Generate blog posts, social media content, and marketing materials</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {contentCreationTools.slice(0, 3).map(tool => (
                  <div
                    key={tool.id}
                    className="flex items-center justify-between p-2 rounded-md firenest-card cursor-pointer"
                    onClick={() => onViewDetails(tool.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-md bg-fiery/20 flex items-center justify-center">
                        {getToolIcon(tool.category)}
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-white">{tool.name}</h4>
                        <p className="text-xs text-white/70">{tool.category}</p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white/70 hover:text-white hover:bg-white/5"
                      onClick={(e) => {
                        e.stopPropagation();
                        onLaunchTool(tool.id);
                      }}
                    >
                      <Zap className="h-3.5 w-3.5 mr-1" />
                      Launch
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full border-white/10 hover:bg-white/5">
                View All Content Creation Tools
              </Button>
            </CardFooter>
          </Card>

          {/* Code Assistance Use Case */}
          <Card className="firenest-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white">Code Assistance</CardTitle>
                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/20">Trending</Badge>
              </div>
              <CardDescription>Generate, debug, and optimize code across multiple languages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {codeAssistanceTools.slice(0, 3).map(tool => (
                  <div
                    key={tool.id}
                    className="flex items-center justify-between p-2 rounded-md firenest-card cursor-pointer"
                    onClick={() => onViewDetails(tool.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-md bg-fiery/20 flex items-center justify-center">
                        {getToolIcon(tool.category)}
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-white">{tool.name}</h4>
                        <p className="text-xs text-white/70">{tool.category}</p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white/70 hover:text-white hover:bg-white/5"
                      onClick={(e) => {
                        e.stopPropagation();
                        onLaunchTool(tool.id);
                      }}
                    >
                      <Zap className="h-3.5 w-3.5 mr-1" />
                      Launch
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full border-white/10 hover:bg-white/5">
                View All Code Assistance Tools
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold text-white mb-4">Trending Tools</h2>
        <Card className="firenest-card">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {tools.filter(tool => tool.popular).slice(0, 3).map(tool => (
                <div
                  key={tool.id}
                  className="flex flex-col items-center text-center p-4 rounded-md firenest-card cursor-pointer"
                  onClick={() => onViewDetails(tool.id)}
                >
                  <div className="h-12 w-12 rounded-full bg-fiery/20 flex items-center justify-center mb-3">
                    {getToolIcon(tool.category)}
                  </div>
                  <h3 className="font-medium text-white mb-1">{tool.name}</h3>
                  <p className="text-xs text-white/70 mb-3 line-clamp-2">{tool.description}</p>
                  <div className="flex items-center gap-2 text-xs text-white/50">
                    <TrendingUp className="h-3.5 w-3.5 text-green-400" />
                    <span>+{Math.floor(Math.random() * 50) + 20}% usage this week</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ToolRecommendations;
