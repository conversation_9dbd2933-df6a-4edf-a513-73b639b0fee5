import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { completeOAuthConnection } from '@/lib/connections';
import { Loading } from '@/components/ui/loading';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';

const AuthCallback = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isProcessing, setIsProcessing] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [toolId, setToolId] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      if (!user) {
        setError('You must be logged in to complete authentication');
        setIsProcessing(false);
        return;
      }

      try {
        // Parse the URL search params
        const searchParams = new URLSearchParams(location.search);
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        
        // Check for error in the URL
        if (error) {
          setError(`Authentication failed: ${error}`);
          setIsProcessing(false);
          return;
        }
        
        // Check for required params
        if (!code || !state) {
          setError('Missing required parameters');
          setIsProcessing(false);
          return;
        }
        
        // Get the tool ID from session storage
        const storedUserId = sessionStorage.getItem(`firenest-oauth-user-${state}`);
        if (!storedUserId || storedUserId !== user.id) {
          setError('Invalid authentication state');
          setIsProcessing(false);
          return;
        }
        
        // Get the service ID from the URL path
        const pathParts = location.pathname.split('/');
        const service = pathParts[pathParts.length - 1];
        
        // Map service to tool ID (this is a simplified approach)
        // In a real app, you might have a more sophisticated mapping
        let mappedToolId;
        switch (service) {
          case 'openai':
            // Could be ChatGPT or DALL-E
            mappedToolId = sessionStorage.getItem(`firenest-oauth-service-${state}`) || 'chatgpt';
            break;
          case 'anthropic':
            mappedToolId = 'claude';
            break;
          case 'github':
            mappedToolId = 'github-copilot';
            break;
          case 'notion':
            mappedToolId = 'notion-ai';
            break;
          default:
            mappedToolId = service;
        }
        
        setToolId(mappedToolId);
        
        // Complete the OAuth flow
        const success = await completeOAuthConnection(user.id, mappedToolId, code, state);
        
        // Clean up session storage
        sessionStorage.removeItem(`firenest-oauth-state-${mappedToolId}`);
        sessionStorage.removeItem(`firenest-oauth-user-${state}`);
        sessionStorage.removeItem(`firenest-oauth-service-${state}`);
        
        setIsSuccess(success);
        
        if (success) {
          notify.success('Authentication successful');
        } else {
          setError('Failed to complete authentication');
        }
      } catch (error) {
        console.error('Error processing callback:', error);
        setError('An unexpected error occurred');
      } finally {
        setIsProcessing(false);
      }
    };

    processCallback();
  }, [user, location]);

  const handleGoToConnections = () => {
    navigate('/dashboard/connections');
  };

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
      <div className="firenest-card max-w-md w-full p-6 rounded-lg border border-white/10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-2">Authentication {isProcessing ? 'in Progress' : isSuccess ? 'Successful' : 'Failed'}</h1>
          
          {isProcessing ? (
            <div className="py-8">
              <Loading size="lg" text="Processing authentication..." />
            </div>
          ) : isSuccess ? (
            <div className="py-8 space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <p className="text-white/70">
                You have successfully connected to {toolId}. You can now use this tool through Firenest.
              </p>
            </div>
          ) : (
            <div className="py-8 space-y-4">
              <AlertCircle className="h-16 w-16 text-red-500 mx-auto" />
              <p className="text-white/70">
                {error || 'Failed to complete authentication. Please try again.'}
              </p>
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center mt-4">
            <Button
              variant="outline"
              onClick={handleGoToDashboard}
              className="border-white/10 hover:bg-white/5"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            
            <Button
              onClick={handleGoToConnections}
              className="bg-fiery hover:bg-fiery-600"
            >
              Manage Connections
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthCallback;
