import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthLayout from '@/components/AuthLayout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Mail, CheckCircle } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { supabase } from '@/lib/supabase';
import { verifyCode, markEmailAsVerified } from '@/lib/verification';

const ManualVerification = () => {
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !verificationCode) {
      notify.error('Please enter both email and verification code', {
        title: 'Missing Information',
        duration: 4000
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // First, check if the verification code is valid format
      if (verificationCode.length !== 6 || !/^\d+$/.test(verificationCode)) {
        notify.error('Invalid verification code. Please enter a 6-digit code.', {
          title: 'Invalid Code',
          duration: 4000
        });
        setIsSubmitting(false);
        return;
      }

      // First, try to sign in with OTP to check if the email exists
      const { error: otpError } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false
        }
      });

      if (otpError && !otpError.message.includes('For security purposes')) {
        // If we get an error that's not about rate limiting, the email might not exist
        toast.error('Email not found. Please check your email address or sign up first.');
        setIsSubmitting(false);
        return;
      }

      // Verify the code against stored codes
      const isCodeValid = verifyCode(email, verificationCode);

      if (!isCodeValid) {
        // Try to verify with a hardcoded backup code for demo purposes
        // In production, you would remove this and rely only on the stored codes
        if (verificationCode === '123456') {
          console.log('Using backup verification code');
        } else {
          notify.error('Invalid verification code. Please check the code and try again.', {
            title: 'Invalid Code',
            duration: 4000
          });
          setIsSubmitting(false);
          return;
        }
      }

      // Mark the email as verified
      const { success, error } = await markEmailAsVerified(email);

      if (!success) {
        console.error('Error marking email as verified:', error);
        notify.error('Unable to verify email. Please try the link in your email or contact support.', {
          title: 'Verification Failed',
          duration: 5000
        });
        setIsSubmitting(false);
        return;
      }

      // Verification successful
      setIsVerified(true);
      notify.success('Email verified successfully!', {
        title: 'Verification Complete',
        duration: 4000
      });

      // Wait a moment before redirecting
      setTimeout(() => {
        navigate('/login', {
          state: { message: 'Email verified successfully. You can now log in.' }
        });
      }, 2000);

    } catch (err) {
      console.error('Error during manual verification:', err);
      notify.error('An unexpected error occurred. Please try again.', {
        title: 'Error',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AuthLayout
      title="Manual Email Verification"
      subtitle="Verify your email with a verification code"
    >
      {isVerified ? (
        <div className="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-6 rounded-md text-center">
          <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-400" />
          <h3 className="text-lg font-medium mb-2">Email Verified!</h3>
          <p>Your email has been successfully verified.</p>
          <p className="mt-2">Redirecting to login page...</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="bg-blue-500/20 border border-blue-500/50 text-blue-200 px-4 py-4 rounded-md">
            <Mail className="h-6 w-6 mb-2 text-blue-400" />
            <h3 className="text-md font-medium mb-2">Didn't receive the verification email?</h3>
            <p className="text-sm">Enter your email and the verification code you received to manually verify your account.</p>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-white/80 mb-2">
              Email Address
            </label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
              className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
            />
          </div>

          <div>
            <label htmlFor="code" className="block text-sm font-medium text-white/80 mb-2">
              Verification Code
            </label>
            <Input
              id="code"
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="6-digit code"
              required
              maxLength={6}
              className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
            />
            <p className="text-xs text-white/50 mt-1">Enter the 6-digit verification code from your email</p>
          </div>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full pop-button mt-2"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              'Verify Email'
            )}
          </Button>

          <div className="text-center mt-4">
            <Button
              type="button"
              onClick={() => navigate('/verify-email')}
              variant="ghost"
              className="text-white/70 hover:text-white"
            >
              Back to Email Verification
            </Button>
          </div>
        </form>
      )}
    </AuthLayout>
  );
};

export default ManualVerification;
