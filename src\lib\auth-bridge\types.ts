/**
 * Authentication Bridge System Types
 * 
 * This file defines the core types for the authentication bridge system,
 * which enables seamless authentication with third-party services.
 */

// Authentication methods supported by the bridge
export type AuthMethod = 'oauth' | 'api_key' | 'credentials' | 'ip_based';

// Status of a service integration
export type IntegrationStatus = 'live' | 'beta' | 'coming_soon';

// Service configuration for authentication
export interface ServiceConfig {
  id: string;
  name: string;
  authMethod: AuthMethod;
  integrationStatus: IntegrationStatus;
  
  // OAuth specific configuration
  oauth?: {
    clientId: string;
    authorizationUrl: string;
    tokenUrl: string;
    redirectUrl: string;
    scope: string[];
    responseType: 'code' | 'token';
  };
  
  // API Key specific configuration
  apiKey?: {
    headerName: string;
    queryParamName?: string;
    isBearer?: boolean;
  };
  
  // Credentials specific configuration
  credentials?: {
    loginUrl: string;
    usernameField: string;
    passwordField: string;
    cookiesToCapture: string[];
  };
  
  // IP based specific configuration
  ipBased?: {
    allowedIps: string[];
    ipHeaderName?: string;
  };
  
  // Service endpoints
  endpoints: {
    baseUrl: string;
    login?: string;
    logout?: string;
    userInfo?: string;
    usage?: string;
  };
  
  // Usage tracking configuration
  usageTracking: {
    metric: 'time' | 'api_calls' | 'resources' | 'custom';
    unitName: string;
    costPerUnit: number;
    minimumUsage?: number;
  };
}

// Session state for a service
export interface ServiceSession {
  serviceId: string;
  userId: string;
  startTime: Date;
  lastActiveTime: Date;
  status: 'active' | 'paused' | 'completed';
  
  // Authentication tokens and data
  authData: {
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
    apiKey?: string;
    cookies?: Record<string, string>;
    ipAddress?: string;
    sessionId?: string;
  };
  
  // Usage metrics
  usageMetrics: {
    timeSpentSeconds: number;
    apiCallsMade: number;
    resourcesConsumed: number;
    customMetric?: number;
    estimatedCreditsUsed: number;
  };
}

// Authentication result
export interface AuthResult {
  success: boolean;
  session?: ServiceSession;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  redirectUrl?: string;
}

// Usage update event
export interface UsageUpdateEvent {
  serviceId: string;
  userId: string;
  sessionId: string;
  timestamp: Date;
  metrics: {
    timeSpentSeconds?: number;
    apiCallsMade?: number;
    resourcesConsumed?: number;
    customMetric?: number;
  };
}
