-- Create the get_auth_user_by_id function with correct return types
-- The error indicates that the function is expecting character varying(255) for the email column
-- instead of text, so we'll cast the values accordingly

CREATE OR REPLACE FUNCTION get_auth_user_by_id(user_id_param UUID)
RETURNS TABLE (
  id UUID,
  email character varying(255),  -- Changed from TEXT to match expected type
  raw_user_meta_data JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email::character varying(255),  -- Cast to match expected type
    au.raw_user_meta_data
  FROM 
    auth.users au
  WHERE 
    au.id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users and service_role
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO authenticated;
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO service_role;
