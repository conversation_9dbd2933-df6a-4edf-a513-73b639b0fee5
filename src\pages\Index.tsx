import { Flame, User, Zap, IndianRupee, BarChart3 } from "lucide-react";
import LogoHeader from "@/components/LogoHeader";
import NewsletterSignup from "@/components/NewsletterSignup";
import FeatureList from "@/components/FeatureList";
import DashboardPreview from "@/components/DashboardPreview";
import Testimonial from "@/components/Testimonial";
import Footer from "@/components/Footer";
import CounterStat from "@/components/CounterStat";
import { useEffect, useState, useRef } from "react";
import DetailPages from "@/components/DetailPages";
import ContactForm from "@/components/ContactForm";
import SolutionTabs from "@/components/SolutionTabs";
import InlineVideoPlayer from "@/components/InlineVideoPlayer";
import WaitlistPopup from "@/components/WaitlistPopup";
import ContactPopup from "@/components/ContactPopup";

const Index = () => {
  const [scrollY, setScrollY] = useState(0);
  const heroRef = useRef<HTMLDivElement>(null);
  
  // State for modals and detail pages
  const [isVideoExpanded, setIsVideoExpanded] = useState(false);
  const [activeDetailPage, setActiveDetailPage] = useState<string | null>(null);
  const [waitlistSubmitted, setWaitlistSubmitted] = useState(false);
  const [isWaitlistPopupOpen, setIsWaitlistPopupOpen] = useState(false);
  const [isContactPopupOpen, setIsContactPopupOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Handle mouse movement for parallax
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (heroRef.current) {
      const { left, top, width, height } = heroRef.current.getBoundingClientRect();
      const x = (e.clientX - left) / width - 0.5;
      const y = (e.clientY - top) / height - 0.5;
      setMousePosition({ x, y });
    }
  };
  
  // Handle opening detail pages
  const openDetailPage = (id: string) => (e: React.MouseEvent) => {
    e.preventDefault();
    setActiveDetailPage(id);
    document.body.style.overflow = 'hidden';
  };
  
  // Handle closing detail pages
  const closeDetailPage = () => {
    setActiveDetailPage(null);
    document.body.style.overflow = 'auto';
  };
  
  // Handle toggling video expansion
  const toggleVideoExpand = () => {
    setIsVideoExpanded(!isVideoExpanded);
    document.body.style.overflow = isVideoExpanded ? 'auto' : 'hidden';
  };
  
  // Handle opening waitlist popup
  const openWaitlistPopup = () => {
    setIsWaitlistPopupOpen(true);
    document.body.style.overflow = 'hidden';
  };
  
  // Handle closing waitlist popup
  const closeWaitlistPopup = () => {
    setIsWaitlistPopupOpen(false);
    document.body.style.overflow = 'auto';
  };
  
  // Handle waitlist submission
  const handleWaitlistSubmit = () => {
    setWaitlistSubmitted(true);
    setTimeout(() => setWaitlistSubmitted(false), 3000);
  };
  
  // Handle opening contact popup
  const openContactPopup = () => {
    setIsContactPopupOpen(true);
    document.body.style.overflow = 'hidden';
  };
  
  // Handle closing contact popup
  const closeContactPopup = () => {
    setIsContactPopupOpen(false);
    document.body.style.overflow = 'auto';
  };

  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />
      
      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>
      
      <LogoHeader onOpenWaitlist={openWaitlistPopup} />
      
      {/* Hero Section */}
      <section 
        ref={heroRef} 
        onMouseMove={handleMouseMove}
        className="relative overflow-hidden pt-24 lg:pt-32"
      >
        {/* Parallax elements */}
        <div 
          className="absolute top-1/2 -translate-y-1/2 -right-28 w-96 h-96 bg-fiery/20 rounded-full blur-[120px] opacity-60 parallax-layer"
          style={{ 
            transform: `translate3d(${mousePosition.x * -30}px, ${mousePosition.y * -30}px, 0)`,
          }}
        ></div>
        <div 
          className="absolute bottom-0 -left-20 w-80 h-80 bg-cool/20 rounded-full blur-[100px] opacity-50 parallax-layer"
          style={{ 
            transform: `translate3d(${mousePosition.x * 30}px, ${mousePosition.y * 30}px, 0)`,
          }}
        ></div>
        
        <div id="about" className="py-12 lg:py-20 px-6 md:px-12">
          <div className="max-w-7xl mx-auto relative z-10">
            <div className="flex flex-col lg:flex-row gap-12 lg:gap-6 items-center">
              <div className="flex flex-col flex-1">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight animate-fade-in">
                  No more <span className="whitespace-nowrap">Subscriptions with</span> <span className="gradient-firenest">
                    <span className="text-fiery animate-pulse-glow">Fir</span>
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool animate-pulse-glow">en</span>
                    <span className="text-cool animate-pulse-glow">est</span>
                  </span>
                </h1>
                
                <p className="text-xl text-white/80 mt-6 animate-fade-in-slow">
                  Revolutionizing the way you access AI tools with a unified prepaid credit center
                </p>
                
                <div className="pt-8 animate-fade-in-slow">
                  <NewsletterSignup primary={true} glowingTrail={false} />
                </div>
                
                <div className="flex items-center mt-4 text-white/70 text-sm animate-fade-in-slow">
                  <div className="flex -space-x-2 mr-2">
                    {[...Array(3)].map((_, i) => (
                      <div 
                        key={i} 
                        className="w-6 h-6 rounded-full bg-gradient-to-br from-fiery/80 to-fiery-600/80 border border-dark flex items-center justify-center"
                      >
                        <User className="text-white h-3 w-3" />
                      </div>
                    ))}
                  </div>
                  <span><span className="font-bold text-white">500+</span> startups already on the waitlist</span>
                </div>
              </div>
              
              <div 
                className="w-full lg:w-1/2 lg:pl-6 animate-fade-in-slow parallax-layer"
                style={{ 
                  transform: `translate3d(${mousePosition.x * -15}px, ${mousePosition.y * -15}px, 0)`,
                }}
              >
                <DashboardPreview />
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Subtle Stats Footer */}
      <div className="border-t border-b border-white/10 py-4 bg-white/[0.02]">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex flex-wrap justify-center md:justify-start gap-4">
              <CounterStat number={500} label="Startups" compact={true} />
              <CounterStat number={50} label="AI Tools" compact={true} />
              <CounterStat number={5000} label="Credits" compact={true} />
            </div>
            <div className="flex items-center gap-2 text-white/60 text-sm">
              <Flame className="h-4 w-4 text-fiery animate-pulse-slow" />
              <span>Backed by Startup India</span>
            </div>
          </div>
        </div>
      </div>
      
      <main className="flex-grow">
        {/* Features Section - Enhanced with Solutions */}
        <section id="features" className="py-20 px-6 md:px-12 relative overflow-hidden">
          {/* Background elements */}
          <div className="geometric-background">
            <div className="geometric-shape geometric-shape-1"></div>
            <div className="geometric-shape geometric-shape-3"></div>
          </div>
          
          <div className="max-w-7xl mx-auto relative">
            <div className="text-center mb-16">
              <span className="interactive-badge inline-block mb-4 animate-pulse-slow">Solutions</span>
              <h2 className="text-4xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-fiery via-white to-cool/90">Tailored for <span className="gradient-firenest">
                <span className="text-fiery">Fir</span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
                <span className="text-cool">est</span>
              </span></h2>
              <p className="text-white/70 max-w-2xl mx-auto text-lg leading-relaxed">Our innovative solutions are crafted specifically for Indian startups, delivering powerful AI tools with flexible pricing that scales with your business growth.</p>
            </div>
            
            {/* Feature Tabs */}
            <div className="mt-16">
              <SolutionTabs onOpenDetailPage={openDetailPage} />
            </div>
            
            {/* Demo Section */}
            <div className="mt-12 bg-gradient-to-br from-white/5 to-white/[0.02] border border-white/10 rounded-xl p-6 md:p-8 shadow-xl overflow-hidden relative">
              <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/20 rounded-full blur-3xl opacity-20"></div>
              
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="w-full md:w-1/2 lg:w-2/5">
                  <InlineVideoPlayer expanded={isVideoExpanded} onToggleExpand={toggleVideoExpand} />
                </div>
                
                <div className="w-full md:w-1/2 lg:w-3/5 text-left relative z-10">
                  <h3 className="text-2xl font-bold mb-4">See Firenest in Action</h3>
                  <p className="text-white/70 mb-6">Experience how our platform can transform your startup's AI capabilities while reducing costs. Our intuitive interface and powerful tools make it easy to get started with AI, even if you're not a technical expert.</p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-start gap-2">
                      <span className="text-fiery mt-1">✓</span>
                      <span>Intuitive dashboard for managing AI credits</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-fiery mt-1">✓</span>
                      <span>Easy integration with your existing workflows</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-fiery mt-1">✓</span>
                      <span>Affordable pay-as-you-go pricing model</span>
                    </li>
                  </ul>
                  <button 
                    className="fire-button"
                    onClick={toggleVideoExpand}
                  >
                    Watch Full Demo
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-16 px-6 md:px-12 darker-bg">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <span className="interactive-badge inline-block mb-4">Pricing</span>
              <h2 className="text-3xl font-bold mb-4">Simple, Credit-Based Pricing</h2>
              <p className="text-white/70 max-w-2xl mx-auto">Pay only for what you use. No subscriptions, no hidden fees.</p>
            </div>
            
            <div className="glass-card max-w-md mx-auto">
              <div className="text-center p-8">
                <h3 className="text-2xl font-bold mb-4">Early Access Special</h3>
                <div className="text-4xl font-bold mb-2">₹0</div>
                <p className="text-white/70 mb-6">Limited time offer</p>
                <div className="space-y-4 mb-8">
                  <div className="flex items-center gap-2">
                    <Flame className="h-5 w-5 text-fiery" />
                    <span>500 AI credits</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Flame className="h-5 w-5 text-fiery" />
                    <span>Priority access to new models</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Flame className="h-5 w-5 text-fiery" />
                    <span>Early adopter benefits</span>
                  </div>
                </div>
                <button 
                  className="pop-button w-full py-3 px-6"
                  onClick={openWaitlistPopup}
                >
                  Join Waitlist
                </button>
              </div>
            </div>
          </div>
        </section>
        
        {/* Testimonial Section */}
        <section className="py-20 px-6 md:px-12 relative">
          {/* Background effect */}
          <div className="absolute inset-0 bg-gradient-to-b from-fiery/5 to-cool-500/5 opacity-30 pointer-events-none"></div>
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-fiery/20 to-transparent"></div>
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cool-500/20 to-transparent"></div>
          
          <div className="max-w-7xl mx-auto relative">
            <div className="text-center mb-12">
              <span className="interactive-badge inline-block mb-4">Testimonials</span>
              <h2 className="text-3xl font-bold mb-4">What Founders Are Saying</h2>
              <p className="text-white/70 max-w-2xl mx-auto">
                Don't just take our word for it. Hear from the startup founders who have experienced the Firenest advantage firsthand.
              </p>
            </div>
            
            <div className="my-16">
            <Testimonial />
            </div>
            
            <div className="text-center mt-16">
              <p className="text-xl text-white/80 mb-8">Beta launching Q2 2025. Secure your spot now.</p>
              <div className="max-w-md mx-auto">
                <NewsletterSignup orangeButton={true} />
              </div>
            </div>
          </div>
        </section>
        
        {/* Contact Section */}
        <section id="contact" className="py-16 px-6 md:px-12 darker-bg">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <span className="interactive-badge inline-block mb-4">Contact</span>
              <h2 className="text-3xl font-bold mb-4">Get in Touch</h2>
              <p className="text-white/70 max-w-2xl mx-auto">Have questions or want to learn more? Reach out to our team.</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-16">
              {[
                {
                  title: "Email Us",
                  content: <>hello@<span className="gradient-firenest">
                    <span className="text-fiery">fir</span>
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
                    <span className="text-cool">est</span>
                  </span>.io</>,
                  icon: <Flame className="h-8 w-8 text-fiery" />
                },
                {
                  title: "Call Us",
                  content: "+91 9876543210",
                  icon: <Flame className="h-8 w-8 text-fiery" />
                },
                {
                  title: "Visit Us",
                  content: "Bangalore, India",
                  icon: <Flame className="h-8 w-8 text-fiery" />
                }
              ].map((contact, index) => (
                <div key={index} className="glass-card-interactive text-center">
                  <div className="bg-fiery/20 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 animate-pulse-glow">
                    {contact.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{contact.title}</h3>
                  <p className="text-white/70">{contact.content}</p>
                </div>
              ))}
            </div>
            
            <ContactForm />
          </div>
        </section>
        
        {/* Signup Section */}
        <section className="py-16 px-6 md:px-12" id="signup">
          <div className="max-w-7xl mx-auto text-center">
            <div className="inline-flex items-center gap-3 bg-white/10 rounded-full px-6 py-3 mb-8">
              <div className="w-3 h-3 rounded-full bg-fiery animate-pulse-glow"></div>
              <p className="font-medium">Join the waitlist today</p>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Don't wait—secure your early access now!</h2>
            
            <div className="max-w-md mx-auto mb-8">
              <NewsletterSignup />
            </div>
            
            <div className="flex justify-center items-center gap-3 text-white/60 font-medium">
              <Flame className="h-5 w-5 text-fiery animate-pulse-slow" />
              <span>Join 500+ founders already on the waitlist</span>
            </div>
          </div>
        </section>
      </main>
      
      <Footer onOpenWaitlist={openWaitlistPopup} onOpenContact={openContactPopup} />
      
      {/* Modals */}
      <DetailPages id={activeDetailPage || ''} isOpen={activeDetailPage !== null} onClose={closeDetailPage} />
      <WaitlistPopup isOpen={isWaitlistPopupOpen} onClose={closeWaitlistPopup} />
      <ContactPopup isOpen={isContactPopupOpen} onClose={closeContactPopup} />
    </div>
  );
};

export default Index;
