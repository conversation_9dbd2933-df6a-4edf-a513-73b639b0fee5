import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Mail, Plus, RefreshCw, UserPlus, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

/**
 * Team Invitations page with professional design
 */
const Invites = () => {
  // Mock data for pending invitations
  const pendingInvites = [
    {
      id: 1,
      email: '<EMAIL>',
      role: 'Member',
      sentAt: '2023-04-08T14:30:00Z',
      expiresIn: '2 days'
    },
    {
      id: 2,
      email: '<EMAIL>',
      role: 'Admin',
      sentAt: '2023-04-09T10:15:00Z',
      expiresIn: '3 days'
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Team Invitations</h1>
          <p className="text-white/70">Invite new members to your team</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-lg text-white">Pending Invitations</CardTitle>
              <CardDescription>
                {pendingInvites.length} active invitations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pendingInvites.length > 0 ? (
                <div className="space-y-4">
                  {pendingInvites.map((invite) => (
                    <div
                      key={invite.id}
                      className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-3 rounded-md bg-white/5"
                    >
                      <div className="flex items-center gap-3 mb-2 sm:mb-0">
                        <div className="h-10 w-10 rounded-full bg-fiery/20 flex items-center justify-center">
                          <Mail className="h-5 w-5 text-fiery" />
                        </div>
                        <div>
                          <div className="font-medium text-white">{invite.email}</div>
                          <div className="text-sm text-white/70 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            Expires in {invite.expiresIn}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 w-full sm:w-auto">
                        <Badge variant="outline" className="bg-white/5 text-white/70 border-white/10">
                          {invite.role}
                        </Badge>
                        <div className="flex gap-2 ml-auto sm:ml-0">
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70 hover:text-white hover:bg-white/10">
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-white/70 hover:text-red-500 hover:bg-white/10">
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-white/5 mb-4">
                    <Mail className="h-6 w-6 text-white/50" />
                  </div>
                  <h3 className="text-sm font-medium text-white mb-1">No pending invitations</h3>
                  <p className="text-sm text-white/70">Invite team members to get started</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-lg text-white">Send Invitation</CardTitle>
              <CardDescription>
                Invite a new team member
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-white/70">Email address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="firenest-card text-white"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role" className="text-white/70">Role</Label>
                  <Select>
                    <SelectTrigger id="role" className="firenest-card text-white">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent className="firenest-card border text-white">
                      <SelectItem value="member">Member</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="pt-2">
                  <Button className="w-full bg-fiery hover:bg-fiery/90 text-white">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Send Invitation
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          <Card className="firenest-card mt-4">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-white">Bulk Invite</CardTitle>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full border-white/10 text-white hover:bg-white/5">
                <Plus className="h-4 w-4 mr-2" />
                Import CSV
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Invites;
