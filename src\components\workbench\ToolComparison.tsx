import React from 'react';
import {
  X,
  Zap,
  Star,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  MessageSquare,
  Image,
  Headphones,
  Code,
  FileText,
  Clock,
  BarChart3,
  Check,
  Minus
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';

interface ToolComparisonProps {
  tools: any[];
  onClose: () => void;
  onLaunch: (toolId: string) => void;
}

const ToolComparison: React.FC<ToolComparisonProps> = ({
  tools,
  onClose,
  onLaunch
}) => {
  // Get appropriate icon for tool category
  const getToolIcon = (category: string) => {
    switch(category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Get feature comparison data
  const getFeatureComparisonData = () => {
    return [
      {
        feature: 'Response Time',
        values: tools.map(tool => {
          const responseTime = tool.metrics?.responseTime || Math.random() * 3 + 0.5;
          return {
            value: `${responseTime.toFixed(1)}s`,
            rating: responseTime < 1 ? 'excellent' : responseTime < 2 ? 'good' : 'average'
          };
        })
      },
      {
        feature: 'Success Rate',
        values: tools.map(tool => {
          const successRate = tool.metrics?.successRate || Math.random() * 10 + 90;
          return {
            value: `${successRate.toFixed(1)}%`,
            rating: successRate > 98 ? 'excellent' : successRate > 95 ? 'good' : 'average'
          };
        })
      },
      {
        feature: 'Cost Efficiency',
        values: tools.map(tool => {
          const costEfficiency = tool.metrics?.costEfficiency || Math.random() * 5 + 1;
          const costPerToken = tool.pricing.costPerUnit / costEfficiency;
          return {
            value: `${costPerToken.toFixed(2)} credits/token`,
            rating: costPerToken < 0.5 ? 'excellent' : costPerToken < 1 ? 'good' : 'average'
          };
        })
      },
      {
        feature: 'Max Input Length',
        values: tools.map(tool => {
          const maxInputLength = tool.limits?.maxInputLength || Math.floor(Math.random() * 8000 + 2000);
          return {
            value: `${maxInputLength.toLocaleString()} tokens`,
            rating: maxInputLength > 8000 ? 'excellent' : maxInputLength > 4000 ? 'good' : 'average'
          };
        })
      },
      {
        feature: 'Max Output Length',
        values: tools.map(tool => {
          const maxOutputLength = tool.limits?.maxOutputLength || Math.floor(Math.random() * 4000 + 1000);
          return {
            value: `${maxOutputLength.toLocaleString()} tokens`,
            rating: maxOutputLength > 4000 ? 'excellent' : maxOutputLength > 2000 ? 'good' : 'average'
          };
        })
      }
    ];
  };

  // Get capability comparison data
  const getCapabilityComparisonData = () => {
    return [
      {
        capability: 'Text Generation',
        values: tools.map(tool => tool.capabilities?.textGeneration || tool.category === 'Text Generation')
      },
      {
        capability: 'Image Generation',
        values: tools.map(tool => tool.capabilities?.imageGeneration || tool.category === 'Image Generation')
      },
      {
        capability: 'Code Generation',
        values: tools.map(tool => tool.capabilities?.codeGeneration || tool.category === 'Code Generation')
      },
      {
        capability: 'Audio Processing',
        values: tools.map(tool => tool.capabilities?.audioProcessing || tool.category === 'Audio Processing')
      },
      {
        capability: 'Document Analysis',
        values: tools.map(tool => tool.capabilities?.documentAnalysis || false)
      },
      {
        capability: 'Multi-modal Input',
        values: tools.map(tool => tool.capabilities?.multimodalInput || Math.random() > 0.5)
      },
      {
        capability: 'API Integration',
        values: tools.map(tool => tool.capabilities?.apiIntegration || Math.random() > 0.7)
      }
    ];
  };

  // Get rating color
  const getRatingColor = (rating: string) => {
    switch(rating) {
      case 'excellent':
        return 'text-green-400';
      case 'good':
        return 'text-blue-400';
      case 'average':
        return 'text-amber-400';
      default:
        return 'text-white/70';
    }
  };

  const featureComparisonData = getFeatureComparisonData();
  const capabilityComparisonData = getCapabilityComparisonData();

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <div className="firenest-card w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="icon"
              className="h-9 w-9 border-white/10 hover:bg-white/5"
              onClick={onClose}
            >
              <ArrowLeft className="h-4 w-4 text-white/70" />
            </Button>
            <h2 className="text-lg font-medium text-white">Tool Comparison</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-white/70 hover:text-white hover:bg-white/5"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Tool Headers */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {tools.map((tool, index) => (
              <Card key={index} className="firenest-card">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
                      {getToolIcon(tool.category)}
                    </div>
                    <div>
                      <CardTitle className="text-white">{tool.name}</CardTitle>
                      <p className="text-xs text-white/70">{tool.category}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-white/80">{tool.description}</p>
                  <div className="flex items-center justify-between text-xs text-white/70">
                    <div className="flex items-center">
                      <Zap className="h-3 w-3 mr-1 text-fiery" />
                      <span>{tool.pricing.costPerUnit} credits/use</span>
                    </div>
                    <div className="flex items-center">
                      <Star className="h-3 w-3 mr-1 text-yellow-400" />
                      <span>{tool.rating.toFixed(1)}</span>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    className="w-full bg-fiery hover:bg-fiery-600"
                    onClick={() => onLaunch(tool.id)}
                  >
                    <Zap className="h-3.5 w-3.5 mr-1" />
                    <span>Launch</span>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Performance Comparison */}
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    <TableHead className="text-white/70">Feature</TableHead>
                    {tools.map((tool, index) => (
                      <TableHead key={index} className="text-white/70 text-center">{tool.name}</TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {featureComparisonData.map((row, rowIndex) => (
                    <TableRow key={rowIndex} className="border-white/10">
                      <TableCell className="font-medium text-white">{row.feature}</TableCell>
                      {row.values.map((cell, cellIndex) => (
                        <TableCell key={cellIndex} className={`text-center ${getRatingColor(cell.rating)}`}>
                          {cell.value}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Capabilities Comparison */}
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Capabilities Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="border-white/10">
                    <TableHead className="text-white/70">Capability</TableHead>
                    {tools.map((tool, index) => (
                      <TableHead key={index} className="text-white/70 text-center">{tool.name}</TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {capabilityComparisonData.map((row, rowIndex) => (
                    <TableRow key={rowIndex} className="border-white/10">
                      <TableCell className="font-medium text-white">{row.capability}</TableCell>
                      {row.values.map((supported, cellIndex) => (
                        <TableCell key={cellIndex} className="text-center">
                          {supported ? (
                            <div className="flex justify-center">
                              <Check className="h-5 w-5 text-green-400" />
                            </div>
                          ) : (
                            <div className="flex justify-center">
                              <Minus className="h-5 w-5 text-white/30" />
                            </div>
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Use Case Comparison */}
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Best Use Cases</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {tools.map((tool, index) => (
                  <div key={index} className="firenest-card p-4">
                    <h3 className="font-medium text-white mb-2">{tool.name}</h3>
                    <ul className="space-y-2 text-sm text-white/70">
                      {Array(3).fill(0).map((_, i) => {
                        const useCases = [
                          'Content creation for marketing',
                          'Customer support automation',
                          'Data analysis and visualization',
                          'Code generation and debugging',
                          'Image creation for social media',
                          'Document summarization',
                          'Meeting transcription and notes',
                          'Email drafting and responses',
                          'Product descriptions',
                          'Creative writing assistance'
                        ];
                        return (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span>{useCases[(index + i) % useCases.length]}</span>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="border-t border-white/10 p-4 flex justify-end">
          <Button
            variant="outline"
            className="border-white/10 hover:bg-white/5"
            onClick={onClose}
          >
            Close Comparison
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ToolComparison;
