import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import GoogleStyleAuthorizePage from './GoogleStyleAuthorizePage';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loading } from '@/components/ui/loading';
import { Flame, Shield, AlertTriangle, CheckCircle, XCircle, Lock } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useState, useEffect } from 'react';

// Uncomment the line below to use the Google-style authorization page
// const AuthorizePage: React.FC = () => {
//   return <GoogleStyleAuthorizePage />;
// };

interface AuthParams {
  client_id: string;
  redirect_uri: string;
  response_type: string;
  scope: string;
  state: string;
}

interface PartnerInfo {
  id: string;
  name: string;
  company: string;
  logoUrl?: string;
  website?: string;
}

const AuthorizePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authParams, setAuthParams] = useState<AuthParams | null>(null);
  const [partnerInfo, setPartnerInfo] = useState<PartnerInfo | null>(null);
  const [scopes, setScopes] = useState<string[]>([]);

  useEffect(() => {
    const parseQueryParams = async () => {
      try {
        setIsLoading(true);

        // Parse query parameters
        const params = new URLSearchParams(location.search);

        // Check if this is a request from the API server
        const requestParam = params.get('request');
        if (requestParam) {
          try {
            // Parse the request JSON
            const request = JSON.parse(requestParam);

            // Extract parameters from the request
            const client_id = request.client_id;
            const redirect_uri = request.redirect_uri;
            const state = request.state;
            const scope = request.scope;
            const response_type = 'code'; // Always code for OAuth flow

            // Set auth params
            setAuthParams({
              client_id,
              redirect_uri,
              response_type,
              scope: scope || '',
              state
            });

            // Parse scopes
            if (scope) {
              setScopes(scope.split(' '));
            } else {
              setScopes([]);
            }

            // Fetch partner information using the partner_id from the request
            if (request.partner_id && request.partner_name) {
              setPartnerInfo({
                id: request.partner_id,
                name: request.partner_name,
                company: request.partner_name,
                logoUrl: undefined,
                website: undefined
              });
              setIsLoading(false);
              return;
            } else {
              // Fetch partner information from the database
              await fetchPartnerInfo(client_id);
            }
            return;
          } catch (error) {
            console.error('Error parsing request parameter:', error);
            setError('Invalid authorization request format.');
            return;
          }
        }

        // Regular flow - parse individual parameters
        const client_id = params.get('client_id');
        const redirect_uri = params.get('redirect_uri');
        const response_type = params.get('response_type');
        const scope = params.get('scope');
        const state = params.get('state');

        // Validate required parameters
        if (!client_id || !redirect_uri || !response_type || !state) {
          setError('Invalid authorization request. Missing required parameters.');
          return;
        }

        // Validate response type (only 'code' is supported for now)
        if (response_type !== 'code') {
          setError(`Unsupported response type: ${response_type}. Only 'code' is supported.`);
          return;
        }

        // Set auth params
        setAuthParams({
          client_id,
          redirect_uri,
          response_type,
          scope: scope || '',
          state
        });

        // Parse scopes
        if (scope) {
          setScopes(scope.split(' '));
        } else {
          setScopes([]);
        }

        // Fetch partner information
        await fetchPartnerInfo(client_id);
      } catch (error) {
        console.error('Error parsing query parameters:', error);
        setError('An error occurred while processing the authorization request.');
      } finally {
        setIsLoading(false);
      }
    };

    parseQueryParams();
  }, [location.search]);

  const fetchPartnerInfo = async (clientId: string) => {
    try {
      console.log('Fetching partner info for client ID:', clientId);

      if (!clientId) {
        setError('Invalid client ID');
        return;
      }

      // Query the database to find the partner tool with this client ID
      const { data: integrationConfigs, error: configError } = await supabase
        .from('integration_configs')
        .select('tool_id, config_data, updated_at')
        .eq('config_data->clientId', clientId);

      if (configError) {
        console.error('Error fetching integration config:', configError);
        throw configError;
      }

      console.log('Integration configs found:', integrationConfigs?.length || 0);

      // Handle case where no configs are found
      if (!integrationConfigs || integrationConfigs.length === 0) {
        // Try a direct SQL query as a fallback
        const { data: sqlResult, error: sqlError } = await supabase
          .rpc('find_integration_by_client_id', { client_id_param: clientId });

        if (sqlError || !sqlResult || sqlResult.length === 0) {
          console.error('Error with SQL fallback:', sqlError);
          setError(`Unknown client ID: ${clientId}`);
          return;
        }

        // Use the first result
        const toolId = sqlResult[0].tool_id;
        console.log('Found tool ID via SQL fallback:', toolId);

        // Get the tool details
        const { data: tool, error: toolError } = await supabase
          .from('partner_tools')
          .select('partner_id, name')
          .eq('id', toolId)
          .maybeSingle();

        if (toolError || !tool) {
          console.error('Error fetching tool details:', toolError);
          setError(`Tool not found for client ID: ${clientId}`);
          return;
        }

        // Get the partner details
        const { data: partner, error: partnerError } = await supabase
          .from('partner_accounts')
          .select('id, name, company, logo_url, website')
          .eq('id', tool.partner_id)
          .maybeSingle();

        if (partnerError || !partner) {
          console.error('Error fetching partner details:', partnerError);
          setError(`Partner not found for tool: ${tool.name}`);
          return;
        }

        setPartnerInfo({
          id: partner.id,
          name: tool.name,
          company: partner.company,
          logoUrl: partner.logo_url,
          website: partner.website
        });
        return;
      }

      // If we have multiple results, use the most recently updated one
      let integrationConfig;
      if (integrationConfigs.length > 1) {
        console.warn(`Multiple integration configs found for client ID: ${clientId}. Using the most recent one.`);

        // Sort by updated_at in descending order to get the most recent one
        integrationConfigs.sort((a, b) => {
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        });

        integrationConfig = integrationConfigs[0];
      } else {
        integrationConfig = integrationConfigs[0];
      }

      const toolId = integrationConfig.tool_id;
      console.log('Found tool ID:', toolId);

      // Get the tool details
      const { data: tool, error: toolError } = await supabase
        .from('partner_tools')
        .select('partner_id, name')
        .eq('id', toolId)
        .maybeSingle();

      if (toolError) {
        console.error('Error fetching tool details:', toolError);
        throw toolError;
      }

      if (!tool) {
        console.error('Tool not found for ID:', toolId);
        setError(`Tool not found for client ID: ${clientId}`);
        return;
      }

      console.log('Tool details:', tool);

      // Get the partner details
      const { data: partner, error: partnerError } = await supabase
        .from('partner_accounts')
        .select('id, name, company, logo_url, website')
        .eq('id', tool.partner_id)
        .maybeSingle();

      if (partnerError) {
        console.error('Error fetching partner details:', partnerError);
        throw partnerError;
      }

      if (!partner) {
        console.error('Partner not found for ID:', tool.partner_id);
        setError(`Partner not found for tool: ${tool.name}`);
        return;
      }

      console.log('Partner details:', partner);

      setPartnerInfo({
        id: partner.id,
        name: tool.name,
        company: partner.company,
        logoUrl: partner.logo_url,
        website: partner.website
      });
    } catch (error) {
      console.error('Error fetching partner information:', error);
      setError('Failed to fetch application information.');
    }
  };

  const handleAuthorize = async () => {
    if (!user || !authParams || !partnerInfo) return;

    setIsProcessing(true);

    try {
      console.log('Authorizing application with user:', user.id);
      console.log('Auth params:', authParams);
      console.log('Partner info:', partnerInfo);

      // Generate an authorization code
      const code = generateAuthCode();
      console.log('Generated authorization code:', code);

      // Store the authorization code and related information in the database
      const { error } = await supabase
        .from('auth_codes')
        .insert({
          code,
          user_id: user.id,
          client_id: authParams.client_id,
          partner_id: partnerInfo.id,
          redirect_uri: authParams.redirect_uri,
          scope: authParams.scope,
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes expiration
          used: false
        });

      if (error) {
        console.error('Error storing authorization code:', error);
        throw error;
      }

      console.log('Authorization code stored successfully');

      // Construct the redirect URL
      const redirectUrl = new URL(authParams.redirect_uri);
      redirectUrl.searchParams.append('code', code);
      redirectUrl.searchParams.append('state', authParams.state);

      console.log('Redirecting to:', redirectUrl.toString());

      // Redirect the user back to the client application
      window.location.href = redirectUrl.toString();
    } catch (error) {
      console.error('Error authorizing application:', error);
      setError('Failed to authorize application. Please try again.');
      setIsProcessing(false);
    }
  };

  const handleDeny = () => {
    if (!authParams) return;

    // Construct the redirect URL with an error
    const redirectUrl = new URL(authParams.redirect_uri);
    redirectUrl.searchParams.append('error', 'access_denied');
    redirectUrl.searchParams.append('state', authParams.state);

    // Redirect the user back to the client application
    window.location.href = redirectUrl.toString();
  };

  const generateAuthCode = (): string => {
    // Generate a random string for the authorization code
    const randomBytes = new Uint8Array(32);
    window.crypto.getRandomValues(randomBytes);
    const code = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');

    // Add a prefix to make it clear this is an auth code
    // Include a timestamp to help with debugging
    const timestamp = Date.now().toString(36);
    return `ac_${timestamp}_${code}`;
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950">
        <div className="text-center">
          <Loading size="lg" />
          <p className="mt-4 text-white/70">Loading authorization request...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <Card className="firenest-card max-w-md w-full border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-500" />
            </div>
            <CardTitle className="text-xl text-white">Authorization Error</CardTitle>
            <CardDescription className="text-white/70">
              {error}
            </CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button
              className="bg-fiery hover:bg-fiery/90 text-white"
              onClick={() => navigate('/dashboard')}
            >
              Return to Dashboard
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Render not logged in state
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <Card className="firenest-card max-w-md w-full border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Lock className="w-6 h-6 text-blue-500" />
            </div>
            <CardTitle className="text-xl text-white">Authentication Required</CardTitle>
            <CardDescription className="text-white/70">
              You need to be logged in to authorize this application.
            </CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button
              className="bg-fiery hover:bg-fiery/90 text-white"
              onClick={() => navigate(`/login?redirect=${encodeURIComponent(window.location.href)}`)}
            >
              Log In to Continue
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Render authorization page
  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
      <Card className="firenest-card max-w-lg w-full border-0 shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 rounded-full bg-gradient-to-br from-fiery to-fiery/50 flex items-center justify-center">
            <Flame className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl text-white">Authorize Application</CardTitle>
          <CardDescription className="text-white/70">
            {partnerInfo?.name} by {partnerInfo?.company} wants to access your Firenest account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center mr-4 flex-shrink-0">
                <Shield className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <h3 className="text-white font-medium mb-1">This application will be able to:</h3>
                <ul className="text-white/70 text-sm space-y-2">
                  {scopes.includes('openid') && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      <span>Verify your identity</span>
                    </li>
                  )}
                  {scopes.includes('profile') && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      <span>Access your profile information</span>
                    </li>
                  )}
                  {scopes.includes('email') && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      <span>Access your email address</span>
                    </li>
                  )}
                  {scopes.includes('credits') && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      <span>Use your Firenest credits</span>
                    </li>
                  )}
                  {scopes.length === 0 && (
                    <li className="flex items-center">
                      <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2" />
                      <span>No specific permissions requested</span>
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-yellow-500 font-medium mb-1">Important</h4>
                <p className="text-white/70 text-sm">
                  Only authorize applications that you trust. You can revoke access at any time from your account settings.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            className="border-white/10 hover:bg-white/5 text-white"
            onClick={handleDeny}
            disabled={isProcessing}
          >
            <XCircle className="w-4 h-4 mr-2" />
            Deny
          </Button>
          <Button
            className="bg-fiery hover:bg-fiery/90 text-white"
            onClick={handleAuthorize}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <Loading size="sm" className="mr-2" />
                Authorizing...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Authorize
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AuthorizePage;
