-- Create Early Access Requests Table
CREATE TABLE IF NOT EXISTS early_access_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  name TEXT,
  company TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index on email to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS early_access_email_idx ON early_access_requests (email);

-- Create Contact Messages Table
CREATE TABLE IF NOT EXISTS contact_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  company TEXT,
  phone TEXT,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Newsletter Subscribers Table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index on email to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS newsletter_email_idx ON newsletter_subscribers (email);

-- Enable RLS
ALTER TABLE early_access_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;

-- Create policies for inserting data (public can insert)
CREATE POLICY "Allow public inserts to early_access_requests" 
  ON early_access_requests FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow public inserts to contact_messages" 
  ON contact_messages FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow public inserts to newsletter_subscribers" 
  ON newsletter_subscribers FOR INSERT 
  WITH CHECK (true);

-- Create policies for reading data (only authenticated users)
CREATE POLICY "Allow authenticated reads to early_access_requests" 
  ON early_access_requests FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated reads to contact_messages" 
  ON contact_messages FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated reads to newsletter_subscribers" 
  ON newsletter_subscribers FOR SELECT 
  USING (auth.role() = 'authenticated');
