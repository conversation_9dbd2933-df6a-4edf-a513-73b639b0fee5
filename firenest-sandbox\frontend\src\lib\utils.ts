import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(amount)
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num)
}

export function formatPercentage(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  }).format(value)
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    created: 'bg-gray-500',
    uploading: 'bg-blue-500',
    validating: 'bg-yellow-500',
    ready: 'bg-green-500',
    simulating: 'bg-purple-500',
    complete: 'bg-emerald-500',
    error: 'bg-red-500',
    failed: 'bg-red-500',
    uploaded: 'bg-blue-500',
    validated: 'bg-green-500',
    invalid: 'bg-red-500',
    queued: 'bg-gray-500',
    running: 'bg-blue-500'
  }
  
  return statusColors[status.toLowerCase()] || 'bg-gray-500'
}

export function getStatusText(status: string): string {
  const statusTexts: Record<string, string> = {
    created: 'Created',
    uploading: 'Uploading',
    validating: 'Validating',
    ready: 'Ready',
    simulating: 'Simulating',
    complete: 'Complete',
    error: 'Error',
    failed: 'Failed',
    uploaded: 'Uploaded',
    validated: 'Validated',
    invalid: 'Invalid',
    queued: 'Queued',
    running: 'Running'
  }
  
  return statusTexts[status.toLowerCase()] || status
}
