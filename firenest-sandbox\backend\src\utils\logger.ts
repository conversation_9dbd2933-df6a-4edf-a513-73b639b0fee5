/**
 * Logger Utility
 * Structured logging with CloudWatch integration
 */

import winston from 'winston';
import { config } from '@/config/environment';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log colors
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(logColors);

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  levels: logLevels,
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss:ms'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf((info) => {
      const { timestamp, level, message, ...meta } = info;
      
      const logEntry = {
        timestamp,
        level,
        message,
        service: 'firenest-sandbox-api',
        environment: config.environment,
        ...meta
      };

      return JSON.stringify(logEntry);
    })
  ),
  defaultMeta: {
    service: 'firenest-sandbox-api',
    environment: config.environment
  },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize({ all: true }),
        winston.format.simple(),
        winston.format.printf((info) => {
          const { timestamp, level, message, ...meta } = info;
          const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
          return `${timestamp} [${level}]: ${message} ${metaStr}`;
        })
      )
    }),

    // File transport for persistent logging
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),

    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// Add CloudWatch transport for production
if (config.environment === 'production') {
  // Note: In a real implementation, you would add AWS CloudWatch transport here
  // For now, we'll use console and file transports
  logger.info('Production logging configured');
}

// Create a stream object for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  }
};

// Helper functions for structured logging
export const logWithContext = (level: string, message: string, context: any = {}) => {
  logger.log(level, message, context);
};

export const logError = (message: string, error: Error, context: any = {}) => {
  logger.error(message, {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    ...context
  });
};

export const logAuditEvent = (event: string, userId: string, details: any = {}) => {
  logger.info('Audit Event', {
    event,
    userId,
    timestamp: new Date().toISOString(),
    ...details
  });
};

export const logSecurityEvent = (event: string, details: any = {}) => {
  logger.warn('Security Event', {
    event,
    timestamp: new Date().toISOString(),
    ...details
  });
};

export const logPerformance = (operation: string, duration: number, details: any = {}) => {
  logger.info('Performance Metric', {
    operation,
    duration,
    timestamp: new Date().toISOString(),
    ...details
  });
};

// Performance timing helper
export class PerformanceTimer {
  private startTime: number;
  private operation: string;

  constructor(operation: string) {
    this.operation = operation;
    this.startTime = Date.now();
  }

  end(details: any = {}) {
    const duration = Date.now() - this.startTime;
    logPerformance(this.operation, duration, details);
    return duration;
  }
}

// Request logging helper
export const logRequest = (req: any, res: any, duration?: number) => {
  const logData = {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id,
    duration
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request Error', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// Database query logging
export const logDatabaseQuery = (query: string, params: any[], duration: number) => {
  logger.debug('Database Query', {
    query: query.substring(0, 200), // Truncate long queries
    paramCount: params?.length || 0,
    duration
  });
};

// File upload logging
export const logFileUpload = (filename: string, size: number, userId: string) => {
  logger.info('File Upload', {
    filename,
    size,
    userId,
    timestamp: new Date().toISOString()
  });
};

// Simulation logging
export const logSimulation = (simulationId: string, status: string, details: any = {}) => {
  logger.info('Simulation Event', {
    simulationId,
    status,
    timestamp: new Date().toISOString(),
    ...details
  });
};

export default logger;
