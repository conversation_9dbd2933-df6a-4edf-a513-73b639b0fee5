// This file provides mock authentication functions for testing purposes
// when the Supabase tables haven't been set up yet

import { notify } from '@/components/ui/notification-system';
// Generate a random UUID since we might not have the uuid package installed
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Define a type for the mock user
interface MockUser {
  id: string;
  email: string;
  name: string;
  avatarUrl: string | null;
}

// Define a type for the mock session
interface MockSession {
  access_token: string;
  expires_at: number;
  user: MockUser;
}

// Mock sign in function
export async function mockSignIn(email: string, password: string) {
  try {
    console.log('Using mock sign in with:', email);

    // Simple validation
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }

    // Create a mock user session
    const mockUser: MockUser = {
      id: generateUUID(),
      email: email,
      name: email.split('@')[0],
      avatarUrl: null,
    };

    // Create a mock session
    const mockSession: MockSession = {
      access_token: 'mock_token_' + Date.now(),
      expires_at: Date.now() + 3600000, // 1 hour from now
      user: mockUser
    };

    // Store in localStorage for persistence
    localStorage.setItem('mockUser', JSON.stringify(mockUser));
    localStorage.setItem('mockSession', JSON.stringify(mockSession));

    return {
      success: true,
      data: {
        user: mockUser,
        session: mockSession
      }
    };
  } catch (error) {
    console.error('Error in mock sign in:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Login Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Mock sign up function
export async function mockSignUp(email: string, password: string, name?: string) {
  try {
    console.log('Using mock sign up with:', email);

    // Simple validation
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }

    // Create a mock user
    const mockUser: MockUser = {
      id: generateUUID(),
      email: email,
      name: name || email.split('@')[0],
      avatarUrl: null,
    };

    // Create a mock session (but don't activate it yet to simulate email verification)
    const mockSession: MockSession = {
      access_token: 'mock_token_' + Date.now(),
      expires_at: Date.now() + 3600000, // 1 hour from now
      user: mockUser
    };

    // Store in localStorage for persistence but don't set the session
    localStorage.setItem('mockUser', JSON.stringify(mockUser));
    // We don't set mockSession here to simulate email verification required

    return {
      success: true,
      data: {
        user: mockUser,
        session: null // Simulate email verification required
      }
    };
  } catch (error) {
    console.error('Error in mock sign up:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Signup Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Mock sign out function
export async function mockSignOut() {
  try {
    console.log('Using mock sign out');

    // Remove from localStorage
    localStorage.removeItem('mockUser');
    localStorage.removeItem('mockSession');

    return { success: true };
  } catch (error) {
    console.error('Error in mock sign out:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    notify.error(errorMessage, {
      title: 'Logout Error',
      duration: 5000
    });
    return { success: false, error };
  }
}

// Mock get session function
export async function mockGetSession() {
  try {
    console.log('Using mock get session');

    // Get from localStorage
    const mockUserString = localStorage.getItem('mockUser');
    const mockSessionString = localStorage.getItem('mockSession');

    if (!mockUserString || !mockSessionString) {
      console.log('No mock user or session found in localStorage');
      return { user: null, session: null };
    }

    const mockUser = JSON.parse(mockUserString);
    const mockSession = JSON.parse(mockSessionString);

    // Check if session is expired
    if (mockSession.expires_at < Date.now()) {
      console.log('Mock session expired');
      localStorage.removeItem('mockSession');
      return { user: null, session: null };
    }

    console.log('Mock session found:', { user: mockUser, session: mockSession });
    return {
      user: mockUser,
      session: mockSession
    };
  } catch (error) {
    console.error('Error in mock get session:', error);
    return { user: null, session: null };
  }
}

// Mock user profile
export async function mockGetUserProfile(userId: string) {
  try {
    console.log('Using mock get user profile for:', userId);

    return {
      success: true,
      data: {
        id: userId,
        userId: userId,
        company: 'Mock Company',
        jobTitle: 'Developer',
        bio: 'This is a mock user profile for testing purposes.',
        website: 'https://example.com',
        phone: '+1234567890',
        country: 'India',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Error in mock get user profile:', error);
    return {
      success: false,
      error,
      data: {
        id: userId,
        userId: userId,
        createdAt: new Date().toISOString()
      }
    };
  }
}

// Mock user credits
export async function mockGetUserCredits(userId: string) {
  try {
    console.log('Using mock get user credits for:', userId);

    return {
      success: true,
      data: {
        totalCredits: 1000,
        usedCredits: 250,
        availableCredits: 750
      }
    };
  } catch (error) {
    console.error('Error in mock get user credits:', error);
    return {
      success: false,
      error,
      data: {
        totalCredits: 500,
        usedCredits: 0,
        availableCredits: 500
      }
    };
  }
}

// Mock credit transactions
export async function mockGetCreditTransactions(userId: string, limit: number = 10) {
  try {
    console.log('Using mock get credit transactions for:', userId);

    const mockTransactions = [
      {
        id: generateUUID(),
        userId: userId,
        amount: 500,
        description: 'Welcome bonus credits',
        transactionType: 'bonus',
        createdAt: new Date().toISOString()
      },
      {
        id: generateUUID(),
        userId: userId,
        amount: 250,
        description: 'Referral bonus',
        transactionType: 'bonus',
        createdAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      },
      {
        id: generateUUID(),
        userId: userId,
        amount: -50,
        description: 'ChatGPT usage',
        transactionType: 'usage',
        createdAt: new Date(Date.now() - 172800000).toISOString() // 2 days ago
      },
      {
        id: generateUUID(),
        userId: userId,
        amount: -75,
        description: 'Midjourney usage',
        transactionType: 'usage',
        createdAt: new Date(Date.now() - 259200000).toISOString() // 3 days ago
      },
      {
        id: generateUUID(),
        userId: userId,
        amount: 300,
        description: 'Credit purchase',
        transactionType: 'purchase',
        createdAt: new Date(Date.now() - 345600000).toISOString() // 4 days ago
      }
    ];

    return {
      success: true,
      data: mockTransactions.slice(0, limit)
    };
  } catch (error) {
    console.error('Error in mock get credit transactions:', error);
    return { success: false, error, data: [] };
  }
}

// Mock update user profile
export async function mockUpdateUserProfile(userId: string, profileData: any) {
  try {
    console.log('Using mock update user profile for:', userId, profileData);

    return { success: true };
  } catch (error) {
    console.error('Error in mock update user profile:', error);
    return { success: false, error };
  }
}
