/**
 * API Route for v1 Endpoint
 * 
 * This file serves as an entry point for v1 API endpoints.
 */

import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Return API information
  res.status(200).json({
    name: 'Firenest API',
    version: 'v1',
    description: 'API for Firenest platform',
    endpoints: {
      auth: '/api/v1/auth',
      usage: '/api/v1/usage',
      session: '/api/v1/session'
    }
  });
}
