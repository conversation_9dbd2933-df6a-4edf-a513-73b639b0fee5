import { useState } from "react";
import { notify } from "@/components/ui/notification-system";
import { cn } from "@/lib/utils";
import { submitEarlyAccess } from "@/lib/supabase";

type NewsletterSignupProps = {
  primary?: boolean;
  glowingTrail?: boolean;
  orangeButton?: boolean;
  customButtonText?: string;
};

const NewsletterSignup = ({
  primary = false,
  glowingTrail = false,
  orangeButton = false,
  customButtonText = "Get Early Access"
}: NewsletterSignupProps) => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      notify.error("Please enter your email address", {
        title: "Missing Information",
        position: "top-center",
        duration: 4000
      });
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Submitting email to Supabase:', email);
      const result = await submitEarlyAccess(email);
      console.log('Supabase response:', result);

      if (result.success) {
        setEmail("");
        notify.success("Thanks for signing up! We'll be in touch soon.", {
          title: "Signup Complete",
          position: "top-center",
          duration: 4000
        });
      } else {
        console.error('Supabase error details:', result.error);
        notify.error(`Error: ${result.error?.message || 'Something went wrong'}`, {
          title: "Signup Failed",
          position: "top-center",
          duration: 5000
        });
      }
    } catch (error) {
      console.error("Error submitting early access request:", error);
      // Display more detailed error information
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      notify.error(`Submission failed: ${errorMessage}`, {
        title: "Error",
        position: "top-center",
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getButtonClass = () => {
    let baseClass = "";

    if (orangeButton) {
      baseClass = "orange-gradient-button whitespace-nowrap py-3 px-6";
    } else {
      baseClass = "pop-button whitespace-nowrap py-3 px-6";
    }

    if (glowingTrail) {
      baseClass += " glowing-trail-button";
    }

    if (isSubmitting) {
      baseClass += " opacity-80";
    }

    return baseClass;
  };

  return (
    <form onSubmit={handleSubmit} className="w-full max-w-md">
      <div className={`flex flex-col sm:flex-row gap-3 w-full ${primary ? 'mb-3' : 'mb-2'}`}>
        <input
          type="email"
          placeholder="Enter your email for early access"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className={`bg-white/10 border border-white/20 rounded-md px-4 py-3 text-white placeholder-white/50 flex-grow focus:outline-none focus:ring-2 focus:ring-fiery`}
        />
        <button
          type="submit"
          disabled={isSubmitting}
          className={getButtonClass()}
        >
          {isSubmitting ? "Signing Up..." : customButtonText}
        </button>
      </div>
      <div className="text-sm text-white/60">
        <p>Limited spots, sign up today!</p>
      </div>
    </form>
  );
};

export default NewsletterSignup;
