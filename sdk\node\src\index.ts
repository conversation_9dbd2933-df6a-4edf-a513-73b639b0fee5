/**
 * Firenest SDK for Node.js
 *
 * This SDK provides a simple interface for integrating with Firenest,
 * handling authentication, authorization checks, and usage reporting.
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { verify } from 'jsonwebtoken';

// SDK Configuration
export interface FirenestSDKConfig {
  clientId: string;
  clientSecret: string;
  apiBaseUrl?: string;
  environment?: 'sandbox' | 'production';
}

// Authorization check result
export interface AccessCheckResult {
  allowed: boolean;
  reason?: string;
  estimatedCost?: number;
}

// Usage reporting options
export interface UsageReportOptions {
  unitsConsumed?: number;
  metadata?: Record<string, any>;
}

// Usage reporting result
export interface UsageReportResult {
  success: boolean;
  eventId?: string;
  error?: string;
}

/**
 * Firenest SDK main class
 */
export class FirenestSDK {
  private clientId: string;
  private clientSecret: string;
  private apiBaseUrl: string;
  private httpClient: AxiosInstance;

  /**
   * Create a new instance of the Firenest SDK
   *
   * @param config SDK configuration
   */
  constructor(config: FirenestSDKConfig) {
    this.clientId = config.clientId;
    this.clientSecret = config.clientSecret;

    // Set API base URL based on environment
    const environment = config.environment || 'production';
    if (config.apiBaseUrl) {
      this.apiBaseUrl = config.apiBaseUrl;
    } else {
      this.apiBaseUrl = environment === 'production'
        ? 'https://api.firenest.com'
        : 'https://sandbox.firenest.com';
    }

    // Create HTTP client
    this.httpClient = axios.create({
      baseURL: this.apiBaseUrl,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'FirenestSDK/0.1.0 Node.js'
      }
    });
  }

  /**
   * Generate a login URL for redirecting users to Firenest
   *
   * @param redirectUri The URI to redirect to after authentication
   * @param state Optional state parameter for security
   * @param scope Optional scope parameter
   * @returns The authorization URL
   */
  public getLoginUrl(
    redirectUri: string,
    state?: string,
    scope: string[] = ['openid', 'profile']
  ): string {
    const authState = state || uuidv4();

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientId,
      redirect_uri: redirectUri,
      state: authState,
      scope: scope.join(' ')
    });

    return `${this.apiBaseUrl}/api/v1/auth/authorize?${params.toString()}`;
  }

  /**
   * Handle the callback from Firenest
   *
   * @param requestUrl The full URL of the callback request
   * @returns The user information
   */
  public async handleCallback(requestUrl: string): Promise<any> {
    // Parse the URL to extract the code and state
    const url = new URL(requestUrl);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    if (error) {
      throw new Error(`Authentication error: ${error}`);
    }

    if (!code) {
      throw new Error('No authorization code found in the callback URL');
    }

    // Exchange the code for tokens
    try {
      const response = await this.httpClient.post('/api/v1/auth/token', {
        grant_type: 'authorization_code',
        code,
        client_id: this.clientId,
        client_secret: this.clientSecret,
        redirect_uri: url.origin + url.pathname
      });

      const { access_token, id_token, refresh_token, expires_in } = response.data;

      // Decode the ID token
      // Note: In a production environment with proper JWKS setup,
      // you should verify the signature using the public key from the JWKS endpoint
      let decodedToken;
      try {
        // Try to verify if possible
        decodedToken = verify(id_token, this.clientSecret, {
          algorithms: ['HS256'],
          ignoreExpiration: false,
          audience: this.clientId // Verify the token is intended for this client
        }) as any;
      } catch (error) {
        // Fallback to just decoding without verification
        console.warn('Could not verify ID token signature, falling back to decode-only');
        const jwt = id_token.split('.');
        if (jwt.length !== 3) {
          throw new Error('Invalid ID token format');
        }

        try {
          decodedToken = JSON.parse(Buffer.from(jwt[1], 'base64').toString());
        } catch (decodeError) {
          throw new Error('Failed to decode ID token');
        }
      }

      // Return user information and tokens
      return {
        // User information from ID token
        firenestUserId: decodedToken.sub,
        email: decodedToken.email,
        name: decodedToken.name,

        // Token information
        accessToken: access_token,
        idToken: id_token,
        refreshToken: refresh_token,
        expiresIn: expires_in,

        // Raw token data for advanced use cases
        tokenResponse: response.data
      };
    } catch (error) {
      console.error('Error exchanging code for tokens:', error);
      throw new Error('Failed to exchange authorization code for tokens');
    }
  }

  /**
   * Check if a user has access to a feature
   *
   * @param firenestUserId The Firenest user ID
   * @param featureId The feature ID to check access for
   * @returns Access check result
   */
  public async checkAccess(
    firenestUserId: string,
    featureId: string
  ): Promise<AccessCheckResult> {
    try {
      const response = await this.httpClient.post(
        '/api/v1/authorize',
        {
          userId: firenestUserId,
          featureId
        },
        {
          auth: {
            username: this.clientId,
            password: this.clientSecret
          }
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Error checking access:', error);

      // If the server returned an error response, use that
      if (error.response && error.response.data) {
        return {
          allowed: false,
          reason: error.response.data.message || 'Server error'
        };
      }

      // Otherwise, return a generic error
      return {
        allowed: false,
        reason: 'Failed to check access'
      };
    }
  }

  /**
   * Report usage of a feature
   *
   * @param firenestUserId The Firenest user ID
   * @param featureId The feature ID that was used
   * @param options Optional usage reporting options
   * @returns Usage report result
   */
  public async reportUsage(
    firenestUserId: string,
    featureId: string,
    options?: UsageReportOptions
  ): Promise<UsageReportResult> {
    try {
      const response = await this.httpClient.post(
        '/api/v1/usage/track',
        {
          userId: firenestUserId,
          featureId,
          units: options?.unitsConsumed,
          metadata: options?.metadata,
          timestamp: new Date().toISOString()
        },
        {
          auth: {
            username: this.clientId,
            password: this.clientSecret
          }
        }
      );

      return {
        success: true,
        eventId: response.data.eventId
      };
    } catch (error: any) {
      console.error('Error reporting usage:', error);

      // If the server returned an error response, use that
      if (error.response && error.response.data) {
        return {
          success: false,
          error: error.response.data.message || 'Server error'
        };
      }

      // Otherwise, return a generic error
      return {
        success: false,
        error: 'Failed to report usage'
      };
    }
  }
}
