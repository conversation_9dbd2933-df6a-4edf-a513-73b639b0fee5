# Firenest Partner SDK Integration & Onboarding Checklist

This document outlines the technical steps and considerations for integrating partner applications with Firenest. The goal is to provide a seamless experience for partners, handling SSO, authorization checks, and usage reporting.

**Core Strategy:** OAuth 2.0/OIDC + Backend SDK + Webhooks

**Assumptions (Firenest Provides):**
- [x] A secure Partner Dashboard portal with comprehensive analytics.
- [x] Robust backend APIs (OAuth/OIDC endpoints, Authorization check, Usage reporting).
- [x] Well-documented SDKs for various backend languages (Node.js, Python, Java, PHP, Ruby, Go).
- [x] A Sandbox environment mirroring Production functionality.
- [x] Integration testing tools and validation mechanisms.
- [x] Webhook support for real-time usage tracking and notifications.

---

## Phase 1: Setup & Configuration (Firenest Partner Dashboard)

This phase involves the initial setup actions a partner must perform within the Firenest Partner Dashboard.

### 1.1 Register & Create Application
- **Action (Partner):**
  - [x] Sign up on the [Firenest Partner Dashboard](https://partners.firenest.app).
  - [x] Create a new "Application" entry representing their SaaS product.
  - [x] Complete the partner profile with:
    - [x] Company details (name, website, description)
    - [x] Company logo (512x512px PNG with transparency)
    - [x] Contact information (technical and business contacts)
    - [x] Tool category and subcategories
    - [x] Pricing model and credit consumption strategy
- **Output (Firenest):**
  - [x] Firenest system generates a unique `client_id`.
  - [x] Firenest system generates a unique `client_secret`.
  - [x] Assign a dedicated partner success manager.
- **Security Note (Partner):**
  - [x] Confirm `client_secret` will be stored securely on the partner's backend (e.g., environment variables, secret manager).
  - [x] Confirm `client_secret` will NEVER be exposed client-side.
  - [x] Enable two-factor authentication for the partner account.

### 1.2 Configure Redirect URIs
- **Action (Partner):**
  - [x] In the Partner Dashboard, register the exact callback URIs where Firenest will redirect users after authentication.
- **Details (Partner):**
  - [x] Specify a development `redirect_uri` (e.g., `http://localhost:3000/auth/firenest/callback`).
  - [x] Specify a staging `redirect_uri` (e.g., `https://staging.partnerapp.com/auth/firenest/callback`).
  - [x] Specify a production `redirect_uri` (e.g., `https://partnerapp.com/auth/firenest/callback`).
- **Best Practices:**
  - [x] Use HTTPS for all non-localhost redirect URIs.
  - [x] Keep redirect URIs as specific as possible (avoid wildcard paths).
  - [x] Consider using a dedicated subdomain for authentication (e.g., `auth.partnerapp.com`).
- **Security (Firenest Implementation):**
  - [x] Firenest backend must strictly validate the `redirect_uri` parameter in authorization requests against this registered list.
  - [x] Implement PKCE (Proof Key for Code Exchange) for added security.
  - [x] Monitor for suspicious authentication patterns.

### 1.3 Define Metered Features
- **Action (Partner):**
  - [x] In the Partner Dashboard, define all specific premium features that will consume FireNest credits.
- **Details for each feature (Partner):**
  - [x] Define `featureId` (string): Unique, machine-readable identifier (e.g., `export-csv-pro`, `generate-report-advanced`).
  - [x] Define `displayName` (string): Human-readable name (e.g., "Pro CSV Export").
  - [x] Define `description` (string): Brief explanation of the feature.
  - [x] Define `defaultCreditCost` (integer): The number of credits consumed per usage by default.

---

## Phase 2: SDK Integration (Partner Backend/Frontend)

This phase covers the partner's development work to integrate the FireNest SDK.

### 2.1 Install & Initialize SDK
- **Action (Partner):**
  - [x] Add the appropriate FireNest backend SDK (e.g., `@firenest/sdk-node`) as a dependency to their backend application.
  - [x] Initialize the SDK early in their application lifecycle.
  - [x] Provide `client_id` to SDK.
  - [x] Provide `client_secret` to SDK.
  - [x] Specify the FireNest environment (Sandbox/Production) for SDK initialization (e.g., based on `NODE_ENV`).

### 2.2 Implement SSO Login Flow
- **Action (Partner - Frontend):**
  - [x] Add a "Login with FireNest" button/link in their application's UI.
- **Action (Partner - Backend):**
  - **Login Route (e.g., `/login/firenest`):**
    - [x] Create a backend route to initiate the login.
    - [x] Use `firenestSdk.getLoginUrl(redirectUri, [state])` to generate the FireNest authorization URL.
    - [x] Ensure `redirectUri` used here matches one registered in Phase 1.2.
    - [x] Redirect the user's browser to the generated FireNest authorization URL.
  - **Callback Route (e.g., `/auth/firenest/callback`):**
    - [x] Create a backend callback route that matches a registered `redirectUri`.
    - [x] Use `firenestSdk.handleCallback(requestUrl)` to process the incoming request.
    - [x] SDK internally extracts the authorization code.
    - [x] SDK internally exchanges the code for `id_token` and `access_token`.
    - [x] SDK internally validates the tokens.
    - [x] Extract the verified `firenestUserId` (and other claims like email, name if needed) from the SDK's response.
    - [x] Find or create a user record in the partner's database, linking it to `firenestUserId`.
    - [x] Establish a standard login session for the user within the partner application.
    - [x] Redirect the user to their dashboard or intended destination within the partner app.

---

## Phase 3: Core Logic Integration (Partner Backend)

This phase involves integrating the core authorization and usage reporting mechanisms.

### 3.1 Implement Access Control (Guard Premium Features)
- **Action (Partner):**
  - [x] Identify all backend code points where a premium feature (defined in Phase 1.3) is about to be executed.
  - [x] **Before** executing the premium logic, insert a call to `firenestSdk.checkAccess(firenestUserId, featureId)`.
    - [x] Retrieve the `firenestUserId` associated with the currently logged-in partner user.
    - [x] Use the correct `featureId` (matching one defined in Phase 1.3).
  - [x] Check the `accessResult.allowed` status from the SDK.
  - [x] If `accessResult.allowed` is `false`:
    - [x] Deny access to the feature.
    - [x] Return an appropriate error to the user (e.g., HTTP 402 Payment Required, with `accessResult.reason`).
  - [x] If `accessResult.allowed` is `true`:
    - [x] Proceed with executing the premium feature logic.
- **Requirement (Partner):**
  - [x] Ensure this `checkAccess` call happens server-side.

### 3.2 Implement Usage Reporting
- **Action (Partner):**
  - [x] Identify all backend code points where a premium feature has been **successfully executed and consumed** by the user.
  - [x] **After** successful execution, insert a call to `firenestSdk.reportUsage(firenestUserId, featureId, [options])`.
    - [x] Retrieve the `firenestUserId`.
    - [x] Use the correct `featureId`.
    - [x] Optionally, pass `unitsConsumed` if the cost varies dynamically and is not the `defaultCreditCost`.
  - **Critical Error Handling (Partner):**
    - [x] Implement robust error handling for the `reportUsage` call.
    - [x] Log any failures to report usage.
    - [x] Consider implementing a retry mechanism with backoff for transient network errors.
    - [x] Consider a dead-letter queue or manual reconciliation process for persistent failures to ensure usage is eventually reported and credits are deducted.
- **Requirement (Partner):**
  - [x] Ensure this `reportUsage` call happens server-side.
- **SDK Responsibility (FireNest Implementation):**
  - [x] FireNest's `/usage` API endpoint should be designed to be idempotent.

---

## Phase 4: Testing & Go-Live

### 4.1 Integration Testing
- **Action (Partner):**
  - [x] Configure SDK to use `environment: 'sandbox'`.
  - [x] Thoroughly test the entire integration flow in the Firenest Sandbox environment.
  - [x] Use the Firenest Integration Testing Tool in the Partner Dashboard.
- **Testing Checklist (Partner):**
  - **Authentication Testing:**
    - [x] OAuth/OIDC login flow successful.
    - [x] Token refresh mechanism works correctly.
    - [x] SSO logout (ensuring partner session is cleared).
    - [x] Firenest User ID correctly linked to partner's internal user account.
    - [x] Test with invalid credentials and verify proper error handling.

  - **Authorization Testing:**
    - [x] `checkAccess` correctly allows features with sufficient sandbox credits.
    - [x] `checkAccess` correctly denies features with insufficient sandbox credits (verify error/HTTP 402).
    - [x] Test rate limiting and throttling mechanisms.
    - [x] Verify proper error messages are displayed to users when access is denied.

  - **Usage Reporting Testing:**
    - [x] `reportUsage` successfully deducts credits in the sandbox Firenest account after feature use.
    - [x] Verify webhook notifications are received for usage events.
    - [x] Test with various credit amounts and feature combinations.
    - [x] Verify usage analytics are correctly displayed in the Partner Dashboard.

  - **Resilience Testing:**
    - [x] Test edge cases (e.g., network errors during API calls).
    - [x] Test token expiry/refresh mechanisms.
    - [x] Verify retry mechanisms for failed API calls and webhooks.
    - [x] Test with simulated high load to ensure performance under stress.

  - **User Experience Testing:**
    - [x] Verify seamless user flow from Firenest to partner application.
    - [x] Test with different user accounts and permission levels.
    - [x] Verify proper error messages and user notifications.
    - [x] Test on different browsers and devices.

- **Firenest Responsibility:**
  - [x] Provide comprehensive testing tools in the Partner Dashboard.
  - [x] Offer test accounts with sandbox credits for integration testing.
  - [x] Provide detailed logs and diagnostics for troubleshooting.
  - [x] Ensure sandbox API functionality mirrors production APIs.

### 4.2 Production Deployment
- **Action (Partner):**
  - [x] Ensure all production `redirect_uri`(s) are correctly registered in the Firenest Partner Dashboard (from Phase 1.2).
  - [x] Configure the SDK to use `environment: 'production'`.
  - [x] Deploy the integrated code to their production environment.
  - [x] Set up monitoring and alerting for integration health.
  - [x] Configure analytics dashboards to track usage patterns.

- **Pre-Launch Checklist:**
  - [x] Verify all authentication flows work in production.
  - [x] Confirm webhook endpoints are properly configured and receiving events.
  - [x] Test credit deduction with real accounts (using minimal credits).
  - [x] Verify error handling and user messaging in production environment.
  - [x] Ensure proper logging is in place for troubleshooting.

- **Action (Both Firenest & Partner):**
  - [x] Perform final smoke tests in the production environment.
  - [x] Monitor application logs and Firenest Partner Dashboard closely post-launch.
  - [x] Establish communication channels for immediate support during launch.
  - [x] Schedule a post-launch review to address any issues or improvements.

### 4.3 Analytics & Monitoring
- **Action (Partner):**
  - [x] Set up comprehensive monitoring for the Firenest integration:
    - [x] Configure real-time alerts for authentication failures.
    - [x] Monitor API response times and error rates.
    - [x] Track webhook delivery success rates.
    - [x] Set up dashboards for usage patterns and user engagement.
  - [x] Implement detailed logging for troubleshooting:
    - [x] Log all authentication events with appropriate privacy controls.
    - [x] Track feature usage and credit consumption.
    - [x] Monitor user session data for optimization opportunities.
  - [x] Configure analytics to measure integration success:
    - [x] Track conversion rates from Firenest users.
    - [x] Measure feature adoption and engagement metrics.
    - [x] Analyze credit consumption patterns to optimize pricing.

- **Action (Firenest):**
  - [x] Provide comprehensive analytics in the Partner Dashboard:
    - [x] Real-time usage statistics and trends.
    - [x] User acquisition and retention metrics.
    - [x] Credit consumption patterns and forecasts.
    - [x] Authentication success rates and error tracking.
  - [x] Implement proactive monitoring and support:
    - [x] Monitor API usage and provide support for any integration issues.
    - [x] Alert partners about unusual activity or potential issues.
    - [x] Provide a clear escalation path for urgent issues.
    - [x] Schedule regular check-ins with partners to review performance.

- **Continuous Improvement:**
  - [x] Establish a feedback loop between Firenest and partners.
  - [x] Regularly review analytics to identify optimization opportunities.
  - [x] Plan quarterly reviews of integration performance and user feedback.
  - [x] Stay updated on new Firenest features and integration options.

---

## Key Considerations for Future-Proofing & Best Practices

These are ongoing considerations for both Firenest and the integrating partner.

- **API Versioning (Firenest):**
  - [x] Firenest APIs should be versioned (e.g., `/api/v1/...`).
  - [x] Maintain backward compatibility for at least 12 months after introducing new versions.
- **Webhooks (Firenest - Recommended):**
  - [x] Configure webhook endpoints in the Partner Dashboard for real-time event notifications
  - [x] Implement webhook handlers for the following events:
    - [x] `session.start`: When a user starts using your tool
    - [x] `session.end`: When a user finishes using your tool
    - [x] `feature.use`: When a user uses a specific feature
    - [x] `credit.consume`: When credits are deducted
    - [x] `credit.low`: When a user's credit balance is running low
    - [x] `subscription.change`: When a user's subscription status changes
  - [x] Implement proper security for webhook verification (signature validation)
  - [x] Set up retry mechanisms for failed webhook deliveries
- **Granular OIDC Scopes (FireNest):**
  - [x] Use appropriate OIDC scopes (e.g., `openid profile email firenest_credits`) to request only necessary user data.
- **SDK Updates (FireNest & Partner):**
  - [x] FireNest to maintain and update SDKs.
  - [x] FireNest to establish a clear communication channel for partners regarding SDK updates and API changes.
  - [x] Partner to plan for incorporating SDK updates.
- **Idempotency for Usage Reporting (FireNest):**
  - [x] Ensure FireNest's `/usage` endpoint is robustly idempotent.
- **Error Handling & Reconciliation (Partner & FireNest):**
  - [x] Partner: Emphasize robust error handling for `reportUsage`.
  - [ ] FireNest: Consider providing reconciliation tools/reports in the Partner Dashboard.
- **Security Best Practices (Partner):**
  - [x] Regularly review secure storage of `client_secret`.
  - [x] Sanitize all inputs.
  - [x] Follow security best practices for their chosen framework and language.
- **Documentation (FireNest):**
  - [x] Provide comprehensive SDK documentation with examples for various frameworks.
  - [x] Maintain clear API documentation.
  - [x] Offer troubleshooting guides.
