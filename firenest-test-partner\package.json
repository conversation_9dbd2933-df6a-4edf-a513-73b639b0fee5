{"name": "firenest-test-partner", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"autoprefixer": "^10.4.17", "axios": "^1.6.7", "i": "^0.3.7", "postcss": "^8.4.35", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.0", "tailwindcss": "^3.4.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^6.3.5"}}