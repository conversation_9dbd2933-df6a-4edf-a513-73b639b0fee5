-- Database schema for Firenest API

-- Partners table
CREATE TABLE IF NOT EXISTS partners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  website_url TEXT,
  logo_url TEXT,
  client_id TEXT UNIQUE NOT NULL,
  client_secret TEXT NOT NULL,
  redirect_uris TEXT[] NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Partner API keys table
CREATE TABLE IF NOT EXISTS partner_api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  partner_id UUID NOT NULL REFERENCES partners(id) ON DELETE CASCADE,
  api_key TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  last_used_at TIMESTAMPTZ,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  UNIQUE (partner_id, name)
);

-- Partner tools table
CREATE TABLE IF NOT EXISTS partner_tools (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  partner_id UUID NOT NULL REFERENCES partners(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  credit_cost_per_unit NUMERIC NOT NULL DEFAULT 1,
  metric_type TEXT NOT NULL DEFAULT 'api_calls',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  active BOOLEAN NOT NULL DEFAULT TRUE,
  UNIQUE (partner_id, name)
);

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  display_name TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  active BOOLEAN NOT NULL DEFAULT TRUE
);

-- User credits table
CREATE TABLE IF NOT EXISTS user_credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  balance NUMERIC NOT NULL DEFAULT 0,
  last_updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (user_id)
);

-- Credit transactions table
CREATE TABLE IF NOT EXISTS credit_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  amount NUMERIC NOT NULL,
  transaction_type TEXT NOT NULL,
  description TEXT,
  balance_after NUMERIC NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Authorization codes table
CREATE TABLE IF NOT EXISTS auth_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT UNIQUE NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  partner_id UUID NOT NULL REFERENCES partners(id) ON DELETE CASCADE,
  redirect_uri TEXT NOT NULL,
  scope TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  used BOOLEAN NOT NULL DEFAULT FALSE
);

-- Access tokens table
CREATE TABLE IF NOT EXISTS access_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  access_token TEXT UNIQUE NOT NULL,
  refresh_token TEXT UNIQUE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  partner_id UUID NOT NULL REFERENCES partners(id) ON DELETE CASCADE,
  scope TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  revoked BOOLEAN NOT NULL DEFAULT FALSE
);

-- Usage sessions table
CREATE TABLE IF NOT EXISTS usage_sessions (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tool_id UUID NOT NULL REFERENCES partner_tools(id) ON DELETE CASCADE,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  status TEXT NOT NULL,
  metrics JSONB NOT NULL DEFAULT '{}'::JSONB,
  estimated_credits NUMERIC NOT NULL DEFAULT 0,
  actual_credits_used NUMERIC,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Usage events table
CREATE TABLE IF NOT EXISTS usage_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES usage_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tool_id UUID NOT NULL REFERENCES partner_tools(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL,
  credits_used NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- API logs table
CREATE TABLE IF NOT EXISTS api_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  endpoint TEXT NOT NULL,
  method TEXT NOT NULL,
  partner_id UUID REFERENCES partners(id) ON DELETE SET NULL,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  request_data JSONB,
  response_status INTEGER NOT NULL,
  response_data JSONB,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ip_address TEXT,
  user_agent TEXT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_usage_events_session_id ON usage_events(session_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_user_id ON usage_events(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_events_tool_id ON usage_events(tool_id);
CREATE INDEX IF NOT EXISTS idx_usage_sessions_user_id ON usage_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_sessions_tool_id ON usage_sessions(tool_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_access_tokens_user_id ON access_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_access_tokens_partner_id ON access_tokens(partner_id);
CREATE INDEX IF NOT EXISTS idx_auth_codes_user_id ON auth_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_codes_partner_id ON auth_codes(partner_id);
CREATE INDEX IF NOT EXISTS idx_partner_tools_partner_id ON partner_tools(partner_id);
CREATE INDEX IF NOT EXISTS idx_partner_api_keys_partner_id ON partner_api_keys(partner_id);

-- Add Row Level Security policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE access_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_codes ENABLE ROW LEVEL SECURITY;

-- Create policies for users
CREATE POLICY users_select ON users
  FOR SELECT USING (auth.uid() = id OR EXISTS (
    SELECT 1 FROM access_tokens 
    WHERE user_id = users.id AND partner_id IN (
      SELECT id FROM partners WHERE client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
    )
  ));

-- Create policies for user_credits
CREATE POLICY user_credits_select ON user_credits
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM access_tokens 
    WHERE user_id = user_credits.user_id AND partner_id IN (
      SELECT id FROM partners WHERE client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
    )
  ));

-- Create policies for credit_transactions
CREATE POLICY credit_transactions_select ON credit_transactions
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM access_tokens 
    WHERE user_id = credit_transactions.user_id AND partner_id IN (
      SELECT id FROM partners WHERE client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
    )
  ));

-- Create policies for usage_sessions
CREATE POLICY usage_sessions_select ON usage_sessions
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM access_tokens 
    WHERE user_id = usage_sessions.user_id AND partner_id IN (
      SELECT id FROM partners WHERE client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
    )
  ));

-- Create policies for usage_events
CREATE POLICY usage_events_select ON usage_events
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM access_tokens 
    WHERE user_id = usage_events.user_id AND partner_id IN (
      SELECT id FROM partners WHERE client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
    )
  ));

-- Create policies for access_tokens
CREATE POLICY access_tokens_select ON access_tokens
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM partners 
    WHERE id = access_tokens.partner_id AND client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
  ));

-- Create policies for auth_codes
CREATE POLICY auth_codes_select ON auth_codes
  FOR SELECT USING (auth.uid() = user_id OR EXISTS (
    SELECT 1 FROM partners 
    WHERE id = auth_codes.partner_id AND client_id = current_setting('request.jwt.claims', true)::json->>'client_id'
  ));
