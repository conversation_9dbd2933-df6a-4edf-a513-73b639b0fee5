import { useState } from 'react';
import { initiateFirenestLogin } from '../lib/firenest-integration';

const PricingTable = () => {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annual'>('monthly');

  const toggleBillingPeriod = () => {
    setBillingPeriod(billingPeriod === 'monthly' ? 'annual' : 'monthly');
  };

  const plans = [
    {
      name: "Starter",
      description: "Perfect for individuals and small teams just getting started.",
      monthlyPrice: 29,
      annualPrice: 290,
      features: [
        "AI content generation (10,000 words/mo)",
        "5 user seats",
        "50 templates",
        "Basic SEO optimization",
        "Email support",
        "1 integration"
      ],
      popular: false,
      cta: "Get Started"
    },
    {
      name: "Professional",
      description: "Ideal for growing teams and businesses scaling their content.",
      monthlyPrice: 79,
      annualPrice: 790,
      features: [
        "AI content generation (50,000 words/mo)",
        "15 user seats",
        "200+ templates",
        "Advanced SEO optimization",
        "Priority support",
        "10 integrations",
        "Analytics dashboard",
        "Team collaboration tools"
      ],
      popular: true,
      cta: "Get Started"
    },
    {
      name: "Enterprise",
      description: "For large organizations with advanced content needs.",
      monthlyPrice: 199,
      annualPrice: 1990,
      features: [
        "AI content generation (Unlimited)",
        "Unlimited user seats",
        "All templates",
        "Enterprise-grade SEO tools",
        "Dedicated account manager",
        "Unlimited integrations",
        "Advanced analytics & reporting",
        "Custom AI model training",
        "API access",
        "SSO & advanced security"
      ],
      popular: false,
      cta: "Contact Sales"
    }
  ];

  const renderCheckmark = () => (
    <svg className="feature-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  );

  return (
    <section id="pricing" className="section bg-dark-800">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Simple, <span className="gradient-text">Transparent Pricing</span>
          </h2>
          <p className="text-light-600 text-lg mb-8">
            Choose the plan that's right for your team. All plans include a 14-day free trial.
          </p>
          
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 text-sm ${billingPeriod === 'monthly' ? 'text-white' : 'text-light-600'}`}>Monthly</span>
            <button 
              onClick={toggleBillingPeriod}
              className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none"
              style={{ 
                backgroundColor: billingPeriod === 'annual' ? 'var(--primary-color)' : 'var(--light-gray)'
              }}
            >
              <span 
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingPeriod === 'annual' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 text-sm ${billingPeriod === 'annual' ? 'text-white' : 'text-light-600'}`}>
              Annual <span className="text-primary text-xs font-medium ml-1">Save 20%</span>
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div 
              key={index} 
              className={plan.popular ? "pricing-card-popular" : "pricing-card"}
            >
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-light-600 mb-6">{plan.description}</p>
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-white">
                    ${billingPeriod === 'monthly' ? plan.monthlyPrice : plan.annualPrice}
                  </span>
                  <span className="text-light-600 ml-2">
                    /{billingPeriod === 'monthly' ? 'month' : 'year'}
                  </span>
                </div>
              </div>
              
              <ul className="feature-list mb-8 flex-grow">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="feature-item">
                    {renderCheckmark()}
                    <span className="feature-text">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <button 
                onClick={initiateFirenestLogin}
                className={`w-full ${plan.popular ? 'btn btn-primary' : 'btn btn-outline'}`}
              >
                {plan.cta}
              </button>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <div className="card max-w-3xl mx-auto bg-dark-700 border-primary">
            <div className="flex flex-col md:flex-row items-center">
              <div className="mb-6 md:mb-0 md:mr-8">
                <div className="w-16 h-16 rounded-full bg-primary-900 flex items-center justify-center mx-auto md:mx-0">
                  <svg className="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <div className="text-center md:text-left">
                <h3 className="text-xl font-semibold text-white mb-2">Need a custom plan?</h3>
                <p className="text-light-600 mb-4">
                  Contact our sales team for custom pricing, on-premise deployment, and special requirements.
                </p>
                <a href="#" className="btn btn-primary inline-flex">
                  Contact Sales
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingTable;
