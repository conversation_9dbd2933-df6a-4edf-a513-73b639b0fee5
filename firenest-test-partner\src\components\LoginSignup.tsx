import { useState } from 'react';
import { initiateFirenestLogin } from '../lib/firenest-integration';

const LoginSignup = () => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // This is just UI, no actual implementation
    console.log('Form submitted:', { email, password, name });
  };

  return (
    <div className="bg-dark-800 border border-dark-600 rounded-xl p-6 shadow-lg w-full max-w-md">
      {/* Tabs */}
      <div className="flex mb-6 border-b border-dark-600">
        <button
          className={`flex-1 py-3 font-medium text-center ${
            activeTab === 'login'
              ? 'text-primary border-b-2 border-primary'
              : 'text-light-600 hover:text-light'
          }`}
          onClick={() => setActiveTab('login')}
        >
          Login
        </button>
        <button
          className={`flex-1 py-3 font-medium text-center ${
            activeTab === 'signup'
              ? 'text-primary border-b-2 border-primary'
              : 'text-light-600 hover:text-light'
          }`}
          onClick={() => setActiveTab('signup')}
        >
          Sign Up
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit}>
        {activeTab === 'signup' && (
          <div className="mb-4">
            <label htmlFor="name" className="block text-light mb-2 text-sm">
              Full Name
            </label>
            <input
              type="text"
              id="name"
              className="input"
              placeholder="John Doe"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required={activeTab === 'signup'}
            />
          </div>
        )}

        <div className="mb-4">
          <label htmlFor="email" className="block text-light mb-2 text-sm">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            className="input"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>

        <div className="mb-6">
          <label htmlFor="password" className="block text-light mb-2 text-sm">
            Password
          </label>
          <input
            type="password"
            id="password"
            className="input"
            placeholder={activeTab === 'signup' ? 'Create a password' : 'Enter your password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>

        <button type="submit" className="btn btn-primary w-full mb-4">
          {activeTab === 'login' ? 'Login' : 'Create Account'}
        </button>

        <div className="relative mb-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-dark-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-dark-800 text-light-600">Or continue with</span>
          </div>
        </div>

        <button
          type="button"
          onClick={initiateFirenestLogin}
          className="w-full flex items-center justify-center py-3 px-4 rounded-lg font-medium transition-all duration-300 bg-gradient-to-r from-orange-500 to-red-600 text-white hover:shadow-lg"
        >
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="white" fillOpacity="0.2" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 16L16 12L12 8" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 12H16" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          Sign in with Firenest
        </button>

        <p className="text-light-600 text-sm text-center mt-6">
          {activeTab === 'login' ? (
            <>
              Don't have an account?{' '}
              <button
                type="button"
                className="text-primary hover:underline"
                onClick={() => setActiveTab('signup')}
              >
                Sign up
              </button>
            </>
          ) : (
            <>
              Already have an account?{' '}
              <button
                type="button"
                className="text-primary hover:underline"
                onClick={() => setActiveTab('login')}
              >
                Log in
              </button>
            </>
          )}
        </p>
      </form>
    </div>
  );
};

export default LoginSignup;
