// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://pruhlxyhiylteqhazzlj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBydWhseHloaXlsdGVxaGF6emxqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1Nzg3NTMsImV4cCI6MjA1OTE1NDc1M30.mi1KLxsxFWwRJ2EluMRGwr8PbHn0aFV1yIBotWMeUK4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);