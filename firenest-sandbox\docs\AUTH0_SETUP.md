# Auth0 Configuration Guide

## Overview
This document provides comprehensive instructions for setting up Auth0 as the primary authentication provider for Firenest Sandbox, ensuring SOC 2 compliance and enterprise-grade security.

## Auth0 Tenant Setup

### 1. Create Auth0 Tenant
1. Sign up for Auth0 at https://auth0.com
2. Create a new tenant with the following settings:
   - **Tenant Name**: `firenest-sandbox-prod`
   - **Region**: Choose closest to your users (e.g., US, EU, AU)
   - **Environment**: Production

### 2. Application Configuration

#### Single Page Application (SPA)
1. Navigate to Applications > Create Application
2. Choose "Single Page Web Applications"
3. Configure the following settings:

**Basic Information:**
- Name: `Firenest Sandbox Frontend`
- Type: Single Page Application
- Technology: React

**Application URIs:**
- Allowed Callback URLs: 
  ```
  http://localhost:3000,
  https://sandbox.firenest.com,
  https://staging-sandbox.firenest.com
  ```
- Allowed Logout URLs:
  ```
  http://localhost:3000,
  https://sandbox.firenest.com,
  https://staging-sandbox.firenest.com
  ```
- Allowed Web Origins:
  ```
  http://localhost:3000,
  https://sandbox.firenest.com,
  https://staging-sandbox.firenest.com
  ```

**Advanced Settings:**
- Grant Types: Authorization Code, Refresh Token
- Token Endpoint Authentication Method: None
- OIDC Conformant: Enabled

#### Machine to Machine Application (API)
1. Create another application for backend API access
2. Choose "Machine to Machine Applications"
3. Configure for Auth0 Management API access

### 3. API Configuration

#### Create Custom API
1. Navigate to APIs > Create API
2. Configure:
   - **Name**: `Firenest Sandbox API`
   - **Identifier**: `https://api.sandbox.firenest.com`
   - **Signing Algorithm**: RS256

#### Scopes Definition
Add the following scopes:
```
read:profile - Read user profile information
write:profile - Update user profile information
read:projects - Read project data
write:projects - Create and update projects
read:simulations - Read simulation data
write:simulations - Create and run simulations
admin:users - Manage users (admin only)
admin:system - System administration (admin only)
```

### 4. User Management

#### User Roles
Create the following roles in Auth0 Dashboard:
1. **User** (Default)
   - Permissions: `read:profile`, `write:profile`, `read:projects`, `write:projects`, `read:simulations`, `write:simulations`
2. **Admin**
   - Permissions: All user permissions + `admin:users`, `admin:system`
3. **Viewer**
   - Permissions: `read:profile`, `read:projects`, `read:simulations`

#### Custom Claims Rule
Create a rule to add custom claims to tokens:

```javascript
function addCustomClaims(user, context, callback) {
  const namespace = 'https://firenest.com/';
  const assignedRoles = (context.authorization || {}).roles;
  const assignedPermissions = (context.authorization || {}).permissions;

  let idTokenClaims = context.idToken || {};
  let accessTokenClaims = context.accessToken || {};

  idTokenClaims[`${namespace}roles`] = assignedRoles;
  idTokenClaims[`${namespace}permissions`] = assignedPermissions;
  
  accessTokenClaims[`${namespace}roles`] = assignedRoles;
  accessTokenClaims[`${namespace}permissions`] = assignedPermissions;

  context.idToken = idTokenClaims;
  context.accessToken = accessTokenClaims;

  callback(null, user, context);
}
```

### 5. Security Configuration

#### Password Policy
Configure strong password requirements:
- Minimum length: 12 characters
- Require uppercase and lowercase letters
- Require numbers and special characters
- Password history: 5 passwords
- Password expiration: 90 days (for admin accounts)

#### Multi-Factor Authentication
Enable MFA for all users:
1. Navigate to Security > Multi-factor Auth
2. Enable the following factors:
   - SMS
   - Voice
   - Email
   - Push notifications via Auth0 Guardian
   - Time-based One-Time Password (TOTP)

#### Brute Force Protection
Configure brute force protection:
- Max attempts: 5
- Block duration: 15 minutes
- Progressive delay: Enabled

#### Suspicious IP Throttling
Enable and configure:
- Max attempts per IP: 100 per hour
- Block suspicious IPs: Enabled

### 6. Compliance Features

#### Audit Logs
Enable comprehensive audit logging:
1. Navigate to Monitoring > Logs
2. Configure log retention: 30 days minimum
3. Set up log streaming to external SIEM if required

#### Data Export
Configure user data export capabilities:
1. Enable user data export API
2. Set up automated compliance reports

### 7. Environment Variables

#### Frontend (.env)
```bash
VITE_AUTH0_DOMAIN=your-tenant.auth0.com
VITE_AUTH0_CLIENT_ID=your-spa-client-id
VITE_AUTH0_AUDIENCE=https://api.sandbox.firenest.com
```

#### Backend (.env)
```bash
AUTH_PROVIDER_TYPE=auth0
AUTH_PROVIDER_DOMAIN=your-tenant.auth0.com
AUTH_PROVIDER_CLIENT_ID=your-m2m-client-id
AUTH_PROVIDER_CLIENT_SECRET=your-m2m-client-secret
AUTH0_AUDIENCE=https://api.sandbox.firenest.com
```

### 8. Testing Configuration

#### Test Users
Create test users for different roles:
1. **Admin User**: <EMAIL>
2. **Regular User**: <EMAIL>
3. **Viewer User**: <EMAIL>

#### Test Scenarios
Verify the following scenarios:
1. User registration and email verification
2. Login with username/password
3. Login with social providers (if configured)
4. MFA enrollment and authentication
5. Password reset flow
6. Token refresh
7. Logout and session termination

### 9. Monitoring and Alerting

#### Set up alerts for:
- Failed login attempts exceeding threshold
- Unusual login patterns
- Token validation failures
- API rate limit violations
- MFA bypass attempts

### 10. Production Checklist

Before going live, ensure:
- [ ] Custom domain configured (auth.firenest.com)
- [ ] SSL certificates properly configured
- [ ] All test URLs removed from allowed origins
- [ ] Rate limiting configured appropriately
- [ ] Audit logging enabled and tested
- [ ] Backup and disaster recovery plan in place
- [ ] Security monitoring alerts configured
- [ ] Compliance documentation updated
- [ ] Penetration testing completed
- [ ] SOC 2 audit trail verified

## Security Best Practices

1. **Token Management**
   - Use short-lived access tokens (15 minutes)
   - Implement secure refresh token rotation
   - Store tokens securely (httpOnly cookies for web)

2. **API Security**
   - Validate all JWT tokens server-side
   - Implement proper CORS policies
   - Use HTTPS everywhere
   - Implement rate limiting

3. **User Management**
   - Enforce strong password policies
   - Require MFA for admin accounts
   - Regular access reviews
   - Automated user provisioning/deprovisioning

4. **Monitoring**
   - Real-time security monitoring
   - Automated threat detection
   - Regular security assessments
   - Incident response procedures

## Support and Troubleshooting

### Common Issues
1. **CORS Errors**: Verify allowed origins in Auth0 dashboard
2. **Token Validation Failures**: Check audience and issuer configuration
3. **MFA Issues**: Verify MFA policies and user enrollment
4. **Rate Limiting**: Review and adjust rate limits as needed

### Support Contacts
- Auth0 Support: https://support.auth0.com
- Internal Security Team: <EMAIL>
- DevOps Team: <EMAIL>
