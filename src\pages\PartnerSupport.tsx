import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Search, MessageSquare, Mail, Phone, FileText, HelpCircle } from 'lucide-react';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerSupport: React.FC = () => {
  const navigate = useNavigate();
  const { partner, isLoading } = usePartner();
  const [activeTab, setActiveTab] = useState('help');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (isLoading) {
    return <Loading />;
  }

  if (!partner) {
    navigate('/partner');
    return null;
  }

  const handleSubmitTicket = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!subject || !message) {
      notify.error('Please fill in all required fields');
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      notify.success('Support ticket submitted successfully');
      setSubject('');
      setMessage('');
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Support</h1>
                <p className="text-white/60">Get help with your Firenest integration</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Tabs */}
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="firenest-card">
            <TabsTrigger value="help" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Help Center
            </TabsTrigger>
            <TabsTrigger value="contact" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Contact Support
            </TabsTrigger>
            <TabsTrigger value="faq" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              FAQ
            </TabsTrigger>
          </TabsList>

          {/* Help Center Content */}
          <TabsContent value="help" className="space-y-6">
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/50" />
              <Input
                type="text"
                placeholder="Search for help..."
                className="pl-10 py-6 firenest-card text-white text-lg"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <HelpCircle className="w-5 h-5 mr-2 text-fiery" />
                    Getting Started
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-white/70">
                    <li className="hover:text-white cursor-pointer">• How to create a partner account</li>
                    <li className="hover:text-white cursor-pointer">• Setting up your first tool</li>
                    <li className="hover:text-white cursor-pointer">• Understanding the integration process</li>
                    <li className="hover:text-white cursor-pointer">• Firenest platform overview</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <HelpCircle className="w-5 h-5 mr-2 text-fiery" />
                    Authentication
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-white/70">
                    <li className="hover:text-white cursor-pointer">• OAuth 2.0 setup guide</li>
                    <li className="hover:text-white cursor-pointer">• OpenID Connect implementation</li>
                    <li className="hover:text-white cursor-pointer">• API key authentication</li>
                    <li className="hover:text-white cursor-pointer">• Troubleshooting auth issues</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <HelpCircle className="w-5 h-5 mr-2 text-fiery" />
                    Usage Tracking
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-white/70">
                    <li className="hover:text-white cursor-pointer">• Setting up usage tracking</li>
                    <li className="hover:text-white cursor-pointer">• Webhook configuration</li>
                    <li className="hover:text-white cursor-pointer">• Credit system explained</li>
                    <li className="hover:text-white cursor-pointer">• Viewing usage reports</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Contact Support Content */}
          <TabsContent value="contact" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card className="firenest-card">
                  <CardHeader>
                    <CardTitle className="text-white">Submit a Support Ticket</CardTitle>
                    <CardDescription>
                      Our support team will respond within 24 hours
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmitTicket} className="space-y-4">
                      <div className="space-y-2">
                        <label htmlFor="email" className="text-white">Email</label>
                        <Input
                          id="email"
                          type="email"
                          value={email || (partner?.email || '')}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Your email address"
                          className="firenest-card"
                          disabled={!!partner?.email}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <label htmlFor="subject" className="text-white">Subject</label>
                        <Input
                          id="subject"
                          value={subject}
                          onChange={(e) => setSubject(e.target.value)}
                          placeholder="Brief description of your issue"
                          className="firenest-card"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <label htmlFor="message" className="text-white">Message</label>
                        <Textarea
                          id="message"
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          placeholder="Describe your issue in detail"
                          className="firenest-card min-h-[150px]"
                        />
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="w-full bg-fiery hover:bg-fiery/90"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? 'Submitting...' : 'Submit Ticket'}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>
              
              <div>
                <Card className="firenest-card">
                  <CardHeader>
                    <CardTitle className="text-white">Contact Information</CardTitle>
                    <CardDescription>
                      Alternative ways to reach our support team
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-start">
                      <Mail className="w-5 h-5 text-fiery mr-3 mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Email Support</h3>
                        <p className="text-white/70"><EMAIL></p>
                        <p className="text-white/60 text-sm mt-1">Response within 24 hours</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <MessageSquare className="w-5 h-5 text-fiery mr-3 mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Live Chat</h3>
                        <p className="text-white/70">Available Monday-Friday</p>
                        <p className="text-white/60 text-sm mt-1">9am - 5pm EST</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <Phone className="w-5 h-5 text-fiery mr-3 mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Phone Support</h3>
                        <p className="text-white/70">Premium partners only</p>
                        <p className="text-white/60 text-sm mt-1">By appointment</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* FAQ Content */}
          <TabsContent value="faq" className="space-y-6">
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-white">Frequently Asked Questions</CardTitle>
                <CardDescription>
                  Quick answers to common questions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                  <h3 className="text-lg font-medium text-white mb-2">How do I set up OAuth 2.0 authentication?</h3>
                  <p className="text-white/70">
                    To set up OAuth 2.0 authentication, navigate to the Authentication tab in your tool settings. 
                    Enter your client ID, client secret, and configure the authorization and token URLs. 
                    Make sure to set the correct redirect URL in your OAuth provider settings.
                  </p>
                </div>

                <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                  <h3 className="text-lg font-medium text-white mb-2">How does the credit system work?</h3>
                  <p className="text-white/70">
                    Firenest uses a credit-based system to track usage of your tool. When a user accesses your tool through Firenest, 
                    credits are deducted based on the usage metrics you've configured. You can set up different credit costs for 
                    different features or actions within your tool.
                  </p>
                </div>

                <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                  <h3 className="text-lg font-medium text-white mb-2">How do I implement OpenID Connect?</h3>
                  <p className="text-white/70">
                    To implement OpenID Connect, select OIDC as your authentication method in the Authentication tab. 
                    Configure your OIDC issuer URL, client ID, and client secret. Firenest will automatically discover 
                    the necessary endpoints based on the issuer URL. Make sure to configure the correct redirect URL in your OIDC provider.
                  </p>
                </div>

                <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                  <h3 className="text-lg font-medium text-white mb-2">How do I track usage with webhooks?</h3>
                  <p className="text-white/70">
                    To track usage with webhooks, navigate to the Webhooks tab and configure a webhook endpoint in your application. 
                    Firenest will send usage events to this endpoint whenever a user interacts with your tool. 
                    You can then process these events and report usage back to Firenest using the Usage Tracking API.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerSupport;
