import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

// Testimonial data
const testimonials = [
  {
    quote: "Firenest is just what Indian startups needed — AI tools that don't break the bank. The credit system lets us scale our usage as we grow.",
    name: "<PERSON><PERSON>",
    position: "Founder, InnovateX Labs",
    initial: "P",
    gradient: "from-fiery to-cool-500"
  },
  {
    quote: "The platform's integration capabilities saved us countless hours. We connect all our AI tools in one place and only pay for what we use.",
    name: "<PERSON><PERSON>",
    position: "CTO, CodeVerse",
    initial: "R",
    gradient: "from-cool-500 to-fiery-300"
  },
  {
    quote: "As a bootstrapped startup, every rupee counts. Firenest's credit system gave us access to enterprise-grade AI without the enterprise-grade price tag.",
    name: "<PERSON><PERSON>",
    position: "CEO, DataSprint",
    initial: "A",
    gradient: "from-fiery-400 to-purple-600"
  },
  {
    quote: "The customer support team is exceptional. They helped us tailor the perfect AI solution for our unique needs within our budget constraints.",
    name: "<PERSON><PERSON><PERSON>",
    position: "Founder, TechTribe",
    initial: "V",
    gradient: "from-amber-500 to-fiery"
  }
];

const Testimonial = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const nextTestimonial = () => {
    setActiveIndex((current) => (current + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveIndex((current) => (current - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <div className="relative max-w-4xl mx-auto">
      {/* Testimonial Cards */}
      <div className="overflow-hidden px-4">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${activeIndex * 100}%)` }}
        >
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="w-full flex-shrink-0 px-4"
            >
              <div className="h-full relative firenest-card p-8 transition-all duration-300 hover:border-fiery/20">
                <p className="text-lg italic text-white/90 mb-6">
                  {testimonial.quote}
                </p>
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 bg-gradient-to-br ${testimonial.gradient} rounded-full flex items-center justify-center text-white font-bold shadow-md`}>
                    {testimonial.initial}
                  </div>
                  <div>
                    <p className="font-medium text-white">{testimonial.name}</p>
                    <p className="text-sm text-white/60">{testimonial.position}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Dots */}
      <div className="flex justify-center mt-8 gap-2">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setActiveIndex(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === activeIndex ? 'w-6 bg-fiery' : 'bg-white/30 hover:bg-white/50'
            }`}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevTestimonial}
        className="absolute top-1/2 -left-4 -translate-y-1/2 w-10 h-10 rounded-full bg-white/5 border border-white/10 flex items-center justify-center text-white/70 hover:text-white hover:bg-white/10 hover:border-fiery/30 transition-all duration-300"
        aria-label="Previous testimonial"
      >
        <ChevronLeft className="h-5 w-5" />
      </button>
      <button
        onClick={nextTestimonial}
        className="absolute top-1/2 -right-4 -translate-y-1/2 w-10 h-10 rounded-full bg-white/5 border border-white/10 flex items-center justify-center text-white/70 hover:text-white hover:bg-white/10 hover:border-fiery/30 transition-all duration-300"
        aria-label="Next testimonial"
      >
        <ChevronRight className="h-5 w-5" />
      </button>
    </div>
  );
};

export default Testimonial;
