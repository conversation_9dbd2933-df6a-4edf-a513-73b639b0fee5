@echo off
echo ========================================
echo Firenest Sandbox - Quick Setup Script
echo ========================================
echo.

echo [1/6] Checking prerequisites...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo [2/6] Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo [3/6] Installing frontend dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo [4/6] Creating environment files...
cd ..\backend
if not exist .env (
    echo Creating backend .env file...
    (
        echo # Database Configuration
        echo DB_HOST=localhost
        echo DB_PORT=5432
        echo DB_NAME=firenest_sandbox
        echo DB_USERNAME=sandbox_admin
        echo DB_PASSWORD=your_password
        echo DB_SSL=false
        echo.
        echo # Authentication ^(Development^)
        echo JWT_SECRET=your-32-character-secret-key-here-dev
        echo AUTH_PROVIDER_TYPE=development
        echo AUTH_PROVIDER_DOMAIN=localhost
        echo AUTH_PROVIDER_CLIENT_ID=dev-client-id
        echo AUTH_PROVIDER_CLIENT_SECRET=dev-client-secret
        echo.
        echo # Security
        echo ENCRYPTION_KEY=your-32-character-encryption-key-dev
        echo HASH_SALT=12
        echo.
        echo # Application
        echo NODE_ENV=development
        echo PORT=3001
        echo CORS_ALLOWED_ORIGINS=http://localhost:3000
        echo.
        echo # Logging
        echo LOG_LEVEL=debug
        echo.
        echo # AWS ^(Optional - comment out if not using^)
        echo # AWS_REGION=us-east-1
        echo # AWS_ACCESS_KEY_ID=your_access_key
        echo # AWS_SECRET_ACCESS_KEY=your_secret_key
        echo # S3_BUCKET_NAME=your_s3_bucket
        echo # SQS_QUEUE_URL=your_sqs_queue_url
        echo # SQS_DLQ_URL=your_dlq_url
    ) > .env
    echo Backend .env file created!
) else (
    echo Backend .env file already exists, skipping...
)

cd ..\frontend
if not exist .env (
    echo Creating frontend .env file...
    (
        echo VITE_API_URL=http://localhost:3001/api/v1
        echo VITE_AUTH_DOMAIN=localhost
        echo VITE_AUTH_CLIENT_ID=dev-client-id
        echo VITE_ENVIRONMENT=development
    ) > .env
    echo Frontend .env file created!
) else (
    echo Frontend .env file already exists, skipping...
)

echo.
echo [5/6] Database setup instructions...
echo.
echo IMPORTANT: You need to set up PostgreSQL database manually:
echo.
echo Option 1 - Local PostgreSQL:
echo   1. Install PostgreSQL from https://www.postgresql.org/download/
echo   2. Create database: CREATE DATABASE firenest_sandbox;
echo   3. Create user: CREATE USER sandbox_admin WITH PASSWORD 'your_password';
echo   4. Grant privileges: GRANT ALL PRIVILEGES ON DATABASE firenest_sandbox TO sandbox_admin;
echo   5. Run schema: psql -U sandbox_admin -d firenest_sandbox -f database/schema.sql
echo.
echo Option 2 - Docker PostgreSQL:
echo   1. Run: docker run --name firenest-postgres -e POSTGRES_DB=firenest_sandbox -e POSTGRES_USER=sandbox_admin -e POSTGRES_PASSWORD=your_password -p 5432:5432 -d postgres:14
echo   2. Wait 10 seconds, then run: docker exec -i firenest-postgres psql -U sandbox_admin -d firenest_sandbox ^< database/schema.sql
echo.

echo [6/6] Setup complete!
echo.
echo ========================================
echo Next Steps:
echo ========================================
echo 1. Set up PostgreSQL database ^(see instructions above^)
echo 2. Start the backend: cd backend ^&^& npm run dev
echo 3. Start the frontend: cd frontend ^&^& npm run dev
echo 4. Open http://localhost:3000 in your browser
echo.
echo For detailed setup instructions, see SETUP.md
echo ========================================

cd ..
pause
