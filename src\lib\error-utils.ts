import { notify } from '@/components/ui/notification-system';

// Error severity levels
export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// Error categories
export enum ErrorCategory {
  NETWORK = 'network',
  AUTH = 'authentication',
  DATABASE = 'database',
  VALIDATION = 'validation',
  API = 'api',
  RENDERING = 'rendering',
  UNKNOWN = 'unknown'
}

// Error context interface
export interface ErrorContext {
  userId?: string;
  path?: string;
  component?: string;
  action?: string;
  additionalData?: Record<string, any>;
}

/**
 * Log an error to the console and potentially to an error tracking service
 */
export function logError(
  error: Error | unknown,
  errorInfo?: React.ErrorInfo | null,
  severity: ErrorSeverity = ErrorSeverity.ERROR,
  category: ErrorCategory = ErrorCategory.UNKNOWN,
  context: ErrorContext = {}
): void {
  // Extract error message
  const errorMessage = error instanceof Error ? error.message : String(error);

  // Create error log object
  const errorLog = {
    timestamp: new Date().toISOString(),
    message: errorMessage,
    stack: error instanceof Error ? error.stack : undefined,
    componentStack: errorInfo?.componentStack,
    severity,
    category,
    context: {
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...context
    }
  };

  // Log to console
  console.error('Firenest Error:', errorLog);

  // In a production environment, you would send this to your error tracking service
  // Example: sendToErrorTrackingService(errorLog);

  // Store in localStorage for debugging (limited to last 50 errors)
  storeErrorInLocalStorage(errorLog);
}

/**
 * Store error in localStorage for debugging purposes
 */
function storeErrorInLocalStorage(errorLog: any): void {
  try {
    // Get existing errors
    const storedErrors = localStorage.getItem('firenest-errors');
    let errors = storedErrors ? JSON.parse(storedErrors) : [];

    // Add new error
    errors.unshift(errorLog);

    // Limit to 50 errors
    if (errors.length > 50) {
      errors = errors.slice(0, 50);
    }

    // Save back to localStorage
    localStorage.setItem('firenest-errors', JSON.stringify(errors));
  } catch (e) {
    console.error('Failed to store error in localStorage:', e);
  }
}

/**
 * Get all stored errors from localStorage
 */
export function getStoredErrors(): any[] {
  try {
    const storedErrors = localStorage.getItem('firenest-errors');
    return storedErrors ? JSON.parse(storedErrors) : [];
  } catch (e) {
    console.error('Failed to retrieve errors from localStorage:', e);
    return [];
  }
}

/**
 * Clear all stored errors from localStorage
 */
export function clearStoredErrors(): void {
  try {
    localStorage.removeItem('firenest-errors');
  } catch (e) {
    console.error('Failed to clear errors from localStorage:', e);
  }
}

/**
 * Handle API errors and display appropriate toast messages
 */
export function handleApiError(
  error: any,
  fallbackMessage: string = 'An error occurred while processing your request'
): void {
  let errorMessage = fallbackMessage;
  let category = ErrorCategory.API;

  // Extract error message based on common API error patterns
  if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  } else if (error?.message) {
    errorMessage = error.message;
  }

  // Determine error category
  if (error?.response?.status === 401 || error?.response?.status === 403) {
    category = ErrorCategory.AUTH;
  } else if (error?.response?.status === 422) {
    category = ErrorCategory.VALIDATION;
  } else if (error?.code === 'ECONNABORTED' || !navigator.onLine) {
    category = ErrorCategory.NETWORK;
    errorMessage = 'Network connection issue. Please check your internet connection.';
  }

  // Log the error
  logError(error, null, ErrorSeverity.ERROR, category);

  // Show toast notification
  notify.error(errorMessage, {
    title: 'Error',
    duration: 5000
  });
}

/**
 * Format error stack trace for display
 */
export function formatErrorStack(stack?: string): string {
  if (!stack) return 'No stack trace available';

  // Clean up the stack trace for better readability
  return stack
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join('\n');
}

/**
 * Get user-friendly error message based on error type
 */
export function getUserFriendlyErrorMessage(
  error: any,
  fallback: string = 'Something went wrong. Please try again later.'
): string {
  // Network errors
  if (!navigator.onLine) {
    return 'You appear to be offline. Please check your internet connection.';
  }

  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return 'The request timed out. Please try again.';
  }

  // Authentication errors
  if (error?.response?.status === 401) {
    return 'Your session has expired. Please log in again.';
  }

  if (error?.response?.status === 403) {
    return 'You don\'t have permission to perform this action.';
  }

  // Server errors
  if (error?.response?.status >= 500) {
    return 'Our server is experiencing issues. Please try again later.';
  }

  // Validation errors
  if (error?.response?.status === 422 && error?.response?.data?.errors) {
    const firstError = Object.values(error.response.data.errors)[0];
    return Array.isArray(firstError) ? firstError[0] : String(firstError);
  }

  // Use error message if available
  if (error?.message && typeof error.message === 'string') {
    return error.message;
  }

  // Fallback
  return fallback;
}

/**
 * Create a diagnostic report for an error
 */
export function createDiagnosticReport(error: any, context: ErrorContext = {}): string {
  const timestamp = new Date().toISOString();
  const errorMessage = error instanceof Error ? error.message : String(error);
  const stack = error instanceof Error ? error.stack : undefined;

  return `
Firenest Error Report
=====================
Timestamp: ${timestamp}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
Error: ${errorMessage}

${context.userId ? `User ID: ${context.userId}` : ''}
${context.component ? `Component: ${context.component}` : ''}
${context.action ? `Action: ${context.action}` : ''}

Stack Trace:
${formatErrorStack(stack)}

Additional Context:
${JSON.stringify(context.additionalData || {}, null, 2)}
`;
}

/**
 * Redirect to the error diagnostic page with error details
 *
 * @deprecated Use ErrorDiagnosticPage component directly instead
 */
export function redirectToErrorDiagnostics(
  error: Error | unknown,
  category: ErrorCategory = ErrorCategory.UNKNOWN,
  component?: string
): void {
  // Extract error details
  const errorMessage = error instanceof Error ? error.message : String(error);
  const stack = error instanceof Error ? error.stack : undefined;

  // Create URL with error parameters
  const params = new URLSearchParams({
    message: errorMessage,
    category: category,
    path: window.location.pathname
  });

  if (stack) {
    params.append('stack', stack);
  }

  if (component) {
    params.append('component', component);
  }

  // Redirect to error diagnostics page
  window.location.href = `/error-diagnostics?${params.toString()}`;
}
