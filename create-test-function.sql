-- Create a function to test table creation
CREATE OR REPLACE FUNCTION create_test_table()
RET<PERSON>NS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  CREATE TABLE IF NOT EXISTS public.test_table (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    created_at timestamptz DEFAULT now()
  );
END;
$$;

-- <PERSON> execute permission to anon role
GRANT EXECUTE ON FUNCTION create_test_table() TO anon;