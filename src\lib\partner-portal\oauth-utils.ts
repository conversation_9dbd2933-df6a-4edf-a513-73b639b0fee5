import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a random string of specified length
 * This function works in any environment, with a fallback for environments
 * where Web Crypto API is not available
 *
 * @param length The length of the random string
 * @returns A random string
 */
function generateRandomString(length: number): string {
  try {
    // Check if Web Crypto API is available
    if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
      const array = new Uint8Array(length);
      window.crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    } else {
      // Fallback to Math.random if Web Crypto API is not available
      console.warn('Web Crypto API not available, using fallback random function');
      let result = '';
      const characters = 'abcdef0123456789';
      for (let i = 0; i < length * 2; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }
      return result;
    }
  } catch (error) {
    // If Web Crypto API fails, use the fallback
    console.error('Error using Web Crypto API for random generation:', error);
    console.warn('Falling back to simple random function');
    let result = '';
    const characters = 'abcdef0123456789';
    for (let i = 0; i < length * 2; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }
}

/**
 * Generate a client ID for a partner tool
 * Format: tool-{toolId}-{random}
 *
 * @param toolId The ID of the tool
 * @param partnerName The name of the partner (used to create a more readable client ID)
 * @returns A unique client ID
 */
export function generateClientId(toolId: string, partnerName: string): string {
  // Create a slug from the partner name (lowercase, replace spaces with dashes)
  const partnerSlug = partnerName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

  // Generate a short random string (8 characters)
  const randomStr = generateRandomString(4);

  // Format: {partnerSlug}-{toolId-short}-{random}
  // We use a substring of the toolId to keep the client ID shorter
  const shortToolId = toolId.substring(0, 8);

  return `${partnerSlug}-${shortToolId}-${randomStr}`;
}

/**
 * Generate a client secret for a partner tool
 * This creates a cryptographically secure random string
 *
 * @returns A secure client secret
 */
export function generateClientSecret(): string {
  // Generate 32 bytes of random data (256 bits)
  const randomData = generateRandomString(32);

  // Create a prefix to identify this as a client secret
  const prefix = 'cs_';

  // Return with the prefix
  return `${prefix}${randomData}`;
}

/**
 * Simple hash function that works in any environment
 * This is a fallback for environments where Web Crypto API is not available
 *
 * @param str The string to hash
 * @returns A simple hash of the string
 */
function simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }

  // Convert to hex string and ensure it's 64 characters long (similar to SHA-256)
  const hashHex = Math.abs(hash).toString(16).padStart(8, '0');
  return hashHex.repeat(8).substring(0, 64);
}

/**
 * Hash a client secret for storage
 * This should be used when storing client secrets in the database
 *
 * @param clientSecret The client secret to hash
 * @returns The hashed client secret
 */
export async function hashClientSecret(clientSecret: string): Promise<string> {
  try {
    // Check if Web Crypto API is available
    if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
      // Convert the client secret to an ArrayBuffer
      const encoder = new TextEncoder();
      const data = encoder.encode(clientSecret);

      // Hash the client secret using SHA-256
      const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);

      // Convert the hash to a hex string
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');

      return hashHex;
    } else {
      // Fallback to simple hash if Web Crypto API is not available
      console.warn('Web Crypto API not available, using fallback hash function');
      return simpleHash(clientSecret);
    }
  } catch (error) {
    // If Web Crypto API fails, use the fallback
    console.error('Error using Web Crypto API:', error);
    console.warn('Falling back to simple hash function');
    return simpleHash(clientSecret);
  }
}

/**
 * Verify a client secret against its hash
 *
 * @param clientSecret The client secret to verify
 * @param hashedSecret The hashed client secret from the database
 * @returns True if the client secret matches the hash
 */
export async function verifyClientSecret(clientSecret: string, hashedSecret: string): Promise<boolean> {
  const hashedInput = await hashClientSecret(clientSecret);
  return hashedInput === hashedSecret;
}
