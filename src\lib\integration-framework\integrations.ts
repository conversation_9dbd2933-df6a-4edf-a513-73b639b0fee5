/**
 * Integrations
 * 
 * This file contains the data for integrations.
 */

import { IntegrationMetadata } from './types';

// Integrations
export const integrations: IntegrationMetadata[] = [
  // OpenAI - ChatGPT
  {
    id: 'chatgpt',
    partnerId: 'openai',
    name: 'ChatGPT',
    description: 'Advanced AI assistant for writing, answering questions, and creative tasks',
    longDescription: 'ChatGPT is an AI language model developed by OpenAI that can engage in conversational dialogue and provide responses that can appear remarkably human. It can answer questions, assist with writing, generate creative content, help with coding, and much more.',
    category: 'ai_writing',
    tags: ['AI', 'Writing', 'Assistant', 'GPT'],
    features: [
      {
        id: 'text-generation',
        name: 'Text Generation',
        description: 'Generate human-like text based on prompts',
        isAvailable: true
      },
      {
        id: 'question-answering',
        name: 'Question Answering',
        description: 'Answer questions with accurate and relevant information',
        isAvailable: true
      },
      {
        id: 'content-summarization',
        name: 'Content Summarization',
        description: 'Summarize long texts into concise summaries',
        isAvailable: true
      },
      {
        id: 'language-translation',
        name: 'Language Translation',
        description: 'Translate text between different languages',
        isAvailable: true
      },
      {
        id: 'code-assistance',
        name: 'Code Assistance',
        description: 'Help with writing and debugging code',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Draft emails and messages',
      'Generate creative content',
      'Answer research questions',
      'Explain complex topics'
    ],
    pricing: {
      metricType: 'time',
      unitName: 'minute',
      costPerUnit: 5,
      minimumUsage: 1
    },
    rating: 4.8,
    reviewCount: 1243,
    popular: true,
    new: false,
    createdAt: new Date('2022-11-30'),
    updatedAt: new Date('2023-09-01')
  },
  
  // OpenAI - DALL-E
  {
    id: 'dalle',
    partnerId: 'openai',
    name: 'DALL-E',
    description: 'OpenAI\'s AI system that creates realistic images and art from text descriptions',
    longDescription: 'DALL-E is an AI system by OpenAI that can create realistic images and art from a text description. It can generate, edit, and manipulate images based on natural language prompts, enabling creative visual content creation without traditional design skills.',
    category: 'image_generation',
    tags: ['AI', 'Art', 'Images', 'Design'],
    features: [
      {
        id: 'text-to-image',
        name: 'Text-to-Image Generation',
        description: 'Generate images from text descriptions',
        isAvailable: true
      },
      {
        id: 'image-editing',
        name: 'Image Editing',
        description: 'Edit existing images with text prompts',
        isAvailable: true
      },
      {
        id: 'variations',
        name: 'Variations Creation',
        description: 'Create variations of existing images',
        isAvailable: true
      },
      {
        id: 'outpainting',
        name: 'Outpainting',
        description: 'Extend images beyond their original boundaries',
        isAvailable: true
      },
      {
        id: 'inpainting',
        name: 'Inpainting',
        description: 'Replace parts of an image with AI-generated content',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Create custom illustrations',
      'Design marketing materials',
      'Generate product mockups',
      'Visualize concepts'
    ],
    pricing: {
      metricType: 'resources',
      unitName: 'image',
      costPerUnit: 8
    },
    rating: 4.7,
    reviewCount: 892,
    popular: true,
    new: false,
    createdAt: new Date('2021-01-05'),
    updatedAt: new Date('2023-08-15')
  },
  
  // Midjourney
  {
    id: 'midjourney',
    partnerId: 'midjourney',
    name: 'Midjourney',
    description: 'AI image generation tool for creating stunning artwork and visualizations',
    longDescription: 'Midjourney is an AI program that generates images from textual descriptions. It can create highly detailed and artistic images based on text prompts, allowing users to visualize concepts, create artwork, and design visual content without traditional artistic skills.',
    category: 'image_generation',
    tags: ['AI', 'Art', 'Design', 'Images'],
    features: [
      {
        id: 'text-to-image',
        name: 'Text-to-Image Generation',
        description: 'Generate images from text descriptions',
        isAvailable: true
      },
      {
        id: 'style-customization',
        name: 'Style Customization',
        description: 'Customize the style of generated images',
        isAvailable: true
      },
      {
        id: 'high-resolution',
        name: 'High-Resolution Output',
        description: 'Generate high-resolution images',
        isAvailable: true
      },
      {
        id: 'variations',
        name: 'Variations Generation',
        description: 'Generate variations of images',
        isAvailable: true
      },
      {
        id: 'community',
        name: 'Community Features',
        description: 'Share and explore images created by the community',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Create concept art',
      'Design marketing materials',
      'Visualize product ideas',
      'Generate artistic content'
    ],
    pricing: {
      metricType: 'resources',
      unitName: 'image',
      costPerUnit: 10
    },
    rating: 4.9,
    reviewCount: 987,
    popular: true,
    new: false,
    createdAt: new Date('2022-03-15'),
    updatedAt: new Date('2023-07-20')
  },
  
  // GitHub - Copilot
  {
    id: 'github-copilot',
    partnerId: 'github',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that helps you write better code faster',
    longDescription: 'GitHub Copilot is an AI pair programmer that offers autocomplete-style suggestions as you code. It helps developers write code faster and with less work by suggesting whole lines or blocks of code as you type, based on the context of what you\'re working on.',
    category: 'code_assistant',
    tags: ['Coding', 'Development', 'Productivity', 'AI'],
    features: [
      {
        id: 'code-completion',
        name: 'Code Completion',
        description: 'Suggest code completions as you type',
        isAvailable: true
      },
      {
        id: 'function-generation',
        name: 'Function Generation',
        description: 'Generate entire functions based on comments',
        isAvailable: true
      },
      {
        id: 'comment-to-code',
        name: 'Comment-to-Code Conversion',
        description: 'Convert comments into code',
        isAvailable: true
      },
      {
        id: 'multi-language',
        name: 'Multiple Language Support',
        description: 'Support for multiple programming languages',
        isAvailable: true
      },
      {
        id: 'ide-integration',
        name: 'IDE Integration',
        description: 'Integration with popular IDEs',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Generate boilerplate code',
      'Implement algorithms',
      'Debug existing code',
      'Learn new programming languages'
    ],
    pricing: {
      metricType: 'time',
      unitName: 'hour',
      costPerUnit: 15,
      minimumUsage: 1
    },
    rating: 4.7,
    reviewCount: 856,
    popular: true,
    new: false,
    createdAt: new Date('2021-06-29'),
    updatedAt: new Date('2023-05-10')
  },
  
  // Descript
  {
    id: 'descript',
    partnerId: 'descript',
    name: 'Descript',
    description: 'All-in-one audio and video editing with AI transcription and editing',
    longDescription: 'Descript is an all-in-one audio/video editing platform that allows you to edit media as easily as a text document. It features AI-powered transcription, voice cloning, screen recording, publishing, and collaboration tools.',
    category: 'video_creation',
    tags: ['Video', 'Audio', 'Editing', 'Transcription'],
    features: [
      {
        id: 'ai-transcription',
        name: 'AI Transcription',
        description: 'Automatically transcribe audio and video',
        isAvailable: true
      },
      {
        id: 'text-based-editing',
        name: 'Text-Based Editing',
        description: 'Edit audio and video by editing text',
        isAvailable: true
      },
      {
        id: 'voice-cloning',
        name: 'Voice Cloning',
        description: 'Create a synthetic version of your voice',
        isAvailable: true
      },
      {
        id: 'screen-recording',
        name: 'Screen Recording',
        description: 'Record your screen with audio',
        isAvailable: true
      },
      {
        id: 'collaboration',
        name: 'Collaboration Tools',
        description: 'Collaborate with others on projects',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Create and edit podcasts',
      'Produce video content',
      'Generate accurate transcripts',
      'Collaborate on media projects'
    ],
    pricing: {
      metricType: 'time',
      unitName: 'minute',
      costPerUnit: 8
    },
    rating: 4.6,
    reviewCount: 542,
    popular: false,
    new: false,
    createdAt: new Date('2019-12-10'),
    updatedAt: new Date('2023-04-05')
  },
  
  // Jasper
  {
    id: 'jasper',
    partnerId: 'jasper',
    name: 'Jasper',
    description: 'AI content platform for marketing teams to create content that converts',
    longDescription: 'Jasper is an AI content creation platform designed for marketing teams. It helps create high-quality, SEO-optimized content for blogs, social media, emails, and ads. Jasper can write in different tones and styles while maintaining brand consistency.',
    category: 'ai_writing',
    tags: ['Marketing', 'Content', 'SEO', 'Writing'],
    features: [
      {
        id: 'long-form-content',
        name: 'Long-Form Content Creation',
        description: 'Create long-form content like blog posts and articles',
        isAvailable: true
      },
      {
        id: 'seo-optimization',
        name: 'SEO Optimization',
        description: 'Optimize content for search engines',
        isAvailable: true
      },
      {
        id: 'template-library',
        name: 'Template Library',
        description: 'Access a library of content templates',
        isAvailable: true
      },
      {
        id: 'brand-voice',
        name: 'Brand Voice Customization',
        description: 'Customize the tone and style of content',
        isAvailable: true
      },
      {
        id: 'team-collaboration',
        name: 'Team Collaboration',
        description: 'Collaborate with team members on content',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Write blog posts',
      'Create marketing copy',
      'Generate social media content',
      'Produce email campaigns'
    ],
    pricing: {
      metricType: 'resources',
      unitName: 'word',
      costPerUnit: 0.1
    },
    rating: 4.5,
    reviewCount: 723,
    popular: false,
    new: false,
    createdAt: new Date('2021-01-20'),
    updatedAt: new Date('2023-03-15')
  },
  
  // Runway
  {
    id: 'runway',
    partnerId: 'runway',
    name: 'Runway',
    description: 'AI video generation and editing with state-of-the-art models',
    longDescription: 'Runway is an applied AI research company that creates next-generation creation tools. Their platform offers text-to-video generation, image-to-video, inpainting, outpainting, and various other AI-powered video creation and editing tools.',
    category: 'video_creation',
    tags: ['Video', 'AI', 'Generation', 'Editing'],
    features: [
      {
        id: 'text-to-video',
        name: 'Text-to-Video Generation',
        description: 'Generate videos from text descriptions',
        isAvailable: true
      },
      {
        id: 'image-to-video',
        name: 'Image-to-Video',
        description: 'Convert images to videos',
        isAvailable: true
      },
      {
        id: 'video-editing',
        name: 'Video Editing',
        description: 'Edit videos with AI assistance',
        isAvailable: true
      },
      {
        id: 'green-screen',
        name: 'Green Screen Removal',
        description: 'Remove green screen backgrounds from videos',
        isAvailable: true
      },
      {
        id: 'motion-tracking',
        name: 'Motion Tracking',
        description: 'Track motion in videos',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Create video content from text',
      'Edit videos with AI assistance',
      'Remove backgrounds from videos',
      'Generate visual effects'
    ],
    pricing: {
      metricType: 'time',
      unitName: 'minute',
      costPerUnit: 12
    },
    rating: 4.7,
    reviewCount: 389,
    popular: false,
    new: true,
    createdAt: new Date('2022-09-05'),
    updatedAt: new Date('2023-08-20')
  },
  
  // Notion AI
  {
    id: 'notion-ai',
    partnerId: 'notion',
    name: 'Notion AI',
    description: 'AI writing assistant integrated with Notion for better notes and documents',
    longDescription: 'Notion AI is an AI writing assistant built directly into Notion. It helps users draft, edit, summarize, improve, and generate content within their Notion workspace, making note-taking and document creation more efficient and powerful.',
    category: 'productivity',
    tags: ['Productivity', 'Writing', 'Notes', 'Organization'],
    features: [
      {
        id: 'content-generation',
        name: 'Content Generation',
        description: 'Generate content based on prompts',
        isAvailable: true
      },
      {
        id: 'summarization',
        name: 'Summarization',
        description: 'Summarize long documents',
        isAvailable: true
      },
      {
        id: 'translation',
        name: 'Translation',
        description: 'Translate text between languages',
        isAvailable: true
      },
      {
        id: 'editing-assistance',
        name: 'Editing Assistance',
        description: 'Get help with editing and improving text',
        isAvailable: true
      },
      {
        id: 'notion-integration',
        name: 'Notion Integration',
        description: 'Seamless integration with Notion',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Draft meeting notes',
      'Summarize long documents',
      'Improve writing clarity',
      'Generate content ideas'
    ],
    pricing: {
      metricType: 'resources',
      unitName: 'page',
      costPerUnit: 5
    },
    rating: 4.6,
    reviewCount: 412,
    popular: false,
    new: true,
    createdAt: new Date('2022-11-15'),
    updatedAt: new Date('2023-07-10')
  },
  
  // Claude
  {
    id: 'claude',
    partnerId: 'anthropic',
    name: 'Claude',
    description: 'Anthropic\'s AI assistant for helpful, harmless, and honest conversations',
    longDescription: 'Claude is an AI assistant created by Anthropic, designed to be helpful, harmless, and honest. It can engage in natural conversations, answer questions, assist with writing tasks, and provide information on a wide range of topics with a focus on safety and accuracy.',
    category: 'ai_writing',
    tags: ['AI', 'Assistant', 'Writing', 'Research'],
    features: [
      {
        id: 'natural-conversation',
        name: 'Natural Conversation',
        description: 'Engage in natural, human-like conversations',
        isAvailable: true
      },
      {
        id: 'content-generation',
        name: 'Content Generation',
        description: 'Generate various types of content',
        isAvailable: true
      },
      {
        id: 'question-answering',
        name: 'Question Answering',
        description: 'Answer questions accurately and helpfully',
        isAvailable: true
      },
      {
        id: 'summarization',
        name: 'Summarization',
        description: 'Summarize long texts',
        isAvailable: true
      },
      {
        id: 'research-assistance',
        name: 'Research Assistance',
        description: 'Assist with research tasks',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Research complex topics',
      'Draft professional content',
      'Get detailed explanations',
      'Brainstorm ideas'
    ],
    pricing: {
      metricType: 'time',
      unitName: 'minute',
      costPerUnit: 6
    },
    rating: 4.8,
    reviewCount: 675,
    popular: true,
    new: true,
    createdAt: new Date('2022-12-15'),
    updatedAt: new Date('2023-09-01')
  },
  
  // Stable Diffusion
  {
    id: 'stable-diffusion',
    partnerId: 'stability-ai',
    name: 'Stable Diffusion',
    description: 'Open-source AI image generation model for creating detailed images from text',
    longDescription: 'Stable Diffusion is an open-source AI model that generates detailed images based on text descriptions. It can be run locally or via cloud services, offering flexibility for developers and creators to generate high-quality images for various applications.',
    category: 'image_generation',
    tags: ['AI', 'Open Source', 'Images', 'Generation'],
    features: [
      {
        id: 'text-to-image',
        name: 'Text-to-Image Generation',
        description: 'Generate images from text descriptions',
        isAvailable: true
      },
      {
        id: 'image-to-image',
        name: 'Image-to-Image Transformation',
        description: 'Transform existing images based on prompts',
        isAvailable: true
      },
      {
        id: 'inpainting',
        name: 'Inpainting',
        description: 'Replace parts of an image with AI-generated content',
        isAvailable: true
      },
      {
        id: 'outpainting',
        name: 'Outpainting',
        description: 'Extend images beyond their original boundaries',
        isAvailable: true
      },
      {
        id: 'custom-training',
        name: 'Custom Model Training',
        description: 'Train custom models on specific datasets',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Generate art and illustrations',
      'Create design assets',
      'Produce concept visualizations',
      'Develop custom image generators'
    ],
    pricing: {
      metricType: 'resources',
      unitName: 'image',
      costPerUnit: 5
    },
    rating: 4.6,
    reviewCount: 763,
    popular: true,
    new: false,
    createdAt: new Date('2022-08-22'),
    updatedAt: new Date('2023-06-15')
  },
  
  // Grammarly
  {
    id: 'grammarly',
    partnerId: 'grammarly',
    name: 'Grammarly',
    description: 'AI-powered writing assistant that helps with grammar, clarity, and style',
    longDescription: 'Grammarly is an AI-powered writing assistant that helps users improve their writing by checking grammar, spelling, punctuation, clarity, engagement, and delivery mistakes. It works across various platforms and applications to enhance writing quality in real-time.',
    category: 'ai_writing',
    tags: ['Writing', 'Grammar', 'Productivity', 'Editing'],
    features: [
      {
        id: 'grammar-checking',
        name: 'Grammar Checking',
        description: 'Check grammar, spelling, and punctuation',
        isAvailable: true
      },
      {
        id: 'style-suggestions',
        name: 'Style Suggestions',
        description: 'Get suggestions for improving writing style',
        isAvailable: true
      },
      {
        id: 'tone-detection',
        name: 'Tone Detection',
        description: 'Detect and adjust the tone of writing',
        isAvailable: true
      },
      {
        id: 'plagiarism-detection',
        name: 'Plagiarism Detection',
        description: 'Check for plagiarism in writing',
        isAvailable: true
      },
      {
        id: 'browser-extension',
        name: 'Browser Extension',
        description: 'Use Grammarly in web browsers',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Edit professional documents',
      'Improve email communication',
      'Enhance academic writing',
      'Polish creative content'
    ],
    pricing: {
      metricType: 'time',
      unitName: 'month',
      costPerUnit: 30,
      minimumUsage: 1
    },
    rating: 4.8,
    reviewCount: 1542,
    popular: true,
    new: false,
    createdAt: new Date('2009-08-01'),
    updatedAt: new Date('2023-05-20')
  },
  
  // Synthesia
  {
    id: 'synthesia',
    partnerId: 'synthesia',
    name: 'Synthesia',
    description: 'AI video generation platform that creates videos with virtual avatars',
    longDescription: 'Synthesia is an AI video generation platform that allows users to create professional-looking videos with virtual avatars. Users can type in a script and select from various AI avatars to create videos in multiple languages without needing cameras, microphones, or actors.',
    category: 'video_creation',
    tags: ['Video', 'Avatars', 'Presentation', 'Marketing'],
    features: [
      {
        id: 'ai-avatars',
        name: 'AI Avatars',
        description: 'Use virtual avatars in videos',
        isAvailable: true
      },
      {
        id: 'text-to-speech',
        name: 'Text-to-Speech',
        description: 'Convert text to speech for videos',
        isAvailable: true
      },
      {
        id: 'multiple-languages',
        name: 'Multiple Languages',
        description: 'Create videos in multiple languages',
        isAvailable: true
      },
      {
        id: 'custom-backgrounds',
        name: 'Custom Backgrounds',
        description: 'Use custom backgrounds in videos',
        isAvailable: true
      },
      {
        id: 'screen-recording',
        name: 'Screen Recording Integration',
        description: 'Integrate screen recordings into videos',
        isAvailable: true
      }
    ],
    usageExamples: [
      'Create training videos',
      'Produce multilingual content',
      'Generate product demonstrations',
      'Develop educational materials'
    ],
    pricing: {
      metricType: 'resources',
      unitName: 'video',
      costPerUnit: 25
    },
    rating: 4.5,
    reviewCount: 328,
    popular: false,
    new: true,
    createdAt: new Date('2021-03-10'),
    updatedAt: new Date('2023-08-05')
  }
];
