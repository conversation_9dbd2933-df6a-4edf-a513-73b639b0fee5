/**
 * Projects Management Page
 * SOC 2 Alignment: CC6.1 (Logical Access), CC6.3 (Access Revocation)
 */

import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate, useParams } from 'react-router-dom'
import {
  FileText,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowLeft,
  Calendar,
  User,
  Building2,
  BarChart3,
  Upload,
  Settings,
  Share2,
  FolderOpen
} from 'lucide-react'
import { projectsApi, workspacesApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatDate } from '@/lib/utils'
import toast from 'react-hot-toast'

// Create Project Modal Component
interface CreateProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

function CreateProjectModal({ isOpen, onClose, onSuccess }: CreateProjectModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    workspaceId: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch workspaces for dropdown
  const { data: workspaces } = useQuery({
    queryKey: ['workspaces'],
    queryFn: async () => {
      const response = await workspacesApi.list({ limit: 50 })
      return response.data
    },
    enabled: isOpen
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim() || !formData.workspaceId) {
      toast.error('Project name and workspace are required')
      return
    }

    setIsSubmitting(true)
    try {
      await projectsApi.create(formData)
      toast.success('Project created successfully!')
      onSuccess()
      onClose()
      setFormData({ name: '', description: '', workspaceId: '' })
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to create project')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-sidebar-background border border-white/10 rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-white mb-4">Create New Project</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Project Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              placeholder="Enter project name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Workspace *
            </label>
            <select
              value={formData.workspaceId}
              onChange={(e) => setFormData({ ...formData, workspaceId: e.target.value })}
              className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white"
              required
            >
              <option value="">Select a workspace</option>
              {workspaces?.data?.map((workspace: any) => (
                <option key={workspace.id} value={workspace.id}>
                  {workspace.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full bg-background border border-white/10 rounded-lg px-3 py-2 text-white placeholder-gray-400 h-24 resize-none"
              placeholder="Describe your project"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="bg-fiery hover:bg-fiery/90">
              {isSubmitting ? <LoadingSpinner size="sm" /> : 'Create Project'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Project Status Badge Component
function ProjectStatusBadge({ status }: { status: string }) {
  const statusConfig = {
    ACTIVE: { variant: 'success' as const, icon: CheckCircle, label: 'Active' },
    PLANNING: { variant: 'info' as const, icon: Clock, label: 'Planning' },
    ON_HOLD: { variant: 'warning' as const, icon: Pause, label: 'On Hold' },
    COMPLETE: { variant: 'success' as const, icon: CheckCircle, label: 'Complete' },
    CANCELLED: { variant: 'destructive' as const, icon: AlertCircle, label: 'Cancelled' }
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PLANNING
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className="flex items-center">
      <Icon className="w-3 h-3 mr-1" />
      {config.label}
    </Badge>
  )
}

// Project Card Component
interface ProjectCardProps {
  project: any
  onDelete: () => void
  onClick: () => void
}

function ProjectCard({ project, onDelete, onClick }: ProjectCardProps) {
  const [showMenu, setShowMenu] = useState(false)

  return (
    <div className="firenest-card hover:bg-white/5 transition-colors cursor-pointer relative">
      <div onClick={onClick} className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <FolderOpen className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">{project.name}</h3>
              <p className="text-sm text-gray-400">{project.workspace_name}</p>
            </div>
          </div>

          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                setShowMenu(!showMenu)
              }}
            >
              <MoreVertical className="w-4 h-4" />
            </Button>

            {showMenu && (
              <div className="absolute right-0 top-8 bg-sidebar-background border border-white/10 rounded-lg shadow-lg z-10 min-w-[150px]">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onClick()
                    setShowMenu(false)
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white/5 flex items-center"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onDelete()
                    setShowMenu(false)
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-red-400 hover:bg-red-500/10 flex items-center"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {project.description && (
          <p className="text-gray-400 text-sm mb-4 line-clamp-2">
            {project.description}
          </p>
        )}

        <div className="flex items-center justify-between">
          <ProjectStatusBadge status={project.status} />
          <span className="text-gray-500 text-sm">
            {formatDate(project.updated_at)}
          </span>
        </div>
      </div>
    </div>
  )
}

export function ProjectsPage() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Fetch projects
  const { data: projects, isLoading, error } = useQuery({
    queryKey: ['projects', statusFilter, searchTerm],
    queryFn: async () => {
      const params: any = { limit: 50 }
      if (statusFilter !== 'all') params.status = statusFilter
      if (searchTerm) params.search = searchTerm

      const response = await projectsApi.list(params)
      return response.data
    }
  })

  // Delete project mutation
  const deleteMutation = useMutation({
    mutationFn: async (projectId: string) => {
      const response = await projectsApi.delete(projectId)
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Project deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete project')
    }
  })

  const handleCreateSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['projects'] })
  }

  const handleDeleteProject = async (projectId: string, projectName: string) => {
    if (window.confirm(`Are you sure you want to delete "${projectName}"? This action cannot be undone.`)) {
      deleteMutation.mutate(projectId)
    }
  }

  // Filter projects based on search and status
  const filteredProjects = projects?.data || []

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="main-content">
        <div className="text-center py-12">
          <FolderOpen className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Error Loading Projects</h2>
          <p className="text-gray-400 mb-4">Failed to load projects. Please try again.</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Projects</h1>
          <p className="text-gray-400 mt-1">
            Manage your pricing intelligence projects and collaborate with your team
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="bg-fiery hover:bg-fiery/90"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Project
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-sidebar-background border border-white/10 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="bg-sidebar-background border border-white/10 rounded-lg px-3 py-2 text-white"
        >
          <option value="all">All Status</option>
          <option value="ACTIVE">Active</option>
          <option value="PLANNING">Planning</option>
          <option value="ON_HOLD">On Hold</option>
          <option value="COMPLETE">Complete</option>
        </select>
      </div>

      {/* Projects Grid */}
      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <FolderOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">
            {searchTerm || statusFilter !== 'all' ? 'No projects found' : 'No projects yet'}
          </h3>
          <p className="text-gray-400 mb-6">
            {searchTerm || statusFilter !== 'all'
              ? 'Try adjusting your search terms or filters'
              : 'Create your first project to start building pricing intelligence models'
            }
          </p>
          {!searchTerm && statusFilter === 'all' && (
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-fiery hover:bg-fiery/90"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Project
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project: any) => (
            <ProjectCard
              key={project.id}
              project={project}
              onDelete={() => handleDeleteProject(project.id, project.name)}
              onClick={() => navigate(`/projects/${project.id}`)}
            />
          ))}
        </div>
      )}

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
      />
    </div>
  )
}

export function ProjectDetailPage() {
  const { projectId } = useParams<{ projectId: string }>()
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  // Fetch project details
  const { data: project, isLoading, error } = useQuery({
    queryKey: ['project', projectId],
    queryFn: async () => {
      if (!projectId) throw new Error('Project ID is required')
      const response = await projectsApi.get(projectId)
      return response.data.data
    },
    enabled: !!projectId
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="main-content">
        <div className="text-center py-12">
          <FolderOpen className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Project Not Found</h2>
          <p className="text-gray-400 mb-4">The project you're looking for doesn't exist or you don't have access to it.</p>
          <Button onClick={() => navigate('/projects')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="main-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/projects')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-white">{project.name}</h1>
            <p className="text-gray-400 mt-1">{project.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <ProjectStatusBadge status={project.status} />
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Upload Data
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Project Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <Upload className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">0</p>
              <p className="text-gray-400">Data Files</p>
            </div>
          </div>
        </div>

        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">0</p>
              <p className="text-gray-400">Models</p>
            </div>
          </div>
        </div>

        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Play className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">0</p>
              <p className="text-gray-400">Simulations</p>
            </div>
          </div>
        </div>

        <div className="firenest-card">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-fiery/20 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-fiery" />
            </div>
            <div>
              <p className="text-2xl font-bold text-white">{formatDate(project.created_at)}</p>
              <p className="text-gray-400">Created</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="firenest-card hover:bg-white/5 transition-colors cursor-pointer" onClick={() => navigate(`/projects/${projectId}/upload`)}>
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Upload className="w-8 h-8 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Upload Data</h3>
            <p className="text-gray-400 text-sm">Upload CSV files and datasets for analysis</p>
          </div>
        </div>

        <div className="firenest-card hover:bg-white/5 transition-colors cursor-pointer" onClick={() => navigate(`/projects/${projectId}/models`)}>
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="w-8 h-8 text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Build Models</h3>
            <p className="text-gray-400 text-sm">Create pricing models and configure parameters</p>
          </div>
        </div>

        <div className="firenest-card hover:bg-white/5 transition-colors cursor-pointer" onClick={() => navigate(`/projects/${projectId}/simulations`)}>
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Play className="w-8 h-8 text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Run Simulations</h3>
            <p className="text-gray-400 text-sm">Execute pricing simulations and analyze results</p>
          </div>
        </div>
      </div>
    </div>
  )
}
