-- Firenest Credit System - Production-Ready SQL Functions
-- This file contains the final, production-ready implementation of the credit system functions
-- These functions follow industry standards for financial transactions and are suitable for use with real payments

-- Add necessary extensions if not already added
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Function to add bonus credits
CREATE OR REPLACE FUNCTION add_bonus_credits(
  user_id UUID,
  amount INTEGER,
  description TEXT DEFAULT NULL,
  idempotency_key TEXT DEFAULT NULL,
  modified_by UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  transaction_id UUID;
  current_credits INTEGER;
  new_total_credits INTEGER;
  result JSON;
  existing_transaction UUID;
BEGIN
  -- Input validation
  IF amount <= 0 THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Credit amount must be greater than 0'
    );
  END IF;
  
  -- Check for existing transaction with this idempotency key
  IF idempotency_key IS NOT NULL THEN
    SELECT id INTO existing_transaction
    FROM credit_transactions
    WHERE credit_transactions.idempotency_key = add_bonus_credits.idempotency_key;
    
    IF existing_transaction IS NOT NULL THEN
      -- Return success with existing transaction ID to prevent duplicate processing
      RETURN json_build_object(
        'success', TRUE,
        'data', json_build_object(
          'transaction_id', existing_transaction,
          'message', 'Transaction already processed',
          'idempotent', TRUE
        )
      );
    END IF;
  END IF;

  -- Generate transaction ID
  transaction_id := uuid_generate_v4();

  -- Get current credits
  SELECT total_credits INTO current_credits
  FROM user_credits
  WHERE user_credits.user_id = add_bonus_credits.user_id;

  -- If user doesn't have a credit record, create one
  IF current_credits IS NULL THEN
    INSERT INTO user_credits (user_id, total_credits, used_credits)
    VALUES (add_bonus_credits.user_id, 0, 0)
    RETURNING total_credits INTO current_credits;
  END IF;

  -- Calculate new total
  new_total_credits := current_credits + amount;

  -- Update user_credits
  UPDATE user_credits
  SET 
    total_credits = new_total_credits,
    updated_at = NOW()
  WHERE user_credits.user_id = add_bonus_credits.user_id;

  -- Create transaction record
  INSERT INTO credit_transactions (
    id,
    user_id,
    amount,
    description,
    transaction_type,
    idempotency_key,
    modified_by,
    created_at
  )
  VALUES (
    transaction_id,
    add_bonus_credits.user_id,
    amount,
    COALESCE(description, format('Bonus: %s credits', amount)),
    'bonus',
    add_bonus_credits.idempotency_key,
    COALESCE(modified_by, add_bonus_credits.user_id),
    NOW()
  );

  -- Return success with new credit balance
  RETURN json_build_object(
    'success', TRUE,
    'data', json_build_object(
      'transaction_id', transaction_id,
      'new_total_credits', new_total_credits,
      'new_available_credits', new_total_credits - COALESCE((SELECT used_credits FROM user_credits WHERE user_credits.user_id = add_bonus_credits.user_id), 0),
      'idempotent', FALSE
    )
  );
EXCEPTION WHEN OTHERS THEN
  -- Return error information
  RETURN json_build_object(
    'success', FALSE,
    'error', SQLERRM,
    'error_detail', SQLSTATE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to purchase credits
CREATE OR REPLACE FUNCTION purchase_credits(
  user_id UUID,
  amount INTEGER,
  description TEXT DEFAULT NULL,
  payment_method TEXT DEFAULT 'card',
  payment_reference TEXT DEFAULT NULL,
  payment_amount DECIMAL(10, 2) DEFAULT NULL,
  payment_currency TEXT DEFAULT 'USD',
  idempotency_key TEXT DEFAULT NULL,
  modified_by UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  transaction_id UUID;
  current_credits INTEGER;
  new_total_credits INTEGER;
  result JSON;
  existing_transaction UUID;
BEGIN
  -- Input validation
  IF amount <= 0 THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Credit amount must be greater than 0'
    );
  END IF;
  
  IF payment_amount IS NOT NULL AND payment_amount <= 0 THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Payment amount must be greater than 0'
    );
  END IF;
  
  -- Check for existing transaction with this idempotency key
  IF idempotency_key IS NOT NULL THEN
    SELECT id INTO existing_transaction
    FROM credit_transactions
    WHERE credit_transactions.idempotency_key = purchase_credits.idempotency_key;
    
    IF existing_transaction IS NOT NULL THEN
      -- Return success with existing transaction ID to prevent duplicate processing
      RETURN json_build_object(
        'success', TRUE,
        'data', json_build_object(
          'transaction_id', existing_transaction,
          'message', 'Transaction already processed',
          'idempotent', TRUE
        )
      );
    END IF;
  END IF;

  -- Generate transaction ID
  transaction_id := uuid_generate_v4();

  -- Get current credits
  SELECT total_credits INTO current_credits
  FROM user_credits
  WHERE user_credits.user_id = purchase_credits.user_id;

  -- If user doesn't have a credit record, create one
  IF current_credits IS NULL THEN
    INSERT INTO user_credits (user_id, total_credits, used_credits)
    VALUES (purchase_credits.user_id, 0, 0)
    RETURNING total_credits INTO current_credits;
  END IF;

  -- Calculate new total
  new_total_credits := current_credits + amount;

  -- Update user_credits
  UPDATE user_credits
  SET 
    total_credits = new_total_credits,
    updated_at = NOW()
  WHERE user_credits.user_id = purchase_credits.user_id;

  -- Create transaction record
  INSERT INTO credit_transactions (
    id,
    user_id,
    amount,
    description,
    transaction_type,
    payment_method,
    payment_reference,
    payment_amount,
    payment_currency,
    idempotency_key,
    modified_by,
    created_at
  )
  VALUES (
    transaction_id,
    purchase_credits.user_id,
    amount,
    COALESCE(description, 'Credit purchase'),
    'purchase',
    payment_method,
    payment_reference,
    payment_amount,
    payment_currency,
    purchase_credits.idempotency_key,
    COALESCE(modified_by, purchase_credits.user_id),
    NOW()
  );

  -- Return success with new credit balance
  RETURN json_build_object(
    'success', TRUE,
    'data', json_build_object(
      'transaction_id', transaction_id,
      'new_total_credits', new_total_credits,
      'new_available_credits', new_total_credits - COALESCE((SELECT used_credits FROM user_credits WHERE user_credits.user_id = purchase_credits.user_id), 0),
      'idempotent', FALSE
    )
  );
EXCEPTION WHEN OTHERS THEN
  -- Return error information
  RETURN json_build_object(
    'success', FALSE,
    'error', SQLERRM,
    'error_detail', SQLSTATE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to use credits
CREATE OR REPLACE FUNCTION use_credits(
  user_id UUID,
  amount INTEGER,
  service_id TEXT DEFAULT NULL,
  description TEXT DEFAULT NULL,
  idempotency_key TEXT DEFAULT NULL,
  modified_by UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  transaction_id UUID;
  current_total_credits INTEGER;
  current_used_credits INTEGER;
  available_credits INTEGER;
  new_used_credits INTEGER;
  result JSON;
  existing_transaction UUID;
BEGIN
  -- Input validation
  IF amount <= 0 THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Credit amount must be greater than 0'
    );
  END IF;
  
  -- Check for existing transaction with this idempotency key
  IF idempotency_key IS NOT NULL THEN
    SELECT id INTO existing_transaction
    FROM credit_transactions
    WHERE credit_transactions.idempotency_key = use_credits.idempotency_key;
    
    IF existing_transaction IS NOT NULL THEN
      -- Return success with existing transaction ID to prevent duplicate processing
      RETURN json_build_object(
        'success', TRUE,
        'data', json_build_object(
          'transaction_id', existing_transaction,
          'message', 'Transaction already processed',
          'idempotent', TRUE
        )
      );
    END IF;
  END IF;

  -- Generate transaction ID
  transaction_id := uuid_generate_v4();

  -- Get current credits
  SELECT total_credits, used_credits 
  INTO current_total_credits, current_used_credits
  FROM user_credits
  WHERE user_credits.user_id = use_credits.user_id;

  -- If user doesn't have a credit record, return error
  IF current_total_credits IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'User has no credit record'
    );
  END IF;

  -- Calculate available credits
  available_credits := current_total_credits - current_used_credits;

  -- Check if user has enough credits
  IF available_credits < amount THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', format('Not enough credits. Available: %s, Required: %s', available_credits, amount)
    );
  END IF;

  -- Calculate new used credits
  new_used_credits := current_used_credits + amount;

  -- Update user_credits
  UPDATE user_credits
  SET 
    used_credits = new_used_credits,
    updated_at = NOW()
  WHERE user_credits.user_id = use_credits.user_id;

  -- Create transaction record
  INSERT INTO credit_transactions (
    id,
    user_id,
    amount,
    description,
    transaction_type,
    service_id,
    idempotency_key,
    modified_by,
    created_at
  )
  VALUES (
    transaction_id,
    use_credits.user_id,
    -amount, -- Negative amount for usage
    COALESCE(description, format('Used %s credits', amount)),
    'usage',
    service_id,
    use_credits.idempotency_key,
    COALESCE(modified_by, use_credits.user_id),
    NOW()
  );

  -- Return success with new credit balance
  RETURN json_build_object(
    'success', TRUE,
    'data', json_build_object(
      'transaction_id', transaction_id,
      'new_total_credits', current_total_credits,
      'new_available_credits', current_total_credits - new_used_credits,
      'idempotent', FALSE
    )
  );
EXCEPTION WHEN OTHERS THEN
  -- Return error information
  RETURN json_build_object(
    'success', FALSE,
    'error', SQLERRM,
    'error_detail', SQLSTATE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to refund credits
CREATE OR REPLACE FUNCTION refund_credits(
  user_id UUID,
  amount INTEGER,
  description TEXT DEFAULT NULL,
  idempotency_key TEXT DEFAULT NULL,
  modified_by UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  transaction_id UUID;
  current_total_credits INTEGER;
  current_used_credits INTEGER;
  new_used_credits INTEGER;
  result JSON;
  existing_transaction UUID;
BEGIN
  -- Input validation
  IF amount <= 0 THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Credit amount must be greater than 0'
    );
  END IF;
  
  -- Check for existing transaction with this idempotency key
  IF idempotency_key IS NOT NULL THEN
    SELECT id INTO existing_transaction
    FROM credit_transactions
    WHERE credit_transactions.idempotency_key = refund_credits.idempotency_key;
    
    IF existing_transaction IS NOT NULL THEN
      -- Return success with existing transaction ID to prevent duplicate processing
      RETURN json_build_object(
        'success', TRUE,
        'data', json_build_object(
          'transaction_id', existing_transaction,
          'message', 'Transaction already processed',
          'idempotent', TRUE
        )
      );
    END IF;
  END IF;

  -- Generate transaction ID
  transaction_id := uuid_generate_v4();

  -- Get current credits
  SELECT total_credits, used_credits 
  INTO current_total_credits, current_used_credits
  FROM user_credits
  WHERE user_credits.user_id = refund_credits.user_id;

  -- If user doesn't have a credit record, return error
  IF current_total_credits IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'User has no credit record'
    );
  END IF;

  -- Calculate new used credits (ensure it doesn't go below 0)
  new_used_credits := GREATEST(0, current_used_credits - amount);

  -- Update user_credits
  UPDATE user_credits
  SET 
    used_credits = new_used_credits,
    updated_at = NOW()
  WHERE user_credits.user_id = refund_credits.user_id;

  -- Create transaction record
  INSERT INTO credit_transactions (
    id,
    user_id,
    amount,
    description,
    transaction_type,
    idempotency_key,
    modified_by,
    created_at
  )
  VALUES (
    transaction_id,
    refund_credits.user_id,
    amount,
    COALESCE(description, format('Refund: %s credits', amount)),
    'refund',
    refund_credits.idempotency_key,
    COALESCE(modified_by, refund_credits.user_id),
    NOW()
  );

  -- Return success with new credit balance
  RETURN json_build_object(
    'success', TRUE,
    'data', json_build_object(
      'transaction_id', transaction_id,
      'new_total_credits', current_total_credits,
      'new_available_credits', current_total_credits - new_used_credits,
      'idempotent', FALSE
    )
  );
EXCEPTION WHEN OTHERS THEN
  -- Return error information
  RETURN json_build_object(
    'success', FALSE,
    'error', SQLERRM,
    'error_detail', SQLSTATE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create RLS policies for the functions
-- Allow users to call these functions for their own user_id
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can purchase their own credits" ON purchase_credits;
  DROP POLICY IF EXISTS "Users can use their own credits" ON use_credits;
  DROP POLICY IF EXISTS "Users can receive bonus credits" ON add_bonus_credits;
  DROP POLICY IF EXISTS "Users can refund their own credits" ON refund_credits;
  DROP POLICY IF EXISTS "Users can insert their own transactions" ON credit_transactions;
  DROP POLICY IF EXISTS "Users can update their own credits" ON user_credits;
  DROP POLICY IF EXISTS "Users can view their own transactions" ON credit_transactions;
  
  -- Create new policies
  CREATE POLICY "Users can purchase their own credits" 
    ON purchase_credits
    FOR ALL
    USING (auth.uid() = purchase_credits.user_id);

  CREATE POLICY "Users can use their own credits" 
    ON use_credits
    FOR ALL
    USING (auth.uid() = use_credits.user_id);

  CREATE POLICY "Users can receive bonus credits" 
    ON add_bonus_credits
    FOR ALL
    USING (auth.uid() = add_bonus_credits.user_id);

  CREATE POLICY "Users can refund their own credits" 
    ON refund_credits
    FOR ALL
    USING (auth.uid() = refund_credits.user_id);

  -- Create policy for inserting into credit_transactions
  CREATE POLICY "Users can insert their own transactions" 
    ON credit_transactions FOR INSERT 
    WITH CHECK (auth.uid() = credit_transactions.user_id);

  -- Create policy for updating user_credits
  CREATE POLICY "Users can update their own credits" 
    ON user_credits FOR UPDATE
    USING (auth.uid() = user_credits.user_id);
    
  -- Create policy for viewing own transactions
  CREATE POLICY "Users can view their own transactions" 
    ON credit_transactions FOR SELECT 
    USING (auth.uid() = credit_transactions.user_id);
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Error creating policies: %', SQLERRM;
END
$$;
