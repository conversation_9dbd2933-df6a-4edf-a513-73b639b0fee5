import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { ThemedBadge as Badge } from '@/components/ui/badge';
import { notify } from '@/components/ui/notification-system';
import ConnectedApps from '@/components/dashboard/ConnectedApps';
import {
  User,
  Building,
  Globe,
  Phone,
  Briefcase,
  Loader2,
  Shield,
  Bell,
  Mail,
  CreditCard,
  Key,
  Lock,
  LogOut,
  Save,
  Trash2,
  MapPin,
  CheckCircle
} from 'lucide-react';
import { UserProfile } from '@/lib/auth';

/**
 * Enhanced Profile Settings Page with professional design patterns
 * Inspired by industry leaders like Zapier and HubSpot
 */
const NewProfileSettings = () => {
  const { user, profile, updateProfile, logout } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const location = useLocation();
  const navigate = useNavigate();

  // Parse tab from URL query parameter and update URL when tab changes
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    if (tabParam && ['profile', 'notifications', 'security', 'billing'].includes(tabParam)) {
      setActiveTab(tabParam);
      console.log('Setting active tab from URL:', tabParam);
    }
  }, [location.search]);

  // Update the URL when the tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    console.log('Tab changed to:', value);

    // Update the URL without causing a full page reload
    navigate(`/dashboard/settings?tab=${value}`, { replace: true });
  };

  const [formData, setFormData] = useState<Partial<UserProfile>>({
    company: profile?.company || '',
    jobTitle: profile?.jobTitle || '',
    bio: profile?.bio || '',
    website: profile?.website || '',
    phone: profile?.phone || '',
    country: profile?.country || '',
  });

  // Notification settings (mock data)
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    creditAlerts: true,
    productUpdates: true,
    marketingEmails: false,
    securityAlerts: true
  });

  // Security settings (mock data)
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: '30',
    loginNotifications: true
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log('Submitting profile data:', formData);
      const result = await updateProfile(formData);
      if (!result.success) {
        throw new Error('Failed to update profile');
      }
      notify.success('Profile updated successfully', {
        title: 'Profile Updated',
        duration: 4000
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      notify.error('Failed to update profile', {
        title: 'Update Failed',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSecurityChange = (key: string, value: any) => {
    setSecuritySettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white">Account Settings</h1>
        <p className="text-white/70">Manage your account preferences and settings</p>
      </div>

      <Tabs defaultValue="profile" value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <div className="flex overflow-x-auto pb-2">
          <TabsList className="firenest-card">
            <TabsTrigger value="profile" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Profile
            </TabsTrigger>
            <TabsTrigger value="notifications" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Notifications
            </TabsTrigger>
            <TabsTrigger value="security" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Security
            </TabsTrigger>
            <TabsTrigger value="billing" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Billing
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-white">Personal Information</CardTitle>
                  <CardDescription>Update your profile details</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-white/70">Full Name</Label>
                        <div className="flex items-center firenest-card rounded-md px-3">
                          <User className="h-4 w-4 text-white/50 mr-2" />
                          <Input
                            id="name"
                            value={user?.name || ''}
                            disabled
                            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                        <p className="text-xs text-white/50">Name is managed through your account settings</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-white/70">Email Address</Label>
                        <div className="flex items-center firenest-card rounded-md px-3">
                          <Mail className="h-4 w-4 text-white/50 mr-2" />
                          <Input
                            id="email"
                            value={user?.email || ''}
                            disabled
                            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                        <p className="text-xs text-white/50">Email is managed through your account settings</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="company" className="text-white/70">Company</Label>
                        <div className="flex items-center firenest-card rounded-md px-3">
                          <Building className="h-4 w-4 text-white/50 mr-2" />
                          <Input
                            id="company"
                            name="company"
                            value={formData.company || ''}
                            onChange={handleChange}
                            placeholder="Your company name"
                            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="jobTitle" className="text-white/70">Job Title</Label>
                        <div className="flex items-center firenest-card rounded-md px-3">
                          <Briefcase className="h-4 w-4 text-white/50 mr-2" />
                          <Input
                            id="jobTitle"
                            name="jobTitle"
                            value={formData.jobTitle || ''}
                            onChange={handleChange}
                            placeholder="Your job title"
                            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="website" className="text-white/70">Website</Label>
                        <div className="flex items-center firenest-card rounded-md px-3">
                          <Globe className="h-4 w-4 text-white/50 mr-2" />
                          <Input
                            id="website"
                            name="website"
                            value={formData.website || ''}
                            onChange={handleChange}
                            placeholder="https://example.com"
                            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-white/70">Phone Number</Label>
                        <div className="flex items-center firenest-card rounded-md px-3">
                          <Phone className="h-4 w-4 text-white/50 mr-2" />
                          <Input
                            id="phone"
                            name="phone"
                            value={formData.phone || ''}
                            onChange={handleChange}
                            placeholder="+91 9876543210"
                            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio" className="text-white/70">Bio</Label>
                      <Textarea
                        id="bio"
                        name="bio"
                        value={formData.bio || ''}
                        onChange={handleChange}
                        placeholder="Tell us about yourself"
                        className="min-h-[100px] firenest-card focus-visible:ring-fiery/50"
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-fiery hover:bg-fiery-600 text-white"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-white">Account Information</CardTitle>
                  <CardDescription>Your account details</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-center mb-6">
                      <div className="w-24 h-24 rounded-full bg-gradient-to-br from-fiery to-fiery-600 flex items-center justify-center text-3xl font-bold text-white">
                        {user?.name ? user.name.substring(0, 2).toUpperCase() : 'U'}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm text-white/70">Account ID</p>
                      <p className="text-sm font-mono firenest-card p-2 rounded text-white/90 overflow-x-auto">{user?.id}</p>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm text-white/70">Account Created</p>
                      <p className="text-sm text-white">{profile?.createdAt ? new Date(profile.createdAt).toLocaleDateString() : 'N/A'}</p>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm text-white/70">Account Status</p>
                      <div className="flex items-center">
                        <div className="h-2 w-2 rounded-full bg-green-400 mr-2"></div>
                        <p className="text-sm text-white">Active</p>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-white/10">
                      <Button
                        variant="outline"
                        className="w-full text-red-400 border-red-400/20 hover:bg-red-400/10"
                        onClick={handleLogout}
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign Out
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Notification Preferences</CardTitle>
              <CardDescription>Manage how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Email Notifications</Label>
                    <p className="text-sm text-white/70">Receive notifications via email</p>
                  </div>
                  <Switch
                    checked={notificationSettings.emailNotifications}
                    onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}
                  />
                </div>
                <Separator className="bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Credit Alerts</Label>
                    <p className="text-sm text-white/70">Get notified when your credits are running low</p>
                  </div>
                  <Switch
                    checked={notificationSettings.creditAlerts}
                    onCheckedChange={(checked) => handleNotificationChange('creditAlerts', checked)}
                  />
                </div>
                <Separator className="bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Product Updates</Label>
                    <p className="text-sm text-white/70">Receive updates about new features and improvements</p>
                  </div>
                  <Switch
                    checked={notificationSettings.productUpdates}
                    onCheckedChange={(checked) => handleNotificationChange('productUpdates', checked)}
                  />
                </div>
                <Separator className="bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Marketing Emails</Label>
                    <p className="text-sm text-white/70">Receive promotional emails and offers</p>
                  </div>
                  <Switch
                    checked={notificationSettings.marketingEmails}
                    onCheckedChange={(checked) => handleNotificationChange('marketingEmails', checked)}
                  />
                </div>
                <Separator className="bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Security Alerts</Label>
                    <p className="text-sm text-white/70">Get notified about important security events</p>
                  </div>
                  <Switch
                    checked={notificationSettings.securityAlerts}
                    onCheckedChange={(checked) => handleNotificationChange('securityAlerts', checked)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t border-white/10 pt-6">
              <Button className="bg-fiery hover:bg-fiery-600 text-white ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save Preferences
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Security Settings</CardTitle>
              <CardDescription>Manage your account security</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Two-Factor Authentication</Label>
                    <p className="text-sm text-white/70">Add an extra layer of security to your account</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={securitySettings.twoFactorAuth}
                      onCheckedChange={(checked) => handleSecurityChange('twoFactorAuth', checked)}
                    />
                    {securitySettings.twoFactorAuth ? (
                      <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">Enabled</span>
                    ) : (
                      <span className="text-xs bg-white/10 text-white/70 px-2 py-1 rounded-full">Disabled</span>
                    )}
                  </div>
                </div>
                <Separator className="bg-white/10" />

                <div>
                  <Label className="text-white mb-2 block">Session Timeout</Label>
                  <p className="text-sm text-white/70 mb-3">Automatically log out after a period of inactivity</p>
                  <div className="grid grid-cols-3 gap-2">
                    {['15', '30', '60'].map((minutes) => (
                      <Button
                        key={minutes}
                        type="button"
                        variant={securitySettings.sessionTimeout === minutes ? "default" : "outline"}
                        className={securitySettings.sessionTimeout === minutes
                          ? "bg-fiery hover:bg-fiery-600 text-white"
                          : "border-white/10 hover:bg-white/5 text-white/80"}
                        onClick={() => handleSecurityChange('sessionTimeout', minutes)}
                      >
                        {minutes} minutes
                      </Button>
                    ))}
                  </div>
                </div>
                <Separator className="bg-white/10" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Login Notifications</Label>
                    <p className="text-sm text-white/70">Get notified when someone logs into your account</p>
                  </div>
                  <Switch
                    checked={securitySettings.loginNotifications}
                    onCheckedChange={(checked) => handleSecurityChange('loginNotifications', checked)}
                  />
                </div>
                <Separator className="bg-white/10" />

                <div>
                  <Label className="text-white mb-2 block">Change Password</Label>
                  <p className="text-sm text-white/70 mb-3">Update your password regularly for better security</p>
                  <Button variant="outline" className="border-white/10 hover:bg-white/5 text-white">
                    <Lock className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t border-white/10 pt-6">
              <Button className="bg-fiery hover:bg-fiery-600 text-white ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save Security Settings
              </Button>
            </CardFooter>
          </Card>

          {/* Connected Applications */}
          <ConnectedApps />

          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Danger Zone</CardTitle>
              <CardDescription>Irreversible account actions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 border border-red-500/20 rounded-lg bg-red-500/5">
                <h3 className="text-red-400 font-medium mb-2">Delete Account</h3>
                <p className="text-sm text-white/70 mb-4">
                  Once you delete your account, there is no going back. This action is permanent and will remove all your data.
                </p>
                <Button variant="destructive" className="bg-red-500 hover:bg-red-600 text-white">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Billing Tab */}
        <TabsContent value="billing" className="space-y-6">
          <Card className="firenest-card-accent border-l-4 border-l-fiery">
            <CardHeader>
              <CardTitle className="text-white">Credits Management</CardTitle>
              <CardDescription>Manage your Firenest credits</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-white/70">
                View your credit balance, purchase history, and buy more credits from our dedicated credits page.
              </p>
              <Button asChild className="bg-fiery hover:bg-fiery-600 text-white">
                <Link to="/dashboard/credits">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Go to Credits Page
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Payment Methods</CardTitle>
              <CardDescription>Manage your payment methods</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 firenest-card flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-md bg-blue-500/20 flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-blue-400" />
                  </div>
                  <div>
                    <p className="font-medium text-white">Visa ending in 4242</p>
                    <p className="text-xs text-white/70">Expires 12/2025</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-500/20 text-green-400 border-none">Default</Badge>
                  <Button variant="ghost" size="sm" className="h-8 text-white/70 hover:text-white">
                    Edit
                  </Button>
                </div>
              </div>

              <Button variant="outline" className="border-white/10 hover:bg-white/5 text-white w-full">
                <CreditCard className="h-4 w-4 mr-2" />
                Add Payment Method
              </Button>
            </CardContent>
          </Card>

          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Billing History</CardTitle>
              <CardDescription>View your past invoices</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { id: 'INV-001', date: '2023-05-15', amount: 199, status: 'Paid' },
                  { id: 'INV-002', date: '2023-04-15', amount: 199, status: 'Paid' },
                  { id: 'INV-003', date: '2023-03-15', amount: 199, status: 'Paid' },
                ].map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-4 firenest-card">
                    <div>
                      <p className="font-medium text-white">{invoice.id}</p>
                      <p className="text-xs text-white/70">{new Date(invoice.date).toLocaleDateString()}</p>
                    </div>
                    <div className="flex items-center gap-4">
                      <p className="text-white">${invoice.amount}</p>
                      <Badge className="bg-green-500/20 text-green-400 border-none">{invoice.status}</Badge>
                      <Button variant="ghost" size="sm" className="h-8 text-white/70 hover:text-white">
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-white">Billing Address</CardTitle>
              <CardDescription>Your billing information</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="addressLine1" className="text-white/70">Address Line 1</Label>
                    <Input
                      id="addressLine1"
                      placeholder="123 Main St"
                      className="firenest-card focus-visible:ring-fiery/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="addressLine2" className="text-white/70">Address Line 2</Label>
                    <Input
                      id="addressLine2"
                      placeholder="Apt 4B"
                      className="firenest-card focus-visible:ring-fiery/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city" className="text-white/70">City</Label>
                    <Input
                      id="city"
                      placeholder="New York"
                      className="firenest-card focus-visible:ring-fiery/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state" className="text-white/70">State/Province</Label>
                    <Input
                      id="state"
                      placeholder="NY"
                      className="firenest-card focus-visible:ring-fiery/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="zipCode" className="text-white/70">Zip/Postal Code</Label>
                    <Input
                      id="zipCode"
                      placeholder="10001"
                      className="firenest-card focus-visible:ring-fiery/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country" className="text-white/70">Country</Label>
                    <Input
                      id="country"
                      placeholder="United States"
                      className="firenest-card focus-visible:ring-fiery/50"
                    />
                  </div>
                </div>
              </form>
            </CardContent>
            <CardFooter className="border-t border-white/10 pt-6">
              <Button className="bg-fiery hover:bg-fiery-600 text-white ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save Billing Address
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NewProfileSettings;
