import { useState, useEffect, useRef } from 'react';
import { Outlet } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import SimpleSidebar from './SimpleSidebar';
import EnterpriseHeader from './EnterpriseHeader';
import { FullScreenLoading } from '@/components/ui/loading';
import { usePageVisibility } from '@/hooks/usePageVisibility';

/**
 * Simple Dashboard Layout with clean sidebar
 */
const SimpleDashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false); // Start with sidebar closed
  const [isMobile, setIsMobile] = useState(false);
  const { user, isLoading } = useAuth();
  const [theme, setTheme] = useState<'dark' | 'darker'>('darker'); // Default theme is darker
  const isVisible = usePageVisibility();
  const initialLoadComplete = useRef(false);
  const [showLoading, setShowLoading] = useState(isLoading);

  // Handle responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);

      // Auto-close sidebar on mobile
      if (mobile && sidebarOpen) {
        setSidebarOpen(false);
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [sidebarOpen]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Only show loading on initial load, not when switching tabs
  useEffect(() => {
    // If this is the initial load and we're loading, show the loading screen
    if (isLoading && !initialLoadComplete.current) {
      setShowLoading(true);
    } else if (!isLoading) {
      // Once loading is complete, mark initial load as done and hide loading
      initialLoadComplete.current = true;
      setShowLoading(false);
    }
  }, [isLoading]);

  // When page becomes visible again (tab switch), don't show loading
  useEffect(() => {
    if (isVisible && initialLoadComplete.current) {
      setShowLoading(false);
    }
  }, [isVisible]);

  if (showLoading) {
    return <FullScreenLoading text="Loading dashboard..." variant="spinner" />;
  }

  return (
    <div className="min-h-screen flex bg-dark-950 text-white relative">
      {/* Global top gradient overlay - ensures it covers the entire top including sidebar */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />
      {/* Overlay for mobile when sidebar is open */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden transition-opacity duration-300"
          onClick={toggleSidebar}
        />
      )}

      {/* Sidebar */}
      <SimpleSidebar
        isMobile={isMobile}
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
      />

      {/* Main content */}
      <div id="main-content" className="flex-1 flex flex-col">
        <EnterpriseHeader
          toggleSidebar={toggleSidebar}
          user={user}
          theme={theme}
          setTheme={setTheme}
        />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>

        {/* Footer */}
        <footer className="border-t border-white/5 py-4 px-6 text-white/40 text-xs">
          <div className="flex justify-between items-center max-w-7xl mx-auto">
            <p>© {new Date().getFullYear()} Firenest. All rights reserved.</p>
            <div className="flex items-center gap-4">
              <a href="#" className="hover:text-white/60 transition-colors">Terms</a>
              <a href="#" className="hover:text-white/60 transition-colors">Privacy</a>
              <a href="#" className="hover:text-white/60 transition-colors">Status</a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default SimpleDashboardLayout;
