import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { getCodeSnippets, saveCodeSnippet } from '@/lib/partner-portal/api';
import { ArrowLeft, Download, Copy, Check, Code, FileCode, Server, Database, Layers } from 'lucide-react';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerIntegration: React.FC = () => {
  const navigate = useNavigate();
  const { partner, tools } = usePartner();

  const [selectedToolId, setSelectedToolId] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('javascript');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  // Code snippets for different languages
  const [javascriptCode, setJavascriptCode] = useState('');
  const [pythonCode, setPythonCode] = useState('');
  const [rubyCode, setRubyCode] = useState('');
  const [phpCode, setPhpCode] = useState('');

  useEffect(() => {
    // Set the first tool as selected by default if available
    if (tools.length > 0 && !selectedToolId) {
      setSelectedToolId(tools[0].id);
    }
  }, [tools, selectedToolId]);

  useEffect(() => {
    const loadCodeSnippets = async () => {
      if (!selectedToolId) return;

      setIsLoading(true);
      try {
        const snippets = await getCodeSnippets(selectedToolId);

        // Set code snippets for each language
        snippets.forEach(snippet => {
          switch (snippet.platform) {
            case 'javascript':
              setJavascriptCode(snippet.codeSnippet);
              break;
            case 'python':
              setPythonCode(snippet.codeSnippet);
              break;
            case 'ruby':
              setRubyCode(snippet.codeSnippet);
              break;
            case 'php':
              setPhpCode(snippet.codeSnippet);
              break;
          }
        });

        // If no snippets found, generate default ones
        if (snippets.length === 0) {
          const selectedTool = tools.find(tool => tool.id === selectedToolId);
          if (selectedTool) {
            generateDefaultSnippets(selectedTool.name);
          }
        }
      } catch (error) {
        console.error('Error loading code snippets:', error);
        notify.error('Failed to load code snippets');
      } finally {
        setIsLoading(false);
      }
    };

    loadCodeSnippets();
  }, [selectedToolId, tools]);

  const generateDefaultSnippets = (toolName: string) => {
    // JavaScript snippet
    setJavascriptCode(`// Firenest Integration for ${toolName}
const firenestIntegration = {
  init: function(apiKey) {
    this.apiKey = apiKey;

    // Add event listener for Firenest redirects
    window.addEventListener('message', this.handleMessage.bind(this));

    console.log('Firenest integration initialized');
  },

  handleMessage: function(event) {
    // Verify the origin
    if (event.origin !== 'https://firenest.io') return;

    const { type, data } = event.data;

    if (type === 'FIRENEST_AUTH') {
      // Handle authentication data
      this.handleAuth(data);
    }
  },

  handleAuth: function(authData) {
    // Process authentication data from Firenest
    const { userId, sessionId, token } = authData;

    // Store the session information
    localStorage.setItem('firenest_session', JSON.stringify({
      userId,
      sessionId,
      token
    }));

    // Notify your backend about the new session
    this.notifyBackend(userId, sessionId, token);
  },

  notifyBackend: function(userId, sessionId, token) {
    // Send the session information to your backend
    fetch('https://your-api.com/firenest/session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': \`Bearer \${this.apiKey}\`
      },
      body: JSON.stringify({
        userId,
        sessionId,
        token
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('Backend notified:', data);
    })
    .catch(error => {
      console.error('Error notifying backend:', error);
    });
  }
};

// Initialize the integration
firenestIntegration.init('YOUR_API_KEY');`);

    // Python snippet
    setPythonCode(`# Firenest Integration for ${toolName}
import requests
import json

class FirenestIntegration:
    def __init__(self, api_key):
        self.api_key = api_key
        print("Firenest integration initialized")

    def handle_auth(self, user_id, session_id, token):
        """Process authentication data from Firenest"""
        # Store the session information (implement your storage method)
        session_data = {
            "userId": user_id,
            "sessionId": session_id,
            "token": token
        }

        # Notify your backend about the new session
        self.notify_backend(user_id, session_id, token)

    def notify_backend(self, user_id, session_id, token):
        """Send the session information to your backend"""
        try:
            response = requests.post(
                "https://your-api.com/firenest/session",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                },
                json={
                    "userId": user_id,
                    "sessionId": session_id,
                    "token": token
                }
            )

            if response.status_code == 200:
                print("Backend notified:", response.json())
            else:
                print("Error notifying backend:", response.status_code)
        except Exception as e:
            print("Error notifying backend:", e)

# Initialize the integration
firenest = FirenestIntegration("YOUR_API_KEY")

# Example usage in a Flask app
# from flask import Flask, request
# app = Flask(__name__)
#
# @app.route('/firenest/callback', methods=['POST'])
# def firenest_callback():
#     data = request.json
#     firenest.handle_auth(data['userId'], data['sessionId'], data['token'])
#     return {"success": True}
`);

    // Ruby snippet
    setRubyCode(`# Firenest Integration for ${toolName}
require 'net/http'
require 'uri'
require 'json'

class FirenestIntegration
  def initialize(api_key)
    @api_key = api_key
    puts "Firenest integration initialized"
  end

  def handle_auth(user_id, session_id, token)
    # Process authentication data from Firenest
    # Store the session information (implement your storage method)
    session_data = {
      userId: user_id,
      sessionId: session_id,
      token: token
    }

    # Notify your backend about the new session
    notify_backend(user_id, session_id, token)
  end

  def notify_backend(user_id, session_id, token)
    # Send the session information to your backend
    begin
      uri = URI.parse("https://your-api.com/firenest/session")
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true

      request = Net::HTTP::Post.new(uri.path, {
        'Content-Type' => 'application/json',
        'Authorization' => "Bearer #{@api_key}"
      })

      request.body = {
        userId: user_id,
        sessionId: session_id,
        token: token
      }.to_json

      response = http.request(request)

      if response.code.to_i == 200
        puts "Backend notified: #{JSON.parse(response.body)}"
      else
        puts "Error notifying backend: #{response.code}"
      end
    rescue => e
      puts "Error notifying backend: #{e.message}"
    end
  end
end

# Initialize the integration
firenest = FirenestIntegration.new("YOUR_API_KEY")

# Example usage in a Sinatra app
# require 'sinatra'
# post '/firenest/callback' do
#   data = JSON.parse(request.body.read)
#   firenest.handle_auth(data['userId'], data['sessionId'], data['token'])
#   {success: true}.to_json
# end
`);

    // PHP snippet
    setPhpCode(`<?php
// Firenest Integration for ${toolName}

class FirenestIntegration {
    private $apiKey;

    public function __construct($apiKey) {
        $this->apiKey = $apiKey;
        echo "Firenest integration initialized\\n";
    }

    public function handleAuth($userId, $sessionId, $token) {
        // Process authentication data from Firenest
        // Store the session information (implement your storage method)
        $sessionData = [
            'userId' => $userId,
            'sessionId' => $sessionId,
            'token' => $token
        ];

        // Notify your backend about the new session
        $this->notifyBackend($userId, $sessionId, $token);
    }

    private function notifyBackend($userId, $sessionId, $token) {
        // Send the session information to your backend
        try {
            $ch = curl_init('https://your-api.com/firenest/session');

            $data = json_encode([
                'userId' => $userId,
                'sessionId' => $sessionId,
                'token' => $token
            ]);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if ($httpCode === 200) {
                echo "Backend notified: " . $response . "\\n";
            } else {
                echo "Error notifying backend: " . $httpCode . "\\n";
            }

            curl_close($ch);
        } catch (Exception $e) {
            echo "Error notifying backend: " . $e->getMessage() . "\\n";
        }
    }
}

// Initialize the integration
$firenest = new FirenestIntegration("YOUR_API_KEY");

// Example usage in a PHP application
// if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_SERVER['REQUEST_URI'] === '/firenest/callback') {
//     $data = json_decode(file_get_contents('php://input'), true);
//     $firenest->handleAuth($data['userId'], $data['sessionId'], $data['token']);
//     header('Content-Type: application/json');
//     echo json_encode(['success' => true]);
// }
?>`);
  };

  const handleCopyCode = () => {
    let codeToCopy = '';

    switch (selectedLanguage) {
      case 'javascript':
        codeToCopy = javascriptCode;
        break;
      case 'python':
        codeToCopy = pythonCode;
        break;
      case 'ruby':
        codeToCopy = rubyCode;
        break;
      case 'php':
        codeToCopy = phpCode;
        break;
    }

    navigator.clipboard.writeText(codeToCopy).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const handleDownloadCode = () => {
    let codeToCopy = '';
    let fileName = '';

    switch (selectedLanguage) {
      case 'javascript':
        codeToCopy = javascriptCode;
        fileName = 'firenest-integration.js';
        break;
      case 'python':
        codeToCopy = pythonCode;
        fileName = 'firenest_integration.py';
        break;
      case 'ruby':
        codeToCopy = rubyCode;
        fileName = 'firenest_integration.rb';
        break;
      case 'php':
        codeToCopy = phpCode;
        fileName = 'firenest_integration.php';
        break;
    }

    const blob = new Blob([codeToCopy], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Integration Code</h1>
                <p className="text-white/60">Get code snippets to integrate with Firenest</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Tool Selection</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="toolSelect">Select Tool</Label>
                      <Select value={selectedToolId} onValueChange={setSelectedToolId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tool" />
                        </SelectTrigger>
                        <SelectContent>
                          {tools.map(tool => (
                            <SelectItem key={tool.id} value={tool.id}>{tool.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Integration Guide</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-fiery/20 flex items-center justify-center flex-shrink-0">
                        <Code className="w-4 h-4 text-fiery" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Client Libraries</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Official SDKs for multiple programming languages
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                        <FileCode className="w-4 h-4 text-blue-500" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Code Snippets</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Ready-to-use code examples for quick integration
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                        <Server className="w-4 h-4 text-purple-500" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">API Reference</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Comprehensive API documentation for developers
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-green-500/20 flex items-center justify-center flex-shrink-0">
                        <Layers className="w-4 h-4 text-green-500" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Integration Steps</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Step-by-step guide to integrate with Firenest
                        </p>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-2 text-white/70 hover:text-white"
                      onClick={() => window.open('https://docs.firenest.io/integration', '_blank')}
                    >
                      View Documentation
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Language Selection</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/5 cursor-pointer" onClick={() => setSelectedLanguage('javascript')}>
                      <div className={`w-3 h-3 rounded-full ${selectedLanguage === 'javascript' ? 'bg-yellow-400' : 'bg-white/20'}`}></div>
                      <span className={`${selectedLanguage === 'javascript' ? 'text-white' : 'text-white/70'}`}>JavaScript</span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/5 cursor-pointer" onClick={() => setSelectedLanguage('python')}>
                      <div className={`w-3 h-3 rounded-full ${selectedLanguage === 'python' ? 'bg-blue-400' : 'bg-white/20'}`}></div>
                      <span className={`${selectedLanguage === 'python' ? 'text-white' : 'text-white/70'}`}>Python</span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/5 cursor-pointer" onClick={() => setSelectedLanguage('ruby')}>
                      <div className={`w-3 h-3 rounded-full ${selectedLanguage === 'ruby' ? 'bg-red-400' : 'bg-white/20'}`}></div>
                      <span className={`${selectedLanguage === 'ruby' ? 'text-white' : 'text-white/70'}`}>Ruby</span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/5 cursor-pointer" onClick={() => setSelectedLanguage('php')}>
                      <div className={`w-3 h-3 rounded-full ${selectedLanguage === 'php' ? 'bg-purple-400' : 'bg-white/20'}`}></div>
                      <span className={`${selectedLanguage === 'php' ? 'text-white' : 'text-white/70'}`}>PHP</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {selectedToolId && (
              <div className="space-y-8">
                <Card className="firenest-card">
                  <CardHeader>
                    <CardTitle className="text-xl text-white">Integration Code</CardTitle>
                    <CardDescription>
                      Use these code snippets to integrate your tool with Firenest
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white/60 mb-6">
                      These code snippets show how to integrate your tool with Firenest. The code handles authentication, user redirection, and usage tracking.
                    </p>

                    <Tabs defaultValue="javascript" value={selectedLanguage} onValueChange={setSelectedLanguage} className="w-full">
                      <TabsList className="grid w-full grid-cols-4 mb-8">
                        <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                        <TabsTrigger value="python">Python</TabsTrigger>
                        <TabsTrigger value="ruby">Ruby</TabsTrigger>
                        <TabsTrigger value="php">PHP</TabsTrigger>
                      </TabsList>

                      <div className="relative">
                        <div className="absolute top-2 right-2 flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-2"
                            onClick={handleCopyCode}
                          >
                            {copied ? (
                              <>
                                <Check className="w-4 h-4 text-green-500" />
                                <span>Copied!</span>
                              </>
                            ) : (
                              <>
                                <Copy className="w-4 h-4" />
                                <span>Copy</span>
                              </>
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center space-x-2"
                            onClick={handleDownloadCode}
                          >
                            <Download className="w-4 h-4" />
                            <span>Download</span>
                          </Button>
                        </div>

                        <TabsContent value="javascript">
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm whitespace-pre-wrap">
                              {javascriptCode}
                            </pre>
                          </div>
                        </TabsContent>

                        <TabsContent value="python">
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm whitespace-pre-wrap">
                              {pythonCode}
                            </pre>
                          </div>
                        </TabsContent>

                        <TabsContent value="ruby">
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm whitespace-pre-wrap">
                              {rubyCode}
                            </pre>
                          </div>
                        </TabsContent>

                        <TabsContent value="php">
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm whitespace-pre-wrap">
                              {phpCode}
                            </pre>
                          </div>
                        </TabsContent>
                      </div>
                    </Tabs>
                  </CardContent>
                </Card>

                <Card className="firenest-card">
                  <CardHeader>
                    <CardTitle className="text-xl text-white">Integration Steps</CardTitle>
                    <CardDescription>
                      Follow these steps to integrate your tool with Firenest
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="flex items-start space-x-4">
                        <div className="w-8 h-8 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-1">
                          <span className="text-fiery font-medium">1</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-white">Add the Firenest integration code to your application</h3>
                          <p className="text-white/70 mt-1">
                            Copy the code snippet above and add it to your application. Make sure to replace 'YOUR_API_KEY' with your actual API key from the Firenest dashboard.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4">
                        <div className="w-8 h-8 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-1">
                          <span className="text-fiery font-medium">2</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-white">Set up the callback endpoint</h3>
                          <p className="text-white/70 mt-1">
                            Create an endpoint in your application to receive authentication data from Firenest. This endpoint should call the handleAuth method of the Firenest integration.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4">
                        <div className="w-8 h-8 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-1">
                          <span className="text-fiery font-medium">3</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-white">Implement session management</h3>
                          <p className="text-white/70 mt-1">
                            Store the Firenest session information securely and use it to track user activity and report usage back to Firenest.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4">
                        <div className="w-8 h-8 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-1">
                          <span className="text-fiery font-medium">4</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-white">Test the integration</h3>
                          <p className="text-white/70 mt-1">
                            Use the Firenest testing tools to verify that your integration is working correctly. You can find these tools in the Firenest dashboard.
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      className="bg-fiery hover:bg-fiery/90 text-white"
                      onClick={() => window.open('https://docs.firenest.io/integration', '_blank')}
                    >
                      View Full Documentation
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerIntegration;
