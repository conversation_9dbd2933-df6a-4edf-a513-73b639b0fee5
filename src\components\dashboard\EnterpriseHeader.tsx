import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import {
  Menu,
  Search,
  Bell,
  HelpCircle,
  User,
  LogOut,
  Settings,
  CreditCard,
  ChevronDown,
  X,
  Zap,
  Sun,
  Moon,
  Globe,
  Shield,
  LifeBuoy,
  BarChart
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface EnterpriseHeaderProps {
  toggleSidebar: () => void;
  user: any;
  theme: 'dark' | 'darker';
  setTheme: (theme: 'dark' | 'darker') => void;
}

/**
 * Enterprise-grade Dashboard Header
 * Inspired by industry leaders like Stripe, Vercel, and Linear
 */
const EnterpriseHeader = ({ toggleSidebar, user, theme, setTheme }: EnterpriseHeaderProps) => {
  const { logout, credits } = useAuth();
  const [searchOpen, setSearchOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Handle clicks outside of profile dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setProfileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Mock search function
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    if (query.length > 1) {
      setIsSearching(true);
      // Simulate API call delay
      setTimeout(() => {
        setSearchResults([
          { id: 1, type: 'tool', name: 'ChatGPT', description: 'AI chat assistant' },
          { id: 2, type: 'tool', name: 'Midjourney', description: 'AI image generation' },
          { id: 3, type: 'page', name: 'Credits', description: 'Manage your credits' },
          { id: 4, type: 'page', name: 'Settings', description: 'Account settings' },
        ]);
        setIsSearching(false);
      }, 300);
    } else {
      setSearchResults([]);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd+K or Ctrl+K to focus search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setSearchOpen(true);
        setTimeout(() => {
          searchRef.current?.focus();
        }, 100);
      }

      // Escape to close search
      if (e.key === 'Escape' && searchOpen) {
        setSearchOpen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [searchOpen]);

  return (
    <header className={cn(
      "sticky top-0 z-20 backdrop-blur-md border-b shadow-sm relative",
      'bg-dark-900/95 border-white/10'
    )}>
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 w-full h-16 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-0" />

      <div className="flex h-16 items-center justify-between px-4">
        {/* Left section */}
        <div className="flex items-center gap-4">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="lg:hidden text-white/70 hover:text-white hover:bg-white/5"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Logo - only visible on mobile */}
          <div className="lg:hidden flex items-center">
            <div className="h-8 w-8 rounded-md bg-gradient-to-br from-fiery to-fiery-600 flex items-center justify-center">
              <Zap className="h-5 w-5 text-white" />
            </div>
            <h1 className="ml-2 text-xl font-bold">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-fiery via-fiery to-cool-500 bg-size-200">
                <span className="text-fiery">Fir</span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
                <span className="text-cool">est</span>
              </span>
            </h1>
          </div>

          {/* Global Search */}
          <div className={cn(
            "relative",
            searchOpen ? "w-full lg:w-96" : "hidden md:block w-64 lg:w-80"
          )}>
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-4 w-4 text-white/50" />
            </div>
            <Input
              ref={searchRef}
              type="search"
              placeholder="Search tools, credits, settings... (⌘K)"
              className={cn(
                "pl-10 pr-4 py-2 border text-sm text-white/90 placeholder:text-white/50",
                theme === 'darker'
                  ? "bg-white/5 border-white/10 focus:border-fiery/50 focus:ring-fiery/20"
                  : "bg-white/10 border-white/20 focus:border-fiery/60 focus:ring-fiery/30"
              )}
              onChange={handleSearch}
              onFocus={() => setSearchOpen(true)}
            />
            {searchOpen && (
              <>
                <button
                  className="absolute inset-y-0 right-0 flex items-center pr-3"
                  onClick={() => setSearchOpen(false)}
                >
                  <X className="h-4 w-4 text-white/50 hover:text-white" />
                </button>

                {/* Search results dropdown */}
                <div className={cn(
                  "absolute top-full left-0 right-0 mt-1 rounded-md shadow-lg overflow-hidden z-50",
                  'firenest-card border'
                )}>
                  {isSearching ? (
                    <div className="p-4 text-center text-white/70">
                      <div className="animate-spin inline-block w-4 h-4 border-2 border-white/20 border-t-white/80 rounded-full mr-2"></div>
                      Searching...
                    </div>
                  ) : searchResults.length > 0 ? (
                    <div className="max-h-80 overflow-y-auto">
                      {searchResults.map((result) => (
                        <Link
                          key={result.id}
                          to={result.type === 'tool' ? `/dashboard/workbench?tool=${result.name.toLowerCase()}` : `/dashboard/${result.name.toLowerCase()}`}
                          className="flex items-center px-4 py-3 hover:bg-white/5 transition-colors"
                          onClick={() => setSearchOpen(false)}
                        >
                          <div className={cn(
                            "h-8 w-8 rounded-md flex items-center justify-center mr-3",
                            result.type === 'tool' ? 'bg-fiery/20' : 'bg-blue-500/20'
                          )}>
                            {result.type === 'tool' ? (
                              <Zap className="h-4 w-4 text-fiery" />
                            ) : (
                              <Globe className="h-4 w-4 text-blue-400" />
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-white">{result.name}</p>
                            <p className="text-xs text-white/70">{result.description}</p>
                          </div>
                          <Badge variant="outline" className="ml-auto">
                            {result.type}
                          </Badge>
                        </Link>
                      ))}
                    </div>
                  ) : searchRef.current?.value && searchRef.current.value.length > 0 ? (
                    <div className="p-4 text-center text-white/70">
                      No results found
                    </div>
                  ) : null}

                  <div className="px-4 py-2 border-t border-white/10 flex justify-between items-center text-xs text-white/50">
                    <span>Press <kbd className="px-1.5 py-0.5 bg-white/10 rounded text-xs">↑</kbd> <kbd className="px-1.5 py-0.5 bg-white/10 rounded text-xs">↓</kbd> to navigate</span>
                    <span>Press <kbd className="px-1.5 py-0.5 bg-white/10 rounded text-xs">Enter</kbd> to select</span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Right section */}
        <div className="flex items-center gap-3">
          {/* Credits indicator with dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "hidden md:flex items-center gap-2 px-3 py-1.5 rounded-full border-white/10 hover:bg-white/5 hover:border-fiery/30",
                  'firenest-card'
                )}
              >
                <Zap className="h-4 w-4 text-fiery" />
                <span className="text-sm font-medium text-white">{credits?.availableCredits || 0}</span>
                <span className="text-xs text-white/70">credits</span>
                <ChevronDown className="h-3.5 w-3.5 text-white/50 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 firenest-card border border-white/10 text-white p-0">
              <div className="p-4 border-b border-white/10 bg-gradient-to-r from-fiery/10 to-transparent">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">Your Firenest Wallet</h4>
                  <Button variant="ghost" size="sm" className="h-7 text-xs text-white/70 hover:text-white">
                    View History
                  </Button>
                </div>
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 rounded-full bg-fiery/20 flex items-center justify-center">
                    <Zap className="h-6 w-6 text-fiery" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{credits?.availableCredits || 0}</div>
                    <div className="text-xs text-white/70">Available Credits</div>
                  </div>
                </div>
              </div>

              <div className="p-4 space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-white/70">Used today:</span>
                    <span className="font-medium">{Math.ceil(Math.random() * 20)} credits</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-white/70">Used this week:</span>
                    <span className="font-medium">{Math.ceil(Math.random() * 100)} credits</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-white/70">Active sessions:</span>
                    <span className="font-medium">{Math.floor(Math.random() * 3)}</span>
                  </div>
                </div>

                <div className="pt-3 border-t border-white/10">
                  <Button
                    className="w-full bg-gradient-to-r from-fiery to-fiery-600 hover:from-fiery-600 hover:to-fiery-700"
                    asChild
                  >
                    <Link to="/dashboard/credits">
                      <CreditCard className="h-4 w-4 mr-2" />
                      Add Credits
                    </Link>
                  </Button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>



          {/* Theme toggle */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setTheme(theme === 'dark' ? 'darker' : 'dark')}
                  className="text-white/70 hover:text-white hover:bg-white/5"
                >
                  {theme === 'dark' ? (
                    <Moon className="h-5 w-5" />
                  ) : (
                    <Sun className="h-5 w-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle theme</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Help dropdown */}
          <DropdownMenu>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-white/70 hover:text-white hover:bg-white/5"
                    >
                      <HelpCircle className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Help & Resources</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DropdownMenuContent align="end" className={cn(
              "w-56 text-white",
              'firenest-card border'
            )}>
              <DropdownMenuLabel>Help & Resources</DropdownMenuLabel>
              <DropdownMenuSeparator className={theme === 'darker' ? 'bg-white/10' : 'bg-white/20'} />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <LifeBuoy className="mr-2 h-4 w-4" />
                  <span>Support Center</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Globe className="mr-2 h-4 w-4" />
                  <span>Documentation</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Shield className="mr-2 h-4 w-4" />
                  <span>API Reference</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator className={theme === 'darker' ? 'bg-white/10' : 'bg-white/20'} />
              <DropdownMenuItem>
                <span>Contact Support</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Notifications dropdown */}
          <DropdownMenu>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="relative text-white/70 hover:text-white hover:bg-white/5"
                    >
                      <Bell className="h-5 w-5" />
                      <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-fiery"></span>
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Notifications</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DropdownMenuContent align="end" className={cn(
              "w-80 text-white",
              'firenest-card border'
            )}>
              <DropdownMenuLabel className="flex items-center justify-between">
                <span>Notifications</span>
                <Button variant="ghost" size="sm" className="h-8 text-xs text-white/70 hover:text-white">
                  Mark all as read
                </Button>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className={theme === 'darker' ? 'bg-white/10' : 'bg-white/20'} />
              <div className="max-h-80 overflow-y-auto py-1">
                {/* Notification items */}
                <div className={cn(
                  "px-4 py-3 hover:bg-white/5 cursor-pointer border-l-2 border-fiery",
                  theme === 'darker' ? 'hover:bg-white/5' : 'hover:bg-white/10'
                )}>
                  <div className="flex gap-3">
                    <div className="h-9 w-9 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0">
                      <Zap className="h-5 w-5 text-fiery" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Credits Added</p>
                      <p className="text-xs text-white/70 mt-0.5">100 credits have been added to your account.</p>
                      <p className="text-xs text-white/50 mt-1">2 hours ago</p>
                    </div>
                  </div>
                </div>
                <div className={cn(
                  "px-4 py-3 cursor-pointer",
                  theme === 'darker' ? 'hover:bg-white/5' : 'hover:bg-white/10'
                )}>
                  <div className="flex gap-3">
                    <div className="h-9 w-9 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                      <Bell className="h-5 w-5 text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">New Feature Available</p>
                      <p className="text-xs text-white/70 mt-0.5">Check out our new AI tool integration.</p>
                      <p className="text-xs text-white/50 mt-1">1 day ago</p>
                    </div>
                  </div>
                </div>
              </div>
              <DropdownMenuSeparator className={theme === 'darker' ? 'bg-white/10' : 'bg-white/20'} />
              <DropdownMenuItem asChild className="justify-center text-sm text-white/70 hover:text-white">
                <Link to="/dashboard/notifications">View all notifications</Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User dropdown */}
          <div className="relative" ref={profileRef}>
            <DropdownMenu open={profileOpen} onOpenChange={setProfileOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn(
                    "flex items-center gap-2 px-2 py-1 rounded-md h-9 transition-colors",
                    theme === 'darker' ? 'hover:bg-white/5' : 'hover:bg-white/10',
                    "text-white"
                  )}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.avatarUrl} alt={user?.name || 'User'} />
                    <AvatarFallback className="bg-gradient-to-br from-fiery to-fiery-600 text-white">
                      {user?.name ? user.name.substring(0, 2).toUpperCase() : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="hidden md:block text-left">
                    <p className="text-sm font-medium truncate max-w-[120px]">{user?.name || 'User'}</p>
                    <p className="text-xs text-white/70 truncate max-w-[120px]">{user?.email || '<EMAIL>'}</p>
                  </div>
                  <ChevronDown className="h-4 w-4 text-white/70" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className={cn(
                "w-56 text-white",
                'firenest-card border'
              )}>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium">{user?.name || 'User'}</p>
                    <p className="text-xs text-white/70 truncate">{user?.email || '<EMAIL>'}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className={theme === 'darker' ? 'bg-white/10' : 'bg-white/20'} />
                <DropdownMenuGroup>
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard/settings?tab=profile">
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard/credits">
                      <CreditCard className="mr-2 h-4 w-4" />
                      <span>Billing</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard/settings">
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator className={theme === 'darker' ? 'bg-white/10' : 'bg-white/20'} />
                <DropdownMenuItem onClick={logout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};

export default EnterpriseHeader;
