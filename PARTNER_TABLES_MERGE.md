# Partner Tables Merge

This document describes the merge of the `partners` and `partner_accounts` tables in the Firenest database.

## Background

The Firenest database had two tables for storing partner information:

1. `partners` - Used for OAuth/OIDC authentication with client_id, client_secret, and redirect_uris
2. `partner_accounts` - Used for the partner portal with information like name, email, company, etc.

Having two separate tables for essentially the same entity was causing confusion and making it difficult to maintain the codebase. This merge consolidates all partner information into a single `partner_accounts` table.

## Changes Made

### Database Changes

1. Added missing columns from `partners` to `partner_accounts`:
   - `client_id` (TEXT, UNIQUE)
   - `client_secret` (TEXT)
   - `redirect_uris` (TEXT[])
   - `website_url` (TEXT)
   - `active` (BOOLEAN)

2. Migrated data from `partners` to `partner_accounts`:
   - For partners that existed in both tables, updated the `partner_accounts` record with OAuth information
   - For partners that only existed in `partners`, created new records in `partner_accounts`

3. Created a view called `partners_view` for backward compatibility:
   - This view shows data from `partner_accounts` in the format of the old `partners` table
   - Triggers on the view handle inserts and updates to maintain compatibility

### Code Changes

1. Updated API endpoints to use `partner_accounts` instead of `partners`:
   - `/api/v1/auth/authorize.ts`
   - `/api/v1/auth/token.ts`
   - `server.js`

2. Created migration scripts:
   - `supabase-merge-partner-tables.sql` - SQL migration script
   - `scripts/merge-partner-tables.js` - Node.js script to run the migration

## How to Run the Migration

1. Make sure you have the Supabase environment variables set:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `SUPABASE_SERVICE_KEY`

2. Run the migration script:
   ```bash
   node scripts/merge-partner-tables.js
   ```

3. Verify that the migration was successful:
   - Check that the `partners_view` exists
   - Check that partners with `client_id` exist in the `partner_accounts` table

## Future Considerations

1. The `partners` table is not dropped by the migration script to ensure backward compatibility. Once all code has been updated to use `partner_accounts`, the `partners` table can be dropped.

2. The `partners_view` provides backward compatibility for any code that still references the `partners` table. This view can be removed once all code has been updated.

3. If any issues are encountered with the migration, the `partners` table can still be used as a fallback since it has not been dropped.

## Testing

After running the migration, test the following functionality:

1. Partner login via the partner portal
2. OAuth/OIDC authentication flow
3. Partner feature management
4. Usage tracking and credit deduction

## Troubleshooting

If you encounter any issues with the migration, check the following:

1. Make sure the Supabase environment variables are set correctly
2. Check the Supabase logs for any errors
3. Verify that the `partners_view` was created successfully
4. Check that partners with `client_id` exist in the `partner_accounts` table

If necessary, you can revert to using the `partners` table by updating the code to reference `partners` instead of `partner_accounts`.
