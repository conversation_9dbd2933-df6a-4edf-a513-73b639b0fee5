import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>cle,
  ExternalLink,
  Bar<PERSON>hart,
  Shield,
  Clock,
  CreditCard,
  X
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface LaunchConfirmationProps {
  tool: any;
  usageModel: 'fixed' | 'dynamic';
  creditLimit: number | null;
  trackUsage: boolean;
  onClose: () => void;
  launchTime: number;
}

const LaunchConfirmation: React.FC<LaunchConfirmationProps> = ({
  tool,
  usageModel,
  creditLimit,
  trackUsage,
  onClose,
  launchTime
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [creditsUsed, setCreditsUsed] = useState(usageModel === 'fixed' ? tool.pricing.costPerUnit : 0);
  const [autoCloseTimer, setAutoCloseTimer] = useState(30); // Auto close after 30 seconds
  const [isMinimized, setIsMinimized] = useState(false);

  // Update elapsed time and simulate credit usage for dynamic model
  useEffect(() => {
    const timer = setInterval(() => {
      // Update elapsed time
      const newElapsedTime = Math.floor((Date.now() - launchTime) / 1000);
      setElapsedTime(newElapsedTime);

      // Simulate credit usage for dynamic model
      if (usageModel === 'dynamic') {
        // Simulate increasing credit usage over time
        // In a real implementation, this would be based on actual API usage data
        const newCreditsUsed = Math.min(
          creditLimit || 100, // Cap at credit limit or 100 if no limit
          Math.floor(tool.pricing.costPerUnit * (1 + (newElapsedTime / 60)))
        );
        setCreditsUsed(newCreditsUsed);
      }

      // Update auto-close timer
      setAutoCloseTimer(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [tool, usageModel, creditLimit, launchTime]);

  // Format time as mm:ss
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate progress percentage for credit usage
  const getCreditUsagePercentage = () => {
    if (usageModel === 'fixed') return 100;
    if (creditLimit) return (creditsUsed / creditLimit) * 100;
    return Math.min((creditsUsed / (tool.pricing.costPerUnit * 5)) * 100, 100); // Arbitrary scale if no limit
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-40">
        <Card className="bg-dark-800 border-white/10 shadow-lg">
          <CardContent className="p-3 flex items-center gap-3">
            <div className="h-8 w-8 rounded-full bg-green-500/20 flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-green-400" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-white">{tool.name} Active</p>
              <p className="text-xs text-white/70">{formatTime(elapsedTime)} • {creditsUsed} credits used</p>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-white/70 hover:text-white hover:bg-white/5"
                onClick={() => setIsMinimized(false)}
              >
                <BarChart className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-white/70 hover:text-white hover:bg-white/5"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-40 w-full max-w-md">
      <Card className="firenest-card shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-green-500/20 to-green-500/5 px-4 py-3 flex items-center justify-between border-b border-white/10">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <h3 className="font-medium text-white">{tool.name} Successfully Launched</h3>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-white/70 hover:text-white hover:bg-white/5"
              onClick={() => setIsMinimized(true)}
            >
              <BarChart className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-white/70 hover:text-white hover:bg-white/5"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <CardContent className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge className="bg-green-500/20 text-green-400 border-green-500/20">Active</Badge>
              <span className="text-xs text-white/70">Session time: {formatTime(elapsedTime)}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="h-7 border-white/10 hover:bg-white/5"
              onClick={() => window.open(tool.website || '#', '_blank')}
            >
              <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
              Open Tool
            </Button>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-fiery" />
                <span className="text-white">Credits Used</span>
              </div>
              <span className="font-medium text-white">
                {creditsUsed} {usageModel === 'dynamic' && creditLimit ? `/ ${creditLimit}` : ''}
              </span>
            </div>

            <Progress
              value={getCreditUsagePercentage()}
              className="h-2 bg-dark-700"
              indicatorClassName={usageModel === 'fixed' ? 'bg-blue-500' : 'bg-fiery'}
            />

            <div className="flex items-center justify-between text-xs text-white/70">
              <span>Usage Model: {usageModel === 'fixed' ? 'Fixed Cost' : 'Pay-per-use'}</span>
              {usageModel === 'dynamic' && creditLimit && (
                <span>{Math.round((creditsUsed / creditLimit) * 100)}% of limit used</span>
              )}
            </div>
          </div>

          <div className="bg-dark-800 rounded-lg p-3 space-y-2">
            <h4 className="text-sm font-medium text-white">Active Settings</h4>
            <div className="space-y-1.5">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-1.5">
                  <Shield className="h-3.5 w-3.5 text-fiery" />
                  <span className="text-white/70">Usage Tracking</span>
                </div>
                <span className="text-white">{trackUsage ? 'Enabled' : 'Disabled'}</span>
              </div>

              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-1.5">
                  <CreditCard className="h-3.5 w-3.5 text-fiery" />
                  <span className="text-white/70">Billing Model</span>
                </div>
                <span className="text-white">{usageModel === 'fixed' ? 'Fixed Cost' : 'Pay-per-use'}</span>
              </div>

              {usageModel === 'dynamic' && (
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-1.5">
                    <Shield className="h-3.5 w-3.5 text-fiery" />
                    <span className="text-white/70">Credit Limit</span>
                  </div>
                  <span className="text-white">{creditLimit ? `${creditLimit} credits` : 'No limit'}</span>
                </div>
              )}
            </div>
          </div>

          <div className="text-xs text-white/50 flex items-center justify-between">
            <span>This window will close automatically in {autoCloseTimer}s</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs text-white/70 hover:text-white hover:bg-white/5"
              onClick={() => setIsMinimized(true)}
            >
              Minimize
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LaunchConfirmation;
