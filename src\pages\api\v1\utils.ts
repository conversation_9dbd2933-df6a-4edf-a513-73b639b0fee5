/**
 * API Utilities
 *
 * This file contains utility functions for the API endpoints.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/lib/supabase';
import { verifyAccessToken as verifyToken } from '@/lib/auth/token-utils';

// API response types
export interface ApiResponse {
  success: boolean;
  message?: string;
  data?: any;
  error?: any;
}

// Error types
export enum ErrorType {
  UNAUTHORIZED = 'unauthorized',
  INVALID_REQUEST = 'invalid_request',
  NOT_FOUND = 'not_found',
  SERVER_ERROR = 'server_error',
  RATE_LIMITED = 'rate_limited',
  INSUFFICIENT_CREDITS = 'insufficient_credits',
}

// Error messages
export const ErrorMessages = {
  [ErrorType.UNAUTHORIZED]: 'Unauthorized access',
  [ErrorType.INVALID_REQUEST]: 'Invalid request parameters',
  [ErrorType.NOT_FOUND]: 'Resource not found',
  [ErrorType.SERVER_ERROR]: 'Internal server error',
  [ErrorType.RATE_LIMITED]: 'Rate limit exceeded',
  [ErrorType.INSUFFICIENT_CREDITS]: 'Insufficient credits',
};

// HTTP status codes
export const StatusCodes = {
  OK: 200,
  CREATED: 201,
  FOUND: 302, // Redirect status code
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
};

/**
 * Verify API key from request headers
 */
export async function verifyApiKey(req: NextApiRequest): Promise<{ valid: boolean; partnerId?: string }> {
  try {
    // Get API key from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { valid: false };
    }

    const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
    if (!apiKey) {
      return { valid: false };
    }

    // Verify API key in database
    const { data, error } = await supabase
      .from('partner_api_keys')
      .select('partner_id, active')
      .eq('api_key', apiKey)
      .eq('active', true)
      .maybeSingle();

    if (error || !data) {
      console.error('Error verifying API key:', error);
      return { valid: false };
    }

    return { valid: true, partnerId: data.partner_id };
  } catch (error) {
    console.error('Error in verifyApiKey:', error);
    return { valid: false };
  }
}

/**
 * Verify access token from request
 */
export async function verifyAccessToken(token: string): Promise<{ valid: boolean; userId?: string; partnerId?: string }> {
  return verifyToken(token, supabase);
}

/**
 * Check if user has sufficient credits
 */
export async function checkUserCredits(userId: string, requiredCredits: number): Promise<boolean> {
  try {
    // Get user's credit balance
    const { data, error } = await supabase
      .from('user_credits')
      .select('balance')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error checking user credits:', error);
      return false;
    }

    // If no credit record found, assume 0 balance
    const balance = data?.balance || 0;

    // Check if balance is sufficient
    return balance >= requiredCredits;
  } catch (error) {
    console.error('Error in checkUserCredits:', error);
    return false;
  }
}

/**
 * Deduct credits from user's balance
 */
export async function deductCredits(userId: string, credits: number, description: string): Promise<boolean> {
  try {
    // Start a transaction
    const { data, error } = await supabase.rpc('deduct_user_credits', {
      p_user_id: userId,
      p_amount: credits,
      p_description: description
    });

    if (error) {
      console.error('Error deducting credits:', error);
      return false;
    }

    return data || false;
  } catch (error) {
    console.error('Error in deductCredits:', error);
    return false;
  }
}

/**
 * Log API request for monitoring and debugging
 */
export async function logApiRequest(
  endpoint: string,
  method: string,
  partnerId: string | null,
  userId: string | null,
  requestData: any,
  responseStatus: number,
  responseData: any
): Promise<void> {
  try {
    await supabase.from('api_logs').insert({
      endpoint,
      method,
      partner_id: partnerId,
      user_id: userId,
      request_data: requestData,
      response_status: responseStatus,
      response_data: responseData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error logging API request:', error);
  }
}

/**
 * API handler wrapper with authentication and error handling
 */
export function withApiAuth(
  handler: (
    req: NextApiRequest,
    res: NextApiResponse,
    context: { userId: string; partnerId: string }
  ) => Promise<void>
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      // Verify API key
      const { valid, partnerId } = await verifyApiKey(req);
      if (!valid || !partnerId) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          success: false,
          error: ErrorType.UNAUTHORIZED,
          message: ErrorMessages[ErrorType.UNAUTHORIZED]
        });
      }

      // Get user ID from request
      const userId = req.body.userId || req.query.userId;
      if (!userId) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          error: ErrorType.INVALID_REQUEST,
          message: 'User ID is required'
        });
      }

      // Call the handler with context
      await handler(req, res, { userId: userId as string, partnerId });
    } catch (error) {
      console.error('API error:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: ErrorMessages[ErrorType.SERVER_ERROR]
      });
    }
  };
}

/**
 * API handler wrapper with token authentication and error handling
 */
export function withTokenAuth(
  handler: (
    req: NextApiRequest,
    res: NextApiResponse,
    context: { userId: string; partnerId: string }
  ) => Promise<void>
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      // Get token from request
      const token = req.headers.authorization?.substring(7) || req.body.token || req.query.token;
      if (!token) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          success: false,
          error: ErrorType.UNAUTHORIZED,
          message: 'Access token is required'
        });
      }

      // Verify token
      const { valid, userId, partnerId } = await verifyAccessToken(token as string);
      if (!valid || !userId || !partnerId) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          success: false,
          error: ErrorType.UNAUTHORIZED,
          message: ErrorMessages[ErrorType.UNAUTHORIZED]
        });
      }

      // Call the handler with context
      await handler(req, res, { userId, partnerId });
    } catch (error) {
      console.error('API error:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: ErrorType.SERVER_ERROR,
        message: ErrorMessages[ErrorType.SERVER_ERROR]
      });
    }
  };
}
