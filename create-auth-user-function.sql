-- Create the get_auth_user_by_id function
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION get_auth_user_by_id(user_id_param UUID)
RETURNS TABLE (
  id UUID,
  email TEXT,
  raw_user_meta_data JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email,
    au.raw_user_meta_data
  FROM 
    auth.users au
  WHERE 
    au.id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users and service_role
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO authenticated;
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO service_role;
