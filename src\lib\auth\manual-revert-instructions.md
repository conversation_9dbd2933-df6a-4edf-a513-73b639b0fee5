# Manual Reversion Instructions

If the automatic reversion script (`revert-changes.js`) fails, you can manually revert the changes using these instructions.

## Option 1: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Paste the following SQL:

```sql
-- Drop the INSERT policies we added
DROP POLICY IF EXISTS "Partners can insert tokens" ON access_tokens;
DROP POLICY IF EXISTS "Users can insert tokens" ON access_tokens;
DROP POLICY IF EXISTS "Service role can insert tokens" ON access_tokens;
```

5. Run the query

## Option 2: Using the Supabase CLI

If you have the Supabase CLI installed, you can run:

```bash
supabase db execute --file=src/lib/auth/revert-policy-changes.sql
```

## Verifying the Reversion

To verify that the policies have been removed, you can run the following SQL query:

```sql
SELECT * FROM pg_policies WHERE tablename = 'access_tokens' AND operation = 'INSERT';
```

This should return no rows if all INSERT policies have been successfully removed.

## Restoring the Original State

If you need to restore the original state of the access_tokens table policies, you can run the following SQL:

```sql
-- Add back the original policies if needed
-- Note: These are the standard policies that should be present
-- Adjust as needed based on your specific configuration

-- Partners can view tokens for their clients
CREATE POLICY "Partners can view their tokens"
ON access_tokens
FOR SELECT
TO authenticated
USING (
  partner_id = auth.uid()
);

-- Users can view their own tokens
CREATE POLICY "Users can view their own tokens"
ON access_tokens
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
);

-- Partners can update tokens for their clients
CREATE POLICY "Partners can update their tokens"
ON access_tokens
FOR UPDATE
TO authenticated
USING (
  partner_id = auth.uid()
);
```

## Troubleshooting

If you encounter any issues with the reversion process, check the following:

1. Make sure you have the necessary permissions to modify policies in your Supabase database
2. Verify that the policies actually exist before trying to drop them
3. Check for any dependencies that might prevent the policies from being dropped

If problems persist, you may need to contact Supabase support for assistance.
