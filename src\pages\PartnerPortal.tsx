import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { createPartnerAccount, getPartnerAccount } from '@/lib/partner-portal/api';
import { supabase } from '@/lib/supabase';
import { PartnerStatus } from '@/lib/partner-portal/types';
import { PlusCircle, Wrench, Settings, Key, Code, ExternalLink, AlertCircle } from 'lucide-react';
import PartnerErrorBoundary from '@/components/partner/PartnerErrorBoundary';

const PartnerPortal: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { partner, tools, isLoading } = usePartner();

  const [activeTab, setActiveTab] = useState('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [company, setCompany] = useState('');
  const [website, setWebsite] = useState('');
  const [isRegistering, setIsRegistering] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [registerError, setRegisterError] = useState('');

  // Handle URL parameters for tab selection
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    if (tab === 'register') {
      setActiveTab('register');
    } else if (tab === 'login') {
      setActiveTab('login');
    }

    // Check for error parameters
    const error = params.get('error');
    if (error) {
      if (tab === 'register') {
        setRegisterError(decodeURIComponent(error));
      } else {
        setLoginError(decodeURIComponent(error));
      }
    }

    // Check for email parameter
    const emailParam = params.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [location]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');

    if (!email || !password) {
      setLoginError('Please enter your email and password');
      notify.error('Please enter your email and password');
      return;
    }

    setIsLoggingIn(true);

    try {
      console.log('Attempting to sign in with:', email);

      // Add a timeout to prevent infinite loading
      const loginPromise = supabase.auth.signInWithPassword({
        email,
        password
      });

      const timeoutPromise = new Promise<{data: null, error: {message: string}}>((resolve) => {
        setTimeout(() => {
          resolve({
            data: null,
            error: { message: 'Login timed out. Please try again.' }
          });
        }, 10000); // 10 second timeout
      });

      // Race the login against the timeout
      const { data, error } = await Promise.race([loginPromise, timeoutPromise]);

      if (error) {
        console.error('Authentication error:', error);

        // Set error message based on the error type
        if (error.message.includes('Invalid login credentials')) {
          setLoginError('Invalid email or password. Please try again.');
          notify.error('Invalid email or password. Please try again.');
        } else if (error.message.includes('Email not confirmed')) {
          setLoginError('Please check your email and confirm your account before logging in.');
          notify.error('Please check your email and confirm your account before logging in.');
        } else if (error.message.includes('timed out')) {
          setLoginError('Connection timed out. Please check your internet connection and try again.');
          notify.error('Connection timed out. Please check your internet connection and try again.');
        } else {
          setLoginError(`Login error: ${error.message}`);
          notify.error(`Login error: ${error.message}`);
        }

        return; // Exit early on auth errors
      }

      if (!data || !data.user) {
        console.error('No user data returned from authentication');
        notify.error('Authentication failed. Please try again.');
        return;
      }

      console.log('Successfully authenticated user:', data.user.id);

      // Step 2: Check if the user has a partner account
      console.log('Checking for partner account with ID:', data.user.id);
      const partnerAccount = await getPartnerAccount(data.user.id);

      if (!partnerAccount) {
        console.log('No partner account found for user:', data.user.id);

        // Get user metadata for name and company
        const { name, company } = data.user.user_metadata || {};

        // Create a partner account automatically if we have the required metadata
        if (name && company) {
          console.log('Creating partner account for user:', data.user.id);

          try {
            const newPartnerAccount = await createPartnerAccount(
              data.user.id,
              name,
              data.user.email || email,
              company
            );

            if (newPartnerAccount) {
              console.log('Partner account created successfully:', newPartnerAccount.id);
              notify.success('Partner account created successfully');

              // Navigate to dashboard after creating the partner account
              navigate('/partner/dashboard');
              return;
            } else {
              console.error('Failed to create partner account - null result');
            }
          } catch (createError) {
            console.error('Error creating partner account:', createError);
          }
        }

        // If we couldn't create a partner account, show an error and redirect to registration
        notify.error('Please complete your registration to access the partner portal');

        // Switch to registration tab instead of signing out
        setActiveTab('register');

        // Pre-fill the registration form with the email
        setName(name || '');
        setCompany(company || '');

        return;
      }

      console.log('Partner account found:', partnerAccount.id);

      // Step 3: Navigate to dashboard
      notify.success('Logged in successfully');
      navigate('/partner/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Generic error handling
      notify.error('An error occurred during login. Please try again.');
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegisterError('');

    // Validate input fields
    if (!email || !password || !name || !company) {
      setRegisterError('Please fill in all required fields');
      notify.error('Please fill in all required fields');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setRegisterError('Please enter a valid email address');
      notify.error('Please enter a valid email address');
      return;
    }

    // Validate password strength
    if (password.length < 6) {
      setRegisterError('Password should be at least 6 characters long');
      notify.error('Password should be at least 6 characters long');
      return;
    }

    setIsRegistering(true);

    try {
      // Step 1: Check if user already exists
      console.log('Checking if user already exists with email:', email);

      const { data: existingUser, error: existingUserError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (existingUser?.user) {
        console.log('User already exists, checking for partner account');

        // Check if they already have a partner account
        const existingPartner = await getPartnerAccount(existingUser.user.id);

        if (existingPartner) {
          // They already have a complete account, redirect to login
          notify.info('You already have an account. Please log in instead.');
          setActiveTab('login');
          return;
        } else {
          // They have an auth account but no partner account, create one
          console.log('Creating partner account for existing user:', existingUser.user.id);

          const partnerAccount = await createPartnerAccount(
            existingUser.user.id,
            name,
            email,
            company,
            website
          );

          if (partnerAccount) {
            notify.success('Account completed successfully! Redirecting to dashboard...');
            navigate('/partner/dashboard');
            return;
          }
        }
      }

      // Step 2: Create the auth user
      console.log('Creating new auth user with email:', email);

      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            company
          },
          emailRedirectTo: `${window.location.origin}/partner/dashboard`
        }
      });

      if (authError) {
        console.error('Auth error during signup:', authError);

        if (authError.message.includes('User already registered')) {
          notify.error('This email is already registered. Please log in instead.');
          setActiveTab('login');
        } else {
          setRegisterError(`Registration error: ${authError.message}`);
          notify.error(`Registration error: ${authError.message}`);
        }

        return;
      }

      if (!authData?.user) {
        console.error('No user returned from auth.signUp');
        notify.error('Failed to create user account. Please try again.');
        return;
      }

      console.log('Auth user created successfully:', authData.user.id);

      // Step 3: Create the partner account
      console.log('Creating partner account for user:', authData.user.id);

      const partnerAccount = await createPartnerAccount(
        authData.user.id,
        name,
        email,
        company,
        website
      );

      if (!partnerAccount) {
        console.error('Failed to create partner account');
        notify.error('Failed to create partner account. Please try again or contact support.');
        return;
      }

      console.log('Partner account created successfully:', partnerAccount.id);

      // Step 4: Check if email confirmation is required
      if (authData.session) {
        // User is already signed in (email confirmation not required)
        console.log('User is already signed in, redirecting to dashboard');
        notify.success('Registration successful! Redirecting to dashboard...');
        navigate('/partner/dashboard');
      } else {
        // Email confirmation is required
        console.log('Email confirmation required');
        notify.success('Registration successful! Please check your email to verify your account.');

        // Switch to login tab
        setActiveTab('login');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Generic error handling
      notify.error('An error occurred during registration. Please try again.');
    } finally {
      setIsRegistering(false);
    }
  };

  // If user is already logged in and has a partner account, redirect to dashboard
  if (!isLoading && partner) {
    navigate('/partner/dashboard');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col md:flex-row">
      {/* Left Side - Content */}
      <div className="md:w-1/2 bg-gradient-to-br from-dark-900 via-dark-950 to-dark-900 p-8 md:p-12 lg:p-16 flex flex-col justify-center relative overflow-hidden">
        <div className="max-w-xl mx-auto md:mx-0 relative z-10">
          {/* Logo */}
          <div className="mb-8 flex items-center">
            <div className="mr-3">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-fiery to-cool bg-clip-text text-transparent">Firenest</h1>
              <p className="text-white/60 text-sm">Partner Portal</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="mb-4">
            <span className="inline-block py-1 px-3 bg-fiery/10 text-fiery rounded-full text-sm font-medium mb-4">
              Partner Program
            </span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            Integrate Your <span className="bg-gradient-to-r from-fiery to-cool bg-clip-text text-transparent">AI Tool</span> with Firenest
          </h1>
          <p className="text-xl text-white/80 mb-8 leading-relaxed">
            Join our ecosystem of premium AI tools and reach thousands of users. Seamless integration, powerful analytics, and increased visibility for your product.
          </p>

          {/* Benefits */}
          <div className="space-y-4 mb-8">
            <div className="flex items-start">
              <div className="mt-1 mr-3 w-5 h-5 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-fiery">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <p className="text-white/70">Seamless redirection to your native platform</p>
            </div>
            <div className="flex items-start">
              <div className="mt-1 mr-3 w-5 h-5 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-fiery">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <p className="text-white/70">Flexible authentication options (OAuth, API keys)</p>
            </div>
            <div className="flex items-start">
              <div className="mt-1 mr-3 w-5 h-5 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-fiery">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <p className="text-white/70">Real-time usage tracking and analytics</p>
            </div>
          </div>

          {/* Trust indicators */}
          <div className="mt-12">
            <p className="text-white/60 mb-4 text-sm uppercase tracking-wider font-medium">Trusted by leading AI companies</p>
            <div className="flex flex-wrap items-center gap-6">
              <div className="h-8 w-24 bg-white/10 rounded-md"></div>
              <div className="h-8 w-20 bg-white/10 rounded-md"></div>
              <div className="h-8 w-28 bg-white/10 rounded-md"></div>
              <div className="h-8 w-22 bg-white/10 rounded-md"></div>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-0 right-0 w-2/3 h-2/3 bg-fiery/5 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-cool/5 rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/4 left-1/4 w-1/3 h-1/3 bg-fiery/3 rounded-full filter blur-2xl"></div>

          {/* Grid pattern overlay */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-grid-pattern"></div>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Forms */}
      <div className="md:w-1/2 bg-dark-950 flex flex-col justify-center p-8 md:p-12 lg:p-16">
        <div className="max-w-md w-full mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="login" id="login-tab" className="text-base">Sign In</TabsTrigger>
              <TabsTrigger value="register" id="register-tab" className="text-base">Create Account</TabsTrigger>
            </TabsList>

            {/* Error display */}
            {loginError && activeTab === 'login' && (
              <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-md flex items-start">
                <AlertCircle className="text-red-500 w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
                <p className="text-red-500 text-sm">{loginError}</p>
              </div>
            )}

            {registerError && activeTab === 'register' && (
              <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-md flex items-start">
                <AlertCircle className="text-red-500 w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
                <p className="text-red-500 text-sm">{registerError}</p>
              </div>
            )}

            <TabsContent value="login">
              <Card className="firenest-card border-0 shadow-lg">
                <CardHeader className="pb-2">
                  <CardTitle className="text-2xl font-bold text-white">Welcome back</CardTitle>
                  <CardDescription className="text-white/60 text-base">
                    Sign in to manage your AI tools and integrations
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleLogin}>
                  <CardContent className="space-y-5 pt-6">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium">Email address</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                        <Button variant="link" className="text-fiery p-0 h-auto text-sm">
                          Forgot password?
                        </Button>
                      </div>
                      <Input
                        id="password"
                        type="password"
                        placeholder="••••••••"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="h-11"
                        required
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex-col space-y-4 pt-2">
                    <Button
                      type="submit"
                      className="w-full bg-fiery hover:bg-fiery/90 text-white h-11 text-base"
                      disabled={isLoggingIn}
                    >
                      {isLoggingIn ? (
                        <>
                          <Loading size="sm" className="mr-2" />
                          Signing in...
                        </>
                      ) : (
                        'Sign in'
                      )}
                    </Button>
                    <p className="text-white/60 text-sm text-center">
                      Don't have an account?{' '}
                      <Button
                        variant="link"
                        className="text-fiery p-0 h-auto text-sm"
                        onClick={() => setActiveTab('register')}
                      >
                        Create one now
                      </Button>
                    </p>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>

            <TabsContent value="register">
              <Card className="firenest-card border-0 shadow-lg">
                <CardHeader className="pb-2">
                  <CardTitle className="text-2xl font-bold text-white">Create your account</CardTitle>
                  <CardDescription className="text-white/60 text-base">
                    Join the Firenest partner ecosystem
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleRegister}>
                  <CardContent className="space-y-5 pt-6">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-medium">Full name</Label>
                      <Input
                        id="name"
                        placeholder="John Doe"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium">Work email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        placeholder="Create a secure password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="h-11"
                        required
                      />
                      <p className="text-white/40 text-xs">Must be at least 6 characters</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-sm font-medium">Company name</Label>
                      <Input
                        id="company"
                        placeholder="Acme Inc."
                        value={company}
                        onChange={(e) => setCompany(e.target.value)}
                        className="h-11"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="website" className="text-sm font-medium">Company website <span className="text-white/40">(optional)</span></Label>
                      <Input
                        id="website"
                        placeholder="https://example.com"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        className="h-11"
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex-col space-y-4 pt-2">
                    <Button
                      type="submit"
                      className="w-full bg-fiery hover:bg-fiery/90 text-white h-11 text-base"
                      disabled={isRegistering}
                    >
                      {isRegistering ? (
                        <>
                          <Loading size="sm" className="mr-2" />
                          Creating account...
                        </>
                      ) : (
                        'Create account'
                      )}
                    </Button>
                    <p className="text-white/60 text-sm text-center">
                      Already have an account?{' '}
                      <Button
                        variant="link"
                        className="text-fiery p-0 h-auto text-sm"
                        onClick={() => setActiveTab('login')}
                      >
                        Sign in
                      </Button>
                    </p>
                    <p className="text-white/40 text-xs text-center mt-4">
                      By creating an account, you agree to our{' '}
                      <Button variant="link" className="text-fiery p-0 h-auto text-xs">Terms of Service</Button>{' '}
                      and{' '}
                      <Button variant="link" className="text-fiery p-0 h-auto text-xs">Privacy Policy</Button>
                    </p>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

const PartnerPortalWithErrorBoundary: React.FC = () => {
  return (
    <PartnerErrorBoundary>
      <PartnerPortal />
    </PartnerErrorBoundary>
  );
};

export default PartnerPortalWithErrorBoundary;
