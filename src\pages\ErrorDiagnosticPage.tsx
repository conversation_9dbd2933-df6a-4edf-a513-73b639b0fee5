import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {
  AlertTriangle,
  RefreshCw,
  Home,
  ChevronDown,
  ChevronUp,
  Copy,
  Download,
  Bug,
  Trash2,
  Clock,
  ArrowLeft,
  Database,
  Wifi,
  Shield,
  FileCode,
  Server
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { notify } from '@/components/ui/notification-system';
import {
  getStoredErrors,
  clearStoredErrors,
  formatErrorStack,
  createDiagnosticReport,
  ErrorCategory
} from '@/lib/error-utils';

interface ErrorDiagnosticPageProps {
  message?: string;
  stack?: string;
  component?: string;
  category?: string;
  path?: string;
  onReset?: () => void;
}

/**
 * A dedicated page for displaying and diagnosing errors in a structured way
 */
const ErrorDiagnosticPage: React.FC<ErrorDiagnosticPageProps> = (props) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [storedErrors, setStoredErrors] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('current');
  const [expandedErrors, setExpandedErrors] = useState<Set<number>>(new Set());

  // Get error details from props or URL parameters
  const errorMessage = props.message || searchParams.get('message') || 'Unknown error';
  const errorStack = props.stack || searchParams.get('stack') || '';
  const errorComponent = props.component || searchParams.get('component') || '';
  const errorCategory = props.category || searchParams.get('category') || 'unknown';
  const errorPath = props.path || searchParams.get('path') || window.location.pathname;

  useEffect(() => {
    // Load stored errors from localStorage
    setStoredErrors(getStoredErrors());
  }, []);

  const toggleErrorDetails = (index: number) => {
    const newExpanded = new Set(expandedErrors);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedErrors(newExpanded);
  };

  const handleClearErrors = () => {
    clearStoredErrors();
    setStoredErrors([]);
    notify.success('Error history cleared', {
      title: 'Success',
      duration: 3000
    });
  };

  const copyErrorDetails = (error: any = null) => {
    const report = error
      ? createDiagnosticReport(new Error(error.message), {
          path: error.context?.path || errorPath,
          additionalData: {
            componentStack: error.componentStack,
            category: error.category,
            timestamp: error.timestamp
          }
        })
      : createDiagnosticReport(new Error(errorMessage), {
          path: errorPath,
          component: errorComponent,
          additionalData: { stack: errorStack, category: errorCategory }
        });

    navigator.clipboard.writeText(report)
      .then(() => notify.success('Error details copied to clipboard', {
        title: 'Copied',
        duration: 3000
      }))
      .catch(() => notify.error('Failed to copy error details', {
        title: 'Copy Failed',
        duration: 4000
      }));
  };

  const downloadErrorReport = (error: any = null) => {
    const report = error
      ? createDiagnosticReport(new Error(error.message), {
          path: error.context?.path || errorPath,
          additionalData: {
            componentStack: error.componentStack,
            category: error.category,
            timestamp: error.timestamp
          }
        })
      : createDiagnosticReport(new Error(errorMessage), {
          path: errorPath,
          component: errorComponent,
          additionalData: { stack: errorStack, category: errorCategory }
        });

    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `firenest-error-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    notify.success('Error report downloaded', {
      title: 'Download Complete',
      duration: 3000
    });
  };

  // Get icon for error category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case ErrorCategory.NETWORK:
        return <Wifi className="h-4 w-4 text-orange-400" />;
      case ErrorCategory.AUTH:
        return <Shield className="h-4 w-4 text-red-400" />;
      case ErrorCategory.DATABASE:
        return <Database className="h-4 w-4 text-blue-400" />;
      case ErrorCategory.API:
        return <Server className="h-4 w-4 text-purple-400" />;
      case ErrorCategory.RENDERING:
        return <FileCode className="h-4 w-4 text-green-400" />;
      default:
        return <Bug className="h-4 w-4 text-yellow-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-dark-900 text-white p-4 md:p-8">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />

      <div className="max-w-6xl mx-auto">
        <header className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => navigate(-1)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Error Diagnostics</h1>
          </div>
          <p className="text-white/70">
            Detailed information about errors to help with troubleshooting
          </p>
        </header>

        <Tabs defaultValue="current" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="firenest-card mb-6">
            <TabsTrigger value="current" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Current Error
            </TabsTrigger>
            <TabsTrigger value="history" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              <Clock className="h-4 w-4 mr-2" />
              Error History ({storedErrors.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="current">
            <Card className="firenest-card mb-6">
              <CardHeader className="border-b border-white/10 pb-4">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-red-500/20 flex items-center justify-center">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                  <CardTitle className="text-xl text-white">Error Details</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pt-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-dark-800 rounded-lg border border-white/5">
                    <h3 className="text-white font-medium mb-3 flex items-center">
                      <Bug className="h-4 w-4 mr-2 text-red-400" />
                      Error Information
                    </h3>
                    <div className="space-y-3 text-white/70 text-sm">
                      <div>
                        <div className="text-white/50 mb-1">Message:</div>
                        <div className="bg-dark-950 p-3 rounded text-white/80">{errorMessage}</div>
                      </div>
                      <div>
                        <div className="text-white/50 mb-1">Category:</div>
                        <div className="flex items-center">
                          {getCategoryIcon(errorCategory)}
                          <span className="ml-2 capitalize">{errorCategory}</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-white/50 mb-1">Location:</div>
                        <div>{errorPath}</div>
                      </div>
                      {errorComponent && (
                        <div>
                          <div className="text-white/50 mb-1">Component:</div>
                          <div>{errorComponent}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="p-4 bg-dark-800 rounded-lg border border-white/5">
                    <h3 className="text-white font-medium mb-3 flex items-center">
                      <RefreshCw className="h-4 w-4 mr-2 text-blue-400" />
                      Troubleshooting Steps
                    </h3>
                    <ul className="text-white/70 text-sm space-y-2 list-disc pl-5">
                      <li>Try refreshing the page</li>
                      <li>Clear your browser cache and cookies</li>
                      <li>Check your internet connection</li>
                      <li>Try again later or contact support if the issue persists</li>
                    </ul>
                  </div>
                </div>

                {errorStack && (
                  <div className="p-4 bg-dark-800 rounded-lg border border-white/5">
                    <h3 className="text-white font-medium mb-3 flex items-center">
                      <FileCode className="h-4 w-4 mr-2 text-green-400" />
                      Stack Trace
                    </h3>
                    <pre className="bg-dark-950 p-3 rounded overflow-auto text-xs text-white/60 max-h-[300px]">
                      {formatErrorStack(errorStack)}
                    </pre>
                  </div>
                )}

                <div className="p-4 bg-dark-800 rounded-lg border border-white/5">
                  <h3 className="text-white font-medium mb-3 flex items-center">
                    <Server className="h-4 w-4 mr-2 text-purple-400" />
                    System Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-white/70">
                    <div>
                      <div className="text-white/50 mb-1">Browser:</div>
                      <div>{navigator.userAgent}</div>
                    </div>
                    <div>
                      <div className="text-white/50 mb-1">Time:</div>
                      <div>{new Date().toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-white/50 mb-1">URL:</div>
                      <div>{window.location.href}</div>
                    </div>
                    <div>
                      <div className="text-white/50 mb-1">Online Status:</div>
                      <div className="flex items-center">
                        {navigator.onLine ? (
                          <>
                            <div className="h-2 w-2 rounded-full bg-green-400 mr-2"></div>
                            <span>Online</span>
                          </>
                        ) : (
                          <>
                            <div className="h-2 w-2 rounded-full bg-red-400 mr-2"></div>
                            <span>Offline</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-4 flex flex-wrap gap-3">
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => props.onReset ? props.onReset() : window.location.reload()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Page
                </Button>
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => navigate(-1)}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Go Back
                </Button>
                <Link to="/dashboard">
                  <Button className="bg-fiery hover:bg-fiery-600">
                    <Home className="h-4 w-4 mr-2" />
                    Return to Dashboard
                  </Button>
                </Link>
                <div className="ml-auto flex gap-2">
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={() => copyErrorDetails()}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                    onClick={() => downloadErrorReport()}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card className="firenest-card mb-6">
              <CardHeader className="border-b border-white/10 pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-blue-500" />
                    </div>
                    <CardTitle className="text-xl text-white">Error History</CardTitle>
                  </div>
                  {storedErrors.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-white/10 hover:bg-white/5"
                      onClick={handleClearErrors}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear History
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                {storedErrors.length > 0 ? (
                  <div className="space-y-4">
                    {storedErrors.map((error, index) => (
                      <div
                        key={index}
                        className="p-4 bg-dark-800 rounded-lg border border-white/5"
                      >
                        <div
                          className="flex items-center justify-between cursor-pointer"
                          onClick={() => toggleErrorDetails(index)}
                        >
                          <div className="flex items-center gap-3">
                            {getCategoryIcon(error.category || 'unknown')}
                            <div>
                              <div className="font-medium text-white">{error.message}</div>
                              <div className="text-xs text-white/50">
                                {new Date(error.timestamp).toLocaleString()} •
                                <span className="ml-1 capitalize">{error.category || 'unknown'}</span>
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-white/50"
                          >
                            {expandedErrors.has(index) ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </Button>
                        </div>

                        {expandedErrors.has(index) && (
                          <div className="mt-4 pt-4 border-t border-white/10">
                            {error.stack && (
                              <div className="mb-4">
                                <h4 className="text-sm font-medium text-white/70 mb-2">Stack Trace:</h4>
                                <pre className="bg-dark-950 p-3 rounded overflow-auto text-xs text-white/60 max-h-[200px]">
                                  {formatErrorStack(error.stack)}
                                </pre>
                              </div>
                            )}

                            {error.componentStack && (
                              <div className="mb-4">
                                <h4 className="text-sm font-medium text-white/70 mb-2">Component Stack:</h4>
                                <pre className="bg-dark-950 p-3 rounded overflow-auto text-xs text-white/60 max-h-[200px]">
                                  {formatErrorStack(error.componentStack)}
                                </pre>
                              </div>
                            )}

                            <div className="flex gap-2 mt-4">
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-white/10 hover:bg-white/5"
                                onClick={() => copyErrorDetails(error)}
                              >
                                <Copy className="h-3.5 w-3.5 mr-1.5" />
                                Copy
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-white/10 hover:bg-white/5"
                                onClick={() => downloadErrorReport(error)}
                              >
                                <Download className="h-3.5 w-3.5 mr-1.5" />
                                Download
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="h-16 w-16 rounded-full bg-white/5 flex items-center justify-center mx-auto mb-4">
                      <AlertTriangle className="h-8 w-8 text-white/30" />
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">No errors in history</h3>
                    <p className="text-white/70 max-w-md mx-auto">
                      There are no stored errors in your history. Errors will appear here when they occur.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ErrorDiagnosticPage;
