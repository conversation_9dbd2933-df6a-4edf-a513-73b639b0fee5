import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import AuthLayout from '@/components/AuthLayout';
import { Button } from '@/components/ui/button';
import { Loader2, Mail, CheckCircle } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { supabase } from '@/lib/supabase';
import { checkEmailVerification } from '@/lib/auth';
import { generateVerificationCode, storeVerificationCode } from '@/lib/verification';

const EmailVerification = () => {
  const [email, setEmail] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Get email from location state if available
    if (location.state?.email) {
      setEmail(location.state.email);
    }

    // Show message if available
    if (location.state?.message) {
      notify.success(location.state.message, {
        title: 'Success',
        duration: 4000
      });
    }
  }, [location]);

  const handleResendVerification = async () => {
    if (!email) {
      notify.error('No email address available', {
        title: 'Missing Information',
        duration: 5000
      });
      return;
    }

    setIsResending(true);
    try {
      // Add the redirect URL when resending the verification email
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/verify-email-confirm`,
        }
      });

      if (error) {
        console.error('Error resending verification email:', error);
        notify.error(error.message || 'Failed to resend verification email', {
          title: 'Email Error',
          duration: 5000
        });
      } else {
        console.log('Verification email resent with redirect URL:', `${window.location.origin}/verify-email-confirm`);
        notify.success('Verification email resent. Please check your inbox and spam folder.', {
          title: 'Email Sent',
          duration: 5000,
          action: {
            label: 'Check Inbox',
            onClick: () => window.open('https://mail.google.com', '_blank')
          }
        });

        // Generate and store a verification code for manual verification
        const verificationCode = generateVerificationCode();
        storeVerificationCode(email, verificationCode);

        // Show the verification code in a toast for demo purposes
        // In production, this would be sent via email or SMS
        notify.custom(
          <div>
            <p><strong>Demo Mode:</strong> Use this verification code for manual verification:</p>
            <p className="font-mono text-lg mt-1">{verificationCode}</p>
          </div>,
          {
            title: 'Verification Code',
            duration: 15000,
            position: 'top-center',
            closeButton: true
          }
        );
      }
    } catch (err) {
      console.error('Unexpected error resending verification:', err);
      notify.error('An unexpected error occurred. Please try again.', {
        title: 'Verification Error',
        duration: 5000
      });
    } finally {
      setIsResending(false);
    }
  };

  const checkVerificationStatus = async () => {
    if (!email) {
      notify.error('No email address available', {
        title: 'Missing Information',
        duration: 5000
      });
      return;
    }

    setIsChecking(true);
    try {
      // First check if there's an existing session for this user
      const { data: sessionData } = await supabase.auth.getSession();

      // If we have a session with the same email and it's confirmed, we're good
      if (sessionData?.session?.user?.email === email &&
          sessionData?.session?.user?.email_confirmed_at) {
        setIsVerified(true);
        notify.success('Your email has been verified!', {
          title: 'Verification Complete',
          duration: 4000,
          position: 'top-center'
        });
        setTimeout(() => {
          navigate('/login', {
            state: { message: 'Email verified successfully. You can now log in.' }
          });
        }, 2000);
        return;
      }

      // Use the dedicated function to check email verification status
      const { verified, error } = await checkEmailVerification(email);

      if (verified) {
        // Email is verified
        setIsVerified(true);
        notify.success('Your email has been verified!', {
          title: 'Verification Complete',
          duration: 4000,
          position: 'top-center'
        });
        setTimeout(() => {
          navigate('/login', {
            state: { message: 'Email verified successfully. You can now log in.' }
          });
        }, 2000);
      } else {
        // Email is not verified
        if (error?.message?.includes('Email not confirmed')) {
          notify.error('Your email is still not verified. Please check your inbox.', {
            title: 'Verification Pending',
            duration: 5000,
            action: {
              label: 'Check Inbox',
              onClick: () => window.open('https://mail.google.com', '_blank')
            }
          });
        } else {
          notify.error('Unable to confirm verification status. Please check your inbox for the verification link.', {
            title: 'Verification Status Unknown',
            duration: 5000,
            action: {
              label: 'Check Inbox',
              onClick: () => window.open('https://mail.google.com', '_blank')
            }
          });
        }
        setIsVerified(false);
      }
    } catch (err) {
      console.error('Unexpected error checking verification:', err);
      notify.error('An unexpected error occurred. Please try again.', {
        title: 'Verification Error',
        duration: 5000
      });
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <AuthLayout
      title="Verify Your Email"
      subtitle="Please verify your email address to continue"
    >
      <div className="space-y-6">
        {isVerified ? (
          <div className="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-6 rounded-md text-center">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-400" />
            <h3 className="text-lg font-medium mb-2">Email Verified!</h3>
            <p>Your email has been successfully verified.</p>
            <p className="mt-2">Redirecting to login page...</p>
          </div>
        ) : (
          <>
            <div className="bg-blue-500/20 border border-blue-500/50 text-blue-200 px-4 py-6 rounded-md text-center">
              <Mail className="h-12 w-12 mx-auto mb-4 text-blue-400" />
              <h3 className="text-lg font-medium mb-2">Verification Required</h3>
              <p>We&apos;ve sent a verification email to:</p>
              <p className="font-medium mt-2">{email || 'your email address'}</p>
              <p className="mt-4 text-sm">Please check your inbox and spam folder for an email from <span className="font-medium"><EMAIL></span> and click the verification link to activate your account.</p>
              <div className="mt-4 p-3 bg-blue-500/10 rounded-md text-xs">
                <p className="font-medium">Troubleshooting Tips:</p>
                <ul className="list-disc pl-4 mt-1 space-y-1">
                  <li>Check your spam/junk folder</li>
                  <li>Add <EMAIL> to your contacts</li>
                  <li>Try using a different email provider</li>
                  <li>If you still don't receive the email, click "Resend Verification Email" below</li>
                  <li>If you have a verification code, you can <a href="/manual-verification" className="text-fiery hover:underline">verify manually</a></li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col space-y-3">
              <Button
                type="button"
                onClick={handleResendVerification}
                disabled={isResending}
                variant="outline"
                className="w-full border-white/20 hover:bg-white/5"
              >
                {isResending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Resending...
                  </>
                ) : (
                  'Resend Verification Email'
                )}
              </Button>

              <Button
                type="button"
                onClick={checkVerificationStatus}
                disabled={isChecking}
                className="w-full pop-button"
              >
                {isChecking ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Checking...
                  </>
                ) : (
                  'I\'ve Verified My Email'
                )}
              </Button>

              <Button
                type="button"
                onClick={() => navigate('/login')}
                variant="ghost"
                className="text-white/70 hover:text-white"
              >
                Back to Login
              </Button>
            </div>
          </>
        )}
      </div>
    </AuthLayout>
  );
};

export default EmailVerification;
