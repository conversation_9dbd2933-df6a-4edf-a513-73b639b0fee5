# Firenest Deployment Checklist

This checklist will help ensure that your Firenest application is ready for deployment.

## Authentication Setup

- [x] Supabase authentication is properly configured
- [x] SQL tables for user data are created in Supabase
- [x] Email authentication is enabled in Supabase
- [x] Mock authentication is disabled (`USE_MOCK_AUTH = false` in `src/lib/auth.ts`)
- [x] Login and signup forms are working correctly
- [x] Error handling is implemented for authentication failures
- [x] Protected routes redirect to login when not authenticated

## Environment Variables

- [ ] Create a `.env.production` file with production environment variables
- [ ] Ensure Supabase URL and API key are set correctly for production
- [ ] Remove any development-specific environment variables

## Build and Performance

- [ ] Run `npm run build` to ensure the application builds successfully
- [ ] Test the production build locally with `npm run preview`
- [ ] Check for any console errors in the production build
- [ ] Optimize images and assets for production
- [ ] Implement code splitting for better performance

## Security

- [ ] Ensure sensitive data is not exposed in client-side code
- [ ] Check that API keys and secrets are properly secured
- [ ] Implement proper CORS settings in Supabase
- [ ] Set up Row Level Security (RLS) policies in Supabase
- [ ] Ensure all forms have proper validation

## Testing

- [ ] Test login functionality with valid credentials
- [ ] Test login with invalid credentials to ensure proper error handling
- [ ] Test signup process and email verification
- [ ] Test password reset functionality
- [ ] Test protected routes to ensure they require authentication
- [ ] Test on different browsers (Chrome, Firefox, Safari, Edge)
- [ ] Test on different devices (desktop, tablet, mobile)

## SEO and Metadata

- [ ] Set appropriate page titles and meta descriptions
- [ ] Add Open Graph tags for social media sharing
- [ ] Ensure proper favicon and app icons are set
- [ ] Create a robots.txt file
- [ ] Create a sitemap.xml file

## Analytics and Monitoring

- [ ] Set up analytics (Google Analytics, Plausible, etc.)
- [ ] Implement error tracking (Sentry, LogRocket, etc.)
- [ ] Set up performance monitoring
- [ ] Configure logging for server-side operations

## Deployment Platform

- [ ] Choose a deployment platform (Vercel, Netlify, AWS, etc.)
- [ ] Configure build settings for the chosen platform
- [ ] Set up environment variables on the deployment platform
- [ ] Configure custom domain and SSL
- [ ] Set up CI/CD pipeline for automated deployments

## Post-Deployment

- [ ] Verify that the deployed application works correctly
- [ ] Check that authentication flows work in production
- [ ] Test all critical user journeys
- [ ] Monitor for any errors or performance issues
- [ ] Set up regular backups of Supabase data

## Supabase-Specific Checks

- [ ] Verify that all required tables exist in Supabase
- [ ] Check that triggers and functions are working correctly
- [ ] Ensure RLS policies are properly configured
- [ ] Test database queries and mutations
- [ ] Verify that email templates are configured correctly

## Final Checks

- [ ] Remove any test or debug code
- [ ] Ensure console.log statements are removed or disabled in production
- [ ] Check for any hardcoded URLs or paths
- [ ] Verify that all links work correctly
- [ ] Test the application with a slow network connection

## Notes for Firenest

1. **Email Verification**: Supabase requires email verification by default. Make sure your users know to check their email after signing up.

2. **Password Requirements**: The current password requirements are:
   - At least 6 characters
   - At least one uppercase letter
   - At least one lowercase letter
   - At least one number

3. **Authentication Tables**: The following tables should be created in Supabase:
   - `users`
   - `user_profiles`
   - `user_settings`
   - `user_credits`
   - `credit_transactions`

4. **Row Level Security**: Make sure RLS policies are properly configured to protect user data.

5. **Error Handling**: The application has been updated to handle common authentication errors, but you should monitor for any unexpected errors after deployment.
