/**
 * Auth0 JWT Validation Middleware
 * SOC 2 Alignment: CC6.1 (Logical Access), CC6.2 (Access Control)
 */

import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import jwksClient from 'jwks-rsa'
import { config } from '@/config/environment'
import { logger } from '@/utils/logger'
import { AuthenticationError } from './errorHandler'

// JWKS client for Auth0
const client = jwksClient({
  jwksUri: `https://${config.auth.provider.domain}/.well-known/jwks.json`,
  requestHeaders: {}, // Optional
  timeout: 30000, // Defaults to 30s
  cache: true,
  cacheMaxEntries: 5, // Default value
  cacheMaxAge: 600000, // Default value (10 minutes)
})

// Function to get signing key
function getKey(header: any, callback: any) {
  client.getSigningKey(header.kid, (err, key) => {
    if (err) {
      logger.error('Error getting signing key:', err)
      return callback(err)
    }
    const signingKey = key?.getPublicKey()
    callback(null, signingKey)
  })
}

// Auth0 JWT verification middleware
export function verifyAuth0Token(req: Request, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return next(new AuthenticationError('Missing or invalid authorization header'))
  }

  const token = authHeader.substring(7) // Remove 'Bearer ' prefix

  // For development/demo mode, allow mock tokens
  if (config.environment === 'development' && token.startsWith('eyJ')) {
    try {
      // Check if it's a mock token (3 parts separated by dots)
      const parts = token.split('.')
      if (parts.length === 3) {
        // Try to decode the payload
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString())
        
        // If it has the expected mock structure, allow it
        if (payload.sub && payload.email && payload.sub.includes('demo')) {
          req.user = {
            sub: payload.sub,
            email: payload.email,
            name: payload.name || 'Demo User',
            role: payload.role || 'user'
          }
          return next()
        }
      }
    } catch (error) {
      // If mock token parsing fails, continue with Auth0 validation
      logger.debug('Mock token parsing failed, trying Auth0 validation')
    }
  }

  // Verify Auth0 JWT token
  jwt.verify(
    token,
    getKey,
    {
      audience: `https://${config.auth.provider.domain}/api/v2/`,
      issuer: `https://${config.auth.provider.domain}/`,
      algorithms: ['RS256']
    },
    (err, decoded) => {
      if (err) {
        logger.error('JWT verification failed:', err)
        return next(new AuthenticationError('Invalid token'))
      }

      if (!decoded || typeof decoded !== 'object') {
        return next(new AuthenticationError('Invalid token payload'))
      }

      // Extract user information from token
      req.user = {
        sub: decoded.sub,
        email: decoded.email,
        name: decoded.name,
        role: decoded['https://firenest.com/role'] || 'user', // Custom claim
        permissions: decoded['https://firenest.com/permissions'] || []
      }

      next()
    }
  )
}

// Optional: Role-based access control middleware
export function requireRole(roles: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'))
    }

    const userRole = req.user.role
    if (!roles.includes(userRole)) {
      return next(new AuthenticationError('Insufficient permissions'))
    }

    next()
  }
}

// Optional: Permission-based access control middleware
export function requirePermission(permission: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'))
    }

    const userPermissions = req.user.permissions || []
    if (!userPermissions.includes(permission)) {
      return next(new AuthenticationError('Insufficient permissions'))
    }

    next()
  }
}
