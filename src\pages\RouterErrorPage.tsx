import React from 'react';
import { useNavigate } from 'react-router-dom';
import ErrorDiagnosticPage from '@/pages/ErrorDiagnosticPage';
import { ErrorCategory } from '@/lib/error-utils';

interface RouterErrorPageProps {
  error: Error | null;
  errorInfo?: React.ErrorInfo | null;
  title?: string;
  message?: string;
  onReset?: () => void;
}

/**
 * RouterErrorPage - A version of ErrorPage that uses React Router's useNavigate hook.
 * This component should only be used within a Router context.
 */
const RouterErrorPage: React.FC<RouterErrorPageProps> = ({
  error,
  errorInfo,
  title = 'Something went wrong',
  message = 'We encountered an unexpected error',
  onReset
}) => {
  const navigate = useNavigate();

  // Determine error category based on error message or stack
  let category = ErrorCategory.UNKNOWN;
  if (error?.stack?.includes('fetch') || error?.message?.includes('network')) {
    category = ErrorCategory.NETWORK;
  } else if (error?.stack?.includes('render') || error?.stack?.includes('component')) {
    category = ErrorCategory.RENDERING;
  }

  // Get component name from error info if possible
  const componentMatch = errorInfo?.componentStack?.match(/\s+in\s+([A-Za-z0-9_]+)/);
  const componentName = componentMatch ? componentMatch[1] : undefined;

  const handleReset = () => {
    if (onReset) {
      onReset();
    } else {
      navigate('/');
    }
  };

  return (
    <ErrorDiagnosticPage
      message={error?.message}
      stack={error?.stack}
      component={componentName}
      category={category}
      path={window.location.pathname}
      onReset={handleReset}
    />
  );
};

export default RouterErrorPage;
