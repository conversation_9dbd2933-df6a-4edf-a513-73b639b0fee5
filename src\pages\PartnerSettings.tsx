import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { updatePartnerAccount } from '@/lib/partner-portal/api';
import { supabase } from '@/lib/supabase';
import { ArrowLeft, Save, Key, AlertTriangle, Copy, Check, Info } from 'lucide-react';

const PartnerSettings: React.FC = () => {
  const navigate = useNavigate();
  const { partner, refreshPartnerData } = usePartner();

  const [name, setName] = useState(partner?.name || '');
  const [company, setCompany] = useState(partner?.company || '');
  const [website, setWebsite] = useState(partner?.website || '');
  const [description, setDescription] = useState(partner?.description || '');
  const [isSaving, setIsSaving] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [idCopied, setIdCopied] = useState(false);

  const handleSaveProfile = async () => {
    if (!partner) return;

    setIsSaving(true);

    try {
      const updatedPartner = await updatePartnerAccount(partner.id, {
        name,
        company,
        website,
        description
      });

      if (!updatedPartner) {
        throw new Error('Failed to update profile');
      }

      await refreshPartnerData();
      notify.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      notify.error(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleChangePassword = async () => {
    if (!newPassword || !confirmPassword) {
      notify.error('Please enter a new password and confirm it');
      return;
    }

    if (newPassword !== confirmPassword) {
      notify.error('New password and confirmation do not match');
      return;
    }

    setIsChangingPassword(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      notify.success('Password updated successfully');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      console.error('Error changing password:', error);
      notify.error(error.message || 'Failed to change password');
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleCopyId = async () => {
    if (!partner) return;

    try {
      await navigator.clipboard.writeText(partner.id);
      setIdCopied(true);
      notify.success('Partner ID copied to clipboard');

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setIdCopied(false);
      }, 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      notify.error('Failed to copy to clipboard');
    }
  };

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Account Settings</h1>
                <p className="text-white/60">Manage your Firenest partner account</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-1">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-xl text-white">Profile Information</CardTitle>
                <CardDescription>
                  Update your account details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="John Doe"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">Company Name</Label>
                  <Input
                    id="company"
                    value={company}
                    onChange={(e) => setCompany(e.target.value)}
                    placeholder="Your Company"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    placeholder="https://yourcompany.com"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Company Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Brief description of your company"
                    rows={4}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  className="bg-fiery hover:bg-fiery/90 text-white flex items-center space-x-2"
                  onClick={handleSaveProfile}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loading size="sm" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      <span>Save Changes</span>
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>

            <Card className="firenest-card mt-8">
              <CardHeader>
                <CardTitle className="text-xl text-white">Change Password</CardTitle>
                <CardDescription>
                  Update your account password
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder="••••••••"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="••••••••"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="••••••••"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  className="bg-fiery hover:bg-fiery/90 text-white"
                  onClick={handleChangePassword}
                  disabled={isChangingPassword}
                >
                  {isChangingPassword ? (
                    <>
                      <Loading size="sm" className="mr-2" />
                      Updating...
                    </>
                  ) : (
                    'Change Password'
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div>
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-xl text-white">API Keys</CardTitle>
                <CardDescription>
                  Manage your API keys for Firenest integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Key className="w-4 h-4 text-white/60" />
                        <span className="font-medium text-white">Partner API Key</span>
                      </div>
                      <span className="text-xs bg-green-500/20 text-green-500 px-2 py-1 rounded-full">Active</span>
                    </div>
                    <div className="bg-dark-900 rounded p-2 font-mono text-sm text-white/80 mb-2">
                      {partner.apiKey ?
                        `${partner.apiKey.substring(0, 8)}...${partner.apiKey.substring(partner.apiKey.length - 4)}` :
                        'No API key generated yet'
                      }
                    </div>
                    <div className="flex justify-end">
                      <Button variant="outline" size="sm">
                        {partner.apiKey ? 'Regenerate' : 'Generate'} Key
                      </Button>
                    </div>
                  </div>

                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <AlertTriangle className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-white mb-1">API Key Security</h4>
                        <p className="text-sm text-white/60">
                          Keep your API keys secure. Do not share them in publicly accessible areas such as GitHub, client-side code, or social media.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="firenest-card mt-8">
              <CardHeader>
                <CardTitle className="text-xl text-white">Account Status</CardTitle>
                <CardDescription>
                  Your current account status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-white/60">Status</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      partner.status === 'active' ? 'bg-green-500/20 text-green-500' :
                      partner.status === 'pending' ? 'bg-yellow-500/20 text-yellow-500' :
                      'bg-red-500/20 text-red-500'
                    }`}>
                      {partner.status.charAt(0).toUpperCase() + partner.status.slice(1)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-white/60">Joined</span>
                    <span className="text-white">{new Date(partner.createdAt).toLocaleDateString()}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-white/60">Email</span>
                    <span className="text-white">{partner.email}</span>
                  </div>

                  <div className="border-t border-white/10 pt-4 mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-white/60">Partner ID</span>
                        <div className="relative group">
                          <Info className="w-4 h-4 text-white/40 cursor-help" />
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-dark-800 text-white/80 text-xs p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                            This is the unique identifier for your partner organization in Firenest. You may need this ID when integrating with Firenest APIs or when contacting support.
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-white/60 hover:text-white hover:bg-white/5"
                        onClick={handleCopyId}
                      >
                        {idCopied ? (
                          <Check className="w-4 h-4 text-green-500" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    <div className="bg-dark-900 rounded p-2 font-mono text-sm text-white/80 break-all">
                      {partner.id}
                    </div>
                  </div>
                </div>

                {partner.status === 'pending' && (
                  <div className="mt-6 bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                    <p className="text-sm text-yellow-500">
                      Your account is pending approval. You can still set up your tools, but they won't be visible to users until approved.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-dark-900 border-t border-white/10 py-6 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-white/60 mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} Firenest. All rights reserved.
            </p>
            <div className="flex space-x-4">
              <Button variant="link" className="text-white/60 hover:text-white">
                Terms of Service
              </Button>
              <Button variant="link" className="text-white/60 hover:text-white">
                Privacy Policy
              </Button>
              <Button variant="link" className="text-white/60 hover:text-white">
                Support
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PartnerSettings;
