import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft, Code, HelpCircle } from 'lucide-react';
import FeatureManagement from '@/components/partner/FeatureManagement';
import { Loading } from '@/components/ui/loading';

const PartnerFeatures: React.FC = () => {
  const navigate = useNavigate();
  const { partner, isLoading } = usePartner();
  const [activeTab, setActiveTab] = useState('features');

  if (isLoading) {
    return <Loading />;
  }

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-dark-900 border-b border-white/10">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="mr-2 text-white/70 hover:text-white hover:bg-white/5"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <div className="h-6 w-px bg-white/10 mx-3"></div>
              <h1 className="text-xl font-bold">Metered Features</h1>
            </div>
            <div>
              <Button
                variant="ghost"
                size="sm"
                className="text-white/70 hover:text-white hover:bg-white/5"
                onClick={() => window.open('https://docs.firenest.com/partners/features', '_blank')}
              >
                <HelpCircle className="w-4 h-4 mr-2" />
                Documentation
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="firenest-card sticky top-24">
              <CardContent className="p-4">
                <nav className="space-y-1">
                  <Button
                    variant={activeTab === 'features' ? 'default' : 'ghost'}
                    className={`w-full justify-start ${
                      activeTab === 'features'
                        ? 'bg-gradient-to-r from-fiery to-fiery/90 text-white'
                        : 'text-white/70 hover:text-white hover:bg-white/5'
                    }`}
                    onClick={() => setActiveTab('features')}
                  >
                    Features
                  </Button>
                  <Button
                    variant={activeTab === 'sdk' ? 'default' : 'ghost'}
                    className={`w-full justify-start ${
                      activeTab === 'sdk'
                        ? 'bg-gradient-to-r from-fiery to-fiery/90 text-white'
                        : 'text-white/70 hover:text-white hover:bg-white/5'
                    }`}
                    onClick={() => setActiveTab('sdk')}
                  >
                    <Code className="w-4 h-4 mr-2" />
                    SDK Integration
                  </Button>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {activeTab === 'features' && (
              <FeatureManagement />
            )}

            {activeTab === 'sdk' && (
              <Card className="firenest-card">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-4">SDK Integration</h2>
                  <p className="text-white/70 mb-6">
                    Use our SDK to integrate your application with Firenest. The SDK provides simple methods for authentication, authorization checks, and usage reporting.
                  </p>

                  <div className="mb-8">
                    <h3 className="text-lg font-medium mb-2">Installation</h3>
                    <div className="bg-dark-800 p-4 rounded-md font-mono text-sm mb-2 overflow-x-auto">
                      npm install @firenest/sdk-node
                    </div>
                    <p className="text-white/60 text-sm">
                      For other platforms, we also offer Python, Ruby, PHP, and Java SDKs.
                    </p>
                  </div>

                  <div className="mb-8">
                    <h3 className="text-lg font-medium mb-2">Initialization</h3>
                    <div className="bg-dark-800 p-4 rounded-md font-mono text-sm mb-2 overflow-x-auto">
                      <pre>{`const { FirenestSDK } = require('@firenest/sdk-node');

const firenest = new FirenestSDK({
  clientId: '${partner.client_id || 'your-client-id'}',
  clientSecret: 'your-client-secret',
  environment: 'sandbox' // or 'production'
});`}</pre>
                    </div>
                  </div>

                  <div className="mb-8">
                    <h3 className="text-lg font-medium mb-2">Checking Access</h3>
                    <div className="bg-dark-800 p-4 rounded-md font-mono text-sm mb-2 overflow-x-auto">
                      <pre>{`// Check if a user has access to a feature
const accessResult = await firenest.checkAccess(
  firenestUserId,
  'feature-id'
);

if (!accessResult.allowed) {
  // User doesn't have access
  throw new Error(\`Access denied: \${accessResult.reason}\`);
}

// User has access, proceed with the feature
// ...`}</pre>
                    </div>
                  </div>

                  <div className="mb-8">
                    <h3 className="text-lg font-medium mb-2">Reporting Usage</h3>
                    <div className="bg-dark-800 p-4 rounded-md font-mono text-sm mb-2 overflow-x-auto">
                      <pre>{`// Report usage after successful execution
await firenest.reportUsage(
  firenestUserId,
  'feature-id'
);

// For features with dynamic costs
await firenest.reportUsage(
  firenestUserId,
  'feature-id',
  {
    unitsConsumed: 5,
    metadata: {
      // Additional information about the usage
    }
  }
);`}</pre>
                    </div>
                  </div>

                  <div className="mb-8">
                    <h3 className="text-lg font-medium mb-2">Error Handling</h3>
                    <div className="bg-dark-800 p-4 rounded-md font-mono text-sm mb-2 overflow-x-auto">
                      <pre>{`// Implement robust error handling for usage reporting
try {
  await firenest.reportUsage(firenestUserId, 'feature-id');
} catch (error) {
  // Log the error but don't fail the user's operation
  console.error('Failed to report usage to Firenest:', error);

  // Implement retry logic for transient errors
  if (isTransientError(error)) {
    setTimeout(async () => {
      try {
        await firenest.reportUsage(firenestUserId, 'feature-id');
      } catch (retryError) {
        console.error('Retry failed:', retryError);
      }
    }, 5000); // Retry after 5 seconds
  }
}`}</pre>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                      onClick={() => navigate('/partner/documentation/sdk')}
                    >
                      View Full Documentation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default PartnerFeatures;
