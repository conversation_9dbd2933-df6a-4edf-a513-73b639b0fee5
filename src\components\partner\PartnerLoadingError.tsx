import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle } from 'lucide-react';
import { usePartner } from '@/contexts/PartnerContext';

interface PartnerLoadingErrorProps {
  title?: string;
  message?: string;
}

/**
 * Component to display when there's an error loading partner data
 */
const PartnerLoadingError: React.FC<PartnerLoadingErrorProps> = ({
  title = 'Failed to load data',
  message
}) => {
  const { errorMessage, retryLoading } = usePartner();
  
  return (
    <div className="w-full h-full flex items-center justify-center p-8">
      <div className="flex flex-col items-center justify-center max-w-md">
        <div className="w-16 h-16 bg-fiery/10 rounded-full flex items-center justify-center mb-4">
          <AlertTriangle className="h-8 w-8 text-fiery" />
        </div>
        <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
        <p className="text-white/70 text-center mb-4">
          {message || 'We encountered an error while loading your data. Please try again.'}
        </p>
        {errorMessage && (
          <div className="bg-fiery/10 p-4 rounded-md mb-6 w-full">
            <p className="text-fiery text-sm">{errorMessage}</p>
          </div>
        )}
        <Button 
          onClick={retryLoading} 
          className="flex items-center gap-2 bg-fiery hover:bg-fiery/90"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Retry</span>
        </Button>
      </div>
    </div>
  );
};

export default PartnerLoadingError;
