import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, Code, FileText, BookOpen, Zap, Key, Webhook } from 'lucide-react';
import { Loading } from '@/components/ui/loading';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerDocumentation: React.FC = () => {
  const navigate = useNavigate();
  const { partner, isLoading } = usePartner();
  const [activeTab, setActiveTab] = useState('guides');

  if (isLoading) {
    return <Loading />;
  }

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Documentation</h1>
                <p className="text-white/60">Comprehensive guides and API documentation</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Search Bar */}
        <div className="relative mb-8">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/50" />
          <Input
            type="text"
            placeholder="Search documentation..."
            className="pl-10 py-6 firenest-card text-white text-lg"
          />
        </div>

        {/* Tabs */}
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="firenest-card">
            <TabsTrigger value="guides" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Guides
            </TabsTrigger>
            <TabsTrigger value="api" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              API Reference
            </TabsTrigger>
            <TabsTrigger value="sdk" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              SDK
            </TabsTrigger>
            <TabsTrigger value="examples" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
              Examples
            </TabsTrigger>
          </TabsList>

          {/* Guides Content */}
          <TabsContent value="guides" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <Zap className="w-5 h-5 mr-2 text-fiery" />
                    Getting Started
                  </CardTitle>
                  <CardDescription>
                    Learn the basics of integrating with Firenest
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/70 mb-4">
                    This guide will walk you through the process of setting up your first integration with Firenest.
                  </p>
                  <Button className="w-full bg-fiery hover:bg-fiery/90">
                    Read Guide
                  </Button>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <Key className="w-5 h-5 mr-2 text-fiery" />
                    Authentication
                  </CardTitle>
                  <CardDescription>
                    Set up OAuth 2.0 and OpenID Connect
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/70 mb-4">
                    Learn how to implement secure authentication using OAuth 2.0 and OpenID Connect protocols.
                  </p>
                  <Button
                    className="w-full bg-fiery hover:bg-fiery/90"
                    onClick={() => navigate('/partner/documentation/oidc')}
                  >
                    Read Guide
                  </Button>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <Webhook className="w-5 h-5 mr-2 text-fiery" />
                    Webhooks
                  </CardTitle>
                  <CardDescription>
                    Configure webhook notifications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/70 mb-4">
                    Learn how to set up and handle webhook notifications for real-time events.
                  </p>
                  <Button className="w-full bg-fiery hover:bg-fiery/90">
                    Read Guide
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* API Reference Content */}
          <TabsContent value="api" className="space-y-6">
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-white">API Reference</CardTitle>
                <CardDescription>
                  Complete reference for the Firenest API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2 flex items-center">
                      <Code className="w-5 h-5 mr-2 text-fiery" />
                      Authentication Endpoints
                    </h3>
                    <p className="text-white/70 mb-4">
                      Endpoints for OAuth 2.0 and OpenID Connect flows
                    </p>
                    <Button
                      variant="outline"
                      className="border-white/10 hover:bg-white/5"
                      onClick={() => navigate('/partner/documentation/oidc#endpoints')}
                    >
                      View Documentation
                    </Button>
                  </div>

                  <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2 flex items-center">
                      <Code className="w-5 h-5 mr-2 text-fiery" />
                      Usage Tracking Endpoints
                    </h3>
                    <p className="text-white/70 mb-4">
                      Endpoints for tracking and reporting usage
                    </p>
                    <Button variant="outline" className="border-white/10 hover:bg-white/5">
                      View Documentation
                    </Button>
                  </div>

                  <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2 flex items-center">
                      <Code className="w-5 h-5 mr-2 text-fiery" />
                      Webhook Endpoints
                    </h3>
                    <p className="text-white/70 mb-4">
                      Endpoints for webhook configuration and management
                    </p>
                    <Button variant="outline" className="border-white/10 hover:bg-white/5">
                      View Documentation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SDK Content */}
          <TabsContent value="sdk" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <FileText className="w-5 h-5 mr-2 text-fiery" />
                    Node.js SDK
                  </CardTitle>
                  <CardDescription>
                    Official Node.js SDK for Firenest
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/70 mb-4">
                    Integrate Firenest into your Node.js applications with our official SDK.
                  </p>
                  <Button
                    className="w-full bg-fiery hover:bg-fiery/90"
                    onClick={() => navigate('/partner/documentation/sdk')}
                  >
                    View Documentation
                  </Button>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-white">
                    <FileText className="w-5 h-5 mr-2 text-fiery" />
                    Python SDK
                  </CardTitle>
                  <CardDescription>
                    Official Python SDK for Firenest
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-white/70 mb-4">
                    Integrate Firenest into your Python applications with our official SDK.
                  </p>
                  <Button
                    className="w-full bg-fiery hover:bg-fiery/90"
                    onClick={() => navigate('/partner/documentation/sdk')}
                  >
                    View Documentation
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Examples Content */}
          <TabsContent value="examples" className="space-y-6">
            <Card className="firenest-card">
              <CardHeader>
                <CardTitle className="text-white">Code Examples</CardTitle>
                <CardDescription>
                  Ready-to-use code examples for common integration scenarios
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2">OAuth 2.0 Integration</h3>
                    <p className="text-white/70 mb-4">
                      Example code for implementing OAuth 2.0 authentication with Firenest
                    </p>
                    <Button variant="outline" className="border-white/10 hover:bg-white/5">
                      View Example
                    </Button>
                  </div>

                  <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2">OpenID Connect Integration</h3>
                    <p className="text-white/70 mb-4">
                      Example code for implementing OpenID Connect authentication with Firenest
                    </p>
                    <Button
                      variant="outline"
                      className="border-white/10 hover:bg-white/5"
                      onClick={() => navigate('/partner/documentation/oidc#code-examples')}
                    >
                      View Example
                    </Button>
                  </div>

                  <div className="p-4 rounded-md bg-dark-800 border border-white/10">
                    <h3 className="text-lg font-medium text-white mb-2">Usage Tracking</h3>
                    <p className="text-white/70 mb-4">
                      Example code for tracking and reporting usage to Firenest
                    </p>
                    <Button variant="outline" className="border-white/10 hover:bg-white/5">
                      View Example
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerDocumentation;
