{"name": "@firenest/sdk-node", "version": "0.1.0", "description": "Firenest SDK for Node.js applications", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint src --ext .ts", "prepublishOnly": "npm run build"}, "keywords": ["firenest", "sdk", "o<PERSON>h", "oidc"], "author": "Firenest", "license": "MIT", "dependencies": {"axios": "^1.6.0", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.6", "@types/jsonwebtoken": "^9.0.4", "@types/node": "^20.8.9", "@types/uuid": "^9.0.6", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}}