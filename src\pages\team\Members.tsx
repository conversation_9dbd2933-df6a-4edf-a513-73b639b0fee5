import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, Plus, Search, UserPlus } from 'lucide-react';
import { Input } from '@/components/ui/input';

/**
 * Team Members page with professional design
 */
const Members = () => {
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data for team members
  const members = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Admin',
      avatarUrl: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=FF4405&color=fff',
      status: 'active',
      lastActive: '2 hours ago'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Member',
      avatarUrl: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=6366F1&color=fff',
      status: 'active',
      lastActive: '5 minutes ago'
    },
    {
      id: 3,
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'Member',
      avatarUrl: 'https://ui-avatars.com/api/?name=Michael+Chen&background=10B981&color=fff',
      status: 'inactive',
      lastActive: '3 days ago'
    },
  ];

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Team Members</h1>
          <p className="text-white/70">Manage your team and permissions</p>
        </div>

        <Button className="bg-fiery hover:bg-fiery/90 text-white">
          <UserPlus className="h-4 w-4 mr-2" />
          Add Member
        </Button>
      </div>

      <Card className="firenest-card">
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle className="text-lg text-white">Members</CardTitle>
              <CardDescription>
                Your team has {members.length} members
              </CardDescription>
            </div>

            <div className="relative w-full md:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-white/50" />
              <Input
                type="text"
                placeholder="Search members..."
                className="pl-9 firenest-card text-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredMembers.length > 0 ? (
              filteredMembers.map((member) => (
                <div
                  key={member.id}
                  className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-3 rounded-md hover:bg-white/5 transition-colors"
                >
                  <div className="flex items-center gap-3 mb-2 sm:mb-0">
                    <Avatar className="h-10 w-10 border border-white/10">
                      <AvatarImage src={member.avatarUrl} alt={member.name} />
                      <AvatarFallback className="bg-fiery/20 text-fiery">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-white flex items-center">
                        {member.name}
                        <div className={`ml-2 h-2 w-2 rounded-full ${member.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      </div>
                      <div className="text-sm text-white/70">{member.email}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 w-full sm:w-auto">
                    <Badge variant="outline" className="bg-white/5 text-white/70 border-white/10">
                      {member.role}
                    </Badge>
                    <div className="text-xs text-white/50 hidden sm:block">
                      Last active {member.lastActive}
                    </div>
                    <Button variant="ghost" size="icon" className="ml-auto sm:ml-0">
                      <MoreHorizontal className="h-4 w-4 text-white/70" />
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-white/5 mb-4">
                  <Search className="h-6 w-6 text-white/50" />
                </div>
                <h3 className="text-sm font-medium text-white mb-1">No members found</h3>
                <p className="text-sm text-white/70">Try adjusting your search query</p>
              </div>
            )}
          </div>

          {filteredMembers.length > 0 && (
            <div className="mt-6 pt-4 border-t border-white/10 flex justify-between items-center">
              <div className="text-sm text-white/70">
                Showing {filteredMembers.length} of {members.length} members
              </div>
              <Button variant="outline" className="border-white/10 text-white hover:bg-white/5">
                <Plus className="h-4 w-4 mr-2" />
                Invite More
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Members;
