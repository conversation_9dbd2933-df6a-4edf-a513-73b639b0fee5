/**
 * Firenest Sandbox Backend API
 * Phase 0: Foundation & Secure Scaffolding
 * 
 * Zero-trust architecture with authentication on every request
 * SOC 2 Alignment: CC6.8 (Secure Software Development)
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { authMiddleware } from '@/middleware/auth';
import { auditMiddleware } from '@/middleware/audit';
import { validateConfig } from '@/config/environment';
import { connectDatabase } from '@/config/database';

// Import routes
import authRoutes from '@/routes/auth';
import workspaceRoutes from '@/routes/workspaces';
import projectRoutes from '@/routes/projects';
import uploadRoutes from '@/routes/uploads';
import modelRoutes from '@/routes/models';
import simulationRoutes from '@/routes/simulations';

const app = express();

// Validate environment configuration
validateConfig();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: config.cors.allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim())
  }
}));

// Health check endpoint (no auth required)
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env['npm_package_version'] || '1.0.0',
    environment: config.environment
  });
});

// API documentation endpoint
app.get('/api/v1', (_req, res) => {
  res.status(200).json({
    name: 'Firenest Sandbox API',
    version: '1.0.0',
    description: 'Pricing Intelligence & Implementation Platform',
    documentation: '/api/v1/docs',
    endpoints: {
      auth: '/api/v1/auth',
      workspaces: '/api/v1/workspaces',
      projects: '/api/v1/projects',
      uploads: '/api/v1/uploads'
    }
  });
});

// Authentication routes (no auth middleware)
app.use('/api/v1/auth', authRoutes);

// Apply authentication middleware to all protected routes
app.use('/api/v1', authMiddleware);

// Apply audit logging middleware to all authenticated routes
app.use('/api/v1', auditMiddleware);

// Protected API routes
app.use('/api/v1/workspaces', workspaceRoutes);
app.use('/api/v1/projects', projectRoutes);
app.use('/api/v1/uploads', uploadRoutes);
app.use('/api/v1/models', modelRoutes);
app.use('/api/v1/simulations', simulationRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found',
    path: req.originalUrl
  });
});

// Global error handler
app.use(errorHandler);

// Start server
async function startServer() {
  try {
    // Connect to database
    await connectDatabase();
    logger.info('Database connection established');

    // Start HTTP server
    const port = config.port;
    app.listen(port, () => {
      logger.info(`Firenest Sandbox API server started on port ${port}`);
      logger.info(`Environment: ${config.environment}`);
      logger.info(`Health check: http://localhost:${port}/health`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();

export default app;
