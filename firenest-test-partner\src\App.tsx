import { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Home from './pages/Home';
import Chat from './pages/Chat';
import Callback from './pages/Callback';
import Pricing from './pages/Pricing';
import Features from './pages/Features';
import { FirenestAuthContext } from './lib/firenest-integration';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check if user is already authenticated
    const firenestSession = localStorage.getItem('firenest_session');
    if (firenestSession) {
      try {
        const session = JSON.parse(firenestSession);
        if (session.userId && session.sessionId && session.token) {
          setUserId(session.userId);
          setSessionId(session.sessionId);
          setToken(session.token);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Error parsing session:', error);
        localStorage.removeItem('firenest_session');
      }
    }
    setLoading(false);
  }, []);

  const login = (userId: string, sessionId: string, token: string) => {
    setUserId(userId);
    setSessionId(sessionId);
    setToken(token);
    setIsAuthenticated(true);
    localStorage.setItem('firenest_session', JSON.stringify({ userId, sessionId, token }));
  };

  const logout = () => {
    setUserId(null);
    setSessionId(null);
    setToken(null);
    setIsAuthenticated(false);
    localStorage.removeItem('firenest_session');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-dark-900">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-light">Loading AtlasAI...</p>
        </div>
      </div>
    );
  }

  return (
    <FirenestAuthContext.Provider value={{ isAuthenticated, userId, sessionId, token, login, logout }}>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/pricing" element={<Pricing />} />
        <Route path="/features" element={<Features />} />
        <Route
          path="/dashboard"
          element={isAuthenticated ? <Chat /> : <Navigate to="/" />}
        />
        <Route
          path="/chat"
          element={isAuthenticated ? <Chat /> : <Navigate to="/" />}
        />
        <Route path="/auth/callback" element={<Callback />} />
      </Routes>
    </FirenestAuthContext.Provider>
  );
}

export default App;