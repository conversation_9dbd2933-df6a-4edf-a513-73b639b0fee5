import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { getPartnerTool, updatePartnerTool, deletePartnerTool, getIntegrationConfig, saveIntegrationConfig } from '@/lib/partner-portal/api';
import {
  PartnerTool,
  ToolStatus,
  OAuthConfig,
  OIDCConfig,
  ApiKeyConfig,
  CredentialsConfig,
  IntegrationConfig
} from '@/lib/partner-portal/types';
import OAuthCredentialsCard from '@/components/partner-portal/OAuthCredentialsCard';
import IntegratedNavigation from '@/components/partner/IntegratedNavigation';
import WizardNavigationButtons from '@/components/partner/WizardNavigationButtons';
import WizardProgressBar from '@/components/partner/WizardProgressBar';
import PartnerFooter from '@/components/partner/PartnerFooter';
import FeatureManagement from '@/components/partner/FeatureManagement';

import { ArrowLeft, Save, Trash2 } from 'lucide-react';

// Authentication method type
type AuthType = 'oauth' | 'oidc' | 'api_key' | 'credentials' | 'ip_based' | 'custom';

const PartnerToolManagement: React.FC = () => {
  const navigate = useNavigate();
  const { toolId } = useParams<{ toolId: string }>();
  const { partner, refreshToolsData } = usePartner();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [tool, setTool] = useState<PartnerTool | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Scroll state variables
  const [scrollY, setScrollY] = useState(0);
  const [isCompact, setIsCompact] = useState(false);
  const headerRef = useRef(null);

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [longDescription, setLongDescription] = useState('');
  const [category, setCategory] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [status, setStatus] = useState<ToolStatus>('draft');
  const [activeTab, setActiveTab] = useState(queryParams.get('tab') || 'setup');
  const [setupStep, setSetupStep] = useState(1);

  // Authentication related states
  const [authType, setAuthType] = useState<AuthType>('oauth');

  // OAuth states
  const [oauthClientId, setOauthClientId] = useState('');
  const [oauthClientSecret, setOauthClientSecret] = useState('');
  const [oauthAuthUrl, setOauthAuthUrl] = useState('');
  const [oauthTokenUrl, setOauthTokenUrl] = useState('');
  const [scope, setScope] = useState('');
  const [responseType, setResponseType] = useState<'code' | 'token'>('code');

  // OIDC states
  const [oidcIssuer, setOidcIssuer] = useState('');
  const [oidcClientId, setOidcClientId] = useState('');
  const [oidcClientSecret, setOidcClientSecret] = useState('');
  const [oidcScope, setOidcScope] = useState('openid profile email');
  const [enablePkce, setEnablePkce] = useState(true);
  const [validateNonce, setValidateNonce] = useState(true);

  // API Key states
  const [apiKeyHeader, setApiKeyHeader] = useState('X-API-Key');
  const [apiKeyLocation, setApiKeyLocation] = useState('header');
  const [apiKeyParam, setApiKeyParam] = useState('api_key');
  const [apiKeyInstructions, setApiKeyInstructions] = useState('');
  const [isBearer, setIsBearer] = useState(false);

  // Credentials states
  const [credentialsLoginUrl, setCredentialsLoginUrl] = useState('');
  const [credentialsUsernameField, setCredentialsUsernameField] = useState('username');
  const [credentialsPasswordField, setCredentialsPasswordField] = useState('password');

  // IP-based states
  const [allowedIps, setAllowedIps] = useState('');
  const [ipHeaderName, setIpHeaderName] = useState('X-Forwarded-For');

  // Custom auth state
  const [customAuthDescription, setCustomAuthDescription] = useState('');

  // Integration config state
  const [integrationConfig, setIntegrationConfig] = useState<IntegrationConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(false);

  // Handle scroll events with throttling for better performance
  useEffect(() => {
    let ticking = false;
    let lastScrollY = window.scrollY;
    let scrollDirection = 'none';

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Determine scroll direction
      scrollDirection = currentScrollY > lastScrollY ? 'down' : 'up';
      lastScrollY = currentScrollY;

      // Use a slightly higher threshold for scrolling down to prevent flickering
      const downThreshold = 30;
      const upThreshold = 10;

      // Only change state when necessary to avoid unnecessary re-renders
      // Add hysteresis to prevent rapid toggling
      if ((currentScrollY > downThreshold && !isCompact && scrollDirection === 'down') ||
          (currentScrollY <= upThreshold && isCompact && scrollDirection === 'up')) {
        setIsCompact(currentScrollY > downThreshold);
      }

      ticking = false;
    };

    // Throttle scroll events for better performance
    const onScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(handleScroll);
        ticking = true;
      }
    };

    // Initial check
    handleScroll();

    window.addEventListener('scroll', onScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [isCompact]);

  // Load integration configuration
  const loadIntegrationConfig = async (toolId: string) => {
    setIsLoadingConfig(true);
    try {
      const config = await getIntegrationConfig(toolId);
      setIntegrationConfig(config);

      if (config) {
        // Set auth type based on the config
        setAuthType(config.authMethod as AuthType);

        // Set OAuth credentials if available
        if (config.authMethod === 'oauth') {
          const oauthConfig = config.configData as OAuthConfig;
          setOauthClientId(oauthConfig.clientId || '');
          setOauthClientSecret(oauthConfig.clientSecret || '');
          setOauthAuthUrl(oauthConfig.authorizationUrl || '');
          setOauthTokenUrl(oauthConfig.tokenUrl || '');
          setScope(oauthConfig.scope?.join(' ') || '');
          setResponseType(oauthConfig.responseType || 'code');
        }

        // Set OIDC config if available
        else if (config.authMethod === 'oidc') {
          const oidcConfig = config.configData as OIDCConfig;
          setOidcIssuer(oidcConfig.issuer || '');
          setOidcClientId(oidcConfig.clientId || '');
          setOidcClientSecret(oidcConfig.clientSecret || '');
          setOidcScope(oidcConfig.scope?.join(' ') || 'openid profile email');
          setEnablePkce(oidcConfig.pkceEnabled || false);
          setValidateNonce(oidcConfig.nonceValidationEnabled || false);
        }

        // Set API key config if available
        else if (config.authMethod === 'api_key') {
          const apiKeyConfig = config.configData as ApiKeyConfig;
          setApiKeyHeader(apiKeyConfig.headerName || 'Authorization');
          setIsBearer(apiKeyConfig.isBearer || false);
          setApiKeyParam(apiKeyConfig.queryParamName || '');
        }

        // Set credentials config if available
        else if (config.authMethod === 'credentials') {
          const credentialsConfig = config.configData as CredentialsConfig;
          setCredentialsLoginUrl(credentialsConfig.loginUrl || '');
          setCredentialsUsernameField(credentialsConfig.usernameField || 'username');
          setCredentialsPasswordField(credentialsConfig.passwordField || 'password');
        }

        // Set IP-based config if available
        else if (config.authMethod === 'ip_based') {
          // Define a simple interface for IP-based config
          interface IpBasedConfig {
            allowedIps: string[];
            ipHeaderName?: string;
          }
          const ipBasedConfig = config.configData as IpBasedConfig;
          setAllowedIps(ipBasedConfig.allowedIps?.join('\n') || '');
          setIpHeaderName(ipBasedConfig.ipHeaderName || 'X-Forwarded-For');
        }
      }
    } catch (error) {
      console.error('Error loading integration config:', error);
      notify.error('Failed to load integration configuration');
    } finally {
      setIsLoadingConfig(false);
    }
  };

  // Handle OAuth credentials change
  const handleOAuthCredentialsChange = (clientId: string, clientSecret: string) => {
    setOauthClientId(clientId);
    setOauthClientSecret(clientSecret);

    // Update the integration config
    if (integrationConfig && integrationConfig.authMethod === 'oauth') {
      const updatedConfig = {
        ...integrationConfig,
        configData: {
          ...(integrationConfig.configData as OAuthConfig),
          clientId,
          clientSecret
        }
      };
      setIntegrationConfig(updatedConfig);
    }
  };

  useEffect(() => {
    const loadTool = async () => {
      if (!toolId) return;

      setIsLoading(true);
      try {
        // Add a timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout loading tool data')), 10000)
        );

        const fetchToolPromise = async () => {
          console.log('Loading tool data for tool ID:', toolId);
          const toolData = await getPartnerTool(toolId);

          if (!toolData) {
            console.error('Tool not found for ID:', toolId);
            notify.error('Tool not found');
            navigate('/partner/dashboard');
            return null;
          }

          // Check if the tool belongs to the current partner
          if (partner && toolData.partnerId !== partner.id) {
            console.error('Permission denied: Tool belongs to partner', toolData.partnerId, 'but current partner is', partner.id);
            notify.error('You do not have permission to manage this tool');
            navigate('/partner/dashboard');
            return null;
          }

          return toolData as PartnerTool;
        };

        // Race the data fetch against the timeout
        const toolData = await Promise.race([fetchToolPromise(), timeoutPromise]) as PartnerTool | null;

        if (toolData) {
          console.log('Tool data loaded successfully:', toolData.id);
          setTool(toolData);

          // Set form values
          setName(toolData.name);
          setDescription(toolData.description || '');
          setLongDescription(toolData.longDescription || '');
          setCategory(toolData.category || '');
          setWebsiteUrl(toolData.websiteUrl || '');
          setStatus(toolData.status);

          // Load integration config
          await loadIntegrationConfig(toolData.id);
        }
      } catch (error) {
        console.error('Error loading tool:', error);
        if (error.message === 'Timeout loading tool data') {
          notify.error('Connection timed out. Please check your internet connection and try again.');
        } else {
          notify.error('Failed to load tool data. Please refresh the page.');
        }
        // Navigate back to dashboard on error
        navigate('/partner/dashboard');
      } finally {
        setIsLoading(false);
      }
    };

    loadTool();
  }, [toolId, partner, navigate]);

  const handleSave = async () => {
    if (!tool) return;

    setIsSaving(true);

    try {
      // Update the tool basic information
      const updatedTool = await updatePartnerTool(tool.id, {
        name,
        description,
        longDescription,
        category,
        websiteUrl,
        status
      });

      if (!updatedTool) {
        throw new Error('Failed to update tool');
      }

      // Save authentication configuration based on the selected auth type
      if (authType === 'oauth') {
        // Create OAuth config
        const oauthConfig: OAuthConfig = {
          clientId: oauthClientId,
          clientSecret: oauthClientSecret,
          authorizationUrl: oauthAuthUrl || `${window.location.origin}/auth/authorize`,
          tokenUrl: oauthTokenUrl || `${window.location.origin}/auth/token`,
          redirectUrl: `${window.location.origin}/auth/callback`,
          scope: scope ? scope.split(' ').filter(s => s) : ['openid', 'profile'],
          responseType: responseType || 'code'
        };

        await saveIntegrationConfig(tool.id, 'oauth', oauthConfig);
      }
      else if (authType === 'oidc') {
        // Create OIDC config
        const authUrl = oidcIssuer ? `${oidcIssuer.replace(/\/$/, '')}/auth` : '';
        const tokenUrl = oidcIssuer ? `${oidcIssuer.replace(/\/$/, '')}/token` : '';
        const userInfoUrl = oidcIssuer ? `${oidcIssuer.replace(/\/$/, '')}/userinfo` : '';
        const jwksUrl = oidcIssuer ? `${oidcIssuer.replace(/\/$/, '')}/.well-known/jwks.json` : '';

        const oidcConfig = {
          issuer: oidcIssuer,
          clientId: oidcClientId,
          clientSecret: oidcClientSecret,
          redirectUrl: `${window.location.origin}/auth/callback`,
          scope: oidcScope ? oidcScope.split(' ').filter(s => s) : ['openid', 'profile', 'email'],
          responseType: 'code' as const,
          pkceEnabled: enablePkce,
          nonceValidationEnabled: validateNonce,
          authorizationUrl: authUrl,
          tokenUrl: tokenUrl,
          userInfoUrl: userInfoUrl,
          jwksUrl: jwksUrl
        };

        await saveIntegrationConfig(tool.id, 'oidc', oidcConfig);
      }
      else if (authType === 'api_key') {
        // Create API key config
        const apiKeyConfig: ApiKeyConfig = {
          headerName: apiKeyHeader,
          queryParamName: apiKeyParam || undefined,
          isBearer: isBearer
        };

        await saveIntegrationConfig(tool.id, 'api_key', apiKeyConfig);
      }
      else if (authType === 'credentials') {
        // Create credentials config
        const credentialsConfig: CredentialsConfig = {
          loginUrl: credentialsLoginUrl,
          usernameField: credentialsUsernameField,
          passwordField: credentialsPasswordField,
          cookiesToCapture: [] // Required by the CredentialsConfig interface
        };

        await saveIntegrationConfig(tool.id, 'credentials', credentialsConfig);
      }
      else if (authType === 'ip_based') {
        // Create IP-based config
        interface IpBasedConfig {
          allowedIps: string[];
          ipHeaderName?: string;
        }

        const ipBasedConfig: IpBasedConfig = {
          allowedIps: allowedIps.split('\n').map(ip => ip.trim()).filter(ip => ip),
          ipHeaderName: ipHeaderName
        };

        await saveIntegrationConfig(tool.id, 'ip_based', ipBasedConfig);
      }
      // Custom auth is not supported by the API, so we'll skip it

      // Reload the integration config
      await loadIntegrationConfig(tool.id);

      setTool(updatedTool);
      await refreshToolsData();
      notify.success('Tool updated successfully');
    } catch (error: unknown) {
      console.error('Error updating tool:', error);
      notify.error(error instanceof Error ? error.message : 'Failed to update tool');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmitForReview = async () => {
    if (!tool) return;

    setIsSaving(true);

    try {
      const updatedTool = await updatePartnerTool(tool.id, {
        status: 'pending_review'
      });

      if (!updatedTool) {
        throw new Error('Failed to submit tool for review');
      }

      setTool(updatedTool);
      setStatus(updatedTool.status);
      await refreshToolsData();
      notify.success('Tool submitted for review');
    } catch (error: unknown) {
      console.error('Error submitting tool for review:', error);
      notify.error(error instanceof Error ? error.message : 'Failed to submit tool for review');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteTool = async () => {
    if (!tool) return;

    if (!window.confirm(`Are you sure you want to delete "${tool.name}"? This action cannot be undone.`)) {
      return;
    }

    setIsDeleting(true);

    try {
      const success = await deletePartnerTool(tool.id);

      if (!success) {
        throw new Error('Failed to delete tool');
      }

      await refreshToolsData();
      notify.success('Tool deleted successfully');
      navigate('/partner/dashboard');
    } catch (error: unknown) {
      console.error('Error deleting tool:', error);
      notify.error(error instanceof Error ? error.message : 'Failed to delete tool');
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-950 flex flex-col items-center justify-center">
        <div className="mb-6">
          <Loading size="lg" className="text-fiery" />
        </div>
        <h2 className="text-xl font-medium text-white mb-2">Loading Tool Data</h2>
        <p className="text-white/60 max-w-md text-center">
          Please wait while we fetch the latest information about your tool...
        </p>
        <Button
          variant="link"
          className="mt-6 text-fiery hover:text-fiery/80"
          onClick={() => window.location.reload()}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 2v6h-6"></path>
            <path d="M3 12a9 9 0 0 1 15-6.7L21 8"></path>
            <path d="M3 22v-6h6"></path>
            <path d="M21 12a9 9 0 0 1-15 6.7L3 16"></path>
          </svg>
          Refresh Page
        </Button>
      </div>
    );
  }

  if (!tool) {
    navigate('/partner/dashboard');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header with IntegratedNavigation */}
      <header
        ref={headerRef}
        className={`bg-dark-950 border-b border-white/10 sticky top-0 z-50 transition-all duration-500 ease-in-out ${
          isCompact ? 'opacity-0 transform -translate-y-full pointer-events-none' : 'opacity-100 transform translate-y-0'
        }`}
        style={{
          boxShadow: scrollY > 10 ? '0 4px 20px rgba(0, 0, 0, 0.3)' : 'none'
        }}
      >
        <div className="container mx-auto px-4 py-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
                className="mr-3 hover:bg-white/5"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>

              <div className="flex items-center">
                <div className="mr-4">
                  <h1 className="font-bold text-white text-2xl flex items-center">
                    {tool.name}
                  </h1>
                  <div className="flex items-center mt-1">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      tool.status === 'active' ? 'bg-green-500' :
                      tool.status === 'pending_review' ? 'bg-yellow-500' :
                      tool.status === 'suspended' ? 'bg-red-500' : 'bg-blue-500'
                    }`}></div>
                    <p className="text-white/60 text-sm capitalize">
                      {tool.status === 'pending_review' ? 'Pending Review' : tool.status}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                size="sm"
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <div className="mr-2 relative">
                      <div className="w-4 h-4 rounded-full border-2 border-white/10 border-t-fiery animate-spin"></div>
                    </div>
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    <span>Save Changes</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Integrated Navigation */}
        <IntegratedNavigation
          setupStep={setupStep}
          activeTab={activeTab}
          toolId={toolId}
          onSetupStepChange={setSetupStep}
          onTabChange={(tab) => {
            setActiveTab(tab);
            navigate(`/partner/tools/${toolId}?tab=${tab}`, { replace: true });
          }}
        />
      </header>

      {/* Main Content */}
      <div
        className={`container mx-auto px-6 transition-all duration-500 ease-in-out ${isCompact ? 'pt-2 pb-6' : 'py-6'}`}
        style={{ willChange: 'padding' }}
      >
        <Tabs
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value);

            // Only update URL, don't automatically advance the wizard step
            // This allows users to view different tabs without changing their progress
            navigate(`/partner/tools/${toolId}?tab=${value}`, { replace: true });
          }}
          className="w-full">

          {/* Desktop Tabs Navigation - Only visible when scrolled */}
          <div
            className={`sticky top-0 z-50 py-2 px-6 -mx-6 border-b border-white/10 transition-all duration-500 ease-out ${
              isCompact
                ? 'opacity-100 transform translate-y-0 animate-fade-in-subtle'
                : 'opacity-0 transform -translate-y-full pointer-events-none'
            }`}
            style={{
              boxShadow: isCompact ? '0 4px 20px rgba(0, 0, 0, 0.3)' : 'none',
              backgroundColor: 'rgb(15, 23, 42)', // Match body background color (bg-dark-950)
              height: isCompact ? 'auto' : '0',
              overflow: isCompact ? 'visible' : 'hidden',
              transitionProperty: 'opacity, transform, height, box-shadow',
              willChange: 'opacity, transform',
              maxWidth: '1200px',
              margin: '0 auto'
            }}
          >
              <div className="flex items-center justify-between w-full max-w-4xl mx-auto">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white hover:bg-white/5 mr-4"
                  onClick={() => navigate('/partner/dashboard')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M19 12H5M12 19l-7-7 7-7" />
                  </svg>
                  Back
                </Button>
                <TabsList className="flex bg-dark-800/80 rounded-lg flex-1 overflow-x-auto hide-scrollbar shadow-sm border border-white/5 p-1.5 space-x-1 md:space-x-2 justify-center">
                <TabsTrigger
                  value="setup"
                  className="flex items-center whitespace-nowrap min-w-max text-xs md:text-sm px-2 md:px-3"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                  Setup
                </TabsTrigger>
                <TabsTrigger value="details" className="flex items-center whitespace-nowrap min-w-max text-xs md:text-sm px-2 md:px-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  Tool Details
                </TabsTrigger>
                <TabsTrigger
                  value="authentication"
                  className="flex items-center whitespace-nowrap min-w-max text-xs md:text-sm px-2 md:px-3"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  Authentication
                </TabsTrigger>
                <TabsTrigger
                  value="integration"
                  className="flex items-center whitespace-nowrap min-w-max text-xs md:text-sm px-2 md:px-3"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="16 18 22 12 16 6"></polyline>
                    <polyline points="8 6 2 12 8 18"></polyline>
                  </svg>
                  Integration
                </TabsTrigger>
                <TabsTrigger
                  value="analytics"
                  className="flex items-center whitespace-nowrap min-w-max text-xs md:text-sm px-2 md:px-3"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="20" x2="18" y2="10"></line>
                    <line x1="12" y1="20" x2="12" y2="4"></line>
                    <line x1="6" y1="20" x2="6" y2="14"></line>
                  </svg>
                  Analytics
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="flex items-center whitespace-nowrap min-w-max text-xs md:text-sm px-2 md:px-3"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                  </svg>
                  Settings
                </TabsTrigger>
              </TabsList>
              </div>
            </div>

          {/* Wizard Progress Bar - Always visible */}
          {toolId && (
            <WizardProgressBar
              setupStep={setupStep}
              activeTab={activeTab}
              toolId={toolId}
              onTabChange={(tab) => {
                setActiveTab(tab);
                navigate(`/partner/tools/${toolId}?tab=${tab}`, { replace: true });
              }}
              onSetupStepChange={setSetupStep}
            />
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 mb-6">
            {tool.status === 'draft' && (
              <Button
                variant="outline"
                className="border-yellow-500/30 text-yellow-500 hover:bg-yellow-500/10 hover:text-yellow-400"
                onClick={handleSubmitForReview}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <div className="mr-2 relative">
                      <div className="w-4 h-4 rounded-full border-2 border-white/10 border-t-yellow-500 animate-spin"></div>
                    </div>
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="9 11 12 14 22 4"></polyline>
                      <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                    </svg>
                    Submit for Review
                  </>
                )}
              </Button>
            )}

            <Button
              variant="outline"
              className="border-white/10 hover:bg-white/5"
              onClick={() => window.open(`/preview/tool/${tool.id}`, '_blank')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              Preview Tool
            </Button>

            <Button
              variant="outline"
              className="border-red-500/30 text-red-500 hover:bg-red-500/10 hover:text-red-400"
              onClick={handleDeleteTool}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="mr-2 relative">
                    <div className="w-4 h-4 rounded-full border-2 border-white/10 border-t-red-500 animate-spin"></div>
                  </div>
                  <span>Deleting...</span>
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="3 6 5 6 21 6"></polyline>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    <line x1="10" y1="11" x2="10" y2="17"></line>
                    <line x1="14" y1="11" x2="14" y2="17"></line>
                  </svg>
                  Delete Tool
                </>
              )}
            </Button>
          </div>

          <TabsContent value="setup" className="mt-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Setup Your SaaS Integration</h2>
                  <p className="text-white/60">Follow these steps to integrate your tool with Firenest</p>
                </div>
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => window.open('https://docs.firenest.com/partners/setup-guide', '_blank')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                  </svg>
                  View Setup Guide
                </Button>
              </div>

              {/* Step Content Container */}
              <Card className="firenest-card border-0 shadow-lg">
                <CardContent className="p-6">
                  {setupStep === 1 && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">Basic Information</h3>
                        <p className="text-white/70">Provide essential details about your SaaS tool</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="setup-name" className="text-white/90">Tool Name <span className="text-red-500">*</span></Label>
                          <Input
                            id="setup-name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="Enter your tool's name"
                            className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                          />
                          <p className="text-white/50 text-sm">Choose a clear, descriptive name (max 50 characters)</p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="setup-category" className="text-white/90">Category <span className="text-red-500">*</span></Label>
                          <Select value={category} onValueChange={setCategory}>
                            <SelectTrigger id="setup-category" className="bg-dark-800/50 border-white/10">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AI Assistant">AI Assistant</SelectItem>
                              <SelectItem value="Image Generation">Image Generation</SelectItem>
                              <SelectItem value="Text Generation">Text Generation</SelectItem>
                              <SelectItem value="Code Assistant">Code Assistant</SelectItem>
                              <SelectItem value="Data Analysis">Data Analysis</SelectItem>
                              <SelectItem value="Audio Processing">Audio Processing</SelectItem>
                              <SelectItem value="Video Generation">Video Generation</SelectItem>
                              <SelectItem value="Translation">Translation</SelectItem>
                              <SelectItem value="Summarization">Summarization</SelectItem>
                              <SelectItem value="Research Assistant">Research Assistant</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <p className="text-white/50 text-sm">Categorizing helps users find your tool</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="setup-description" className="text-white/90">Short Description <span className="text-red-500">*</span></Label>
                        <Textarea
                          id="setup-description"
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Briefly describe what your tool does (max 150 characters)"
                          maxLength={150}
                          rows={2}
                          className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                        />
                        <div className="flex justify-between">
                          <p className="text-white/50 text-sm">This appears in search results and tool cards</p>
                          <p className="text-white/50 text-sm">{description.length}/150</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="setup-websiteUrl" className="text-white/90">Website URL <span className="text-red-500">*</span></Label>
                        <Input
                          id="setup-websiteUrl"
                          value={websiteUrl}
                          onChange={(e) => setWebsiteUrl(e.target.value)}
                          placeholder="https://yourtool.com"
                          type="url"
                          className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                        />
                        <p className="text-white/50 text-sm">Users will be redirected here when launching your tool</p>
                      </div>

                      <div className="bg-fiery/10 border border-fiery/30 rounded-lg p-4 flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-fiery mr-3 mt-0.5 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                        <div>
                          <p className="text-fiery font-medium mb-1">Important</p>
                          <p className="text-white/70 text-sm">
                            Make sure your website URL is accessible and ready to receive redirects from Firenest.
                            This is where users will be sent when they launch your tool.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {setupStep === 2 && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">Authentication Setup</h3>
                        <p className="text-white/70">Configure how users will authenticate with your tool</p>
                      </div>

                      <div className="space-y-4">
                        <Label className="text-white/90">Authentication Method <span className="text-red-500">*</span></Label>
                        <RadioGroup value={authType} onValueChange={(value) => setAuthType(value as AuthType)} className="space-y-4">
                          <div className="flex items-start space-x-3 p-4 rounded-lg border border-white/10 bg-dark-800/30 hover:bg-dark-800/50 transition-colors">
                            <RadioGroupItem value="oauth" id="auth-oauth" className="mt-1" />
                            <div className="space-y-2">
                              <Label htmlFor="auth-oauth" className="text-white font-medium cursor-pointer">OAuth 2.0 (Recommended)</Label>
                              <p className="text-white/70 text-sm">
                                Implement OAuth 2.0 to allow users to authenticate directly with your service.
                                This provides the most seamless experience and highest security.
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start space-x-3 p-4 rounded-lg border border-white/10 bg-dark-800/30 hover:bg-dark-800/50 transition-colors">
                            <RadioGroupItem value="api_key" id="auth-apikey" className="mt-1" />
                            <div className="space-y-2">
                              <Label htmlFor="auth-apikey" className="text-white font-medium cursor-pointer">API Key</Label>
                              <p className="text-white/70 text-sm">
                                Users will need to provide their API key from your service.
                                Firenest will securely store and manage these keys.
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start space-x-3 p-4 rounded-lg border border-white/10 bg-dark-800/30 hover:bg-dark-800/50 transition-colors">
                            <RadioGroupItem value="custom" id="auth-custom" className="mt-1" />
                            <div className="space-y-2">
                              <Label htmlFor="auth-custom" className="text-white font-medium cursor-pointer">Custom Authentication</Label>
                              <p className="text-white/70 text-sm">
                                Implement a custom authentication flow for your specific needs.
                                Our team will work with you to integrate this properly.
                              </p>
                            </div>
                          </div>
                        </RadioGroup>
                      </div>

                      {authType === 'oauth' && (
                        <div className="space-y-6 border-t border-white/10 pt-6">
                          <div>
                            <h4 className="text-lg font-medium text-white mb-2">OAuth 2.0 Configuration</h4>
                            <p className="text-white/70 text-sm">Enter your OAuth credentials and endpoints</p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="oauth-client-id" className="text-white/90">Client ID <span className="text-red-500">*</span></Label>
                              <Input
                                id="oauth-client-id"
                                value={oauthClientId}
                                onChange={(e) => setOauthClientId(e.target.value)}
                                placeholder="Your OAuth client ID"
                                className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="oauth-client-secret" className="text-white/90">Client Secret <span className="text-red-500">*</span></Label>
                              <Input
                                id="oauth-client-secret"
                                value={oauthClientSecret}
                                onChange={(e) => setOauthClientSecret(e.target.value)}
                                type="password"
                                placeholder="Your OAuth client secret"
                                className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="oauth-auth-url" className="text-white/90">Authorization URL <span className="text-red-500">*</span></Label>
                            <Input
                              id="oauth-auth-url"
                              value={oauthAuthUrl}
                              onChange={(e) => setOauthAuthUrl(e.target.value)}
                              placeholder="https://yourtool.com/oauth/authorize"
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="oauth-token-url" className="text-white/90">Token URL <span className="text-red-500">*</span></Label>
                            <Input
                              id="oauth-token-url"
                              value={oauthTokenUrl}
                              onChange={(e) => setOauthTokenUrl(e.target.value)}
                              placeholder="https://yourtool.com/oauth/token"
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="oauth-redirect-url" className="text-white/90">Redirect URL (add to your OAuth provider)</Label>
                            <div className="flex">
                              <Input
                                id="oauth-redirect-url"
                                value="https://firenest.com/oauth/callback"
                                readOnly
                                className="bg-dark-800/50 border-white/10 rounded-r-none"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                className="rounded-l-none border-white/10"
                                onClick={() => {
                                  navigator.clipboard.writeText("https://firenest.com/oauth/callback");
                                  notify.success("Redirect URL copied to clipboard");
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                              </Button>
                            </div>
                            <p className="text-white/50 text-sm">Add this URL to your OAuth provider's allowed redirect URLs</p>
                          </div>
                        </div>
                      )}

                      {authType === 'api_key' && (
                        <div className="space-y-6 border-t border-white/10 pt-6">
                          <div>
                            <h4 className="text-lg font-medium text-white mb-2">API Key Configuration</h4>
                            <p className="text-white/70 text-sm">Configure how API keys should be used</p>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="api-key-header" className="text-white/90">API Key Header Name <span className="text-red-500">*</span></Label>
                            <Input
                              id="api-key-header"
                              value={apiKeyHeader}
                              onChange={(e) => setApiKeyHeader(e.target.value)}
                              placeholder="X-API-Key"
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                            <p className="text-white/50 text-sm">The HTTP header name used to pass the API key</p>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-white/90">API Key Location <span className="text-red-500">*</span></Label>
                            <RadioGroup value={apiKeyLocation} onValueChange={setApiKeyLocation} className="space-y-3">
                              <div className="flex items-center space-x-3">
                                <RadioGroupItem value="header" id="api-key-loc-header" />
                                <Label htmlFor="api-key-loc-header" className="text-white cursor-pointer">HTTP Header (Recommended)</Label>
                              </div>
                              <div className="flex items-center space-x-3">
                                <RadioGroupItem value="query" id="api-key-loc-query" />
                                <Label htmlFor="api-key-loc-query" className="text-white cursor-pointer">Query Parameter</Label>
                              </div>
                            </RadioGroup>
                          </div>

                          {apiKeyLocation === 'query' && (
                            <div className="space-y-2">
                              <Label htmlFor="api-key-param" className="text-white/90">Query Parameter Name <span className="text-red-500">*</span></Label>
                              <Input
                                id="api-key-param"
                                value={apiKeyParam}
                                onChange={(e) => setApiKeyParam(e.target.value)}
                                placeholder="api_key"
                                className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                            </div>
                          )}

                          <div className="space-y-2">
                            <Label htmlFor="api-key-instructions" className="text-white/90">Key Generation Instructions <span className="text-red-500">*</span></Label>
                            <Textarea
                              id="api-key-instructions"
                              value={apiKeyInstructions}
                              onChange={(e) => setApiKeyInstructions(e.target.value)}
                              placeholder="Explain how users can generate an API key from your service..."
                              rows={3}
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                            <p className="text-white/50 text-sm">These instructions will be shown to users when they connect to your tool</p>
                          </div>
                        </div>
                      )}

                      {authType === 'custom' && (
                        <div className="space-y-6 border-t border-white/10 pt-6">
                          <div>
                            <h4 className="text-lg font-medium text-white mb-2">Custom Authentication</h4>
                            <p className="text-white/70 text-sm">Describe your custom authentication requirements</p>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="custom-auth-description" className="text-white/90">Authentication Description <span className="text-red-500">*</span></Label>
                            <Textarea
                              id="custom-auth-description"
                              value={customAuthDescription}
                              onChange={(e) => setCustomAuthDescription(e.target.value)}
                              placeholder="Describe your authentication flow and requirements in detail..."
                              rows={5}
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                          </div>

                          <div className="bg-fiery/10 border border-fiery/30 rounded-lg p-4 flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-fiery mr-3 mt-0.5 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <line x1="12" y1="8" x2="12" y2="12"></line>
                              <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            <div>
                              <p className="text-fiery font-medium mb-1">Custom Integration Required</p>
                              <p className="text-white/70 text-sm">
                                Our team will contact you to discuss your custom authentication requirements and
                                work with you to implement the integration. Please provide as much detail as possible.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Using WizardNavigationButtons component */}
              <WizardNavigationButtons
                setupStep={setupStep}
                handleSave={handleSave}
                isFormValid={name && description && websiteUrl && category ? true : false}
              />
            </div>
          </TabsContent>

          <TabsContent value="details">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Tool Details</h2>
                  <p className="text-white/60">Provide comprehensive information about your AI tool</p>
                </div>
                <Button
                  className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loading size="sm" className="mr-2" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      <span>Save Changes</span>
                    </>
                  )}
                </Button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Info Column */}
                <div className="lg:col-span-2 space-y-6">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Basic Information</CardTitle>
                      <CardDescription>
                        These details will be displayed to users browsing the Firenest marketplace
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-white/90">Tool Name <span className="text-red-500">*</span></Label>
                        <Input
                          id="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Enter your tool's name"
                          className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                        />
                        <p className="text-white/50 text-sm">Choose a clear, descriptive name (max 50 characters)</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description" className="text-white/90">Short Description <span className="text-red-500">*</span></Label>
                        <Textarea
                          id="description"
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Briefly describe what your tool does (max 150 characters)"
                          maxLength={150}
                          rows={2}
                          className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                        />
                        <div className="flex justify-between">
                          <p className="text-white/50 text-sm">This appears in search results and tool cards</p>
                          <p className="text-white/50 text-sm">{description.length}/150</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="longDescription" className="text-white/90">Full Description <span className="text-red-500">*</span></Label>
                        <Textarea
                          id="longDescription"
                          value={longDescription}
                          onChange={(e) => setLongDescription(e.target.value)}
                          placeholder="Provide a comprehensive description of your tool, its features, capabilities, and use cases"
                          rows={8}
                          className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                        />
                        <div className="flex justify-between">
                          <p className="text-white/50 text-sm">Markdown formatting is supported</p>
                          <p className="text-white/50 text-sm">{longDescription.length} characters</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="category" className="text-white/90">Category <span className="text-red-500">*</span></Label>
                          <Select value={category} onValueChange={setCategory}>
                            <SelectTrigger className="bg-dark-800/50 border-white/10">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AI Assistant">AI Assistant</SelectItem>
                              <SelectItem value="Image Generation">Image Generation</SelectItem>
                              <SelectItem value="Text Generation">Text Generation</SelectItem>
                              <SelectItem value="Code Assistant">Code Assistant</SelectItem>
                              <SelectItem value="Data Analysis">Data Analysis</SelectItem>
                              <SelectItem value="Audio Processing">Audio Processing</SelectItem>
                              <SelectItem value="Video Generation">Video Generation</SelectItem>
                              <SelectItem value="Translation">Translation</SelectItem>
                              <SelectItem value="Summarization">Summarization</SelectItem>
                              <SelectItem value="Research Assistant">Research Assistant</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <p className="text-white/50 text-sm">Categorizing helps users find your tool</p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="websiteUrl" className="text-white/90">Website URL <span className="text-red-500">*</span></Label>
                          <Input
                            id="websiteUrl"
                            value={websiteUrl}
                            onChange={(e) => setWebsiteUrl(e.target.value)}
                            placeholder="https://yourtool.com"
                            type="url"
                            className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                          />
                          <p className="text-white/50 text-sm">Users will be redirected here when launching your tool</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Features & Capabilities</CardTitle>
                      <CardDescription>
                        Highlight what makes your tool special
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label className="text-white/90">Key Features</Label>
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                          <p className="text-white/60 text-sm mb-4">Add up to 5 key features of your tool</p>

                          <div className="space-y-3">
                            {[1, 2, 3].map((index) => (
                              <div key={index} className="flex items-start space-x-3">
                                <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-1">
                                  <span className="text-fiery text-xs font-medium">{index}</span>
                                </div>
                                <div className="flex-1">
                                  <Input
                                    placeholder={`Feature ${index} title`}
                                    className="bg-dark-900/50 border-white/10 mb-2"
                                  />
                                  <Textarea
                                    placeholder={`Brief description of feature ${index}`}
                                    rows={2}
                                    className="bg-dark-900/50 border-white/10"
                                  />
                                </div>
                              </div>
                            ))}

                            <Button variant="outline" className="w-full mt-2 border-dashed border-white/20">
                              + Add Another Feature
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-white/90">Use Cases</Label>
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                          <p className="text-white/60 text-sm mb-4">Describe how users can benefit from your tool</p>

                          <Textarea
                            placeholder="Describe common use cases for your tool"
                            rows={4}
                            className="bg-dark-900/50 border-white/10"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Sidebar Column */}
                <div className="space-y-6">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Tool Status</CardTitle>
                      <CardDescription>
                        Manage your tool's visibility
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                        <div className="flex items-center mb-3">
                          <div className={`w-3 h-3 rounded-full mr-2 ${
                            status === 'active' ? 'bg-green-500' :
                            status === 'pending_review' ? 'bg-yellow-500' :
                            status === 'suspended' ? 'bg-red-500' : 'bg-blue-500'
                          }`}></div>
                          <p className="text-white font-medium capitalize">
                            {status === 'pending_review' ? 'Pending Review' : status}
                          </p>
                        </div>

                        {status === 'draft' && (
                          <p className="text-white/60 text-sm">
                            Your tool is in draft mode and not visible to users. Submit for review when ready.
                          </p>
                        )}
                        {status === 'pending_review' && (
                          <p className="text-yellow-500/90 text-sm">
                            Your tool is being reviewed by the Firenest team. You'll be notified when it's approved.
                          </p>
                        )}
                        {status === 'active' && (
                          <p className="text-green-500/90 text-sm">
                            Your tool is live and available to Firenest users.
                          </p>
                        )}
                        {status === 'suspended' && (
                          <p className="text-red-500/90 text-sm">
                            Your tool has been suspended. Contact support for more information.
                          </p>
                        )}
                      </div>

                      {status === 'draft' && (
                        <Button
                          className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white"
                          onClick={handleSubmitForReview}
                          disabled={isSaving}
                        >
                          {isSaving ? (
                            <>
                              <Loading size="sm" className="mr-2" />
                              Submitting...
                            </>
                          ) : (
                            'Submit for Review'
                          )}
                        </Button>
                      )}

                      {status === 'active' && (
                        <Select value={status} onValueChange={(value) => setStatus(value as ToolStatus)}>
                          <SelectTrigger className="bg-dark-800/50 border-white/10">
                            <SelectValue placeholder="Change status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="suspended">Suspend Tool</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </CardContent>
                  </Card>

                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Tool Preview</CardTitle>
                      <CardDescription>
                        See how your tool appears to users
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                        <div className="w-full aspect-video bg-gradient-to-br from-dark-900 to-dark-950 rounded-lg flex items-center justify-center mb-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 text-white/20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                          </svg>
                        </div>

                        <h3 className="text-white font-medium mb-1">{name || "Your Tool Name"}</h3>
                        <p className="text-white/60 text-sm mb-3 line-clamp-2">{description || "Add a short description to see how it will appear to users"}</p>

                        {category && (
                          <div className="inline-block bg-dark-900/80 px-2 py-1 rounded-full text-xs text-white/70">
                            {category}
                          </div>
                        )}
                      </div>

                      <Button
                        variant="outline"
                        className="w-full border-white/10 hover:bg-white/5"
                        onClick={() => window.open(`/preview/tool/${tool.id}`, '_blank')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        View Full Preview
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Danger Zone</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Button
                        variant="outline"
                        className="w-full text-red-500 border-red-500/30 hover:bg-red-500/10"
                        onClick={handleDeleteTool}
                        disabled={isDeleting}
                      >
                        {isDeleting ? (
                          <>
                            <Loading size="sm" className="mr-2" />
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Tool
                          </>
                        )}
                      </Button>
                      <p className="text-white/50 text-xs mt-2 text-center">
                        This action cannot be undone. All data associated with this tool will be permanently deleted.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Wizard Navigation Buttons */}
              <div className="flex justify-between mt-8 pt-6 border-t border-white/10">
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    if (setupStep > 1) {
                      const newStep = setupStep - 1;
                      setSetupStep(newStep);

                      // Update the active tab based on the new step
                      let newTab = activeTab;
                      if (newStep === 1) {
                        newTab = 'setup';
                      } else if (newStep === 2) {
                        newTab = 'authentication';
                      } else if (newStep === 3) {
                        newTab = 'integration';
                      }

                      if (newTab !== activeTab) {
                        setActiveTab(newTab);
                        navigate(`/partner/tools/${toolId}?tab=${newTab}`, { replace: true });
                      }
                    }
                  }}
                  disabled={setupStep === 1}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="19" y1="12" x2="5" y2="12"></line>
                    <polyline points="12 19 5 12 12 5"></polyline>
                  </svg>
                  Previous Step
                </Button>

                <Button
                  className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                  onClick={() => {
                    if (setupStep < 4) {
                      const newStep = setupStep + 1;
                      setSetupStep(newStep);

                      // Update the active tab based on the new step
                      let newTab = activeTab;
                      if (newStep === 2) {
                        newTab = 'authentication';
                      } else if (newStep === 3) {
                        newTab = 'integration';
                      } else if (newStep === 4) {
                        newTab = 'analytics';
                      }

                      if (newTab !== activeTab) {
                        setActiveTab(newTab);
                        navigate(`/partner/tools/${toolId}?tab=${newTab}`, { replace: true });
                      }
                    } else {
                      // Final step completed
                      handleSave();
                      notify.success("Setup completed successfully!");
                    }
                  }}
                >
                  {setupStep < 4 ? (
                    <>
                      Next Step
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                        <polyline points="12 5 19 12 12 19"></polyline>
                      </svg>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Complete Setup
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="authentication">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Authentication Configuration</h2>
                  <p className="text-white/60">Set up how users will authenticate with your tool</p>
                </div>
                <Button
                  className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loading size="sm" className="mr-2" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      <span>Save Configuration</span>
                    </>
                  )}
                </Button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Column */}
                <div className="lg:col-span-2 space-y-6">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Authentication Method</CardTitle>
                      <CardDescription>
                        Choose how Firenest users will authenticate with your tool
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 gap-6">
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div
                              className={`bg-dark-800/50 border ${authType === 'oauth' ? 'border-fiery/30' : 'border-white/10 hover:border-fiery/30'} rounded-lg p-4 cursor-pointer transition-all duration-200 relative group`}
                              onClick={() => setAuthType('oauth')}
                            >
                              <div className={`absolute top-3 right-3 w-4 h-4 rounded-full border-2 ${authType === 'oauth' ? 'border-fiery bg-fiery/20' : 'border-white/20 group-hover:border-fiery/50'}`}></div>
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                </svg>
                              </div>
                              <h3 className="text-white font-medium mb-1">OAuth 2.0</h3>
                              <p className="text-white/60 text-sm">Standard authorization framework</p>
                            </div>

                            <div
                              className={`bg-dark-800/50 border ${authType === 'oidc' ? 'border-fiery/30' : 'border-white/10 hover:border-fiery/30'} rounded-lg p-4 cursor-pointer transition-all duration-200 relative group`}
                              onClick={() => setAuthType('oidc')}
                            >
                              <div className={`absolute top-3 right-3 w-4 h-4 rounded-full border-2 ${authType === 'oidc' ? 'border-fiery bg-fiery/20' : 'border-white/20 group-hover:border-fiery/50'}`}></div>
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-500/5 flex items-center justify-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                                </svg>
                              </div>
                              <h3 className="text-white font-medium mb-1">OpenID Connect</h3>
                              <p className="text-white/60 text-sm">Authentication layer on top of OAuth</p>
                            </div>

                            <div
                              className={`bg-dark-800/50 border ${authType === 'api_key' ? 'border-fiery/30' : 'border-white/10 hover:border-fiery/30'} rounded-lg p-4 cursor-pointer transition-all duration-200 relative group`}
                              onClick={() => setAuthType('api_key')}
                            >
                              <div className={`absolute top-3 right-3 w-4 h-4 rounded-full border-2 ${authType === 'api_key' ? 'border-fiery bg-fiery/20' : 'border-white/20 group-hover:border-fiery/50'}`}></div>
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-fiery/20 to-fiery/5 flex items-center justify-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-fiery" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                                </svg>
                              </div>
                              <h3 className="text-white font-medium mb-1">API Key</h3>
                              <p className="text-white/60 text-sm">Simple key-based authentication</p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div
                              className={`bg-dark-800/50 border ${authType === 'credentials' ? 'border-fiery/30' : 'border-white/10 hover:border-fiery/30'} rounded-lg p-4 cursor-pointer transition-all duration-200 relative group`}
                              onClick={() => setAuthType('credentials')}
                            >
                              <div className={`absolute top-3 right-3 w-4 h-4 rounded-full border-2 ${authType === 'credentials' ? 'border-fiery bg-fiery/20' : 'border-white/20 group-hover:border-fiery/50'}`}></div>
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500/20 to-green-500/5 flex items-center justify-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-green-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                              </div>
                              <h3 className="text-white font-medium mb-1">Username/Password</h3>
                              <p className="text-white/60 text-sm">Traditional credentials-based login</p>
                            </div>

                            <div
                              className={`bg-dark-800/50 border ${authType === 'ip_based' ? 'border-fiery/30' : 'border-white/10 hover:border-fiery/30'} rounded-lg p-4 cursor-pointer transition-all duration-200 relative group`}
                              onClick={() => setAuthType('ip_based')}
                            >
                              <div className={`absolute top-3 right-3 w-4 h-4 rounded-full border-2 ${authType === 'ip_based' ? 'border-fiery bg-fiery/20' : 'border-white/20 group-hover:border-fiery/50'}`}></div>
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500/20 to-yellow-500/5 flex items-center justify-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-yellow-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                                  <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                                  <line x1="6" y1="6" x2="6.01" y2="6"></line>
                                  <line x1="6" y1="18" x2="6.01" y2="18"></line>
                                </svg>
                              </div>
                              <h3 className="text-white font-medium mb-1">IP-Based</h3>
                              <p className="text-white/60 text-sm">Authenticate based on IP address</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* OAuth Credentials Card */}
                  {authType === 'oauth' && (
                    <>
                      {isLoadingConfig ? (
                        <Card className="firenest-card border-0 shadow-lg">
                          <CardHeader>
                            <CardTitle className="text-lg text-white">OAuth Credentials</CardTitle>
                            <CardDescription>
                              Loading OAuth credentials...
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="flex justify-center py-8">
                            <div className="w-8 h-8 rounded-full border-2 border-white/10 border-t-fiery animate-spin"></div>
                          </CardContent>
                        </Card>
                      ) : (
                        <OAuthCredentialsCard
                          toolId={tool?.id || ''}
                          clientId={oauthClientId}
                          clientSecret={oauthClientSecret}
                          onCredentialsChange={handleOAuthCredentialsChange}
                        />
                      )}
                    </>
                  )}

                  {/* OIDC Configuration Card */}
                  {authType === 'oidc' && (
                    <Card className="firenest-card border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle className="text-lg text-white">OpenID Connect Configuration</CardTitle>
                        <CardDescription>
                          Configure OpenID Connect for secure user authentication
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="space-y-2">
                          <Label htmlFor="oidcIssuer">OIDC Issuer URL</Label>
                          <Input
                            id="oidcIssuer"
                            value={oidcIssuer}
                            onChange={(e) => setOidcIssuer(e.target.value)}
                            placeholder="https://yourtool.com"
                            className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                          />
                          <p className="text-sm text-white/60">
                            The base URL of your OpenID Connect provider.
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor="oidcClientId">Client ID</Label>
                            <Input
                              id="oidcClientId"
                              value={oidcClientId}
                              onChange={(e) => setOidcClientId(e.target.value)}
                              placeholder="Your OIDC client ID"
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="oidcClientSecret">Client Secret</Label>
                            <Input
                              id="oidcClientSecret"
                              type="password"
                              value={oidcClientSecret}
                              onChange={(e) => setOidcClientSecret(e.target.value)}
                              placeholder="Your OIDC client secret"
                              className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="oidcRedirectUrl">Redirect URL (provided by Firenest)</Label>
                          <div className="relative">
                            <Input
                              id="oidcRedirectUrl"
                              value={`${window.location.origin}/auth/callback`}
                              readOnly
                              className="bg-dark-800/50 border-white/10 pr-10"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 px-2 text-white/70 hover:text-white"
                              onClick={() => {
                                navigator.clipboard.writeText(`${window.location.origin}/auth/callback`);
                                notify.success('Redirect URL copied to clipboard');
                              }}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                              </svg>
                            </Button>
                          </div>
                          <p className="text-sm text-white/60">
                            Add this URL to your OIDC application's allowed redirect URLs.
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="oidcScope">Scopes</Label>
                          <Input
                            id="oidcScope"
                            value={oidcScope}
                            onChange={(e) => setOidcScope(e.target.value)}
                            placeholder="openid profile email"
                            className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                          />
                          <p className="text-sm text-white/60">
                            Space-separated list of OIDC scopes. The 'openid' scope is required.
                          </p>
                        </div>

                        <div className="bg-blue-900/30 p-4 rounded-lg border border-blue-700/50">
                          <h4 className="text-white font-medium mb-2 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-blue-400">
                              <circle cx="12" cy="12" r="10"></circle>
                              <line x1="12" y1="16" x2="12" y2="12"></line>
                              <line x1="12" y1="8" x2="12.01" y2="8"></line>
                            </svg>
                            Advanced Security Options
                          </h4>
                          <div className="space-y-4 mt-3">
                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id="enablePkce"
                                checked={enablePkce}
                                onChange={(e) => setEnablePkce(e.target.checked)}
                                className="rounded border-white/20 bg-dark-800/50"
                              />
                              <Label htmlFor="enablePkce" className="text-sm font-normal cursor-pointer">
                                Enable PKCE (Proof Key for Code Exchange)
                              </Label>
                            </div>
                            <p className="text-xs text-white/60">
                              PKCE enhances security by preventing authorization code interception attacks.
                              Recommended for all public clients and mobile applications.
                            </p>

                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id="validateNonce"
                                checked={validateNonce}
                                onChange={(e) => setValidateNonce(e.target.checked)}
                                className="rounded border-white/20 bg-dark-800/50"
                              />
                              <Label htmlFor="validateNonce" className="text-sm font-normal cursor-pointer">
                                Validate nonce parameter
                              </Label>
                            </div>
                            <p className="text-xs text-white/60">
                              The nonce parameter helps prevent replay attacks by ensuring each
                              authentication request is unique.
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* API Key Configuration Card */}
                  {authType === 'api_key' && (
                    <Card className="firenest-card border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle className="text-lg text-white">API Key Configuration</CardTitle>
                        <CardDescription>
                          Configure API key authentication settings
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <div className="flex items-start mb-4">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-fiery/20 to-fiery/5 flex items-center justify-center mr-4 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-fiery" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-white font-medium mb-1">How API Key Authentication Works</h3>
                              <p className="text-white/70 text-sm">
                                With API key authentication, Firenest will pass your API key to your service when users access your tool.
                                You'll need to validate this key on your server to authenticate the request.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-4 mt-6">
                            <div className="space-y-2">
                              <Label htmlFor="apiKeyHeader" className="text-white/90">API Key Header Name <span className="text-red-500">*</span></Label>
                              <Input
                                id="apiKeyHeader"
                                value={apiKeyHeader}
                                onChange={(e) => setApiKeyHeader(e.target.value)}
                                placeholder="X-API-Key"
                                className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                              <p className="text-white/50 text-sm">The HTTP header name that will contain your API key</p>
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  checked={isBearer}
                                  onChange={(e) => setIsBearer(e.target.checked)}
                                  className="rounded border-white/20 bg-dark-800/50"
                                />
                                <span>Use Bearer Token Format</span>
                              </Label>
                              <p className="text-white/50 text-sm">
                                If enabled, the API key will be sent as "Bearer YOUR_API_KEY". Otherwise, it will be sent as is.
                              </p>
                            </div>

                            <div className="flex items-center space-x-2 mt-2">
                              <input
                                type="checkbox"
                                id="useQueryParam"
                                checked={!!apiKeyParam}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setApiKeyParam('api_key');
                                  } else {
                                    setApiKeyParam('');
                                  }
                                }}
                                className="rounded bg-dark-900/50 border-white/20 text-fiery focus:ring-fiery/20"
                              />
                              <Label htmlFor="useQueryParam" className="text-white/80 text-sm">Also include API key as a query parameter</Label>
                            </div>

                            {apiKeyParam && (
                              <div className="space-y-2">
                                <Label htmlFor="apiKeyQueryParam" className="text-white/90">Query Parameter Name</Label>
                                <Input
                                  id="apiKeyQueryParam"
                                  value={apiKeyParam}
                                  onChange={(e) => setApiKeyParam(e.target.value)}
                                  placeholder="api_key"
                                  className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                                />
                                <p className="text-white/50 text-sm">The query parameter name that will contain your API key</p>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                          <div className="flex items-start">
                            <div className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                            <div>
                              <h4 className="text-yellow-500 font-medium mb-1">Security Recommendation</h4>
                              <p className="text-white/70 text-sm">
                                For production use, we recommend using a dedicated API key for Firenest integration and implementing rate limiting on your server.
                                Never expose your API key in client-side code.
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Credentials Configuration Card */}
                  {authType === 'credentials' && (
                    <Card className="firenest-card border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle className="text-lg text-white">Username/Password Configuration</CardTitle>
                        <CardDescription>
                          Configure username/password authentication for your tool
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <div className="flex items-start mb-4">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-green-500/20 to-green-500/5 flex items-center justify-center mr-4 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-green-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-white font-medium mb-1">How Credentials Authentication Works</h3>
                              <p className="text-white/70 text-sm">
                                With credentials authentication, Firenest will securely store and manage user credentials for your service.
                                When a user accesses your tool, Firenest will handle the login process using these credentials.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-4 mt-6">
                            <div className="space-y-2">
                              <Label htmlFor="credentialsLoginUrl" className="text-white/90">Login URL <span className="text-red-500">*</span></Label>
                              <Input
                                id="credentialsLoginUrl"
                                value={credentialsLoginUrl}
                                onChange={(e) => setCredentialsLoginUrl(e.target.value)}
                                placeholder="https://yourtool.com/login"
                                className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                              <p className="text-white/50 text-sm">The URL where users log in to your service</p>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="credentialsUsernameField" className="text-white/90">Username Field <span className="text-red-500">*</span></Label>
                              <Input
                                id="credentialsUsernameField"
                                value={credentialsUsernameField}
                                onChange={(e) => setCredentialsUsernameField(e.target.value)}
                                placeholder="username"
                                className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                              <p className="text-white/50 text-sm">The name of the form field for the username</p>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="credentialsPasswordField" className="text-white/90">Password Field <span className="text-red-500">*</span></Label>
                              <Input
                                id="credentialsPasswordField"
                                value={credentialsPasswordField}
                                onChange={(e) => setCredentialsPasswordField(e.target.value)}
                                placeholder="password"
                                className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                              <p className="text-white/50 text-sm">The name of the form field for the password</p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                          <div className="flex items-start">
                            <div className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                            <div>
                              <h4 className="text-yellow-500 font-medium mb-1">Security Note</h4>
                              <p className="text-white/70 text-sm">
                                Username/password authentication is less secure than OAuth or OIDC. We recommend using those methods when possible.
                                Firenest securely encrypts all stored credentials.
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* IP-Based Configuration Card */}
                  {authType === 'ip_based' && (
                    <Card className="firenest-card border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle className="text-lg text-white">IP-Based Authentication</CardTitle>
                        <CardDescription>
                          Configure IP-based authentication for your tool
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <div className="flex items-start mb-4">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500/20 to-yellow-500/5 flex items-center justify-center mr-4 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-yellow-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                                <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                                <line x1="6" y1="6" x2="6.01" y2="6"></line>
                                <line x1="6" y1="18" x2="6.01" y2="18"></line>
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-white font-medium mb-1">How IP-Based Authentication Works</h3>
                              <p className="text-white/70 text-sm">
                                With IP-based authentication, Firenest will only allow access to your tool from the specified IP addresses.
                                This is useful for internal tools or services that should only be accessed from specific locations.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-4 mt-6">
                            <div className="space-y-2">
                              <Label htmlFor="allowedIps" className="text-white/90">Allowed IP Addresses <span className="text-red-500">*</span></Label>
                              <Textarea
                                id="allowedIps"
                                value={allowedIps}
                                onChange={(e) => setAllowedIps(e.target.value)}
                                placeholder="***********&#10;10.0.0.0/24&#10;2001:db8::/32"
                                rows={5}
                                className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10 font-mono"
                              />
                              <p className="text-white/50 text-sm">Enter one IP address or CIDR range per line</p>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="ipHeaderName" className="text-white/90">IP Header Name</Label>
                              <Input
                                id="ipHeaderName"
                                value={ipHeaderName}
                                onChange={(e) => setIpHeaderName(e.target.value)}
                                placeholder="X-Forwarded-For"
                                className="bg-dark-900/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                              />
                              <p className="text-white/50 text-sm">
                                Optional: If your service is behind a proxy, specify the header that contains the client's real IP address
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                          <div className="flex items-start">
                            <div className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                            <div>
                              <h4 className="text-yellow-500 font-medium mb-1">Security Recommendation</h4>
                              <p className="text-white/70 text-sm">
                                IP-based authentication should be used in combination with other authentication methods for better security.
                                IP addresses can be spoofed and may change over time.
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Sidebar Column */}
                <div className="space-y-6">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Authentication Guide</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-fiery text-xs font-medium">1</span>
                          </div>
                          <div>
                            <h4 className="text-white font-medium mb-1">Choose an Auth Method</h4>
                            <p className="text-white/70 text-sm">
                              Select the authentication method that best suits your tool's requirements.
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-fiery text-xs font-medium">2</span>
                          </div>
                          <div>
                            <h4 className="text-white font-medium mb-1">Configure Settings</h4>
                            <p className="text-white/70 text-sm">
                              Enter the required configuration details for your chosen authentication method.
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-fiery text-xs font-medium">3</span>
                          </div>
                          <div>
                            <h4 className="text-white font-medium mb-1">Implement on Your Server</h4>
                            <p className="text-white/70 text-sm">
                              Update your server to validate authentication requests from Firenest.
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-fiery text-xs font-medium">4</span>
                          </div>
                          <div>
                            <h4 className="text-white font-medium mb-1">Test the Integration</h4>
                            <p className="text-white/70 text-sm">
                              Use the test tool to verify your authentication setup works correctly.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="pt-2">
                        <Button
                          variant="outline"
                          className="w-full border-white/10 hover:bg-white/5"
                          onClick={() => window.open('https://docs.firenest.com/partners/authentication', '_blank')}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                          </svg>
                          View Authentication Docs
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Test Authentication</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-white/70 text-sm">
                        Verify your authentication configuration by testing it with our integration tester.
                      </p>

                      <Button
                        className="w-full bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                        </svg>
                        Test Authentication
                      </Button>

                      <div className="bg-dark-800/50 border border-white/10 rounded-lg p-3 mt-2">
                        <p className="text-white/50 text-xs">
                          Testing will simulate the authentication flow that users will experience when accessing your tool through Firenest.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Wizard Navigation Buttons */}
              <div className="flex justify-between mt-8 pt-6 border-t border-white/10">
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    if (setupStep > 1) {
                      const newStep = setupStep - 1;
                      setSetupStep(newStep);

                      // Update the active tab based on the new step
                      let newTab = activeTab;
                      if (newStep === 1) {
                        newTab = 'setup';
                      } else if (newStep === 2) {
                        newTab = 'authentication';
                      } else if (newStep === 3) {
                        newTab = 'integration';
                      }

                      if (newTab !== activeTab) {
                        setActiveTab(newTab);
                        navigate(`/partner/tools/${toolId}?tab=${newTab}`, { replace: true });
                      }
                    }
                  }}
                  disabled={setupStep === 1}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="19" y1="12" x2="5" y2="12"></line>
                    <polyline points="12 19 5 12 12 5"></polyline>
                  </svg>
                  Previous Step
                </Button>

                <Button
                  className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                  onClick={() => {
                    if (setupStep < 4) {
                      const newStep = setupStep + 1;
                      setSetupStep(newStep);

                      // Update the active tab based on the new step
                      let newTab = activeTab;
                      if (newStep === 2) {
                        newTab = 'authentication';
                      } else if (newStep === 3) {
                        newTab = 'integration';
                      } else if (newStep === 4) {
                        newTab = 'analytics';
                      }

                      if (newTab !== activeTab) {
                        setActiveTab(newTab);
                        navigate(`/partner/tools/${toolId}?tab=${newTab}`, { replace: true });
                      }
                    } else {
                      // Final step completed
                      handleSave();
                      notify.success("Setup completed successfully!");
                    }
                  }}
                >
                  {setupStep < 4 ? (
                    <>
                      Next Step
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                        <polyline points="12 5 19 12 12 19"></polyline>
                      </svg>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Complete Setup
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="integration">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Integration Setup</h2>
                  <p className="text-white/60">Connect your tool with Firenest's platform</p>
                </div>
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => window.open('https://docs.firenest.com/partners/integration', '_blank')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                  </svg>
                  View Full Documentation
                </Button>
              </div>

              <Card className="firenest-card border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Integration Overview</CardTitle>
                  <CardDescription>
                    How Firenest connects with your tool
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                    <div className="flex flex-col md:flex-row gap-6">
                      <div className="flex-1">
                        <h3 className="text-white font-medium mb-3">How It Works</h3>
                        <p className="text-white/70 text-sm mb-4">
                          Firenest provides a seamless integration that allows users to access your tool directly from our platform.
                          When a user launches your tool, we handle the authentication and redirect them to your website with the necessary credentials.
                        </p>

                        <div className="space-y-3">
                          <div className="flex items-start">
                            <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                              <span className="text-fiery text-xs font-medium">1</span>
                            </div>
                            <p className="text-white/70 text-sm">
                              User clicks on your tool in the Firenest marketplace
                            </p>
                          </div>

                          <div className="flex items-start">
                            <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                              <span className="text-fiery text-xs font-medium">2</span>
                            </div>
                            <p className="text-white/70 text-sm">
                              Firenest authenticates the user and creates a session
                            </p>
                          </div>

                          <div className="flex items-start">
                            <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                              <span className="text-fiery text-xs font-medium">3</span>
                            </div>
                            <p className="text-white/70 text-sm">
                              User is redirected to your tool with authentication data
                            </p>
                          </div>

                          <div className="flex items-start">
                            <div className="w-6 h-6 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                              <span className="text-fiery text-xs font-medium">4</span>
                            </div>
                            <p className="text-white/70 text-sm">
                              Your tool validates the authentication and tracks usage
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex-1">
                        <h3 className="text-white font-medium mb-3">Integration Benefits</h3>
                        <ul className="space-y-2">
                          <li className="flex items-start">
                            <div className="w-5 h-5 text-green-500 mr-2 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            </div>
                            <span className="text-white/70 text-sm">Seamless user experience with single sign-on</span>
                          </li>
                          <li className="flex items-start">
                            <div className="w-5 h-5 text-green-500 mr-2 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            </div>
                            <span className="text-white/70 text-sm">Automatic usage tracking and credit management</span>
                          </li>
                          <li className="flex items-start">
                            <div className="w-5 h-5 text-green-500 mr-2 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            </div>
                            <span className="text-white/70 text-sm">Access to Firenest's growing user base</span>
                          </li>
                          <li className="flex items-start">
                            <div className="w-5 h-5 text-green-500 mr-2 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            </div>
                            <span className="text-white/70 text-sm">Simplified billing and payment processing</span>
                          </li>
                          <li className="flex items-start">
                            <div className="w-5 h-5 text-green-500 mr-2 flex-shrink-0">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
                                <polyline points="20 6 9 17 4 12"></polyline>
                              </svg>
                            </div>
                            <span className="text-white/70 text-sm">Detailed analytics and usage insights</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Integration Code</CardTitle>
                      <CardDescription>
                        Code snippets to integrate your tool with Firenest
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-white font-medium">Frontend Integration (JavaScript)</h3>
                            <div className="flex space-x-2">
                              <Button variant="outline" size="sm" className="h-8 px-2 border-white/10 hover:bg-white/5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                              </Button>
                              <Button variant="outline" size="sm" className="h-8 px-2 border-white/10 hover:bg-white/5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                  <polyline points="7 10 12 15 17 10"></polyline>
                                  <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                              </Button>
                            </div>
                          </div>

                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm">
{`// Firenest Integration for ${tool.name}
const firenestIntegration = {
  init: function(apiKey) {
    this.apiKey = apiKey;

    // Add event listener for Firenest redirects
    window.addEventListener('message', this.handleMessage.bind(this));

    console.log('Firenest integration initialized');
  },

  handleMessage: function(event) {
    // Verify the origin
    if (event.origin !== 'https://firenest.io') return;

    const { type, data } = event.data;

    if (type === 'FIRENEST_AUTH') {
      // Handle authentication data
      this.handleAuth(data);
    }
  },

  handleAuth: function(authData) {
    // Process authentication data from Firenest
    const { userId, sessionId, token } = authData;

    // Store the session information
    localStorage.setItem('firenest_session', JSON.stringify({
      userId,
      sessionId,
      token
    }));

    // Notify your backend about the new session
    this.notifyBackend(userId, sessionId, token);
  },

  notifyBackend: function(userId, sessionId, token) {
    // Send the session information to your backend
    fetch('https://your-api.com/firenest/session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': \`Bearer \${this.apiKey}\`
      },
      body: JSON.stringify({
        userId,
        sessionId,
        token
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('Backend notified:', data);
    })
    .catch(error => {
      console.error('Error notifying backend:', error);
    });
  }
};

// Initialize the integration
firenestIntegration.init('YOUR_API_KEY');`}
                            </pre>
                          </div>
                        </div>

                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-white font-medium">Backend Integration (Node.js)</h3>
                            <div className="flex space-x-2">
                              <Button variant="outline" size="sm" className="h-8 px-2 border-white/10 hover:bg-white/5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                              </Button>
                              <Button variant="outline" size="sm" className="h-8 px-2 border-white/10 hover:bg-white/5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                  <polyline points="7 10 12 15 17 10"></polyline>
                                  <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                              </Button>
                            </div>
                          </div>

                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm">
{`// Firenest Backend Integration for ${tool.name}
const express = require('express');
const router = express.Router();

// Your Firenest API key
const FIRENEST_API_KEY = 'YOUR_API_KEY';

// Endpoint to receive Firenest session information
router.post('/firenest/session', (req, res) => {
  const { userId, sessionId, token } = req.body;

  // Verify the request
  const apiKey = req.headers.authorization?.split(' ')[1];
  if (apiKey !== FIRENEST_API_KEY) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Store the session information in your database
  // ...

  // Return success
  res.json({ success: true });
});

// Endpoint to track usage and report to Firenest
router.post('/firenest/track-usage', async (req, res) => {
  const { sessionId, usage } = req.body;

  try {
    // Report usage to Firenest
    const response = await fetch('https://api.firenest.io/v1/usage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': \`Bearer \${FIRENEST_API_KEY}\`
      },
      body: JSON.stringify({
        sessionId,
        usage
      })
    });

    const data = await response.json();

    res.json({ success: true, data });
  } catch (error) {
    console.error('Error reporting usage to Firenest:', error);
    res.status(500).json({ error: 'Failed to report usage' });
  }
});

module.exports = router;`}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end">
                      <Button className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Download Integration Kit
                      </Button>
                    </CardFooter>
                  </Card>

                  {/* SDK Integration Section */}
                  <Card className="firenest-card border-0 shadow-lg mt-6">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">SDK Integration</CardTitle>
                      <CardDescription>
                        Use our SDK to integrate your application with Firenest
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-white/70 mb-6">
                        The Firenest SDK provides simple methods for authentication, authorization checks, and usage reporting.
                      </p>

                      <div className="space-y-6">
                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <h3 className="text-white font-medium mb-3">Installation</h3>
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm">npm install @firenest/sdk-node</pre>
                          </div>
                          <p className="text-white/60 text-sm mt-2">
                            For other platforms, we also offer Python, Ruby, PHP, and Java SDKs.
                          </p>
                        </div>

                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <h3 className="text-white font-medium mb-3">Initialization</h3>
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm">{`const { FirenestSDK } = require('@firenest/sdk-node');

const firenest = new FirenestSDK({
  clientId: '${partner?.id || 'your-client-id'}',
  clientSecret: 'your-client-secret',
  environment: 'sandbox' // or 'production'
});`}</pre>
                          </div>
                        </div>

                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <h3 className="text-white font-medium mb-3">Checking Access</h3>
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm">{`// Check if a user has access to a feature
const accessResult = await firenest.checkAccess(
  firenestUserId,
  'feature-id'
);

if (!accessResult.allowed) {
  // User doesn't have access
  throw new Error(\`Access denied: \${accessResult.reason}\`);
}

// User has access, proceed with the feature
// ...`}</pre>
                          </div>
                        </div>

                        <div className="bg-dark-800/50 border border-white/10 rounded-lg p-5">
                          <h3 className="text-white font-medium mb-3">Reporting Usage</h3>
                          <div className="bg-dark-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-white/90 text-sm">{`// Report usage after successful execution
await firenest.reportUsage(
  firenestUserId,
  'feature-id'
);

// For features with dynamic costs
await firenest.reportUsage(
  firenestUserId,
  'feature-id',
  {
    unitsConsumed: 5,
    metadata: {
      // Additional information about the usage
    }
  }
);`}</pre>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end">
                      <Button
                        className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                        onClick={() => navigate('/partner/documentation/sdk')}
                      >
                        View Full Documentation
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <div className="space-y-6">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Integration Status</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
                        <div className="flex items-center mb-3">
                          <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                          <p className="text-white font-medium">Pending Setup</p>
                        </div>
                        <p className="text-white/70 text-sm">
                          Your integration is not yet complete. Follow the steps below to finish setting up your integration.
                        </p>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-dark-800 border border-white/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-white/70 text-xs">1</span>
                          </div>
                          <div>
                            <p className="text-white/70 text-sm">
                              <span className="text-yellow-500">In Progress:</span> Configure authentication method
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-dark-800 border border-white/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-white/70 text-xs">2</span>
                          </div>
                          <div>
                            <p className="text-white/70 text-sm">
                              <span className="text-white/50">Pending:</span> Implement integration code
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-dark-800 border border-white/20 flex items-center justify-center flex-shrink-0 mt-0.5 mr-3">
                            <span className="text-white/70 text-xs">3</span>
                          </div>
                          <div>
                            <p className="text-white/70 text-sm">
                              <span className="text-white/50">Pending:</span> Test integration
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Need Help?</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-white/70 text-sm">
                        Our team is here to help you with your integration. If you have any questions or need assistance, please don't hesitate to reach out.
                      </p>

                      <Button
                        variant="outline"
                        className="w-full border-white/10 hover:bg-white/5"
                        onClick={() => window.open('mailto:<EMAIL>')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                          <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        Contact Support
                      </Button>

                      <Button
                        variant="outline"
                        className="w-full border-white/10 hover:bg-white/5"
                        onClick={() => window.open('https://calendly.com/firenest-partners/integration-support', '_blank')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        Schedule Integration Call
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Wizard Navigation Buttons */}
              <div className="flex justify-between mt-8 pt-6 border-t border-white/10">
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    if (setupStep > 1) {
                      const newStep = setupStep - 1;
                      setSetupStep(newStep);

                      // Update the active tab based on the new step
                      let newTab = activeTab;
                      if (newStep === 1) {
                        newTab = 'setup';
                      } else if (newStep === 2) {
                        newTab = 'authentication';
                      } else if (newStep === 3) {
                        newTab = 'integration';
                      }

                      if (newTab !== activeTab) {
                        setActiveTab(newTab);
                        navigate(`/partner/tools/${toolId}?tab=${newTab}`, { replace: true });
                      }
                    }
                  }}
                  disabled={setupStep === 1}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="19" y1="12" x2="5" y2="12"></line>
                    <polyline points="12 19 5 12 12 5"></polyline>
                  </svg>
                  Previous Step
                </Button>

                <Button
                  className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                  onClick={() => {
                    if (setupStep < 4) {
                      const newStep = setupStep + 1;
                      setSetupStep(newStep);

                      // Update the active tab based on the new step
                      let newTab = activeTab;
                      if (newStep === 2) {
                        newTab = 'authentication';
                      } else if (newStep === 3) {
                        newTab = 'integration';
                      } else if (newStep === 4) {
                        newTab = 'analytics';
                      }

                      if (newTab !== activeTab) {
                        setActiveTab(newTab);
                        navigate(`/partner/tools/${toolId}?tab=${newTab}`, { replace: true });
                      }
                    } else {
                      // Final step completed
                      handleSave();
                      notify.success("Setup completed successfully!");
                    }
                  }}
                >
                  {setupStep < 4 ? (
                    <>
                      Next Step
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                        <polyline points="12 5 19 12 12 19"></polyline>
                      </svg>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Complete Setup
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Analytics & Usage</h2>
                  <p className="text-white/60">Track how users are engaging with your tool</p>
                </div>
                <div className="flex space-x-3">
                  <Select defaultValue="7d">
                    <SelectTrigger className="w-[140px] bg-dark-800/50 border-white/10">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="24h">Last 24 hours</SelectItem>
                      <SelectItem value="7d">Last 7 days</SelectItem>
                      <SelectItem value="30d">Last 30 days</SelectItem>
                      <SelectItem value="90d">Last 90 days</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    className="border-white/10 hover:bg-white/5"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="7 10 12 15 17 10"></polyline>
                      <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Export Data
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="firenest-card border-0 shadow-lg">
                  <CardContent className="pt-6">
                    <div className="flex flex-col">
                      <p className="text-white/60 text-sm">Total Launches</p>
                      <div className="flex items-end justify-between mt-2">
                        <h3 className="text-3xl font-bold text-white">0</h3>
                        <span className="text-green-500 text-sm flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                            <polyline points="17 6 23 6 23 12"></polyline>
                          </svg>
                          0%
                        </span>
                      </div>
                      <div className="h-1 w-full bg-dark-800 mt-4 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-fiery to-cool w-0 rounded-full"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="firenest-card border-0 shadow-lg">
                  <CardContent className="pt-6">
                    <div className="flex flex-col">
                      <p className="text-white/60 text-sm">Unique Users</p>
                      <div className="flex items-end justify-between mt-2">
                        <h3 className="text-3xl font-bold text-white">0</h3>
                        <span className="text-green-500 text-sm flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                            <polyline points="17 6 23 6 23 12"></polyline>
                          </svg>
                          0%
                        </span>
                      </div>
                      <div className="h-1 w-full bg-dark-800 mt-4 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-fiery to-cool w-0 rounded-full"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="firenest-card border-0 shadow-lg">
                  <CardContent className="pt-6">
                    <div className="flex flex-col">
                      <p className="text-white/60 text-sm">Average Session Time</p>
                      <div className="flex items-end justify-between mt-2">
                        <h3 className="text-3xl font-bold text-white">0m</h3>
                        <span className="text-yellow-500 text-sm flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                          </svg>
                          0%
                        </span>
                      </div>
                      <div className="h-1 w-full bg-dark-800 mt-4 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-fiery to-cool w-0 rounded-full"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="firenest-card border-0 shadow-lg">
                  <CardContent className="pt-6">
                    <div className="flex flex-col">
                      <p className="text-white/60 text-sm">Credits Used</p>
                      <div className="flex items-end justify-between mt-2">
                        <h3 className="text-3xl font-bold text-white">0</h3>
                        <span className="text-green-500 text-sm flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                            <polyline points="17 6 23 6 23 12"></polyline>
                          </svg>
                          0%
                        </span>
                      </div>
                      <div className="h-1 w-full bg-dark-800 mt-4 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-fiery to-cool w-0 rounded-full"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">Usage Over Time</CardTitle>
                      <CardDescription>
                        Track tool launches and credit usage
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80 w-full bg-dark-800/50 border border-white/10 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 text-white/20 mx-auto mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="18" y1="20" x2="18" y2="10"></line>
                            <line x1="12" y1="20" x2="12" y2="4"></line>
                            <line x1="6" y1="20" x2="6" y2="14"></line>
                          </svg>
                          <p className="text-white/50">No usage data available yet</p>
                          <p className="text-white/30 text-sm mt-1">Data will appear once users start using your tool</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <Card className="firenest-card border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white">User Demographics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80 w-full bg-dark-800/50 border border-white/10 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 text-white/20 mx-auto mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                          </svg>
                          <p className="text-white/50">No user data available yet</p>
                          <p className="text-white/30 text-sm mt-1">Demographics will appear as users engage with your tool</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <Card className="firenest-card border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Recent Activity</CardTitle>
                  <CardDescription>
                    Latest user interactions with your tool
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-dark-800/50 border border-white/10 rounded-lg p-6 flex items-center justify-center">
                    <div className="text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 text-white/20 mx-auto mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                      <p className="text-white/50">No recent activity</p>
                      <p className="text-white/30 text-sm mt-1">Activity will be shown here once users start using your tool</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <WizardNavigationButtons
                setupStep={setupStep}
                handleSave={handleSave}
                isFormValid={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Tool Settings</h2>
                  <p className="text-white/60">Configure advanced settings for your tool</p>
                </div>
                <Button
                  className="bg-gradient-to-r from-fiery to-fiery/90 hover:from-fiery/90 hover:to-fiery/80 text-white"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loading size="sm" className="mr-2" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      <span>Save Settings</span>
                    </>
                  )}
                </Button>
              </div>

              {/* Metered Features Section */}
              <Card className="firenest-card border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Metered Features</CardTitle>
                  <CardDescription>
                    Configure premium features and pricing for your tool
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FeatureManagement toolId={toolId} />
                </CardContent>
              </Card>

              <Card className="firenest-card border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Usage Settings</CardTitle>
                  <CardDescription>
                    Configure how your tool tracks and reports usage
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="usageType" className="text-white/90">Usage Tracking Method</Label>
                    <Select defaultValue="time">
                      <SelectTrigger id="usageType" className="bg-dark-800/50 border-white/10">
                        <SelectValue placeholder="Select tracking method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="time">Time-based</SelectItem>
                        <SelectItem value="tokens">Token-based</SelectItem>
                        <SelectItem value="requests">Request-based</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-white/50 text-sm">
                      Choose how Firenest will track and bill usage of your tool
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="creditRate" className="text-white/90">Credit Rate</Label>
                    <div className="flex space-x-3">
                      <Input
                        id="creditRate"
                        type="number"
                        placeholder="10"
                        className="bg-dark-800/50 border-white/10 focus:border-fiery/50 focus:ring-fiery/10"
                      />
                      <Select defaultValue="minute">
                        <SelectTrigger className="w-[180px] bg-dark-800/50 border-white/10">
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="minute">Credits per minute</SelectItem>
                          <SelectItem value="request">Credits per request</SelectItem>
                          <SelectItem value="1k_tokens">Credits per 1K tokens</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <p className="text-white/50 text-sm">
                      Define how many credits are consumed based on usage
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerToolManagement;
