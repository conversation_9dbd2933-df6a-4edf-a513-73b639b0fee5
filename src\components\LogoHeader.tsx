import { cn } from "@/lib/utils";
import { Flame, LogIn } from "lucide-react";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle
} from "@/components/ui/navigation-menu";
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

interface LogoHeaderProps {
  className?: string;
  onOpenWaitlist?: () => void;
};

const LogoHeader = ({ className, onOpenWaitlist }: LogoHeaderProps) => {
  const [scrolled, setScrolled] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const { user, isLoading } = useAuth();

  // Force showing login buttons if user is null or loading
  const showLoginButtons = user === null || isLoading;
  const showDashboardButton = user !== null && !isLoading;

  console.log('LogoHeader - user:', user ? 'exists' : 'null', 'isLoading:', isLoading,
              'showLoginButtons:', showLoginButtons, 'showDashboardButton:', showDashboardButton);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);

      // Calculate scroll progress
      const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
      const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scrolled = (winScroll / height) * 100;
      setScrollProgress(scrolled);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Debug log to ensure we're showing the correct buttons
  useEffect(() => {
    console.log('LogoHeader buttons state:', {
      showDashboard: !!user,
      showLoginAndWaitlist: !user
    });
  }, [user]);

  const scrollToSection = (sectionId: string) => (e: React.MouseEvent) => {
    e.preventDefault();
    const element = document.getElementById(sectionId);
    if (element) {
      // Account for fixed header height
      const headerHeight = 70;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - headerHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      });
    }
  };

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 flex justify-between items-center py-4 px-6 md:px-12 w-full z-50 transition-all duration-200",
        scrolled ? "bg-dark-900/90 backdrop-blur-sm border-b border-white/10" : "bg-transparent",
        className
      )}
    >
      {/* Scroll Progress Indicator */}
      <div className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-fiery via-fiery-300 to-cool" style={{ width: `${scrollProgress}%` }}></div>

      <div className="flex items-center gap-2">
        <div className="flex items-center justify-center bg-gradient-to-br from-fiery to-fiery-600 rounded-md w-8 h-8 animate-pulse-slow">
          <Flame className="text-white h-5 w-5" />
        </div>
        <h1 className="text-xl font-bold">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-fiery via-fiery to-cool-500 bg-size-200">
            <span className="text-fiery">Fir</span>
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
            <span className="text-cool">est</span>
          </span>
        </h1>
      </div>

      <div className="hidden md:block">
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <a href="#about" onClick={scrollToSection('about')}>
                <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "bg-transparent text-white/80 hover:text-white hover:bg-white/10")}>
                  About Us
                </NavigationMenuLink>
              </a>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <a href="#features" onClick={scrollToSection('features')}>
                <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "bg-transparent text-white/80 hover:text-white hover:bg-white/10")}>
                  Solutions
                </NavigationMenuLink>
              </a>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <a href="#pricing" onClick={scrollToSection('pricing')}>
                <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "bg-transparent text-white/80 hover:text-white hover:bg-white/10")}>
                  Pricing
                </NavigationMenuLink>
              </a>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <a href="#contact" onClick={scrollToSection('contact')}>
                <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "bg-transparent text-white/80 hover:text-white hover:bg-white/10")}>
                  Contact
                </NavigationMenuLink>
              </a>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <Link to="/partner">
                <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "bg-transparent text-white/80 hover:text-white hover:bg-white/10")}>
                  For Partners
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>

      <div className="flex items-center space-x-2">
        {/* Partners link for mobile */}
        <Link to="/partner" className="md:hidden flex items-center gap-1 text-white/80 hover:text-white transition-colors">
          <Flame className="h-4 w-4 text-fiery" />
          <span className="text-sm">For Partners</span>
        </Link>

        {/* Use our explicit variables to control which buttons to show */}
        {showDashboardButton && (
          <Link to="/dashboard" className="pop-button">
            Dashboard
          </Link>
        )}

        {showLoginButtons && (
          <>
            <Link to="/login" className="flex items-center gap-2 text-white/80 hover:text-white transition-colors mr-2">
              <LogIn className="h-4 w-4" />
              <span>Login</span>
            </Link>
            <button
              onClick={onOpenWaitlist}
              className="pop-button"
            >
              Join Waitlist
            </button>
          </>
        )}
      </div>
    </header>
  );

};


export default LogoHeader;
