/**
 * Integration Framework Hooks
 * 
 * This file provides React hooks for using the integration framework.
 */

import { useState, useEffect, useCallback } from 'react';
import { integrationFramework } from './integration-framework';
import { 
  IntegrationPartner,
  IntegrationMetadata,
  IntegrationHealthStatus,
  IntegrationTestResult,
  IntegrationCategory
} from './types';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook for accessing integration partners
 */
export function useIntegrationPartners() {
  const [partners, setPartners] = useState<IntegrationPartner[]>([]);
  
  useEffect(() => {
    setPartners(integrationFramework.getAllPartners());
  }, []);
  
  const getPartner = useCallback((partnerId: string) => {
    return integrationFramework.getPartner(partnerId);
  }, []);
  
  return {
    partners,
    getPartner
  };
}

/**
 * Hook for accessing integrations
 */
export function useIntegrations(category?: IntegrationCategory) {
  const [integrations, setIntegrations] = useState<IntegrationMetadata[]>([]);
  
  useEffect(() => {
    if (category) {
      setIntegrations(integrationFramework.getIntegrationsByCategory(category));
    } else {
      setIntegrations(integrationFramework.getAllIntegrations());
    }
  }, [category]);
  
  const getIntegration = useCallback((integrationId: string) => {
    return integrationFramework.getIntegration(integrationId);
  }, []);
  
  const getIntegrationsByPartner = useCallback((partnerId: string) => {
    return integrationFramework.getIntegrationsByPartner(partnerId);
  }, []);
  
  const getPopularIntegrations = useCallback(() => {
    return integrationFramework.getPopularIntegrations();
  }, []);
  
  const getNewIntegrations = useCallback(() => {
    return integrationFramework.getNewIntegrations();
  }, []);
  
  return {
    integrations,
    getIntegration,
    getIntegrationsByPartner,
    getPopularIntegrations,
    getNewIntegrations
  };
}

/**
 * Hook for accessing integration health status
 */
export function useIntegrationHealth(integrationId?: string) {
  const [healthStatus, setHealthStatus] = useState<IntegrationHealthStatus | undefined>(undefined);
  const [allHealthStatuses, setAllHealthStatuses] = useState<IntegrationHealthStatus[]>([]);
  
  useEffect(() => {
    if (integrationId) {
      setHealthStatus(integrationFramework.getHealthStatus(integrationId));
    } else {
      setAllHealthStatuses(integrationFramework.getAllHealthStatuses());
    }
    
    // Poll for health status updates every 30 seconds
    const interval = setInterval(() => {
      if (integrationId) {
        setHealthStatus(integrationFramework.getHealthStatus(integrationId));
      } else {
        setAllHealthStatuses(integrationFramework.getAllHealthStatuses());
      }
    }, 30000);
    
    return () => {
      clearInterval(interval);
    };
  }, [integrationId]);
  
  return {
    healthStatus,
    allHealthStatuses
  };
}

/**
 * Hook for running integration tests
 */
export function useIntegrationTests(integrationId: string) {
  const [testResults, setTestResults] = useState<IntegrationTestResult | undefined>(undefined);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    setTestResults(integrationFramework.getTestResults(integrationId));
  }, [integrationId]);
  
  const runTests = useCallback(async () => {
    setIsRunningTests(true);
    setError(null);
    
    try {
      const results = await integrationFramework.runTests(integrationId);
      setTestResults(results);
      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setIsRunningTests(false);
    }
  }, [integrationId]);
  
  return {
    testResults,
    isRunningTests,
    error,
    runTests
  };
}

/**
 * Hook for launching integrations
 */
export function useLaunchIntegration() {
  const { user } = useAuth();
  const [isLaunching, setIsLaunching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const launch = useCallback(async (integrationId: string) => {
    if (!user) {
      setError('You must be logged in to launch an integration');
      return null;
    }
    
    setIsLaunching(true);
    setError(null);
    
    try {
      const result = await integrationFramework.launchIntegration(integrationId, user.id);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setIsLaunching(false);
    }
  }, [user]);
  
  const endSession = useCallback(async (integrationId: string) => {
    if (!user) {
      setError('You must be logged in to end an integration session');
      return false;
    }
    
    try {
      return await integrationFramework.endIntegrationSession(integrationId, user.id);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      return false;
    }
  }, [user]);
  
  return {
    launch,
    endSession,
    isLaunching,
    error
  };
}
