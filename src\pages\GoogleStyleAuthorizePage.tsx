import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Flame, Shield, AlertTriangle, CheckCircle, XCircle, Lock } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface AuthParams {
  client_id: string;
  redirect_uri: string;
  response_type: string;
  scope: string;
  state: string;
}

interface PartnerInfo {
  id: string;
  name: string;
  company: string;
  logoUrl?: string;
  website?: string;
}

const GoogleStyleAuthorizePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authParams, setAuthParams] = useState<AuthParams | null>(null);
  const [partnerInfo, setPartnerInfo] = useState<PartnerInfo | null>(null);
  const [scopes, setScopes] = useState<string[]>([]);

  useEffect(() => {
    const parseQueryParams = () => {
      const searchParams = new URLSearchParams(location.search);

      const params: AuthParams = {
        client_id: searchParams.get('client_id') || '',
        redirect_uri: searchParams.get('redirect_uri') || '',
        response_type: searchParams.get('response_type') || 'code',
        scope: searchParams.get('scope') || 'read write',
        state: searchParams.get('state') || ''
      };

      if (!params.client_id || !params.redirect_uri) {
        setError('Missing required parameters');
        setIsLoading(false);
        return;
      }

      setAuthParams(params);

      // Parse scopes
      if (params.scope) {
        setScopes(params.scope.split(' '));
      }

      fetchPartnerInfo(params.client_id);
    };

    parseQueryParams();
  }, [location]);

  const fetchPartnerInfo = async (clientId: string) => {
    try {
      // First try to get from partners table
      const { data: partner, error: partnerError } = await supabase
        .from('partners')
        .select('id, name, company, logo_url, website')
        .eq('client_id', clientId)
        .maybeSingle();

      if (partnerError || !partner) {
        // Try to get from partner_tools table
        const { data: tool, error: toolError } = await supabase
          .from('partner_tools')
          .select('id, name, partner_id, logo_url, website_url')
          .eq('id', clientId)
          .maybeSingle();

        if (toolError || !tool) {
          throw new Error('Partner not found');
        }

        // Get partner details
        const { data: partnerData, error: partnerDataError } = await supabase
          .from('partner_accounts')
          .select('id, name, company')
          .eq('id', tool.partner_id)
          .maybeSingle();

        if (partnerDataError || !partnerData) {
          throw new Error('Partner account not found');
        }

        setPartnerInfo({
          id: tool.id,
          name: tool.name,
          company: partnerData.company || partnerData.name,
          logoUrl: tool.logo_url,
          website: tool.website_url
        });
      } else {
        setPartnerInfo({
          id: partner.id,
          name: partner.name,
          company: partner.company || partner.name,
          logoUrl: partner.logo_url,
          website: partner.website
        });
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching partner information:', error);
      setError('Failed to fetch application information.');
      setIsLoading(false);
    }
  };

  const handleAuthorize = async () => {
    if (!user || !authParams || !partnerInfo) return;

    setIsProcessing(true);

    try {
      // Generate an authorization code
      const code = generateAuthCode();

      // Store the authorization code and related information in the database
      const { error } = await supabase
        .from('auth_codes')
        .insert({
          code,
          user_id: user.id,
          client_id: authParams.client_id,
          partner_id: partnerInfo.id,
          redirect_uri: authParams.redirect_uri,
          scope: authParams.scope,
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes expiration
          used: false
        });

      if (error) {
        throw error;
      }

      // Construct the redirect URL
      const redirectUrl = new URL(authParams.redirect_uri);
      redirectUrl.searchParams.append('code', code);
      redirectUrl.searchParams.append('state', authParams.state);

      // Redirect the user back to the client application
      window.location.href = redirectUrl.toString();
    } catch (error) {
      console.error('Error authorizing application:', error);
      setError('Failed to authorize application. Please try again.');
      setIsProcessing(false);
    }
  };

  const handleDeny = () => {
    if (!authParams) return;

    // Construct the redirect URL with an error
    const redirectUrl = new URL(authParams.redirect_uri);
    redirectUrl.searchParams.append('error', 'access_denied');
    redirectUrl.searchParams.append('state', authParams.state);

    // Redirect the user back to the client application
    window.location.href = redirectUrl.toString();
  };

  const generateAuthCode = (): string => {
    // Generate a random string for the authorization code
    const randomBytes = new Uint8Array(32);
    window.crypto.getRandomValues(randomBytes);
    const code = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');

    // Add a prefix to make it clear this is an auth code
    // Include a timestamp to help with debugging
    const timestamp = Date.now().toString(36);
    return `ac_${timestamp}_${code}`;
  };

  // Helper function to get a user-friendly description of a scope
  const getScopeDescription = (scope: string): string => {
    const scopeDescriptions: Record<string, string> = {
      'read': 'Read your basic profile information',
      'write': 'Make changes to your account settings',
      'profile': 'Access your profile information',
      'email': 'View your email address',
      'openid': 'Verify your identity',
      'offline_access': 'Access your data when you're not using the application',
    };

    return scopeDescriptions[scope] || `Access to ${scope}`;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
              <Flame className="h-6 w-6 text-fiery" />
            </div>
          </div>
          <div className="bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-dark-700">
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-fiery mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-white mb-2">
                Loading...
              </h2>
              <p className="text-gray-300">
                Please wait while we process your request.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
              <Flame className="h-6 w-6 text-fiery" />
            </div>
          </div>
          <div className="bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-dark-700">
            <div className="p-6 text-center">
              <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">
                Authorization Error
              </h2>
              <p className="text-gray-300 mb-4">
                {error}
              </p>
              <Button
                onClick={() => navigate('/dashboard')}
                className="bg-fiery hover:bg-fiery-600 text-white"
              >
                Return to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Not logged in state
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
              <Flame className="h-6 w-6 text-fiery" />
            </div>
          </div>
          <div className="bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-dark-700">
            <div className="p-6 text-center">
              <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-4">
                <Lock className="h-6 w-6 text-blue-500" />
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">
                Authentication Required
              </h2>
              <p className="text-gray-300 mb-4">
                You need to be logged in to authorize this application.
              </p>
              <Button
                onClick={() => navigate('/login')}
                className="bg-fiery hover:bg-fiery-600 text-white"
              >
                Sign In
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main authorization page
  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 rounded-full bg-fiery/10 flex items-center justify-center">
            <Flame className="h-6 w-6 text-fiery" />
          </div>
        </div>
        <div className="bg-dark-800 rounded-lg shadow-lg overflow-hidden border border-dark-700">
          <div className="border-b border-dark-700 p-6">
            <h1 className="text-xl font-semibold text-white">
              {partnerInfo?.name || 'Application'} wants access to your Firenest account
            </h1>
          </div>

          <div className="p-6">
            <div className="flex items-center mb-6">
              {partnerInfo?.logoUrl ? (
                <img
                  src={partnerInfo.logoUrl}
                  alt={partnerInfo.name}
                  className="w-10 h-10 rounded mr-3"
                />
              ) : (
                <div className="w-10 h-10 rounded bg-dark-700 flex items-center justify-center mr-3">
                  <Shield className="h-5 w-5 text-gray-400" />
                </div>
              )}
              <div>
                <p className="text-sm text-gray-400">
                  {user.email}
                </p>
                <p className="text-xs text-gray-500">
                  {partnerInfo?.company && `${partnerInfo.company} • `}
                  {partnerInfo?.website && (
                    <a
                      href={partnerInfo.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-fiery hover:underline"
                    >
                      {new URL(partnerInfo.website).hostname}
                    </a>
                  )}
                </p>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-300 mb-2">
                This will allow {partnerInfo?.name || 'the application'} to:
              </h3>
              <ul className="space-y-2">
                {scopes.length > 0 ? (
                  scopes.map((scope, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">
                        {getScopeDescription(scope)}
                      </span>
                    </li>
                  ))
                ) : (
                  <>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">
                        Access your basic profile information
                      </span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-300">
                        Track usage of connected services
                      </span>
                    </li>
                  </>
                )}
              </ul>
            </div>

            <div className="bg-yellow-900/20 border border-yellow-800/30 rounded-lg p-3 mb-6">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-yellow-400">
                    Make sure you trust {partnerInfo?.name || 'this application'}
                  </p>
                  <p className="text-xs text-yellow-500 mt-1">
                    You can revoke access at any time from your Firenest account settings.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-dark-700 p-4 flex justify-between">
            <Button
              variant="outline"
              onClick={handleDeny}
              disabled={isProcessing}
              className="border-dark-600 text-gray-300 hover:bg-dark-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAuthorize}
              disabled={isProcessing}
              className="bg-fiery hover:bg-fiery-600 text-white"
            >
              {isProcessing ? (
                <>
                  <span className="mr-2">Processing</span>
                  <div className="h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                </>
              ) : (
                'Continue'
              )}
            </Button>
          </div>
        </div>
        <div className="text-center mt-4">
          <p className="text-xs text-gray-400">
            Secured by <span className="font-medium">Firenest</span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default GoogleStyleAuthorizePage;
