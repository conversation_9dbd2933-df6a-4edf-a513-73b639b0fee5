/**
 * Upload Page
 * Phase 1: Secure Data Ingestion
 * Complete file upload experience with real-time tracking
 */

import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { ArrowLeft, Upload, FileText, Shield, Zap } from 'lucide-react'
import { projectsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { FileUpload } from '@/components/upload/FileUpload'
import { UploadStatus } from '@/components/upload/UploadStatus'
import { UploadHistory } from '@/components/upload/UploadHistory'

export function UploadPage() {
  const { projectId } = useParams<{ projectId: string }>()
  const navigate = useNavigate()
  const [activeUploadId, setActiveUploadId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'upload' | 'status' | 'history'>('upload')

  const { data: project, isLoading } = useQuery({
    queryKey: ['projects', projectId],
    queryFn: () => projectsApi.get(projectId!),
    enabled: !!projectId
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!project?.data?.data) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white mb-2">Project not found</h2>
          <Button variant="outline" onClick={() => navigate('/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  const projectData = project.data.data

  const handleUploadComplete = (uploadId: string) => {
    setActiveUploadId(uploadId)
    setActiveTab('status')
  }

  const handleRetryUpload = () => {
    setActiveTab('upload')
    setActiveUploadId(null)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur">
        <div className="container-responsive py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/projects/${projectId}`)}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Project
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Data Upload</h1>
                <p className="text-gray-400">{projectData.name}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container-responsive py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Security & Trust Banner */}
            <div className="firenest-card-accent">
              <div className="flex items-start space-x-4">
                <Shield className="w-8 h-8 text-fiery flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Enterprise-Grade Security
                  </h3>
                  <p className="text-gray-300 mb-4">
                    Your sensitive pricing data is protected with zero-trust architecture, 
                    end-to-end encryption, and SOC 2 compliance measures.
                  </p>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2" />
                      Encrypted in transit & at rest
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2" />
                      Isolated data processing
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2" />
                      Audit trail for compliance
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="firenest-card">
              <div className="flex space-x-1 p-1 bg-muted rounded-lg">
                <button
                  onClick={() => setActiveTab('upload')}
                  className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'upload'
                      ? 'bg-fiery text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Files
                </button>
                <button
                  onClick={() => setActiveTab('status')}
                  className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'status'
                      ? 'bg-fiery text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                  disabled={!activeUploadId}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Status
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'history'
                      ? 'bg-fiery text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  History
                </button>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'upload' && (
              <FileUpload
                projectId={projectId!}
                onUploadComplete={handleUploadComplete}
              />
            )}

            {activeTab === 'status' && activeUploadId && (
              <UploadStatus
                uploadId={activeUploadId}
                onRetry={handleRetryUpload}
              />
            )}

            {activeTab === 'history' && (
              <UploadHistory projectId={projectId!} />
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Info */}
            <div className="firenest-card">
              <h3 className="text-lg font-semibold text-white mb-4">Project Details</h3>
              <div className="space-y-3">
                <div>
                  <div className="text-sm text-gray-400">Name</div>
                  <div className="text-white">{projectData.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-400">Status</div>
                  <div className="text-white">{projectData.status}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-400">Uploads</div>
                  <div className="text-white">{projectData.upload_count || 0}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-400">Models</div>
                  <div className="text-white">{projectData.model_count || 0}</div>
                </div>
              </div>
            </div>

            {/* Upload Guidelines */}
            <div className="firenest-card">
              <h3 className="text-lg font-semibold text-white mb-4">Upload Guidelines</h3>
              <div className="space-y-4 text-sm text-gray-300">
                <div>
                  <h4 className="font-medium text-white mb-2">Supported Formats</h4>
                  <ul className="space-y-1 text-gray-400">
                    <li>• CSV files (.csv)</li>
                    <li>• JSON files (.json)</li>
                    <li>• Excel files (.xlsx)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">File Size Limits</h4>
                  <ul className="space-y-1 text-gray-400">
                    <li>• Maximum: 100MB per file</li>
                    <li>• Recommended: Under 50MB</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Data Quality</h4>
                  <ul className="space-y-1 text-gray-400">
                    <li>• Include required columns</li>
                    <li>• Use consistent date formats</li>
                    <li>• Avoid empty rows</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Help & Support */}
            <div className="firenest-card">
              <h3 className="text-lg font-semibold text-white mb-4">Need Help?</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="w-4 h-4 mr-2" />
                  View Documentation
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Upload className="w-4 h-4 mr-2" />
                  Download Sample Files
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
