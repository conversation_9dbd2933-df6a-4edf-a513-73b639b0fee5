import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePartner } from '@/contexts/PartnerContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Webhook, Bell, Code, AlertTriangle } from 'lucide-react';
import WebhookConfiguration from '@/components/partner/WebhookConfiguration';
import { Loading } from '@/components/ui/loading';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerWebhooks: React.FC = () => {
  const navigate = useNavigate();
  const { partner, tools } = usePartner();
  const [selectedToolId, setSelectedToolId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Set the first tool as selected by default if available
    if (tools.length > 0 && !selectedToolId) {
      setSelectedToolId(tools[0].id);
    }
  }, [tools, selectedToolId]);

  if (!partner) {
    navigate('/partner');
    return null;
  }

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/dashboard')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Webhook Configuration</h1>
                <p className="text-white/60">Set up webhooks to receive real-time notifications</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Tool Selection</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="toolSelect">Select Tool</Label>
                      <Select value={selectedToolId} onValueChange={setSelectedToolId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tool" />
                        </SelectTrigger>
                        <SelectContent>
                          {tools.map(tool => (
                            <SelectItem key={tool.id} value={tool.id}>{tool.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-lg text-white">Webhook Guide</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-fiery/20 flex items-center justify-center flex-shrink-0">
                        <Webhook className="w-4 h-4 text-fiery" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Real-time Events</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Receive instant notifications about user activity and system events
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                        <Bell className="w-4 h-4 text-blue-500" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Event Types</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Configure which events you want to receive notifications for
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                        <Code className="w-4 h-4 text-purple-500" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Secure Payloads</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Verify webhook signatures to ensure they come from Firenest
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-amber-500/20 flex items-center justify-center flex-shrink-0">
                        <AlertTriangle className="w-4 h-4 text-amber-500" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-white">Retry Mechanism</h3>
                        <p className="text-white/70 text-xs mt-1">
                          Configure automatic retries for failed webhook deliveries
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <div className="space-y-8">
              {selectedToolId ? (
                <WebhookConfiguration partnerId={partner.id} toolId={selectedToolId} />
              ) : (
                <Card className="firenest-card">
                  <CardContent className="p-12 flex justify-center items-center">
                    {isLoading ? (
                      <Loading size="lg" />
                    ) : (
                      <p className="text-white/60 text-center">
                        Please select a tool to configure webhooks
                      </p>
                    )}
                  </CardContent>
                </Card>
              )}

              <Card className="firenest-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">About Webhooks</CardTitle>
                  <CardDescription>
                    Learn how to use webhooks to integrate with Firenest
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-white">What are webhooks?</h3>
                    <p className="text-white/80">
                      Webhooks allow your application to receive real-time notifications about events that occur in Firenest.
                      When an event happens, Firenest sends an HTTP POST request to the URL you configure.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-white">Webhook Events</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-dark-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Session Start</h4>
                        <p className="text-white/70 text-sm">
                          Sent when a user starts a session with your tool.
                        </p>
                      </div>
                      <div className="bg-dark-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Session End</h4>
                        <p className="text-white/70 text-sm">
                          Sent when a user ends a session with your tool.
                        </p>
                      </div>
                      <div className="bg-dark-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Feature Use</h4>
                        <p className="text-white/70 text-sm">
                          Sent when a user uses a specific feature in your tool.
                        </p>
                      </div>
                      <div className="bg-dark-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Credit Consumption</h4>
                        <p className="text-white/70 text-sm">
                          Sent when credits are deducted from a user's account.
                        </p>
                      </div>
                      <div className="bg-dark-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Low Credit Balance</h4>
                        <p className="text-white/70 text-sm">
                          Sent when a user's credit balance falls below a threshold.
                        </p>
                      </div>
                      <div className="bg-dark-800/50 p-4 rounded-lg">
                        <h4 className="font-medium text-white mb-2">Subscription Changes</h4>
                        <p className="text-white/70 text-sm">
                          Sent when a user's subscription status changes.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-white">Webhook Security</h3>
                    <p className="text-white/80">
                      Firenest signs webhook payloads with a secret key to ensure they come from us.
                      You should verify the signature in your webhook handler.
                    </p>
                    <div className="bg-dark-800/50 p-4 rounded-lg">
                      <pre className="text-white/80 text-sm overflow-x-auto">
{`// Example signature verification in Node.js
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(JSON.stringify(payload));
  const calculatedSignature = hmac.digest('hex');
  return crypto.timingSafeEqual(
    Buffer.from(calculatedSignature),
    Buffer.from(signature)
  );
}`}
                      </pre>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-white">SDK Integration</h3>
                    <p className="text-white/80">
                      You can also use our SDK to handle webhook events in your application. The SDK provides methods to verify webhook signatures and process events.
                    </p>
                    <div className="bg-dark-800/50 p-4 rounded-lg">
                      <pre className="text-white/80 text-sm overflow-x-auto">
{`// Example webhook handler with the Firenest SDK
const express = require('express');
const { FirenestSDK } = require('@firenest/sdk-node');

const app = express();
app.use(express.json());

const firenest = new FirenestSDK({
  clientId: process.env.FIRENEST_CLIENT_ID,
  clientSecret: process.env.FIRENEST_CLIENT_SECRET
});

app.post('/webhooks/firenest', (req, res) => {
  const signature = req.headers['x-firenest-signature'];

  // Verify the webhook signature
  if (!firenest.verifyWebhookSignature(req.body, signature)) {
    return res.status(401).send('Invalid signature');
  }

  // Process the webhook event
  const event = req.body;

  switch (event.type) {
    case 'session.start':
      console.log(\`User \${event.userId} started a session\`);
      break;
    case 'feature.use':
      console.log(\`User \${event.userId} used feature \${event.featureId}\`);
      break;
    case 'credit.consume':
      console.log(\`\${event.credits} credits consumed for user \${event.userId}\`);
      break;
    default:
      console.log(\`Received event: \${event.type}\`);
  }

  res.status(200).send('Webhook received');
});

app.listen(3000, () => {
  console.log('Webhook server running on port 3000');
});`}
                      </pre>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      className="text-white border-white/20 hover:bg-white/10"
                      onClick={() => window.open('https://docs.firenest.app/partners/webhooks', '_blank')}
                    >
                      View Webhook Documentation
                    </Button>

                    <Button
                      variant="outline"
                      className="text-white border-white/20 hover:bg-white/10"
                      onClick={() => navigate('/partner/documentation/sdk')}
                    >
                      View SDK Documentation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerWebhooks;
