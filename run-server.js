/**
 * Firenest Server Runner
 * 
 * This script runs the Firenest server with the necessary database migrations.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const PORT = 3333;
const DB_PORT = 6543; // Supabase PostgreSQL port
const DB_HOST = 'localhost';
const DB_NAME = 'postgres';
const DB_USER = 'postgres';
const DB_PASSWORD = 'postgres';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Log with timestamp and color
function log(message, color = colors.reset) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${color}${message}${colors.reset}`);
}

// Run a command and return a promise
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    log(`Running: ${command} ${args.join(' ')}`, colors.cyan);
    
    const proc = spawn(command, args, {
      stdio: 'inherit',
      ...options
    });
    
    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    proc.on('error', (err) => {
      reject(err);
    });
  });
}

// Run SQL file against the database
async function runSqlFile(filePath) {
  if (!fs.existsSync(filePath)) {
    log(`SQL file not found: ${filePath}`, colors.yellow);
    return;
  }
  
  log(`Running SQL file: ${filePath}`, colors.magenta);
  
  try {
    await runCommand('psql', [
      '-h', DB_HOST,
      '-p', DB_PORT.toString(),
      '-d', DB_NAME,
      '-U', DB_USER,
      '-f', filePath
    ], {
      env: {
        ...process.env,
        PGPASSWORD: DB_PASSWORD
      }
    });
    log(`SQL file executed successfully: ${filePath}`, colors.green);
  } catch (error) {
    log(`Error executing SQL file: ${error.message}`, colors.red);
    throw error;
  }
}

// Main function
async function main() {
  try {
    log('Starting Firenest server setup...', colors.bright + colors.blue);
    
    // Run database migrations
    log('Running database migrations...', colors.yellow);
    
    // Partner features migration
    await runSqlFile(path.join(__dirname, 'supabase-partner-features.sql'));
    
    // Start the server
    log('Starting Firenest server...', colors.green);
    await runCommand('node', ['server.js']);
    
  } catch (error) {
    log(`Error: ${error.message}`, colors.red);
    process.exit(1);
  }
}

// Run the main function
main();
