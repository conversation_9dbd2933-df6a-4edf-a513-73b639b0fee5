-- Register the test partner in the database

-- Register the test partner
DO $$
DECLARE
    partner_exists BOOLEAN;
    partner_id UUID;
    tool_id UUID;
    test_user_id UUID;
BEGIN
    -- Check if partner exists in partner_accounts
    SELECT EXISTS(
        SELECT 1 FROM partner_accounts pa WHERE pa.name = 'AtlasAI'
    ) INTO partner_exists;

    IF NOT partner_exists THEN
        -- Insert the test partner into partner_accounts
        INSERT INTO partner_accounts (
            name,
            email,
            company,
            website,
            logo_url,
            description,
            status,
            api_key,
            created_at,
            updated_at
        ) VALUES (
            'AtlasAI',
            '<EMAIL>',
            'AtlasAI Inc',
            'http://localhost:3001',
            'https://example.com/logo.png',
            'AI-powered content creation platform',
            'active',
            '01a0922001a0922001a0922001a0922001a0922001a0922001a0922001a09220',
            NOW(),
            NOW()
        ) RETURNING id INTO partner_id;

        -- Insert the partner tool
        INSERT INTO partner_tools (
            partner_id,
            name,
            description,
            logo_url,
            website_url,
            category,
            status,
            credit_cost_per_unit,
            metric_type,
            created_at,
            updated_at,
            active
        ) VALUES (
            partner_id,
            'AtlasAI',
            'AI-powered content creation platform',
            'https://example.com/logo.png',
            'http://localhost:3001',
            'AI',
            'active',
            1,
            'api_calls',
            NOW(),
            NOW(),
            TRUE
        ) RETURNING id INTO tool_id;

        -- Create integration config for OAuth
        INSERT INTO integration_configs (
            tool_id,
            auth_method,
            config_data,
            created_at,
            updated_at
        ) VALUES (
            tool_id,
            'oauth',
            '{
                "client_id": "atlasai-test-partner",
                "client_secret": "01a0922001a0922001a0922001a0922001a0922001a0922001a0922001a09220",
                "redirect_uris": ["http://localhost:3001/auth/callback"],
                "auth_url": "http://localhost:3000/api/v1/auth/authorize",
                "token_url": "http://localhost:3000/api/v1/auth/token",
                "scope": "read write"
            }'::jsonb,
            NOW(),
            NOW()
        );

        -- Create a test user if it doesn't exist
        SELECT u.id INTO test_user_id FROM users u WHERE u.email = '<EMAIL>';

        IF test_user_id IS NULL THEN
            INSERT INTO users (
                email,
                display_name,
                created_at,
                updated_at,
                active
            ) VALUES (
                '<EMAIL>',
                'Test User',
                NOW(),
                NOW(),
                TRUE
            ) RETURNING id INTO test_user_id;

            -- Add initial credits for the test user
            INSERT INTO user_credits (
                user_id,
                balance,
                last_updated
            ) VALUES (
                test_user_id,
                1000,
                NOW()
            );
        END IF;

        RAISE NOTICE 'Test partner registered successfully with ID: %', partner_id;
        RAISE NOTICE 'Test tool registered successfully with ID: %', tool_id;
        RAISE NOTICE 'Test user registered successfully with ID: %', test_user_id;
    ELSE
        -- Get the partner ID
        SELECT pa.id INTO partner_id FROM partner_accounts pa WHERE pa.name = 'AtlasAI';

        -- Get the tool ID using a subquery to avoid ambiguity
        SELECT pt.id INTO tool_id
        FROM partner_tools pt
        WHERE pt.partner_id = (SELECT pa.id FROM partner_accounts pa WHERE pa.name = 'AtlasAI')
        AND pt.name = 'AtlasAI';

        -- Update integration config using a subquery to avoid ambiguity
        UPDATE integration_configs ic
        SET config_data = jsonb_set(
            ic.config_data,
            '{redirect_uris}',
            '["http://localhost:3001/auth/callback"]'
        )
        WHERE ic.tool_id = (
            SELECT pt.id FROM partner_tools pt
            WHERE pt.partner_id = (SELECT pa.id FROM partner_accounts pa WHERE pa.name = 'AtlasAI')
            AND pt.name = 'AtlasAI'
        );

        RAISE NOTICE 'Test partner already exists with ID: %', partner_id;
        RAISE NOTICE 'Test tool already exists with ID: %', tool_id;
    END IF;

    -- Also create a record in the partners table for backward compatibility with the new API
    -- Check if partners table exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.tables t
        WHERE t.table_schema = 'public'
        AND t.table_name = 'partners'
    ) THEN
        -- Check if partner exists in partners table
        IF NOT EXISTS (SELECT 1 FROM partners p WHERE p.client_id = 'atlasai-test-partner') THEN
            -- Insert into partners table
            INSERT INTO partners (
                id,  -- Use the same ID as in partner_accounts
                name,
                description,
                website_url,
                logo_url,
                client_id,
                client_secret,
                redirect_uris,
                created_at,
                updated_at,
                active
            ) VALUES (
                partner_id,
                'AtlasAI',
                'AI-powered content creation platform',
                'http://localhost:3001',
                'https://example.com/logo.png',
                'atlasai-test-partner',
                '01a0922001a0922001a0922001a0922001a0922001a0922001a0922001a09220',
                ARRAY['http://localhost:3001/auth/callback'],
                NOW(),
                NOW(),
                TRUE
            );

            RAISE NOTICE 'Created record in partners table with ID: %', partner_id;
        ELSE
            RAISE NOTICE 'Record already exists in partners table';
        END IF;
    ELSE
        RAISE NOTICE 'Partners table does not exist, skipping';
    END IF;
END $$;
