/**
 * Component Editor
 * Configuration interface for individual pricing components
 */

import React, { useState, useEffect } from 'react'
import { 
  Settings, 
  Plus, 
  Trash2, 
  Save, 
  X,
  DollarSign,
  Calendar,
  TrendingUp
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { getComponentInfo } from './ComponentPalette'
import type { ModelComponent } from './ModelBuilder'

interface ComponentEditorProps {
  component: ModelComponent | null
  isEditing: boolean
  onUpdateComponent: (component: ModelComponent) => void
  onStartEditing: () => void
  onStopEditing: () => void
}

export function ComponentEditor({
  component,
  isEditing,
  onUpdateComponent,
  onStartEditing,
  onStopEditing
}: ComponentEditorProps) {
  const [config, setConfig] = useState<any>({})

  useEffect(() => {
    if (component) {
      setConfig({ ...component.config })
    }
  }, [component])

  const handleSave = () => {
    if (component) {
      onUpdateComponent({
        ...component,
        config,
        isNew: false
      })
      onStopEditing()
    }
  }

  const handleCancel = () => {
    if (component) {
      setConfig({ ...component.config })
    }
    onStopEditing()
  }

  if (!component) {
    return (
      <div className="firenest-card">
        <div className="text-center py-8">
          <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Component Editor</h3>
          <p className="text-gray-400">
            Select a component to configure its settings
          </p>
        </div>
      </div>
    )
  }

  const componentInfo = getComponentInfo(component.type)

  return (
    <div className="firenest-card">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {componentInfo && (
            <div className={`w-8 h-8 rounded-lg ${componentInfo.bgColor} flex items-center justify-center`}>
              <componentInfo.icon className={`w-4 h-4 ${componentInfo.color}`} />
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-white">
              {componentInfo?.name}
            </h3>
            <p className="text-sm text-gray-400">
              {componentInfo?.description}
            </p>
          </div>
        </div>
        
        {component.isNew && (
          <Badge variant="info">New</Badge>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-6">
          {/* Component-specific configuration */}
          {component.type === 'BASE_FEE' && (
            <BaseFeeEditor config={config} onChange={setConfig} />
          )}
          
          {component.type === 'PER_UNIT_RATE' && (
            <PerUnitRateEditor config={config} onChange={setConfig} />
          )}
          
          {component.type === 'TIERED_RATE' && (
            <TieredRateEditor config={config} onChange={setConfig} />
          )}
          
          {(component.type === 'MINIMUM_FEE' || component.type === 'MAXIMUM_FEE') && (
            <MinMaxFeeEditor config={config} onChange={setConfig} />
          )}

          {/* Action buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
            <Button variant="outline" onClick={handleCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} variant="fiery">
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Component preview */}
          <ComponentPreview component={component} />
          
          <Button onClick={onStartEditing} className="w-full">
            <Settings className="w-4 h-4 mr-2" />
            Configure Component
          </Button>
        </div>
      )}
    </div>
  )
}

// Base Fee Editor
function BaseFeeEditor({ config, onChange }: { config: any; onChange: (config: any) => void }) {
  return (
    <div className="space-y-4">
      <div>
        <label className="form-label mb-2">Amount</label>
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="number"
            value={config.amount || 0}
            onChange={(e) => onChange({ ...config, amount: parseFloat(e.target.value) || 0 })}
            className="form-input pl-10"
            placeholder="0.00"
            step="0.01"
            min="0"
          />
        </div>
      </div>
      
      <div>
        <label className="form-label mb-2">Billing Period</label>
        <select
          value={config.period || 'monthly'}
          onChange={(e) => onChange({ ...config, period: e.target.value })}
          className="form-input"
        >
          <option value="monthly">Monthly</option>
          <option value="yearly">Yearly</option>
          <option value="one-time">One-time</option>
        </select>
      </div>
      
      <div>
        <label className="form-label mb-2">Currency</label>
        <select
          value={config.currency || 'USD'}
          onChange={(e) => onChange({ ...config, currency: e.target.value })}
          className="form-input"
        >
          <option value="USD">USD</option>
          <option value="EUR">EUR</option>
          <option value="GBP">GBP</option>
        </select>
      </div>
    </div>
  )
}

// Per-Unit Rate Editor
function PerUnitRateEditor({ config, onChange }: { config: any; onChange: (config: any) => void }) {
  return (
    <div className="space-y-4">
      <div>
        <label className="form-label mb-2">Metric Name</label>
        <input
          type="text"
          value={config.metricName || ''}
          onChange={(e) => onChange({ ...config, metricName: e.target.value })}
          className="form-input"
          placeholder="e.g., API calls, GB storage, users"
        />
      </div>
      
      <div>
        <label className="form-label mb-2">Rate per Unit</label>
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="number"
            value={config.unitRate || 0}
            onChange={(e) => onChange({ ...config, unitRate: parseFloat(e.target.value) || 0 })}
            className="form-input pl-10"
            placeholder="0.00"
            step="0.001"
            min="0"
          />
        </div>
      </div>
      
      <div>
        <label className="form-label mb-2">Currency</label>
        <select
          value={config.currency || 'USD'}
          onChange={(e) => onChange({ ...config, currency: e.target.value })}
          className="form-input"
        >
          <option value="USD">USD</option>
          <option value="EUR">EUR</option>
          <option value="GBP">GBP</option>
        </select>
      </div>
    </div>
  )
}

// Tiered Rate Editor
function TieredRateEditor({ config, onChange }: { config: any; onChange: (config: any) => void }) {
  const tiers = config.tiers || []

  const addTier = () => {
    const newTiers = [...tiers, { upTo: 0, unitRate: 0 }]
    onChange({ ...config, tiers: newTiers })
  }

  const updateTier = (index: number, field: string, value: any) => {
    const newTiers = [...tiers]
    newTiers[index] = { ...newTiers[index], [field]: value }
    onChange({ ...config, tiers: newTiers })
  }

  const removeTier = (index: number) => {
    const newTiers = tiers.filter((_: any, i: number) => i !== index)
    onChange({ ...config, tiers: newTiers })
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="form-label mb-2">Metric Name</label>
        <input
          type="text"
          value={config.metricName || ''}
          onChange={(e) => onChange({ ...config, metricName: e.target.value })}
          className="form-input"
          placeholder="e.g., API calls, GB storage"
        />
      </div>

      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="form-label">Pricing Tiers</label>
          <Button size="sm" onClick={addTier}>
            <Plus className="w-3 h-3 mr-1" />
            Add Tier
          </Button>
        </div>
        
        <div className="space-y-3">
          {tiers.map((tier: any, index: number) => (
            <div key={index} className="firenest-nested-card">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-white">Tier {index + 1}</span>
                {tiers.length > 1 && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeTier(index)}
                    className="text-red-400"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Up to</label>
                  <input
                    type="text"
                    value={tier.upTo}
                    onChange={(e) => updateTier(index, 'upTo', e.target.value === 'infinity' ? 'infinity' : parseFloat(e.target.value) || 0)}
                    className="form-input text-sm"
                    placeholder="1000 or 'infinity'"
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-400 mb-1 block">Rate</label>
                  <div className="relative">
                    <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
                    <input
                      type="number"
                      value={tier.unitRate}
                      onChange={(e) => updateTier(index, 'unitRate', parseFloat(e.target.value) || 0)}
                      className="form-input text-sm pl-7"
                      placeholder="0.00"
                      step="0.001"
                      min="0"
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Min/Max Fee Editor
function MinMaxFeeEditor({ config, onChange }: { config: any; onChange: (config: any) => void }) {
  return (
    <div className="space-y-4">
      <div>
        <label className="form-label mb-2">Amount</label>
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="number"
            value={config.amount || 0}
            onChange={(e) => onChange({ ...config, amount: parseFloat(e.target.value) || 0 })}
            className="form-input pl-10"
            placeholder="0.00"
            step="0.01"
            min="0"
          />
        </div>
      </div>
      
      <div>
        <label className="form-label mb-2">Period</label>
        <select
          value={config.period || 'monthly'}
          onChange={(e) => onChange({ ...config, period: e.target.value })}
          className="form-input"
        >
          <option value="monthly">Monthly</option>
          <option value="yearly">Yearly</option>
        </select>
      </div>
    </div>
  )
}

// Component Preview
function ComponentPreview({ component }: { component: ModelComponent }) {
  const { config } = component
  
  return (
    <div className="firenest-nested-card">
      <h4 className="text-sm font-medium text-white mb-3">Current Configuration</h4>
      
      {component.type === 'BASE_FEE' && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Amount:</span>
            <span className="text-white">${config.amount}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Period:</span>
            <span className="text-white capitalize">{config.period}</span>
          </div>
        </div>
      )}
      
      {component.type === 'PER_UNIT_RATE' && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Metric:</span>
            <span className="text-white">{config.metricName || 'Not set'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Rate:</span>
            <span className="text-white">${config.unitRate} per unit</span>
          </div>
        </div>
      )}
      
      {component.type === 'TIERED_RATE' && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Metric:</span>
            <span className="text-white">{config.metricName || 'Not set'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Tiers:</span>
            <span className="text-white">{config.tiers?.length || 0} configured</span>
          </div>
        </div>
      )}
    </div>
  )
}
