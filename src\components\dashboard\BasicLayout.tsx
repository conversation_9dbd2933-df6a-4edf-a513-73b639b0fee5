import { useState } from 'react';
import { Outlet, NavLink } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Home, CreditCard, Settings, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * A very basic layout component for testing routes
 */
const BasicLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();

  return (
    <div className="min-h-screen flex bg-dark-950 text-white">
      {/* Sidebar */}
      <aside className="fixed top-0 left-0 z-40 h-full w-64 bg-dark-900 border-r border-white/5">
        <div className="p-4 border-b border-white/5">
          <h2 className="text-xl font-bold">Firenest</h2>
          <p className="text-sm text-white/70">Basic Layout</p>
        </div>
        
        <nav className="p-4 space-y-2">
          <NavLink
            to="/dashboard"
            end
            className={({ isActive }) => cn(
              "flex items-center gap-3 px-3 py-2 rounded-md",
              isActive ? "bg-fiery text-white" : "text-white/70 hover:bg-white/5"
            )}
          >
            <Home className="h-5 w-5" />
            <span>Dashboard</span>
          </NavLink>
          
          <NavLink
            to="/dashboard/credits"
            end
            className={({ isActive }) => cn(
              "flex items-center gap-3 px-3 py-2 rounded-md",
              isActive ? "bg-fiery text-white" : "text-white/70 hover:bg-white/5"
            )}
          >
            <CreditCard className="h-5 w-5" />
            <span>Credits</span>
          </NavLink>
          
          <NavLink
            to="/dashboard/settings"
            end
            className={({ isActive }) => cn(
              "flex items-center gap-3 px-3 py-2 rounded-md",
              isActive ? "bg-fiery text-white" : "text-white/70 hover:bg-white/5"
            )}
          >
            <Settings className="h-5 w-5" />
            <span>Settings</span>
          </NavLink>
        </nav>
      </aside>

      {/* Main content */}
      <div className="flex-1 ml-64">
        <header className="h-16 border-b border-white/5 flex items-center px-6">
          <h1 className="text-xl font-bold">Dashboard</h1>
          <div className="ml-auto">
            {user?.email}
          </div>
        </header>
        
        <main className="p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default BasicLayout;
