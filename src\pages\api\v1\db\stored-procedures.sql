-- Stored procedures for Firenest API

-- Function to deduct credits from a user's balance
CREATE OR REPLACE FUNCTION deduct_user_credits(
  p_user_id UUID,
  p_amount NUMERIC,
  p_description TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  v_current_balance NUMERIC;
  v_transaction_id UUID;
BEGIN
  -- Get current balance
  SELECT balance INTO v_current_balance
  FROM user_credits
  WHERE user_id = p_user_id;
  
  -- If no record exists, create one with 0 balance
  IF v_current_balance IS NULL THEN
    INSERT INTO user_credits (user_id, balance)
    VALUES (p_user_id, 0)
    RETURNING balance INTO v_current_balance;
  END IF;
  
  -- Check if balance is sufficient
  IF v_current_balance < p_amount THEN
    RETURN FALSE;
  END IF;
  
  -- Update balance
  UPDATE user_credits
  SET balance = balance - p_amount
  WHERE user_id = p_user_id;
  
  -- Record transaction
  INSERT INTO credit_transactions (
    user_id,
    amount,
    transaction_type,
    description,
    balance_after
  ) VALUES (
    p_user_id,
    -p_amount,
    'usage',
    p_description,
    v_current_balance - p_amount
  )
  RETURNING id INTO v_transaction_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in deduct_user_credits: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to record a usage event
CREATE OR REPLACE FUNCTION record_usage_event(
  session_id UUID,
  user_id UUID,
  tool_id UUID,
  event_type TEXT,
  event_data JSONB,
  credits_used NUMERIC DEFAULT 0
) RETURNS UUID AS $$
DECLARE
  v_event_id UUID;
BEGIN
  -- Insert usage event
  INSERT INTO usage_events (
    session_id,
    user_id,
    tool_id,
    event_type,
    event_data,
    credits_used
  ) VALUES (
    session_id,
    user_id,
    tool_id,
    event_type,
    event_data,
    credits_used
  )
  RETURNING id INTO v_event_id;
  
  -- Update session's estimated credits if needed
  IF credits_used > 0 THEN
    UPDATE usage_sessions
    SET estimated_credits = estimated_credits + credits_used
    WHERE id = session_id;
  END IF;
  
  RETURN v_event_id;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in record_usage_event: %', SQLERRM;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update session metrics
CREATE OR REPLACE FUNCTION update_session_metrics(
  session_id UUID,
  new_metrics JSONB,
  estimated_credits NUMERIC DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_current_metrics JSONB;
BEGIN
  -- Get current metrics
  SELECT metrics INTO v_current_metrics
  FROM usage_sessions
  WHERE id = session_id;
  
  -- Update metrics
  UPDATE usage_sessions
  SET 
    metrics = v_current_metrics || new_metrics,
    estimated_credits = COALESCE(estimated_credits, usage_sessions.estimated_credits)
  WHERE id = session_id;
  
  RETURN FOUND;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in update_session_metrics: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to end a usage session
CREATE OR REPLACE FUNCTION end_usage_session(
  session_id UUID,
  end_time TIMESTAMPTZ,
  status TEXT,
  metrics JSONB DEFAULT NULL,
  actual_credits_used NUMERIC DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_current_metrics JSONB;
  v_estimated_credits NUMERIC;
  v_actual_credits NUMERIC;
BEGIN
  -- Get current metrics and estimated credits
  SELECT metrics, estimated_credits INTO v_current_metrics, v_estimated_credits
  FROM usage_sessions
  WHERE id = session_id;
  
  -- Calculate actual credits if not provided
  IF actual_credits_used IS NULL THEN
    -- Use estimated credits as actual if not specified
    v_actual_credits := v_estimated_credits;
  ELSE
    v_actual_credits := actual_credits_used;
  END IF;
  
  -- Update session
  UPDATE usage_sessions
  SET 
    end_time = end_time,
    status = status,
    metrics = CASE WHEN metrics IS NOT NULL THEN v_current_metrics || metrics ELSE v_current_metrics END,
    actual_credits_used = v_actual_credits
  WHERE id = session_id;
  
  RETURN FOUND;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in end_usage_session: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
