/** @type {import('tailwindcss').Config} */
export default {
    content: [
      "./index.html",
      "./src/**/*.{js,ts,jsx,tsx}",
    ],
    theme: {
      extend: {
        colors: {
          primary: {
            DEFAULT: '#6C63FF',
            50: '#FFFFFF',
            100: '#F5F4FF',
            200: '#D6D3FF',
            300: '#B7B2FF',
            400: '#9892FF',
            500: '#6C63FF',
            600: '#3B2FFF',
            700: '#0A00FA',
            800: '#0700C7',
            900: '#050094',
          },
          secondary: {
            DEFAULT: '#FF6584',
            50: '#FFFFFF',
            100: '#FFFFFF',
            200: '#FFE5EA',
            300: '#FFBDC9',
            400: '#FF91A7',
            500: '#FF6584',
            600: '#FF2956',
            700: '#ED0035',
            800: '#BA0029',
            900: '#87001E',
          },
          dark: {
            DEFAULT: '#121212',
            50: '#8C8C8C',
            100: '#828282',
            200: '#6E6E6E',
            300: '#5A5A5A',
            400: '#464646',
            500: '#323232',
            600: '#282828',
            700: '#1E1E1E',
            800: '#181818',
            900: '#121212',
          },
          light: {
            DEFAULT: '#F8F9FA',
            50: '#FFFFFF',
            100: '#FFFFFF',
            200: '#FFFFFF',
            300: '#FFFFFF',
            400: '#FFFFFF',
            500: '#F8F9FA',
            600: '#D9DDE0',
            700: '#BAC1C6',
            800: '#9BA5AD',
            900: '#7D8993',
          },
          blue: {
            500: '#3b82f6',
            600: '#2563eb',
          },
        },
        fontFamily: {
          sans: ['Inter', 'sans-serif'],
        },
        boxShadow: {
          'glow': '0 0 20px rgba(108, 99, 255, 0.5)',
        },
      },
    },
    plugins: [],
  }