import React, { useState } from 'react';
import { notify } from '@/components/ui/notification-system';
import { Button } from '@/components/ui/button';
import { CustomDropdown, DropdownButton } from '@/components/ui/custom-dropdown';
import Modal from '@/components/ui/modal';
import {
  Bell,
  Settings,
  User,
  LogOut,
  CreditCard,
  Check,
  AlertTriangle,
  Info,
  Loader2,
  Mail,
  Trash2,
  Download,
  Share2,
  Copy,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import { Loading } from '@/components/ui/loading';

const ComponentsDemo = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalSize, setModalSize] = useState<'sm' | 'md' | 'lg' | 'xl' | 'full'>('md');
  const [isLoading, setIsLoading] = useState(false);

  const showToast = (type: 'success' | 'error' | 'info' | 'warning' | 'loading') => {
    const message = `This is a ${type} toast notification with enhanced styling`;

    switch (type) {
      case 'success':
        notify.success(message, { title: 'Success Toast' });
        break;
      case 'error':
        notify.error(message, { title: 'Error Toast' });
        break;
      case 'info':
        notify.info(message, { title: 'Info Toast' });
        break;
      case 'warning':
        notify.warning(message, { title: 'Warning Toast' });
        break;
      case 'loading':
        const id = notify.loading('This operation may take a few seconds...', { title: 'Loading Toast' });
        setTimeout(() => {
          notify.dismiss(id);
          notify.success('Operation completed successfully!', { title: 'Complete' });
        }, 3000);
        break;
    }
  };

  const showPromiseToast = () => {
    const promise = new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.3) {
          resolve('Data loaded successfully');
        } else {
          reject(new Error('Failed to load data'));
        }
      }, 2000);
    });

    notify.promise(promise, {
      loading: 'Loading data...',
      success: 'Data loaded successfully!',
      error: 'Failed to load data',
    }, { title: 'Promise Toast' });
  };

  const simulateLoading = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8 text-white">Components Demo</h1>

      {/* Toast Notifications */}
      <section className="mb-12 p-6 firenest-card">
        <h2 className="text-xl font-semibold mb-4 text-white">Toast Notifications</h2>
        <div className="flex flex-wrap gap-3">
          <Button onClick={() => showToast('success')} className="bg-fiery hover:bg-fiery-600">
            <Check className="mr-2 h-4 w-4" />
            Success Toast
          </Button>
          <Button onClick={() => showToast('error')} className="bg-red-500 hover:bg-red-600">
            <AlertTriangle className="mr-2 h-4 w-4" />
            Error Toast
          </Button>
          <Button onClick={() => showToast('info')} className="bg-cool-500 hover:bg-cool-600">
            <Info className="mr-2 h-4 w-4" />
            Info Toast
          </Button>
          <Button onClick={() => showToast('warning')} className="bg-amber-500 hover:bg-amber-600">
            <AlertTriangle className="mr-2 h-4 w-4" />
            Warning Toast
          </Button>
          <Button onClick={() => showToast('loading')} className="bg-dark-700 hover:bg-dark-600">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading Toast
          </Button>
          <Button onClick={showPromiseToast} className="bg-purple-500 hover:bg-purple-600">
            Promise Toast
          </Button>
        </div>
      </section>

      {/* Dropdowns */}
      <section className="mb-12 p-6 firenest-card">
        <h2 className="text-xl font-semibold mb-4 text-white">Dropdowns</h2>
        <div className="flex flex-wrap gap-6">
          <div>
            <h3 className="text-md font-medium mb-2 text-white/80">User Dropdown</h3>
            <CustomDropdown
              trigger={
                <div className="flex items-center gap-2 p-2 rounded-md hover:bg-white/5 cursor-pointer">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-br from-fiery to-fiery-600 flex items-center justify-center text-xs font-bold shadow-md">
                    JD
                  </div>
                  <div className="text-left">
                    <p className="text-sm font-medium">John Doe</p>
                    <p className="text-xs text-white/70"><EMAIL></p>
                  </div>
                </div>
              }
              sections={[
                {
                  items: [
                    { id: 'profile', label: 'Profile', icon: <User className="h-4 w-4" />, onClick: () => console.log('Profile clicked') },
                    { id: 'billing', label: 'Billing', icon: <CreditCard className="h-4 w-4" />, onClick: () => console.log('Billing clicked') },
                    { id: 'settings', label: 'Settings', icon: <Settings className="h-4 w-4" />, onClick: () => console.log('Settings clicked') },
                  ]
                },
                {
                  items: [
                    {
                      id: 'logout',
                      label: 'Log out',
                      icon: <LogOut className="h-4 w-4" />,
                      onClick: () => console.log('Logout clicked'),
                      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/10'
                    },
                  ]
                }
              ]}
            />
          </div>

          <div>
            <h3 className="text-md font-medium mb-2 text-white/80">Button Dropdown</h3>
            <DropdownButton
              label="Actions"
              icon={<MoreHorizontal className="h-4 w-4" />}
              sections={[
                {
                  items: [
                    { id: 'edit', label: 'Edit', icon: <Edit className="h-4 w-4" />, onClick: () => console.log('Edit clicked') },
                    { id: 'duplicate', label: 'Duplicate', icon: <Copy className="h-4 w-4" />, onClick: () => console.log('Duplicate clicked') },
                    { id: 'share', label: 'Share', icon: <Share2 className="h-4 w-4" />, onClick: () => console.log('Share clicked') },
                    { id: 'download', label: 'Download', icon: <Download className="h-4 w-4" />, onClick: () => console.log('Download clicked') },
                  ]
                },
                {
                  items: [
                    {
                      id: 'delete',
                      label: 'Delete',
                      icon: <Trash2 className="h-4 w-4" />,
                      onClick: () => console.log('Delete clicked'),
                      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/10'
                    },
                  ]
                }
              ]}
            />
          </div>

          <div>
            <h3 className="text-md font-medium mb-2 text-white/80">Notifications Dropdown</h3>
            <CustomDropdown
              trigger={
                <div className="relative p-2 rounded-full hover:bg-white/5 cursor-pointer">
                  <Bell className="h-5 w-5 text-white/80" />
                  <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-fiery"></span>
                </div>
              }
              sections={[
                {
                  title: "Notifications",
                  items: [
                    {
                      id: 'notification1',
                      label: (
                        <div>
                          <p className="font-medium">Credits Added</p>
                          <p className="text-xs text-white/70">100 credits have been added to your account.</p>
                          <p className="text-xs text-white/50 mt-1">2 hours ago</p>
                        </div>
                      ),
                      icon: <div className="h-9 w-9 rounded-full bg-fiery/20 flex items-center justify-center flex-shrink-0">
                        <CreditCard className="h-5 w-5 text-fiery" />
                      </div>,
                    },
                    {
                      id: 'notification2',
                      label: (
                        <div>
                          <p className="font-medium">New Feature Available</p>
                          <p className="text-xs text-white/70">Check out our new AI tool integration.</p>
                          <p className="text-xs text-white/50 mt-1">1 day ago</p>
                        </div>
                      ),
                      icon: <div className="h-9 w-9 rounded-full bg-cool-500/20 flex items-center justify-center flex-shrink-0">
                        <Bell className="h-5 w-5 text-cool-500" />
                      </div>,
                    },
                  ]
                },
                {
                  items: [
                    {
                      id: 'viewAll',
                      label: 'View All Notifications',
                      onClick: () => console.log('View all clicked'),
                      className: 'text-center justify-center'
                    },
                  ]
                }
              ]}
              width="w-80"
            />
          </div>
        </div>
      </section>

      {/* Modals */}
      <section className="mb-12 p-6 firenest-card">
        <h2 className="text-xl font-semibold mb-4 text-white">Modals</h2>
        <div className="flex flex-wrap gap-3">
          <Button onClick={() => { setModalSize('sm'); setIsModalOpen(true); }}>
            Small Modal
          </Button>
          <Button onClick={() => { setModalSize('md'); setIsModalOpen(true); }}>
            Medium Modal
          </Button>
          <Button onClick={() => { setModalSize('lg'); setIsModalOpen(true); }}>
            Large Modal
          </Button>
          <Button onClick={() => { setModalSize('xl'); setIsModalOpen(true); }}>
            Extra Large Modal
          </Button>
        </div>

        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Modal Demo"
          size={modalSize}
          footer={
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setIsModalOpen(false)}
                className="border-white/10 hover:bg-white/5"
              >
                Cancel
              </Button>
              <Button onClick={() => {
                setIsModalOpen(false);
                notify.success('Action completed!', {
                  title: 'Success',
                  duration: 4000
                });
              }}>
                Confirm
              </Button>
            </div>
          }
        >
          <div className="space-y-4">
            <p className="text-white/80">This is a {modalSize} modal with customizable content and footer.</p>
            <p className="text-white/70">Modals are useful for focused tasks and confirmations.</p>

            <div className="bg-dark-900/50 p-4 rounded-md border border-white/10">
              <h4 className="font-medium mb-2">Modal Features</h4>
              <ul className="list-disc list-inside text-white/70 space-y-1">
                <li>Customizable sizes</li>
                <li>Animated transitions</li>
                <li>Close on ESC key</li>
                <li>Close on outside click</li>
                <li>Prevents body scrolling</li>
              </ul>
            </div>
          </div>
        </Modal>
      </section>

      {/* Loading States */}
      <section className="mb-12 p-6 firenest-card">
        <h2 className="text-xl font-semibold mb-4 text-white">Loading States</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-md font-medium mb-3 text-white/80">Loading Variants</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="firenest-card p-4 flex flex-col items-center">
                <Loading variant="default" size="md" />
                <p className="mt-2 text-sm text-white/70">Default</p>
              </div>
              <div className="firenest-card p-4 flex flex-col items-center">
                <Loading variant="spinner" size="md" />
                <p className="mt-2 text-sm text-white/70">Spinner</p>
              </div>
              <div className="firenest-card p-4 flex flex-col items-center">
                <Loading variant="pulse" size="md" />
                <p className="mt-2 text-sm text-white/70">Pulse</p>
              </div>
              <div className="firenest-card p-4 flex flex-col items-center">
                <Loading variant="dots" size="md" />
                <p className="mt-2 text-sm text-white/70">Dots</p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-md font-medium mb-3 text-white/80">Button Loading States</h3>
            <div className="space-y-4">
              <Button
                onClick={simulateLoading}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Click to Load'
                )}
              </Button>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={simulateLoading}
                  disabled={isLoading}
                  className="flex-1 border-white/10"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Process'
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={simulateLoading}
                  disabled={isLoading}
                  className="flex-1 border-fiery/30 text-fiery hover:bg-fiery/10"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Send
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ComponentsDemo;
