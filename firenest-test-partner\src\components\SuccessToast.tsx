import React, { useEffect, useState } from 'react';
import { CheckCircle, X } from 'lucide-react';

interface SuccessToastProps {
  message: string;
  duration?: number;
  onClose?: () => void;
}

const SuccessToast: React.FC<SuccessToastProps> = ({ 
  message, 
  duration = 5000, 
  onClose 
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      if (onClose) onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-slideDown">
      <div className="bg-dark-800 border border-green-500/30 shadow-lg rounded-full py-2 px-4 flex items-center space-x-3 max-w-md">
        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
        <p className="text-light text-sm">{message}</p>
        <button 
          onClick={() => {
            setIsVisible(false);
            if (onClose) onClose();
          }}
          className="text-light-500 hover:text-light transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default SuccessToast;
