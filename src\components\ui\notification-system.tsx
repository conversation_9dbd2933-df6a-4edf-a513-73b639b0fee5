import React from 'react';
import { Toaster as Sonner<PERSON>oaster, toast as sonnerToast } from 'sonner';
import {
  <PERSON>Circle,
  AlertCircle,
  Info,
  AlertTriangle,
  Flame,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

// ==============================
// Types
// ==============================

export type NotificationPosition =
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right';

export type NotificationOptions = {
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  position?: NotificationPosition;
  duration?: number;
  className?: string;
  closeButton?: boolean;
  id?: string;
  onDismiss?: () => void;
  onAutoClose?: () => void;
};

// ==============================
// Icons
// ==============================

const NotificationIcons = {
  success: (props: React.SVGProps<SVGSVGElement>) => (
    <CheckCircle className="h-4 w-4 text-white" {...props} />
  ),
  error: (props: React.SVGProps<SVGSVGElement>) => (
    <AlertCircle className="h-4 w-4 text-white" {...props} />
  ),
  warning: (props: React.SVGProps<SVGSVGElement>) => (
    <AlertTriangle className="h-4 w-4 text-white" {...props} />
  ),
  info: (props: React.SVGProps<SVGSVGElement>) => (
    <Info className="h-4 w-4 text-white" {...props} />
  ),
  firenest: (props: React.SVGProps<SVGSVGElement>) => (
    <Flame className="h-4 w-4 text-white animate-pulse-slow" {...props} />
  ),
  loading: (props: React.SVGProps<SVGSVGElement>) => (
    <Loader2 className="h-4 w-4 text-white animate-spin" {...props} />
  ),
};

// ==============================
// Toaster Component
// ==============================

export function NotificationToaster() {
  return (
    <SonnerToaster
      position="top-center"
      closeButton={false}
      richColors
      expand={false}
      visibleToasts={3}
      theme="dark"
      pauseWhenPageIsHidden
      hotkey={['altKey', 'KeyT']}
      dir="auto"
      invert={false}
      gap={8}
      offset={0} // Remove default offset to position exactly below header
      className="firenest-toaster z-[9999]"
      toastOptions={{
        descriptionClassName: "opacity-70 h-auto max-h-none transform-none overflow-hidden text-ellipsis whitespace-nowrap",
        unstyled: false,
        className: cn(
          "firenest-notification",
          // Dynamic Island style - Firenest theme
          "group toast-glow rounded-full border-0 py-2 px-6 shadow-lg backdrop-blur-xl w-[375px] mx-auto overflow-hidden",
          // Type-specific gradients with Firenest theme colors
          "data-[type=success]:bg-gradient-to-r data-[type=success]:from-fiery/90 data-[type=success]:to-fiery-600/90",
          "data-[type=error]:bg-gradient-to-r data-[type=error]:from-red-900/90 data-[type=error]:to-red-800/90",
          "data-[type=warning]:bg-gradient-to-r data-[type=warning]:from-amber-900/90 data-[type=warning]:to-amber-800/90",
          "data-[type=info]:bg-gradient-to-r data-[type=info]:from-dark-900/90 data-[type=info]:to-dark-800/90",
          "data-[type=default]:bg-gradient-to-r data-[type=default]:from-fiery/90 data-[type=default]:to-fiery-600/90",
          "data-[type=loading]:bg-gradient-to-r data-[type=loading]:from-dark-900/90 data-[type=loading]:to-dark-800/90",
          "data-[type=firenest]:bg-gradient-to-r data-[type=firenest]:from-fiery/90 data-[type=firenest]:to-fiery-600/90",

          // Background and text
          "bg-dark-900/85 text-white",

          // Hover state
          "group-hover:bg-dark-800/90",

          // Animation
          "transition-all duration-300 ease-in-out",

          // Border glow for Firenest theme
          "border border-fiery/20"
        ),
      }}
    />
  );
}

// ==============================
// Notification API
// ==============================

// Default duration for all notifications
const DEFAULT_DURATION = 4500;

// Generate a unique ID for notifications
let notificationCounter = 0;
const generateUniqueId = (type: string) => {
  notificationCounter++;
  return `${type}-${Date.now()}-${notificationCounter}`;
};

// Helper function to create consistent notification options
const createNotificationOptions = (message: string, options?: NotificationOptions, type?: string) => {
  const notificationType = type || 'default';
  const title = options?.title || (notificationType === 'firenest' ? 'Firenest' : message);
  const description = options?.title ? message : options?.description;

  // Default duration based on notification type
  let duration = options?.duration;
  if (!duration) {
    if (notificationType === 'error') {
      duration = 5000; // Errors show longer
    } else if (notificationType === 'loading') {
      duration = Infinity; // Loading stays until dismissed
    } else {
      duration = DEFAULT_DURATION;
    }
  }

  return {
    id: options?.id || (notificationType === 'loading' ? 'loading-toast' : generateUniqueId(notificationType)),
    title,
    description,
    duration,
    position: options?.position || 'top-center',
    className: options?.className,
    action: options?.action && {
      label: options.action.label,
      onClick: options.action.onClick,
    },
    closeButton: options?.closeButton,
    onDismiss: options?.onDismiss,
    onAutoClose: options?.onAutoClose
  };
};

export const notify = {
  success: (message: string, options?: NotificationOptions) => {
    try {
      const notificationOptions = createNotificationOptions(message, options, 'success');
      return sonnerToast.success(notificationOptions.title, {
        id: notificationOptions.id,
        description: notificationOptions.description,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        action: notificationOptions.action,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        onAutoClose: notificationOptions.onAutoClose,
        icon: <NotificationIcons.success />
      });
    } catch (error) {
      console.error('Notification error:', error);
      return null;
    }
  },

  error: (message: string, options?: NotificationOptions) => {
    try {
      const notificationOptions = createNotificationOptions(message, options, 'error');
      return sonnerToast.error(notificationOptions.title, {
        id: notificationOptions.id,
        description: notificationOptions.description,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        action: notificationOptions.action,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        onAutoClose: notificationOptions.onAutoClose,
        icon: <NotificationIcons.error />
      });
    } catch (error) {
      console.error('Notification error:', error);
      return null;
    }
  },

  warning: (message: string, options?: NotificationOptions) => {
    try {
      const notificationOptions = createNotificationOptions(message, options, 'warning');
      return sonnerToast.warning(notificationOptions.title, {
        id: notificationOptions.id,
        description: notificationOptions.description,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        action: notificationOptions.action,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        onAutoClose: notificationOptions.onAutoClose,
        icon: <NotificationIcons.warning />
      });
    } catch (error) {
      console.error('Notification error:', error);
      return null;
    }
  },

  info: (message: string, options?: NotificationOptions) => {
    try {
      const notificationOptions = createNotificationOptions(message, options, 'info');
      return sonnerToast.info(notificationOptions.title, {
        id: notificationOptions.id,
        description: notificationOptions.description,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        action: notificationOptions.action,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        onAutoClose: notificationOptions.onAutoClose,
        icon: <NotificationIcons.info />
      });
    } catch (error) {
      console.error('Notification error:', error);
      return null;
    }
  },

  firenest: (message: string, options?: NotificationOptions) => {
    try {
      const notificationOptions = createNotificationOptions(message, options, 'firenest');
      return sonnerToast(notificationOptions.title, {
        id: notificationOptions.id,
        description: notificationOptions.description,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        action: notificationOptions.action,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        onAutoClose: notificationOptions.onAutoClose,
        icon: <NotificationIcons.firenest />
      });
    } catch (error) {
      console.error('Notification error:', error);
      return null;
    }
  },

  loading: (message: string, options?: NotificationOptions) => {
    try {
      const notificationOptions = createNotificationOptions(message, options, 'loading');
      return sonnerToast.loading(message, {
        id: notificationOptions.id,
        description: notificationOptions.description,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        icon: <NotificationIcons.loading />
      });
    } catch (error) {
      console.error('Notification error:', error);
      return null;
    }
  },

  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string;
      error: string;
    },
    options?: NotificationOptions
  ) => {
    try {
      // Create consistent options for promise notifications
      const notificationOptions = {
        id: options?.id,
        duration: options?.duration || DEFAULT_DURATION,
        position: options?.position || 'top-center',
        className: options?.className,
      };

      return sonnerToast.promise(promise, {
        loading: messages.loading,
        success: messages.success,
        error: messages.error,
        ...notificationOptions
      });
    } catch (error) {
      console.error('Notification promise error:', error);
      return null;
    }
  },

  custom: (
    content: React.ReactNode,
    options?: NotificationOptions
  ) => {
    try {
      // Create consistent options for custom notifications
      const notificationOptions = createNotificationOptions('', options, 'custom');

      // Custom notifications need special handling
      return sonnerToast.custom(content as any, {
        id: notificationOptions.id,
        duration: notificationOptions.duration,
        position: notificationOptions.position,
        className: notificationOptions.className,
        closeButton: notificationOptions.closeButton,
        onDismiss: notificationOptions.onDismiss,
        onAutoClose: notificationOptions.onAutoClose,
      });
    } catch (error) {
      console.error('Notification custom error:', error);
      return null;
    }
  },

  dismiss: (toastId?: string) => {
    try {
      if (toastId) {
        sonnerToast.dismiss(toastId);
      } else {
        sonnerToast.dismiss();
      }
    } catch (error) {
      console.error('Notification dismiss error:', error);
    }
  },
};

/**
 * @deprecated Use notify instead. This export is for backward compatibility only.
 */
export const toast = notify;

export default notify;
