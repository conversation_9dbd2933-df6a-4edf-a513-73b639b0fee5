/**
 * Error Handler Middleware
 * Centralized error handling with security considerations
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { config } from '@/config/environment';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class ValidationError extends Error {
  statusCode = 400;
  code = 'VALIDATION_ERROR';
  
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends Error {
  statusCode = 401;
  code = 'AUTHENTICATION_ERROR';
  
  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  statusCode = 403;
  code = 'AUTHORIZATION_ERROR';
  
  constructor(message: string = 'Access denied') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends Error {
  statusCode = 404;
  code = 'NOT_FOUND';
  
  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends Error {
  statusCode = 409;
  code = 'CONFLICT';
  
  constructor(message: string = 'Resource conflict') {
    super(message);
    this.name = 'ConflictError';
  }
}

export class InternalServerError extends Error {
  statusCode = 500;
  code = 'INTERNAL_SERVER_ERROR';
  
  constructor(message: string = 'Internal server error') {
    super(message);
    this.name = 'InternalServerError';
  }
}

export function errorHandler(
  error: ApiError,
  req: Request,
  res: Response,
  _next: NextFunction
): void | Response {
  // Log error details
  const errorId = generateErrorId();
  const logContext = {
    errorId,
    method: req.method,
    path: req.path,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: (req as any).user?.id,
    stack: error.stack
  };

  if (error.statusCode && error.statusCode < 500) {
    logger.warn('Client error:', { ...logContext, error: error.message });
  } else {
    logger.error('Server error:', { ...logContext, error: error.message });
  }

  // Determine status code
  const statusCode = error.statusCode || 500;

  // Prepare error response
  const errorResponse: any = {
    error: getErrorType(statusCode),
    message: getErrorMessage(error, statusCode),
    errorId,
    timestamp: new Date().toISOString()
  };

  // Include error code if available
  if (error.code) {
    errorResponse.code = error.code;
  }

  // Include validation details for client errors
  if (statusCode === 400 && error.details) {
    errorResponse.details = error.details;
  }

  // Include stack trace in development
  if (config.environment === 'development' && error.stack) {
    errorResponse.stack = error.stack;
  }

  res.status(statusCode).json(errorResponse);
}

function getErrorType(statusCode: number): string {
  switch (statusCode) {
    case 400:
      return 'Bad Request';
    case 401:
      return 'Unauthorized';
    case 403:
      return 'Forbidden';
    case 404:
      return 'Not Found';
    case 409:
      return 'Conflict';
    case 422:
      return 'Unprocessable Entity';
    case 429:
      return 'Too Many Requests';
    case 500:
      return 'Internal Server Error';
    case 502:
      return 'Bad Gateway';
    case 503:
      return 'Service Unavailable';
    case 504:
      return 'Gateway Timeout';
    default:
      return 'Error';
  }
}

function getErrorMessage(error: ApiError, statusCode: number): string {
  // Don't expose internal error details in production
  if (statusCode >= 500 && config.environment === 'production') {
    return 'An internal server error occurred';
  }

  // Handle specific database errors
  if (error.message.includes('duplicate key value')) {
    return 'A resource with this identifier already exists';
  }

  if (error.message.includes('foreign key constraint')) {
    return 'Referenced resource does not exist';
  }

  if (error.message.includes('not-null constraint')) {
    return 'Required field is missing';
  }

  // Handle JWT errors
  if (error.message.includes('jwt expired')) {
    return 'Authentication token has expired';
  }

  if (error.message.includes('jwt malformed')) {
    return 'Invalid authentication token';
  }

  // Handle validation errors
  if (error.name === 'ValidationError') {
    return error.message;
  }

  // Return original message for client errors
  if (statusCode < 500) {
    return error.message;
  }

  return error.message;
}

function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Async error wrapper for route handlers
export function asyncHandler(
  fn: (req: any, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: any, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// Database error handler
export function handleDatabaseError(error: any): ApiError {
  if (error.code === '23505') { // Unique violation
    return new ConflictError('Resource already exists');
  }

  if (error.code === '23503') { // Foreign key violation
    return new ValidationError('Referenced resource does not exist');
  }

  if (error.code === '23502') { // Not null violation
    return new ValidationError('Required field is missing');
  }

  if (error.code === '42P01') { // Undefined table
    return new InternalServerError('Database schema error');
  }

  // Connection errors
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    return new InternalServerError('Database connection failed');
  }

  // Default to internal server error
  return new InternalServerError(error.message);
}
