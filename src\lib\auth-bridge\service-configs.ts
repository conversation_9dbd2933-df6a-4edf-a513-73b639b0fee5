/**
 * Service Configurations
 *
 * This file contains the configuration for all supported services.
 * Each service has its own authentication method and endpoints.
 */

import { ServiceConfig } from './types';

// Helper function to get the origin safely
const getOrigin = (): string => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return 'http://localhost:3000'; // Default fallback for SSR
};

// Service configurations
export const serviceConfigs: ServiceConfig[] = [
  // OpenAI (ChatGPT)
  {
    id: 'chatgpt',
    name: 'ChatGPT',
    authMethod: 'oauth',
    integrationStatus: 'live',
    oauth: {
      clientId: 'firenest-chatgpt-client',
      authorizationUrl: 'https://auth.openai.com/authorize',
      tokenUrl: 'https://auth.openai.com/token',
      redirectUrl: `${getOrigin()}/auth/callback/openai`,
      scope: ['chat:read', 'chat:write'],
      responseType: 'code'
    },
    endpoints: {
      baseUrl: 'https://api.openai.com/v1',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'time',
      unitName: 'minute',
      costPerUnit: 5,
      minimumUsage: 1
    }
  },

  // Midjourney
  {
    id: 'midjourney',
    name: 'Midjourney',
    authMethod: 'api_key',
    integrationStatus: 'live',
    apiKey: {
      headerName: 'X-API-Key',
      isBearer: false
    },
    endpoints: {
      baseUrl: 'https://api.midjourney.com/v1',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'resources',
      unitName: 'image',
      costPerUnit: 10
    }
  },

  // GitHub Copilot
  {
    id: 'github-copilot',
    name: 'GitHub Copilot',
    authMethod: 'oauth',
    integrationStatus: 'live',
    oauth: {
      clientId: 'firenest-github-client',
      authorizationUrl: 'https://github.com/login/oauth/authorize',
      tokenUrl: 'https://github.com/login/oauth/access_token',
      redirectUrl: `${getOrigin()}/auth/callback/github`,
      scope: ['user', 'copilot'],
      responseType: 'code'
    },
    endpoints: {
      baseUrl: 'https://api.github.com',
      userInfo: '/user',
      usage: '/copilot/usage'
    },
    usageTracking: {
      metric: 'time',
      unitName: 'hour',
      costPerUnit: 15,
      minimumUsage: 1
    }
  },

  // Descript
  {
    id: 'descript',
    name: 'Descript',
    authMethod: 'credentials',
    integrationStatus: 'beta',
    credentials: {
      loginUrl: 'https://auth.descript.com/login',
      usernameField: 'email',
      passwordField: 'password',
      cookiesToCapture: ['session_id', 'auth_token']
    },
    endpoints: {
      baseUrl: 'https://api.descript.com/v1',
      login: '/auth/login',
      logout: '/auth/logout',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'time',
      unitName: 'minute',
      costPerUnit: 8
    }
  },

  // Jasper
  {
    id: 'jasper',
    name: 'Jasper',
    authMethod: 'api_key',
    integrationStatus: 'live',
    apiKey: {
      headerName: 'Authorization',
      isBearer: true
    },
    endpoints: {
      baseUrl: 'https://api.jasper.ai/v1',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'resources',
      unitName: 'word',
      costPerUnit: 0.1
    }
  },

  // Runway
  {
    id: 'runway',
    name: 'Runway',
    authMethod: 'oauth',
    integrationStatus: 'beta',
    oauth: {
      clientId: 'firenest-runway-client',
      authorizationUrl: 'https://auth.runwayml.com/authorize',
      tokenUrl: 'https://auth.runwayml.com/token',
      redirectUrl: `${getOrigin()}/auth/callback/runway`,
      scope: ['user:read', 'projects:read', 'projects:write'],
      responseType: 'code'
    },
    endpoints: {
      baseUrl: 'https://api.runwayml.com/v1',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'time',
      unitName: 'minute',
      costPerUnit: 12
    }
  },

  // Notion AI
  {
    id: 'notion-ai',
    name: 'Notion AI',
    authMethod: 'oauth',
    integrationStatus: 'live',
    oauth: {
      clientId: 'firenest-notion-client',
      authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',
      tokenUrl: 'https://api.notion.com/v1/oauth/token',
      redirectUrl: `${getOrigin()}/auth/callback/notion`,
      scope: ['read', 'write'],
      responseType: 'code'
    },
    endpoints: {
      baseUrl: 'https://api.notion.com/v1',
      userInfo: '/users/me',
      usage: '/ai/usage'
    },
    usageTracking: {
      metric: 'resources',
      unitName: 'page',
      costPerUnit: 5
    }
  },

  // Claude
  {
    id: 'claude',
    name: 'Claude',
    authMethod: 'api_key',
    integrationStatus: 'live',
    apiKey: {
      headerName: 'x-api-key',
      isBearer: false
    },
    endpoints: {
      baseUrl: 'https://api.anthropic.com/v1',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'time',
      unitName: 'minute',
      costPerUnit: 6
    }
  },

  // DALL-E
  {
    id: 'dalle',
    name: 'DALL-E',
    authMethod: 'oauth',
    integrationStatus: 'live',
    oauth: {
      clientId: 'firenest-dalle-client',
      authorizationUrl: 'https://auth.openai.com/authorize',
      tokenUrl: 'https://auth.openai.com/token',
      redirectUrl: `${getOrigin()}/auth/callback/openai`,
      scope: ['dalle:read', 'dalle:write'],
      responseType: 'code'
    },
    endpoints: {
      baseUrl: 'https://api.openai.com/v1',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'resources',
      unitName: 'image',
      costPerUnit: 8
    }
  },

  // Stable Diffusion
  {
    id: 'stable-diffusion',
    name: 'Stable Diffusion',
    authMethod: 'api_key',
    integrationStatus: 'live',
    apiKey: {
      headerName: 'Authorization',
      isBearer: true
    },
    endpoints: {
      baseUrl: 'https://api.stability.ai/v1',
      userInfo: '/user/account',
      usage: '/user/balance'
    },
    usageTracking: {
      metric: 'resources',
      unitName: 'image',
      costPerUnit: 5
    }
  },

  // Grammarly
  {
    id: 'grammarly',
    name: 'Grammarly',
    authMethod: 'oauth',
    integrationStatus: 'live',
    oauth: {
      clientId: 'firenest-grammarly-client',
      authorizationUrl: 'https://auth.grammarly.com/oauth2/authorize',
      tokenUrl: 'https://auth.grammarly.com/oauth2/token',
      redirectUrl: `${getOrigin()}/auth/callback/grammarly`,
      scope: ['user', 'document'],
      responseType: 'code'
    },
    endpoints: {
      baseUrl: 'https://api.grammarly.com/v1',
      userInfo: '/user',
      usage: '/subscription'
    },
    usageTracking: {
      metric: 'time',
      unitName: 'month',
      costPerUnit: 30,
      minimumUsage: 1
    }
  },

  // Synthesia
  {
    id: 'synthesia',
    name: 'Synthesia',
    authMethod: 'credentials',
    integrationStatus: 'beta',
    credentials: {
      loginUrl: 'https://auth.synthesia.io/login',
      usernameField: 'email',
      passwordField: 'password',
      cookiesToCapture: ['session_id', 'auth_token']
    },
    endpoints: {
      baseUrl: 'https://api.synthesia.io/v1',
      login: '/auth/login',
      logout: '/auth/logout',
      userInfo: '/users/me',
      usage: '/usage'
    },
    usageTracking: {
      metric: 'resources',
      unitName: 'video',
      costPerUnit: 25
    }
  }
];
