/**
 * Simulation Builder
 * Phase 3: Create and configure simulations with multiple pricing models
 */

import React, { useState } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { 
  Play, 
  Calculator, 
  CheckCircle, 
  AlertCircle,
  Settings,
  BarChart3,
  Users,
  DollarSign
} from 'lucide-react'
import { simulationsApi, modelsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import toast from 'react-hot-toast'

interface SimulationBuilderProps {
  projectId: string
  onSimulationCreated?: (simulation: any) => void
  onCancel?: () => void
}

export function SimulationBuilder({ projectId, onSimulationCreated, onCancel }: SimulationBuilderProps) {
  const [simulationName, setSimulationName] = useState('')
  const [selectedModelIds, setSelectedModelIds] = useState<string[]>([])
  const [scenarioConfig, setScenarioConfig] = useState({
    includeCustomerSegmentation: true,
    calculateSensitivityAnalysis: true,
    projectionPeriod: 12 // months
  })

  const queryClient = useQueryClient()

  // Load available models for the project
  const { data: models, isLoading: modelsLoading } = useQuery({
    queryKey: ['models', projectId],
    queryFn: () => modelsApi.listByProject(projectId, { sortBy: 'created_at', sortOrder: 'desc' })
  })

  const createSimulationMutation = useMutation({
    mutationFn: (data: any) => simulationsApi.create(data),
    onSuccess: (response) => {
      toast.success('Simulation started successfully!')
      queryClient.invalidateQueries({ queryKey: ['simulations', projectId] })
      onSimulationCreated?.(response.data.data)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to start simulation')
    }
  })

  const handleStartSimulation = () => {
    if (selectedModelIds.length === 0) {
      toast.error('Please select at least one pricing model')
      return
    }

    const simulationData = {
      name: simulationName.trim() || undefined,
      modelIds: selectedModelIds,
      projectId,
      scenarioConfig
    }

    createSimulationMutation.mutate(simulationData)
  }

  const toggleModelSelection = (modelId: string) => {
    setSelectedModelIds(prev => 
      prev.includes(modelId) 
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    )
  }

  if (modelsLoading) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  const modelsList = models?.data?.data || []

  if (modelsList.length === 0) {
    return (
      <div className="firenest-card">
        <div className="text-center py-12">
          <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Pricing Models Available</h3>
          <p className="text-gray-400 mb-6">
            Create pricing models before running simulations
          </p>
          <Button variant="outline" onClick={onCancel}>
            Back to Project
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="firenest-card">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-12 h-12 bg-fiery/20 rounded-lg flex items-center justify-center">
            <Play className="w-6 h-6 text-fiery" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Create Simulation</h2>
            <p className="text-gray-400">
              Run revenue analysis across multiple pricing models
            </p>
          </div>
        </div>

        {/* Simulation Configuration */}
        <div className="space-y-6">
          <div>
            <label className="form-label mb-2">Simulation Name (Optional)</label>
            <input
              type="text"
              value={simulationName}
              onChange={(e) => setSimulationName(e.target.value)}
              placeholder="e.g., Q4 Pricing Analysis"
              className="form-input"
            />
            <p className="text-xs text-gray-400 mt-1">
              Leave empty to auto-generate based on timestamp
            </p>
          </div>

          {/* Scenario Configuration */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Analysis Options</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={scenarioConfig.includeCustomerSegmentation}
                  onChange={(e) => setScenarioConfig(prev => ({
                    ...prev,
                    includeCustomerSegmentation: e.target.checked
                  }))}
                  className="form-checkbox"
                />
                <div>
                  <span className="text-white font-medium">Customer Segmentation Analysis</span>
                  <p className="text-sm text-gray-400">
                    Analyze revenue impact across different customer segments
                  </p>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={scenarioConfig.calculateSensitivityAnalysis}
                  onChange={(e) => setScenarioConfig(prev => ({
                    ...prev,
                    calculateSensitivityAnalysis: e.target.checked
                  }))}
                  className="form-checkbox"
                />
                <div>
                  <span className="text-white font-medium">Sensitivity Analysis</span>
                  <p className="text-sm text-gray-400">
                    Test pricing sensitivity across usage scenarios
                  </p>
                </div>
              </label>

              <div>
                <label className="form-label mb-2">Projection Period (Months)</label>
                <select
                  value={scenarioConfig.projectionPeriod}
                  onChange={(e) => setScenarioConfig(prev => ({
                    ...prev,
                    projectionPeriod: parseInt(e.target.value)
                  }))}
                  className="form-input w-48"
                >
                  <option value={1}>1 Month</option>
                  <option value={3}>3 Months</option>
                  <option value={6}>6 Months</option>
                  <option value={12}>12 Months</option>
                  <option value={24}>24 Months</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Model Selection */}
      <div className="firenest-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Select Pricing Models</h3>
          <Badge variant="secondary">
            {selectedModelIds.length} of {modelsList.length} selected
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {modelsList.map((model: any) => (
            <ModelSelectionCard
              key={model.id}
              model={model}
              isSelected={selectedModelIds.includes(model.id)}
              onToggle={() => toggleModelSelection(model.id)}
            />
          ))}
        </div>

        {selectedModelIds.length > 0 && (
          <div className="mt-6 p-4 bg-fiery/10 border border-fiery/20 rounded-lg">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-fiery" />
              <div>
                <h4 className="text-white font-medium">Ready to Simulate</h4>
                <p className="text-sm text-gray-400">
                  {selectedModelIds.length} model{selectedModelIds.length !== 1 ? 's' : ''} selected for comparison
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Simulation Preview */}
      <div className="firenest-card">
        <h3 className="text-lg font-semibold text-white mb-4">What Will Be Analyzed</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="firenest-nested-card text-center">
            <Users className="w-8 h-8 text-blue-400 mx-auto mb-3" />
            <h4 className="text-white font-medium mb-2">Customer Impact</h4>
            <p className="text-sm text-gray-400">
              Revenue per customer across all selected models
            </p>
          </div>

          <div className="firenest-nested-card text-center">
            <BarChart3 className="w-8 h-8 text-green-400 mx-auto mb-3" />
            <h4 className="text-white font-medium mb-2">Revenue Analysis</h4>
            <p className="text-sm text-gray-400">
              Total revenue projections and comparisons
            </p>
          </div>

          <div className="firenest-nested-card text-center">
            <DollarSign className="w-8 h-8 text-purple-400 mx-auto mb-3" />
            <h4 className="text-white font-medium mb-2">Optimization</h4>
            <p className="text-sm text-gray-400">
              Recommendations for pricing improvements
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          onClick={handleStartSimulation}
          disabled={selectedModelIds.length === 0 || createSimulationMutation.isPending}
          variant="fiery"
        >
          {createSimulationMutation.isPending ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Starting Simulation...
            </>
          ) : (
            <>
              <Play className="w-4 h-4 mr-2" />
              Start Simulation
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

interface ModelSelectionCardProps {
  model: any
  isSelected: boolean
  onToggle: () => void
}

function ModelSelectionCard({ model, isSelected, onToggle }: ModelSelectionCardProps) {
  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'USAGE_BASED':
        return BarChart3
      case 'TIERED':
        return Calculator
      case 'HYBRID':
        return Settings
      case 'SUBSCRIPTION':
        return DollarSign
      default:
        return Calculator
    }
  }

  const ModelIcon = getModelTypeIcon(model.model_type)

  return (
    <div
      onClick={onToggle}
      className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-fiery bg-fiery/10'
          : 'border-white/10 hover:border-white/20 bg-gradient-to-br from-transparent to-white/5'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`w-8 h-8 rounded-lg ${isSelected ? 'bg-fiery/20' : 'bg-white/10'} flex items-center justify-center`}>
            <ModelIcon className={`w-4 h-4 ${isSelected ? 'text-fiery' : 'text-gray-400'}`} />
          </div>
          <div>
            <h4 className="text-white font-medium">{model.name}</h4>
            <Badge variant="secondary" className="text-xs">
              {model.model_type.replace('_', ' ')}
            </Badge>
          </div>
        </div>
        
        {isSelected && (
          <CheckCircle className="w-5 h-5 text-fiery" />
        )}
      </div>

      {model.description && (
        <p className="text-sm text-gray-400 mb-3">{model.description}</p>
      )}

      <div className="flex justify-between text-xs text-gray-400">
        <span>{model.component_count || 0} components</span>
        <span>{model.simulation_count || 0} simulations</span>
      </div>
    </div>
  )
}
