/**
 * <PERSON><PERSON><PERSON> to run the SQL function to create the get_auth_user_by_id function
 */

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://wjwguxccykrbarehqgpq.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indqd2d1eGNjeWtyYmFyZWhxZ3BxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDAyOTQ1MywiZXhwIjoyMDU5NjA1NDUzfQ.6P_W7tcaTpt3nCXv-AnuQrbwKD_IMvn0zIrsSPgq4hw';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function runSqlFunction() {
  try {
    console.log('Reading SQL function file...');
    const sqlFunction = fs.readFileSync('./create-auth-user-function.sql', 'utf8');
    
    console.log('Running SQL function...');
    const { error } = await supabase.rpc('pgbouncer_exec', {
      query: sqlFunction
    });
    
    if (error) {
      // Try direct SQL execution if pgbouncer_exec fails
      console.log('pgbouncer_exec failed, trying direct SQL execution...');
      const { error: directError } = await supabase.sql(sqlFunction);
      
      if (directError) {
        throw new Error(`Error running SQL function: ${directError.message}`);
      }
    }
    
    console.log('SQL function executed successfully');
  } catch (error) {
    console.error('Error running SQL function:', error.message);
    process.exit(1);
  }
}

// Run the function
runSqlFunction()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
