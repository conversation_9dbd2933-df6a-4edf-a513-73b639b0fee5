# Terraform Outputs for Firenest Sandbox Infrastructure

output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}

output "database_port" {
  description = "RDS instance port"
  value       = aws_db_instance.main.port
}

output "database_name" {
  description = "Database name"
  value       = aws_db_instance.main.db_name
}

output "database_username" {
  description = "Database username"
  value       = aws_db_instance.main.username
  sensitive   = true
}

output "database_password" {
  description = "Database password"
  value       = random_password.db_password.result
  sensitive   = true
}

output "s3_bucket_name" {
  description = "Name of the S3 bucket for uploads"
  value       = aws_s3_bucket.uploads.bucket
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket for uploads"
  value       = aws_s3_bucket.uploads.arn
}

output "sqs_queue_url" {
  description = "URL of the SQS queue for jobs"
  value       = aws_sqs_queue.jobs.url
}

output "sqs_queue_arn" {
  description = "ARN of the SQS queue for jobs"
  value       = aws_sqs_queue.jobs.arn
}

output "sqs_dlq_url" {
  description = "URL of the SQS dead letter queue"
  value       = aws_sqs_queue.dlq.url
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.app.name
}

output "ecs_security_group_id" {
  description = "ID of the ECS tasks security group"
  value       = aws_security_group.ecs_tasks.id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = aws_security_group.rds.id
}

# Environment configuration for application deployment
output "environment_config" {
  description = "Environment configuration for application"
  value = {
    AWS_REGION                = var.aws_region
    DATABASE_URL             = "postgresql://${aws_db_instance.main.username}:${random_password.db_password.result}@${aws_db_instance.main.endpoint}:${aws_db_instance.main.port}/${aws_db_instance.main.db_name}"
    S3_BUCKET_NAME          = aws_s3_bucket.uploads.bucket
    SQS_QUEUE_URL           = aws_sqs_queue.jobs.url
    CLOUDWATCH_LOG_GROUP    = aws_cloudwatch_log_group.app.name
    VPC_ID                  = aws_vpc.main.id
    PRIVATE_SUBNET_IDS      = join(",", aws_subnet.private[*].id)
    ECS_SECURITY_GROUP_ID   = aws_security_group.ecs_tasks.id
  }
  sensitive = true
}
