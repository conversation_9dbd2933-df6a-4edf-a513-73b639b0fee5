import React, { useState } from 'react';
import {
  Plus,
  Folder,
  MoreHorizontal,
  Pencil,
  Trash2,
  Play,
  ArrowRight,
  Zap,
  MessageSquare,
  Image,
  Headphones,
  Code,
  FileText,
  Star,
  Clock,
  Lock,
  Share2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { notify } from '@/components/ui/notification-system';

interface ToolCollectionsProps {
  tools: any[];
  onLaunchTool: (toolId: string) => void;
}

interface Collection {
  id: string;
  name: string;
  description: string;
  tools: string[];
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  author?: {
    name: string;
    avatar?: string;
  };
}

const ToolCollections: React.FC<ToolCollectionsProps> = ({ tools, onLaunchTool }) => {
  const [activeTab, setActiveTab] = useState('my-collections');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newCollection, setNewCollection] = useState({
    name: '',
    description: '',
    isPublic: false,
    tools: [] as string[]
  });
  const [editingCollection, setEditingCollection] = useState<Collection | null>(null);

  // Mock collections data
  const myCollections: Collection[] = [
    {
      id: 'content-creation',
      name: 'Content Creation Suite',
      description: 'Tools for creating blog posts, social media content, and marketing materials',
      tools: ['chatgpt', 'dalle', 'midjourney'],
      isPublic: true,
      createdAt: '2023-10-15T12:00:00Z',
      updatedAt: '2023-11-20T14:30:00Z'
    },
    {
      id: 'code-assistant',
      name: 'Code Assistant Workflow',
      description: 'Tools for code generation, debugging, and documentation',
      tools: ['chatgpt', 'claude'],
      isPublic: false,
      createdAt: '2023-09-05T10:15:00Z',
      updatedAt: '2023-11-18T09:45:00Z'
    }
  ];

  const communityCollections: Collection[] = [
    {
      id: 'marketing-toolkit',
      name: 'Marketing Toolkit',
      description: 'Complete toolkit for digital marketing campaigns',
      tools: ['chatgpt', 'dalle', 'midjourney', 'whisper'],
      isPublic: true,
      createdAt: '2023-08-12T15:30:00Z',
      updatedAt: '2023-11-10T11:20:00Z',
      author: {
        name: 'Sarah K.',
        avatar: ''
      }
    },
    {
      id: 'data-analysis',
      name: 'Data Analysis Pipeline',
      description: 'Tools for data processing, analysis, and visualization',
      tools: ['chatgpt', 'claude'],
      isPublic: true,
      createdAt: '2023-07-22T08:45:00Z',
      updatedAt: '2023-10-30T16:15:00Z',
      author: {
        name: 'Michael R.',
        avatar: ''
      }
    },
    {
      id: 'creative-studio',
      name: 'Creative Studio',
      description: 'Tools for creative projects including images, text, and audio',
      tools: ['dalle', 'midjourney', 'whisper'],
      isPublic: true,
      createdAt: '2023-09-18T14:20:00Z',
      updatedAt: '2023-11-15T10:10:00Z',
      author: {
        name: 'Alex M.',
        avatar: ''
      }
    }
  ];

  // Get appropriate icon for tool
  const getToolIcon = (toolId: string) => {
    const tool = tools.find(t => t.id === toolId);
    if (!tool) return <Zap className="h-5 w-5 text-fiery" />;

    switch(tool.category) {
      case 'Text Generation':
        return <MessageSquare className="h-5 w-5 text-fiery" />;
      case 'Image Generation':
        return <Image className="h-5 w-5 text-fiery" />;
      case 'Audio Processing':
        return <Headphones className="h-5 w-5 text-fiery" />;
      case 'Code Generation':
        return <Code className="h-5 w-5 text-fiery" />;
      default:
        return <Zap className="h-5 w-5 text-fiery" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  // Handle creating a new collection
  const handleCreateCollection = () => {
    if (!newCollection.name.trim()) {
      notify.warning('Please enter a collection name', {
        duration: 3000
      });
      return;
    }

    if (newCollection.tools.length === 0) {
      notify.warning('Please select at least one tool', {
        duration: 3000
      });
      return;
    }

    // In a real app, this would save to a database
    notify.success(`Created collection: ${newCollection.name}`, {
      duration: 3000
    });

    setIsCreateDialogOpen(false);
    setNewCollection({
      name: '',
      description: '',
      isPublic: false,
      tools: []
    });
  };

  // Handle editing a collection
  const handleEditCollection = (collection: Collection) => {
    setEditingCollection(collection);
    setNewCollection({
      name: collection.name,
      description: collection.description,
      isPublic: collection.isPublic,
      tools: [...collection.tools]
    });
    setIsCreateDialogOpen(true);
  };

  // Handle deleting a collection
  const handleDeleteCollection = (collectionId: string) => {
    // In a real app, this would delete from a database
    notify.success(`Deleted collection`, {
      duration: 3000
    });
  };

  // Handle toggling a tool in the collection
  const handleToggleTool = (toolId: string) => {
    setNewCollection(prev => {
      if (prev.tools.includes(toolId)) {
        return {
          ...prev,
          tools: prev.tools.filter(id => id !== toolId)
        };
      } else {
        return {
          ...prev,
          tools: [...prev.tools, toolId]
        };
      }
    });
  };

  // Render collection card
  const renderCollectionCard = (collection: Collection, isOwner: boolean = true) => (
    <Card key={collection.id} className="firenest-card overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-md bg-fiery/20 flex items-center justify-center">
              <Folder className="h-5 w-5 text-fiery" />
            </div>
            <div>
              <CardTitle className="text-white">{collection.name}</CardTitle>
              <div className="flex items-center gap-2">
                <p className="text-xs text-white/70">
                  {collection.tools.length} tools
                </p>
                {collection.isPublic ? (
                  <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30 border-none text-xs">Public</Badge>
                ) : (
                  <Badge className="bg-amber-500/20 text-amber-400 hover:bg-amber-500/30 border-none text-xs">Private</Badge>
                )}
              </div>
            </div>
          </div>
          {isOwner && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white/70 hover:text-white hover:bg-white/5">
                  <MoreHorizontal className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-dark-800 border-white/10 text-white">
                <DropdownMenuItem
                  className="hover:bg-white/5 cursor-pointer"
                  onClick={() => handleEditCollection(collection)}
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit Collection
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="hover:bg-white/5 cursor-pointer text-red-400 hover:text-red-300"
                  onClick={() => handleDeleteCollection(collection.id)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Collection
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-white/80 mb-4">{collection.description}</p>

        <div className="space-y-3">
          <h4 className="text-xs font-medium text-white/70">Included Tools:</h4>
          <div className="flex flex-wrap gap-2">
            {collection.tools.map(toolId => {
              const tool = tools.find(t => t.id === toolId);
              return tool ? (
                <Badge
                  key={toolId}
                  className="bg-dark-700 text-white hover:bg-dark-600 border-white/10 cursor-pointer"
                  onClick={() => onLaunchTool(toolId)}
                >
                  <div className="flex items-center gap-1.5">
                    {getToolIcon(toolId)}
                    <span>{tool.name}</span>
                  </div>
                </Badge>
              ) : null;
            })}
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t border-white/10 pt-3 flex justify-between items-center">
        <div className="flex items-center text-xs text-white/50">
          {collection.author ? (
            <div className="flex items-center gap-1.5">
              <Avatar className="h-5 w-5">
                <AvatarImage src={collection.author.avatar} alt={collection.author.name} />
                <AvatarFallback className="bg-fiery text-white text-[10px]">
                  {collection.author.name.split(' ').map(name => name[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <span>{collection.author.name}</span>
            </div>
          ) : (
            <span>Updated {formatDate(collection.updatedAt)}</span>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          className="border-white/10 hover:bg-white/5"
        >
          <Play className="h-3.5 w-3.5 mr-1.5" />
          Run Workflow
        </Button>
      </CardFooter>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-white">Tool Collections & Workflows</h2>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-fiery hover:bg-fiery-600">
              <Plus className="h-4 w-4 mr-2" />
              Create Collection
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-dark-900 border-white/10 text-white">
            <DialogHeader>
              <DialogTitle>{editingCollection ? 'Edit Collection' : 'Create New Collection'}</DialogTitle>
              <DialogDescription>
                Group tools together for specific workflows or projects
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium text-white">
                  Collection Name
                </label>
                <Input
                  id="name"
                  placeholder="e.g., Content Creation Suite"
                  className="bg-dark-800 border-white/10 focus:border-fiery/50"
                  value={newCollection.name}
                  onChange={(e) => setNewCollection(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium text-white">
                  Description
                </label>
                <Textarea
                  id="description"
                  placeholder="Describe what this collection is for..."
                  className="bg-dark-800 border-white/10 focus:border-fiery/50 min-h-[80px]"
                  value={newCollection.description}
                  onChange={(e) => setNewCollection(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublic"
                  className="rounded border-white/10 bg-dark-800"
                  checked={newCollection.isPublic}
                  onChange={(e) => setNewCollection(prev => ({ ...prev, isPublic: e.target.checked }))}
                />
                <label htmlFor="isPublic" className="text-sm text-white">
                  Make this collection public
                </label>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-white">
                  Select Tools
                </label>
                <div className="grid grid-cols-2 gap-2 max-h-[200px] overflow-y-auto pr-2">
                  {tools.map(tool => (
                    <div
                      key={tool.id}
                      className={`flex items-center gap-2 p-2 rounded-md cursor-pointer transition-colors ${
                        newCollection.tools.includes(tool.id)
                          ? 'bg-fiery/20 border border-fiery/30'
                          : 'bg-dark-800 border border-white/10 hover:border-white/20'
                      }`}
                      onClick={() => handleToggleTool(tool.id)}
                    >
                      <div className="h-8 w-8 rounded-md bg-fiery/20 flex items-center justify-center flex-shrink-0">
                        {getToolIcon(tool.id)}
                      </div>
                      <div className="min-w-0">
                        <p className="text-sm font-medium text-white truncate">{tool.name}</p>
                        <p className="text-xs text-white/70 truncate">{tool.category}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                className="border-white/10 hover:bg-white/5"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="bg-fiery hover:bg-fiery-600"
                onClick={handleCreateCollection}
              >
                {editingCollection ? 'Save Changes' : 'Create Collection'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="bg-dark-800 border border-white/10">
          <TabsTrigger value="my-collections" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            My Collections
          </TabsTrigger>
          <TabsTrigger value="community" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Community Collections
          </TabsTrigger>
          <TabsTrigger value="featured" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
            Featured Workflows
          </TabsTrigger>
        </TabsList>

        <TabsContent value="my-collections" className="space-y-4">
          {myCollections.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {myCollections.map(collection => renderCollectionCard(collection))}
            </div>
          ) : (
            <Card className="firenest-card">
              <CardContent className="p-8 flex flex-col items-center justify-center">
                <div className="h-16 w-16 rounded-full bg-white/5 flex items-center justify-center mb-4">
                  <Folder className="h-8 w-8 text-white/30" />
                </div>
                <h3 className="text-lg font-medium text-white mb-2">No collections yet</h3>
                <p className="text-white/70 text-center max-w-md mb-4">
                  Create your first collection to group tools together for specific workflows or projects.
                </p>
                <Button
                  className="bg-fiery hover:bg-fiery-600"
                  onClick={() => setIsCreateDialogOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Collection
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="community" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {communityCollections.map(collection => renderCollectionCard(collection, false))}
          </div>
        </TabsContent>

        <TabsContent value="featured" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="firenest-card overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1">
                <div className="h-full bg-gradient-to-r from-fiery via-purple-500 to-blue-500 w-full"></div>
              </div>
              <CardHeader className="pt-6 pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-md bg-gradient-to-br from-fiery to-purple-500 flex items-center justify-center">
                      <Star className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-white">AI Content Creation Pipeline</CardTitle>
                      <div className="flex items-center gap-2">
                        <p className="text-xs text-white/70">
                          4 tools
                        </p>
                        <Badge className="bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 border-none text-xs">Featured</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/80 mb-4">Complete workflow for creating, editing, and publishing content with AI assistance</p>

                <div className="space-y-3">
                  <h4 className="text-xs font-medium text-white/70">Workflow Steps:</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-fiery/20 flex items-center justify-center text-xs text-fiery font-medium">1</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <MessageSquare className="h-4 w-4 text-fiery" />
                        <span className="text-sm text-white">ChatGPT</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Generate content outline</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-fiery/20 flex items-center justify-center text-xs text-fiery font-medium">2</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <MessageSquare className="h-4 w-4 text-fiery" />
                        <span className="text-sm text-white">Claude</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Expand outline into full content</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-fiery/20 flex items-center justify-center text-xs text-fiery font-medium">3</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <Image className="h-4 w-4 text-fiery" />
                        <span className="text-sm text-white">DALL-E</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Generate supporting images</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-fiery/20 flex items-center justify-center text-xs text-fiery font-medium">4</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <MessageSquare className="h-4 w-4 text-fiery" />
                        <span className="text-sm text-white">ChatGPT</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Final review and optimization</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-3 flex justify-between items-center">
                <div className="flex items-center text-xs text-white/50">
                  <div className="flex items-center gap-1.5">
                    <Avatar className="h-5 w-5">
                      <AvatarFallback className="bg-fiery text-white text-[10px]">
                        FN
                      </AvatarFallback>
                    </Avatar>
                    <span>Firenest Team</span>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-white/10 hover:bg-white/5"
                >
                  <Play className="h-3.5 w-3.5 mr-1.5" />
                  Run Workflow
                </Button>
              </CardFooter>
            </Card>

            <Card className="firenest-card overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1">
                <div className="h-full bg-gradient-to-r from-blue-500 via-teal-500 to-green-500 w-full"></div>
              </div>
              <CardHeader className="pt-6 pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-md bg-gradient-to-br from-blue-500 to-teal-500 flex items-center justify-center">
                      <Star className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-white">Data Analysis Workflow</CardTitle>
                      <div className="flex items-center gap-2">
                        <p className="text-xs text-white/70">
                          3 tools
                        </p>
                        <Badge className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 border-none text-xs">Featured</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/80 mb-4">Comprehensive workflow for analyzing data, generating insights, and creating visualizations</p>

                <div className="space-y-3">
                  <h4 className="text-xs font-medium text-white/70">Workflow Steps:</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center text-xs text-blue-400 font-medium">1</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <Code className="h-4 w-4 text-blue-400" />
                        <span className="text-sm text-white">Data Processor</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Clean and prepare data</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center text-xs text-blue-400 font-medium">2</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <MessageSquare className="h-4 w-4 text-blue-400" />
                        <span className="text-sm text-white">ChatGPT</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Generate insights and analysis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center text-xs text-blue-400 font-medium">3</div>
                      <div className="flex items-center gap-1.5 bg-dark-700 px-2 py-1 rounded-md">
                        <Image className="h-4 w-4 text-blue-400" />
                        <span className="text-sm text-white">Visualization Tool</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-white/50" />
                      <span className="text-xs text-white/70">Create charts and visualizations</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t border-white/10 pt-3 flex justify-between items-center">
                <div className="flex items-center text-xs text-white/50">
                  <div className="flex items-center gap-1.5">
                    <Avatar className="h-5 w-5">
                      <AvatarFallback className="bg-blue-500 text-white text-[10px]">
                        FN
                      </AvatarFallback>
                    </Avatar>
                    <span>Firenest Team</span>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-white/10 hover:bg-white/5"
                >
                  <Play className="h-3.5 w-3.5 mr-1.5" />
                  Run Workflow
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ToolCollections;
