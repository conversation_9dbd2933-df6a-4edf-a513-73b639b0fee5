import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Search, Star, Clock, Zap, ArrowUpRight, SlidersHorizontal, ExternalLink } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { notify } from '@/components/ui/notification-system';

// Import authentication bridge
import { useServiceSessions } from '@/lib/auth-bridge';

// Import usage tracking
import { useUsageSummaries, useUsageAlerts } from '@/lib/usage-tracking';

// Import integration framework
import { useIntegrations, useIntegrationHealth, useLaunchIntegration } from '@/lib/integration-framework';
import { IntegrationMetadata, IntegrationCategory } from '@/lib/integration-framework/types';

// Tool categories mapping
const categoryMapping: Record<IntegrationCategory, string> = {
  ai_writing: 'AI Writing',
  image_generation: 'Image Generation',
  video_creation: 'Video Creation',
  code_assistant: 'Code Assistant',
  data_analysis: 'Data Analysis',
  audio_tools: 'Audio Tools',
  productivity: 'Productivity',
  marketing: 'Marketing',
  design: 'Design'
};

// Tool categories for display
const categories = [
  'All Tools',
  ...Object.values(categoryMapping)
];

// Filter interface
interface FilterOptions {
  minRating: number;
  maxCost: number;
  authMethods: ('oauth' | 'api_key' | 'credentials' | 'ip_based')[];
  integrationStatus: ('live' | 'beta' | 'coming_soon')[];
}

const Workbench = () => {
  const { credits } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Tools');
  const [activeTab, setActiveTab] = useState('all');
  const [selectedTool, setSelectedTool] = useState<IntegrationMetadata | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Filter states
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    minRating: 0,
    maxCost: 50,
    authMethods: ['oauth', 'api_key', 'credentials', 'ip_based'],
    integrationStatus: ['live', 'beta']
  });

  // Get integrations from the integration framework
  const { integrations: allIntegrations } = useIntegrations();

  // Get active sessions from the auth bridge
  const { sessions: activeSessions, refreshSessions } = useServiceSessions();

  // Get usage summaries from the usage tracking system
  const { totalCredits: totalCreditsUsed } = useUsageSummaries('day');

  // Get integration health status
  const { allHealthStatuses } = useIntegrationHealth();

  // Get launch integration function
  const { launch, isLaunching, error: launchError } = useLaunchIntegration();

  // Filter integrations based on all criteria
  const filteredTools = allIntegrations.filter(integration => {
    // Basic filters
    const matchesSearch =
      integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      integration.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      integration.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const displayCategory = categoryMapping[integration.category];
    const matchesCategory = selectedCategory === 'All Tools' || displayCategory === selectedCategory;

    const matchesTab =
      (activeTab === 'all') ||
      (activeTab === 'popular' && integration.popular) ||
      (activeTab === 'new' && integration.new);

    // Advanced filters
    const matchesRating = integration.rating >= filterOptions.minRating;
    const matchesCost = integration.pricing.costPerUnit <= filterOptions.maxCost;

    // Get health status
    const healthStatus = allHealthStatuses.find(status => status.id === integration.id);
    const integrationStatus = healthStatus?.status === 'down' ? 'coming_soon' :
                             healthStatus?.status === 'degraded' ? 'beta' : 'live';

    const matchesStatus = filterOptions.integrationStatus.includes(integrationStatus as any);

    return matchesSearch && matchesCategory && matchesTab &&
           matchesRating && matchesCost && matchesStatus;
  });

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    setFilterOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Toggle auth method filter
  const toggleAuthMethod = (method: 'oauth' | 'api_key' | 'credentials' | 'ip_based') => {
    setFilterOptions(prev => {
      const newMethods = prev.authMethods.includes(method)
        ? prev.authMethods.filter(m => m !== method)
        : [...prev.authMethods, method];
      return {
        ...prev,
        authMethods: newMethods
      };
    });
  };

  // Toggle integration status filter
  const toggleIntegrationStatus = (status: 'live' | 'beta' | 'coming_soon') => {
    setFilterOptions(prev => {
      const newStatuses = prev.integrationStatus.includes(status)
        ? prev.integrationStatus.filter(s => s !== status)
        : [...prev.integrationStatus, status];
      return {
        ...prev,
        integrationStatus: newStatuses
      };
    });
  };

  // Reset all filters
  const resetFilters = () => {
    setFilterOptions({
      minRating: 0,
      maxCost: 50,
      authMethods: ['oauth', 'api_key', 'credentials', 'ip_based'],
      integrationStatus: ['live', 'beta', 'coming_soon']
    });
    setSearchQuery('');
    setSelectedCategory('All Tools');
    setActiveTab('all');
  };

  const handleLaunchTool = async (integration: IntegrationMetadata) => {
    // Show loading toast
    const loadingToast = notify.loading(`Launching ${integration.name}...`, {
      title: 'Launching Tool'
    });

    try {
      // Launch the integration
      const result = await launch(integration.id);

      if (result) {
        // Success
        notify.dismiss(loadingToast);
        notify.success(`Successfully launched ${integration.name}. You can now use the service.`, {
          title: 'Launch Successful',
          duration: 4000
        });

        // Refresh sessions
        refreshSessions();
      } else if (launchError) {
        // Error
        notify.dismiss(loadingToast);
        notify.error(`Failed to launch ${integration.name}: ${launchError}`, {
          title: 'Launch Failed',
          duration: 5000
        });
      }
    } catch (error) {
      // Unexpected error
      notify.dismiss(loadingToast);
      notify.error(`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        title: 'Error',
        duration: 5000
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-white">Workbench</h1>
        <p className="text-white/70 mt-1">Discover and launch AI tools with your Firenest credits</p>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="flex-1 space-y-6">
          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50" />
              <Input
                placeholder="Search tools..."
                className="pl-10 bg-white/5 border-white/10 text-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              className="border-white/10 text-white flex items-center gap-2"
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal className="h-4 w-4" />
              Filters
              {showFilters ? ' (on)' : ''}
            </Button>
          </div>

          {/* Filters dialog */}
          <Dialog open={showFilters} onOpenChange={setShowFilters}>
            <DialogContent className="bg-sidebar-background border-white/10 text-white">
              <DialogHeader>
                <DialogTitle>Filter Tools</DialogTitle>
                <DialogDescription className="text-white/70">
                  Customize your search with these filters
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                {/* Rating filter */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Minimum Rating</h4>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.1"
                      value={filterOptions.minRating}
                      onChange={(e) => handleFilterChange('minRating', parseFloat(e.target.value))}
                      className="w-full"
                    />
                    <span className="text-white/70 min-w-[2rem] text-right">{filterOptions.minRating.toFixed(1)}</span>
                  </div>
                </div>

                {/* Cost filter */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Maximum Cost (credits)</h4>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={filterOptions.maxCost}
                      onChange={(e) => handleFilterChange('maxCost', parseInt(e.target.value))}
                      className="w-full"
                    />
                    <span className="text-white/70 min-w-[2rem] text-right">{filterOptions.maxCost}</span>
                  </div>
                </div>

                {/* Auth method filter */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Authentication Method</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.authMethods.includes('oauth')}
                        onChange={() => toggleAuthMethod('oauth')}
                      />
                      OAuth
                    </label>
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.authMethods.includes('api_key')}
                        onChange={() => toggleAuthMethod('api_key')}
                      />
                      API Key
                    </label>
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.authMethods.includes('credentials')}
                        onChange={() => toggleAuthMethod('credentials')}
                      />
                      Credentials
                    </label>
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.authMethods.includes('ip_based')}
                        onChange={() => toggleAuthMethod('ip_based')}
                      />
                      IP Based
                    </label>
                  </div>
                </div>

                {/* Integration status filter */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Integration Status</h4>
                  <div className="grid grid-cols-3 gap-2">
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.integrationStatus.includes('live')}
                        onChange={() => toggleIntegrationStatus('live')}
                      />
                      Live
                    </label>
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.integrationStatus.includes('beta')}
                        onChange={() => toggleIntegrationStatus('beta')}
                      />
                      Beta
                    </label>
                    <label className="flex items-center gap-2 text-white/70">
                      <input
                        type="checkbox"
                        checked={filterOptions.integrationStatus.includes('coming_soon')}
                        onChange={() => toggleIntegrationStatus('coming_soon')}
                      />
                      Coming Soon
                    </label>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="ghost" onClick={resetFilters}>Reset</Button>
                <Button onClick={() => setShowFilters(false)}>Apply Filters</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Tabs */}
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="bg-white/5 border border-white/10">
              <TabsTrigger value="all" className="data-[state=active]:bg-fiery/20 data-[state=active]:text-fiery">All Tools</TabsTrigger>
              <TabsTrigger value="popular" className="data-[state=active]:bg-fiery/20 data-[state=active]:text-fiery">Popular</TabsTrigger>
              <TabsTrigger value="new" className="data-[state=active]:bg-fiery/20 data-[state=active]:text-fiery">New</TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Tool Detail Dialog */}
          <Dialog open={!!selectedTool} onOpenChange={(open) => !open && setSelectedTool(null)}>
            <DialogContent className="bg-sidebar-background border-white/10 text-white max-w-3xl max-h-[90vh] overflow-y-auto">
              {selectedTool && (
                <>
                  <DialogHeader>
                    <div className="flex items-center gap-3">
                      <div className="text-3xl">{selectedTool.partnerId.charAt(0).toUpperCase()}</div>
                      <div>
                        <DialogTitle className="text-xl text-white">{selectedTool.name}</DialogTitle>
                        <div className="flex items-center gap-2 text-white/70 text-sm mt-1">
                          <div className="flex items-center">
                            <Star className="h-4 w-4 fill-fiery text-fiery mr-1" />
                            <span>{selectedTool.rating}</span>
                            <span className="text-xs">({selectedTool.reviewCount} reviews)</span>
                          </div>
                          <span className="mx-1">•</span>
                          <span>{categoryMapping[selectedTool.category]}</span>

                          {/* Get health status */}
                          {(() => {
                            const healthStatus = allHealthStatuses.find(status => status.id === selectedTool.id);
                            if (healthStatus?.status === 'down') {
                              return <Badge variant="destructive" className="ml-2">Unavailable</Badge>;
                            } else if (healthStatus?.status === 'degraded') {
                              return <Badge variant="outline" className="ml-2 text-yellow-400 border-yellow-400/30 bg-yellow-400/10">Degraded</Badge>;
                            }
                            return null;
                          })()}

                          {selectedTool.new && (
                            <Badge variant="secondary" className="ml-2 bg-fiery/20 text-fiery border-fiery/30">New</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </DialogHeader>

                  <div className="space-y-6 py-4">
                    {/* Description */}
                    <div>
                      <h3 className="text-white font-medium mb-2">Description</h3>
                      <p className="text-white/80">{selectedTool.longDescription || selectedTool.description}</p>
                    </div>

                    {/* Features */}
                    {selectedTool.features && (
                      <div>
                        <h3 className="text-white font-medium mb-2">Key Features</h3>
                        <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {selectedTool.features.map((feature) => (
                            <li key={feature.id} className="flex items-center gap-2 text-white/80">
                              <div className="h-1.5 w-1.5 rounded-full bg-fiery"></div>
                              {feature.name}{!feature.isAvailable && <span className="text-xs text-white/50 ml-1">(Coming soon)</span>}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Usage Examples */}
                    {selectedTool.usageExamples && (
                      <div>
                        <h3 className="text-white font-medium mb-2">Usage Examples</h3>
                        <ul className="space-y-1">
                          {selectedTool.usageExamples.map((example, index) => (
                            <li key={index} className="text-white/80 flex items-start gap-2">
                              <span className="text-fiery">•</span>
                              {example}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Pricing */}
                    <div>
                      <h3 className="text-white font-medium mb-2">Pricing</h3>
                      <div className="bg-white/5 p-4 rounded-md">
                        <div className="flex justify-between items-center">
                          <span className="text-white/80">Cost</span>
                          <span className="text-white font-medium">
                            {selectedTool.pricing.costPerUnit} credits {selectedTool.pricing.unitName.toLowerCase()}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Integration Details */}
                    <div>
                      <h3 className="text-white font-medium mb-2">Integration Details</h3>
                      <div className="bg-white/5 p-4 rounded-md space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-white/80">Partner</span>
                          <span className="text-white/90">
                            {allHealthStatuses.find(status => status.partnerId === selectedTool.partnerId)?.partnerId || selectedTool.partnerId}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-white/80">Website</span>
                          <a
                            href={`https://${selectedTool.partnerId}.com`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-fiery hover:underline flex items-center gap-1"
                          >
                            Visit {selectedTool.name} <ExternalLink className="h-3 w-3" />
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <DialogFooter>
                    {/* Get health status */}
                    {(() => {
                      const healthStatus = allHealthStatuses.find(status => status.id === selectedTool.id);
                      if (healthStatus?.status === 'down') {
                        return (
                          <Button disabled className="w-full">
                            Currently Unavailable
                          </Button>
                        );
                      } else {
                        return (
                          <Button
                            onClick={() => {
                              handleLaunchTool(selectedTool);
                              setSelectedTool(null);
                            }}
                            disabled={isLaunching}
                            className="w-full"
                          >
                            {isLaunching ? 'Launching...' : `Launch ${selectedTool.name}`}
                            <ArrowUpRight className="h-4 w-4 ml-1" />
                          </Button>
                        );
                      }
                    })()}
                  </DialogFooter>
                </>
              )}
            </DialogContent>
          </Dialog>

          {/* Tools grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTools.map((integration) => {
              // Get health status
              const healthStatus = allHealthStatuses.find(status => status.id === integration.id);
              const statusBadge = healthStatus?.status === 'down' ?
                <Badge variant="destructive">Unavailable</Badge> :
                healthStatus?.status === 'degraded' ?
                <Badge variant="outline" className="border-yellow-400/30 text-yellow-400 bg-yellow-400/10">Degraded</Badge> :
                null;

              return (
                <Card
                  key={integration.id}
                  className="firenest-card-accent overflow-hidden group cursor-pointer"
                  onClick={() => setSelectedTool(integration)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{integration.partnerId.charAt(0).toUpperCase()}</div>
                        <div>
                          <CardTitle className="text-lg text-white">{integration.name}</CardTitle>
                          <div className="flex items-center gap-1 text-white/70 text-xs mt-1">
                            <Star className="h-3 w-3 fill-fiery text-fiery" />
                            <span>{integration.rating}</span>
                            <span className="mx-1">•</span>
                            <span>{categoryMapping[integration.category]}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        {statusBadge}
                        {integration.new && (
                          <Badge variant="secondary" className="bg-fiery/20 text-fiery border-fiery/30">New</Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-white/80 mb-4 h-10 line-clamp-2">{integration.description}</p>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-1 text-white/70 text-xs">
                        <Clock className="h-3 w-3" />
                        <span>{integration.pricing.unitName}</span>
                        <span className="mx-1">•</span>
                        <span>{integration.pricing.costPerUnit} credits</span>
                      </div>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLaunchTool(integration);
                        }}
                        disabled={isLaunching || healthStatus?.status === 'down'}
                        className="group-hover:bg-fiery group-hover:text-white transition-colors"
                      >
                        {isLaunching ? 'Launching...' : 'Launch'}
                        <ArrowUpRight className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredTools.length === 0 && (
            <div className="text-center py-12 firenest-card">
              <p className="text-white/70">No tools found matching your criteria</p>
              <Button variant="outline" className="mt-4" onClick={resetFilters}>Reset Filters</Button>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="w-full md:w-64 space-y-4">
          {/* Credits card */}
          <Card className="firenest-card-accent">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <Zap className="h-5 w-5 text-fiery" />
                Your Credits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-enhanced">{credits?.availableCredits || 0}</div>
              <p className="text-sm text-white/70 mt-1">available credits</p>

              {/* Usage summary */}
              {totalCreditsUsed > 0 && (
                <div className="mt-3 pt-3 border-t border-white/10">
                  <div className="flex justify-between text-sm text-white/70">
                    <span>Today's usage:</span>
                    <span className="font-medium text-white">{Math.ceil(totalCreditsUsed)} credits</span>
                  </div>
                </div>
              )}

              <Button className="w-full mt-4">Add Credits</Button>
            </CardContent>
          </Card>

          {/* Active Sessions */}
          {activeSessions.length > 0 && (
            <Card className="firenest-card-accent">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Clock className="h-5 w-5 text-fiery" />
                  Active Sessions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-2 px-4 pb-4">
                  {activeSessions.map((session) => {
                    const integration = allIntegrations.find(i => i.id === session.serviceId);
                    if (!integration) return null;

                    // Calculate time elapsed
                    const startTime = new Date(session.startTime);
                    const now = new Date();
                    const elapsedMs = now.getTime() - startTime.getTime();
                    const elapsedMinutes = Math.floor(elapsedMs / (1000 * 60));
                    const elapsedHours = Math.floor(elapsedMinutes / 60);
                    const remainingMinutes = elapsedMinutes % 60;

                    // Format elapsed time
                    let timeDisplay = '';
                    if (elapsedHours > 0) {
                      timeDisplay = `${elapsedHours}h ${remainingMinutes}m`;
                    } else {
                      timeDisplay = `${elapsedMinutes}m`;
                    }

                    // Get estimated credits used
                    const estimatedCredits = session.usageMetrics.estimatedCreditsUsed;

                    return (
                      <div key={session.serviceId} className="p-2 rounded-md firenest-card">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="text-xl">{integration.partnerId.charAt(0).toUpperCase()}</div>
                            <div>
                              <div className="font-medium text-white">{integration.name}</div>
                              <div className="text-xs text-white/70 flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{timeDisplay}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-white">{Math.ceil(estimatedCredits)} credits</div>
                            <div className="text-xs text-white/70">estimated</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Categories */}
          <Card className="firenest-card">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Categories</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1 px-4 pb-4">
                {categories.map((category) => (
                  <button
                    key={category}
                    className={`w-full text-left px-2 py-1.5 rounded-md transition-colors ${
                      selectedCategory === category
                        ? 'bg-fiery/20 text-fiery'
                        : 'text-white/70 hover:bg-white/5'
                    }`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Workbench;
