mport { createClient } from '@supabase/supabase-js';
import postgres from 'postgres';
import dotenv from 'dotenv';

dotenv.config();

async function testConnections() {
  console.log('Testing connections...');

  // Test Supabase REST API connection
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  // Test PostgreSQL connection with updated settings
  const sql = postgres({
    host: 'wjwguxccykrbarehqgpq.supabase.co',
    port: 6543, // Note: Changed from 5432 to 6543
    database: 'postgres',
    username: 'postgres',
    password: process.env.SUPABASE_DB_PASSWORD,
    ssl: {
      rejectUnauthorized: false,
      require: true
    },
    connection: {
      timeout: 30000 // 30 seconds
    },
    idle_timeout: 30,
    max_lifetime: 60 * 30
  });

  try {
    // Test Supabase REST API
    console.log('Testing Supabase REST API connection...');
    const { data, error } = await supabase.from('_schema').select('*').limit(1);
    if (error) throw error;
    console.log('✅ Supabase REST API connection successful!');

    // Test PostgreSQL connection
    console.log('Testing PostgreSQL connection...');
    const result = await sql`SELECT NOW()`;
    console.log('✅ PostgreSQL connection successful!');
  } catch (error) {
    console.error('❌ Connection test failed:', error);
  } finally {
    await sql.end();
  }
}

testConnections().catch(console.error);