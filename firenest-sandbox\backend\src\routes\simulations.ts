/**
 * Simulations Routes
 * Phase 3: Simulation Engine Backend
 * High-performance simulation processing and analytics
 * SOC 2 Alignment: CC6.2 (Access Control), CC7.1 (System Operations)
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import AWS from 'aws-sdk';
import Joi from 'joi';
import { queryWithUserContext, transactionWithUserContext } from '@/config/database';
import { logger } from '@/utils/logger';
import { validate, schemas } from '@/utils/validation';
import { asyncHandler, NotFoundError, ValidationError, ConflictError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';
import { config } from '@/config/environment';

const router = Router();

// Configure AWS SQS for simulation jobs
const sqs = new AWS.SQS({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region
});

// Get all simulations for a project
router.get('/project/:projectId',
  validate(Joi.object({ projectId: schemas.uuid }), 'params'),
  validate(schemas.pagination, 'query'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { projectId } = req.params;
    const { page, limit, sortBy, sortOrder } = req.query;
    const offset = (page - 1) * limit;

    // Verify project access
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT 1 FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT s.id, s.name, s.status, s.started_at, s.completed_at, s.created_at, s.updated_at,
              s.progress_percentage, s.error_message,
              (SELECT COUNT(*) FROM simulation_model_runs smr WHERE smr.simulation_id = s.id) as model_count,
              (SELECT COUNT(*) FROM simulation_results sr WHERE sr.simulation_id = s.id) as result_count
       FROM simulations s
       WHERE s.project_id = $1
       ORDER BY ${sortBy || 's.created_at'} ${sortOrder}
       LIMIT $2 OFFSET $3`,
      [projectId, limit, offset]
    );

    const countResult = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT COUNT(*) as total FROM simulations WHERE project_id = $1',
      [projectId]
    );

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  })
);

// Get simulation by ID with detailed results
router.get('/:simulationId',
  validate(Joi.object({ simulationId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { simulationId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT s.id, s.name, s.status, s.started_at, s.completed_at, s.created_at, s.updated_at,
              s.progress_percentage, s.error_message, s.project_id,
              sp.name as project_name, sp.status as project_status
       FROM simulations s
       JOIN sandbox_projects sp ON s.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE s.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [simulationId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Simulation not found or access denied');
    }

    // Get simulation model runs
    const modelRunsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT smr.id, smr.model_id, smr.status, smr.started_at, smr.completed_at,
              smr.records_processed, smr.total_records, smr.error_message,
              pm.name as model_name, pm.model_type
       FROM simulation_model_runs smr
       JOIN pricing_models pm ON smr.model_id = pm.id
       WHERE smr.simulation_id = $1
       ORDER BY smr.created_at ASC`,
      [simulationId]
    );

    // Get simulation results summary
    const resultsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sr.model_id, sr.total_revenue, sr.customer_count, sr.avg_revenue_per_customer,
              sr.min_customer_revenue, sr.max_customer_revenue, sr.created_at,
              pm.name as model_name, pm.model_type
       FROM simulation_results sr
       JOIN pricing_models pm ON sr.model_id = pm.id
       WHERE sr.simulation_id = $1
       ORDER BY sr.created_at ASC`,
      [simulationId]
    );

    const simulation = result.rows[0];
    simulation.model_runs = modelRunsResult.rows;
    simulation.results = resultsResult.rows;

    res.json({
      success: true,
      data: simulation
    });
  })
);

// Create new simulation
router.post('/',
  validate(schemas.createSimulation),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { name, modelIds, projectId, scenarioConfig } = req.body;

    // Verify project access and status
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.status, sp.name FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const project = projectCheck.rows[0];
    if (project.status !== 'READY' && project.status !== 'COMPLETE') {
      throw new ValidationError('Project must have validated data before running simulations');
    }

    // Verify all models exist and belong to the project
    const modelsCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT id, name FROM pricing_models 
       WHERE id = ANY($1) AND project_id = $2`,
      [modelIds, projectId]
    );

    if (modelsCheck.rows.length !== modelIds.length) {
      throw new ValidationError('One or more models not found or do not belong to this project');
    }

    // Check for existing running simulations
    const runningCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT COUNT(*) as count FROM simulations 
       WHERE project_id = $1 AND status IN ('QUEUED', 'RUNNING')`,
      [projectId]
    );

    if (parseInt(runningCheck.rows[0].count) > 0) {
      throw new ConflictError('Another simulation is already running for this project');
    }

    const result = await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        // Create simulation record
        const simulationResult = await client.query(
          `INSERT INTO simulations (id, name, project_id, status, progress_percentage)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING id, name, status, created_at`,
          [uuidv4(), name || `Simulation ${new Date().toISOString()}`, projectId, 'QUEUED', 0]
        );

        const simulation = simulationResult.rows[0];

        // Create model run records
        for (const modelId of modelIds) {
          await client.query(
            `INSERT INTO simulation_model_runs (id, simulation_id, model_id, status)
             VALUES ($1, $2, $3, $4)`,
            [uuidv4(), simulation.id, modelId, 'QUEUED']
          );
        }

        return simulation;
      }
    );

    // Queue simulation job
    const jobMessage = {
      jobType: 'RUN_SIMULATION',
      simulationId: result.id,
      projectId,
      modelIds,
      scenarioConfig: scenarioConfig || {},
      userId: req.user.id,
      timestamp: new Date().toISOString()
    };

    await sqs.sendMessage({
      QueueUrl: config.aws.sqs.queueUrl,
      MessageBody: JSON.stringify(jobMessage),
      MessageAttributes: {
        jobType: {
          DataType: 'String',
          StringValue: 'RUN_SIMULATION'
        },
        simulationId: {
          DataType: 'String',
          StringValue: result.id
        }
      }
    }).promise();

    logger.info('Simulation queued', {
      simulationId: result.id,
      userId: req.user.id,
      projectId,
      modelCount: modelIds.length
    });

    res.status(201).json({
      success: true,
      data: result
    });
  })
);

// Get simulation progress and status
router.get('/:simulationId/status',
  validate(Joi.object({ simulationId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { simulationId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT s.id, s.name, s.status, s.progress_percentage, s.started_at, s.completed_at,
              s.error_message, s.created_at, s.updated_at,
              sp.name as project_name
       FROM simulations s
       JOIN sandbox_projects sp ON s.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE s.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [simulationId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Simulation not found or access denied');
    }

    // Get model run progress
    const modelRunsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT smr.model_id, smr.status, smr.records_processed, smr.total_records,
              smr.started_at, smr.completed_at, smr.error_message,
              pm.name as model_name
       FROM simulation_model_runs smr
       JOIN pricing_models pm ON smr.model_id = pm.id
       WHERE smr.simulation_id = $1
       ORDER BY smr.created_at ASC`,
      [simulationId]
    );

    const simulation = result.rows[0];
    simulation.model_runs = modelRunsResult.rows;

    // Calculate next steps based on status
    let nextSteps: any[] = [];
    switch (simulation.status) {
      case 'QUEUED':
        nextSteps = ['Simulation queued for processing', 'Preparing data and models'];
        break;
      case 'RUNNING':
        nextSteps = ['Processing customer data through pricing models', 'Calculating revenue scenarios'];
        break;
      case 'COMPLETE':
        nextSteps = ['View detailed results and analytics', 'Compare with other simulations', 'Export reports'];
        break;
      case 'FAILED':
        nextSteps = ['Review error details', 'Check data and model configuration', 'Retry simulation'];
        break;
    }

    simulation.nextSteps = nextSteps;

    res.json({
      success: true,
      data: simulation
    });
  })
);

// Cancel running simulation
router.post('/:simulationId/cancel',
  validate(Joi.object({ simulationId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { simulationId } = req.params;

    // Verify simulation access and status
    const simulationCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT s.status FROM simulations s
       JOIN sandbox_projects sp ON s.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE s.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [simulationId, req.user.id]
    );

    if (simulationCheck.rows.length === 0) {
      throw new NotFoundError('Simulation not found or access denied');
    }

    const simulation = simulationCheck.rows[0];
    if (simulation.status !== 'QUEUED' && simulation.status !== 'RUNNING') {
      throw new ValidationError('Can only cancel queued or running simulations');
    }

    await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        // Update simulation status
        await client.query(
          `UPDATE simulations 
           SET status = 'CANCELLED', completed_at = NOW(), updated_at = NOW()
           WHERE id = $1`,
          [simulationId]
        );

        // Update model run statuses
        await client.query(
          `UPDATE simulation_model_runs 
           SET status = 'CANCELLED', completed_at = NOW()
           WHERE simulation_id = $1 AND status IN ('QUEUED', 'RUNNING')`,
          [simulationId]
        );
      }
    );

    logger.info('Simulation cancelled', {
      simulationId,
      userId: req.user.id
    });

    res.json({
      success: true,
      message: 'Simulation cancelled successfully'
    });
  })
);

// Delete simulation
router.delete('/:simulationId',
  validate(Joi.object({ simulationId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { simulationId } = req.params;

    // Check simulation access
    const simulationCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT s.name, s.status FROM simulations s
       JOIN sandbox_projects sp ON s.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE s.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [simulationId, req.user.id]
    );

    if (simulationCheck.rows.length === 0) {
      throw new NotFoundError('Simulation not found or access denied');
    }

    const simulation = simulationCheck.rows[0];
    if (simulation.status === 'RUNNING') {
      throw new ConflictError('Cannot delete running simulation. Cancel it first.');
    }

    await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        // Delete in correct order due to foreign key constraints
        await client.query('DELETE FROM simulation_results WHERE simulation_id = $1', [simulationId]);
        await client.query('DELETE FROM simulation_model_runs WHERE simulation_id = $1', [simulationId]);
        await client.query('DELETE FROM simulations WHERE id = $1', [simulationId]);
      }
    );

    logger.info('Simulation deleted', {
      simulationId,
      userId: req.user.id,
      simulationName: simulation.name
    });

    res.json({
      success: true,
      message: 'Simulation deleted successfully'
    });
  })
);

export default router;
