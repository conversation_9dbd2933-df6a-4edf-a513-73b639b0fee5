import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import {
  getToolsWithConnectionStatus,
  initializeConnection,
  deleteUserConnection
} from '@/lib/connections';
import { integrations } from '@/lib/integration-framework/integrations';
import { serviceConfigs } from '@/lib/auth-bridge/service-configs';
import { notify } from '@/components/ui/notification-system';
import { Loading } from '@/components/ui/loading';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ThemedBadge as Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  Key,
  Lock,
  Globe,
  ExternalLink,
  ArrowLeft
} from 'lucide-react';
import ConnectionCard from '@/components/connections/ConnectionCard';
import ApiKeyModal from '@/components/connections/ApiKeyModal';
import CredentialsModal from '@/components/connections/CredentialsModal';
import ConnectionDetailsModal from '@/components/connections/ConnectionDetailsModal';

// Define categories for tools
const CATEGORIES = [
  { id: 'all', name: 'All Tools' },
  { id: 'ai_writing', name: 'AI Writing' },
  { id: 'image_generation', name: 'Image Generation' },
  { id: 'code_assistance', name: 'Code Assistance' },
  { id: 'audio_video', name: 'Audio & Video' },
  { id: 'productivity', name: 'Productivity' }
];

const ConnectionManager = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [tools, setTools] = useState<any[]>([]);
  const [filteredTools, setFilteredTools] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTool, setSelectedTool] = useState<any | null>(null);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [showCredentialsModal, setShowCredentialsModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // Load tools with connection status
  useEffect(() => {
    const loadTools = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Check if all required tables exist in Supabase
        const requiredTables = [
          'user_connections',
          'connection_logs',
          'tool_launches',
          'usage_sessions',
          'usage_events'
        ];

        let missingTables = [];

        // Check each table
        for (const tableName of requiredTables) {
          const { data, error } = await supabase
            .from(tableName)
            .select('count')
            .limit(1)
            .single();

          if (error && error.code === '42P01') {
            console.error(`Table ${tableName} not found:`, error);
            missingTables.push(tableName);
          }
        }

        // If any tables are missing, show an error
        if (missingTables.length > 0) {
          console.error('Missing tables:', missingTables);
          notify.error(`Connection system tables not fully set up. Missing: ${missingTables.join(', ')}. Please run the SQL setup scripts.`);
          setIsLoading(false);
          return;
        }

        const toolsWithStatus = await getToolsWithConnectionStatus(user.id);
        setTools(toolsWithStatus);
        setFilteredTools(toolsWithStatus);
      } catch (error) {
        console.error('Error loading tools:', error);
        notify.error('Failed to load tools');
      } finally {
        setIsLoading(false);
      }
    };

    loadTools();
  }, [user]);

  // Filter tools based on search query and category
  useEffect(() => {
    if (!tools.length) return;

    let filtered = [...tools];

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query) ||
        (tool.tags && tool.tags.some((tag: string) => tag.toLowerCase().includes(query)))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    setFilteredTools(filtered);
  }, [searchQuery, selectedCategory, tools]);

  // Handle connect button click
  const handleConnect = async (tool: any) => {
    if (!user) {
      notify.error('You must be logged in to connect to tools');
      return;
    }

    setSelectedTool(tool);

    // Handle different auth methods
    switch (tool.authMethod) {
      case 'oauth':
        await handleOAuthConnect(tool);
        break;
      case 'api_key':
        setShowApiKeyModal(true);
        break;
      case 'credentials':
        setShowCredentialsModal(true);
        break;
      case 'ip_based':
        await handleIpBasedConnect(tool);
        break;
      default:
        notify.error(`Unsupported authentication method: ${tool.authMethod}`);
    }
  };

  // Handle OAuth connection
  const handleOAuthConnect = async (tool: any) => {
    if (!user) return;

    setIsConnecting(true);
    try {
      const result = await initializeConnection(user.id, tool.id);

      if (!result.success) {
        notify.error(result.error || 'Failed to initialize connection');
        return;
      }

      if (result.redirectUrl) {
        // Open OAuth authorization URL in a new window
        window.open(result.redirectUrl, '_blank');
      }
    } catch (error) {
      console.error('Error connecting to tool:', error);
      notify.error('Failed to connect to tool');
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle IP-based connection
  const handleIpBasedConnect = async (tool: any) => {
    if (!user) return;

    setIsConnecting(true);
    try {
      const result = await initializeConnection(user.id, tool.id);

      if (!result.success) {
        notify.error(result.error || 'Failed to initialize connection');
        return;
      }

      // Refresh the tool list
      const toolsWithStatus = await getToolsWithConnectionStatus(user.id);
      setTools(toolsWithStatus);

      notify.success(`Successfully connected to ${tool.name}`);
    } catch (error) {
      console.error('Error connecting to tool:', error);
      notify.error('Failed to connect to tool');
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle disconnect button click
  const handleDisconnect = async (tool: any) => {
    if (!user) return;

    if (confirm(`Are you sure you want to disconnect from ${tool.name}?`)) {
      try {
        const success = await deleteUserConnection(user.id, tool.id);

        if (success) {
          // Refresh the tool list
          const toolsWithStatus = await getToolsWithConnectionStatus(user.id);
          setTools(toolsWithStatus);

          notify.success(`Successfully disconnected from ${tool.name}`);
        }
      } catch (error) {
        console.error('Error disconnecting from tool:', error);
        notify.error('Failed to disconnect from tool');
      }
    }
  };

  // Handle view details button click
  const handleViewDetails = (tool: any) => {
    setSelectedTool(tool);
    setShowDetailsModal(true);
  };

  // Refresh the tool list
  const handleRefresh = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const toolsWithStatus = await getToolsWithConnectionStatus(user.id);
      setTools(toolsWithStatus);
      setFilteredTools(toolsWithStatus);
      notify.success('Tool connections refreshed');
    } catch (error) {
      console.error('Error refreshing tools:', error);
      notify.error('Failed to refresh tools');
    } finally {
      setIsLoading(false);
    }
  };

  // Get connection status icon
  const getStatusIcon = (tool: any) => {
    if (!tool.connection) {
      return <XCircle className="h-4 w-4 text-gray-400" />;
    }

    switch (tool.connection.status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <RefreshCw className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get auth method icon
  const getAuthMethodIcon = (authMethod: string) => {
    switch (authMethod) {
      case 'oauth':
        return <Zap className="h-4 w-4 text-blue-400" />;
      case 'api_key':
        return <Key className="h-4 w-4 text-yellow-400" />;
      case 'credentials':
        return <Lock className="h-4 w-4 text-purple-400" />;
      case 'ip_based':
        return <Globe className="h-4 w-4 text-green-400" />;
      default:
        return <Zap className="h-4 w-4 text-blue-400" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loading size="lg" text="Loading tools..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Connection Manager</h1>
          <p className="text-white/70">Manage your connections to AI tools and services</p>
        </div>
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
          className="border-white/10 hover:bg-white/5"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
          <Input
            placeholder="Search tools..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={handleRefresh}
          className="border-white/10 hover:bg-white/5"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {/* Categories */}
      <Tabs defaultValue="all" value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="firenest-card">
          {CATEGORIES.map((category) => (
            <TabsTrigger
              key={category.id}
              value={category.id}
              className="data-[state=active]:bg-fiery data-[state=active]:text-white"
            >
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      {/* Connection status summary */}
      <div className="firenest-card p-4 border border-white/10 rounded-lg">
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-green-500"></div>
            <span className="text-sm text-white/70">
              Connected: {tools.filter(t => t.isConnected).length}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
            <span className="text-sm text-white/70">
              Pending: {tools.filter(t => t.connection?.status === 'pending').length}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-red-500"></div>
            <span className="text-sm text-white/70">
              Failed: {tools.filter(t => t.connection?.status === 'failed').length}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-gray-500"></div>
            <span className="text-sm text-white/70">
              Not Connected: {tools.filter(t => !t.connection).length}
            </span>
          </div>
        </div>
      </div>

      {/* Tools grid */}
      {filteredTools.length === 0 ? (
        <div className="firenest-card p-8 border border-white/10 rounded-lg text-center">
          <p className="text-white/70">No tools found matching your criteria</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTools.map((tool) => (
            <ConnectionCard
              key={tool.id}
              tool={tool}
              statusIcon={getStatusIcon(tool)}
              authMethodIcon={getAuthMethodIcon(tool.authMethod)}
              onConnect={() => handleConnect(tool)}
              onDisconnect={() => handleDisconnect(tool)}
              onViewDetails={() => handleViewDetails(tool)}
              isConnecting={isConnecting && selectedTool?.id === tool.id}
            />
          ))}
        </div>
      )}

      {/* Modals */}
      {selectedTool && (
        <>
          <ApiKeyModal
            isOpen={showApiKeyModal}
            onClose={() => setShowApiKeyModal(false)}
            tool={selectedTool}
            onRefresh={handleRefresh}
          />

          <CredentialsModal
            isOpen={showCredentialsModal}
            onClose={() => setShowCredentialsModal(false)}
            tool={selectedTool}
            onRefresh={handleRefresh}
          />

          <ConnectionDetailsModal
            isOpen={showDetailsModal}
            onClose={() => setShowDetailsModal(false)}
            tool={selectedTool}
            onDisconnect={() => {
              handleDisconnect(selectedTool);
              setShowDetailsModal(false);
            }}
          />
        </>
      )}
    </div>
  );
};

export default ConnectionManager;
