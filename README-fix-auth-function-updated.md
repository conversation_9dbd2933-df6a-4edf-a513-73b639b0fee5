# Fix for User Data Fetching Issue

There's an issue with the `get_auth_user_by_id` function that's causing errors when fetching user data during the OAuth flow. The error message indicates a type mismatch:

```
Error fetching user with RPC: {
  code: '42804',
  details: 'Returned type character varying(255) does not match expected type text in column 2.',
  hint: null,
  message: 'structure of query does not match function result type'
}
```

This means the function is returning `text` for the email column, but the caller is expecting `character varying(255)`.

## Steps to Fix

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Create a new query
4. Paste the following SQL code:

```sql
-- Create the get_auth_user_by_id function with correct return types
CREATE OR REPLACE FUNCTION get_auth_user_by_id(user_id_param UUID)
RETURNS TABLE (
  id UUID,
  email character varying(255),  -- Changed from TEXT to match expected type
  raw_user_meta_data JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email::character varying(255),  -- Cast to match expected type
    au.raw_user_meta_data
  FROM 
    auth.users au
  WHERE 
    au.id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users and service_role
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO authenticated;
GRANT EXECUTE ON FUNCTION get_auth_user_by_id TO service_role;
```

5. Run the query
6. Restart your API server

This will create the necessary function with the correct return types to match what the caller is expecting.

## Alternative: Modify the Server Code

If you can't modify the database function, you can also modify the server code to handle the error gracefully. In the server.js file, find the section that calls the RPC function and modify it to handle the error better:

```javascript
// Try to get user from auth.users table using RPC
const { data, error } = await supabase.rpc('get_auth_user_by_id', {
  user_id_param: authCodeData.user_id
});

if (error) {
  console.error('Error fetching user with RPC:', error);
  
  // Try to get user directly from the users table instead
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, email, display_name')
    .eq('id', authCodeData.user_id)
    .maybeSingle();
    
  if (!userError && userData) {
    return {
      id: userData.id,
      email: userData.email,
      name: userData.display_name || 'User',
      display_name: userData.display_name || 'User'
    };
  }
  
  // Continue with fallback methods...
}
```

This approach provides a fallback mechanism if the RPC function fails.
