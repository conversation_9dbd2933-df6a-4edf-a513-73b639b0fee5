<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Firenest Test Partner</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .gradient-bg {
      background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }
    .firenest-button {
      background: linear-gradient(135deg, #ff4500 0%, #ff7e00 100%);
      transition: all 0.3s ease;
    }
    .firenest-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(255, 69, 0, 0.2), 0 4px 6px -2px rgba(255, 69, 0, 0.1);
    }
  </style>
</head>
<body class="gradient-bg min-h-screen text-white">
  <div class="container mx-auto px-4 py-12">
    <header class="mb-12 text-center">
      <h1 class="text-4xl font-bold mb-2">Test Partner Application</h1>
      <p class="text-xl text-gray-300">Firenest Integration Demo</p>
    </header>

    <main class="max-w-3xl mx-auto">
      <div class="bg-gray-800 rounded-lg shadow-xl overflow-hidden">
        <div class="p-8">
          <h2 class="text-2xl font-bold mb-6">Welcome to the Test Partner App</h2>
          
          <p class="mb-6 text-gray-300">
            This application demonstrates how to integrate with Firenest to provide premium features
            to your users. Sign in with Firenest to access premium features.
          </p>
          
          <div class="mb-8 bg-gray-700 rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Premium Features</h3>
            <ul class="list-disc list-inside text-gray-300 space-y-2">
              <li>Advanced data export</li>
              <li>AI-powered content generation</li>
              <li>Custom report builder</li>
              <li>Priority API access</li>
            </ul>
          </div>
          
          <div class="flex justify-center">
            <a href="/login/firenest" class="firenest-button text-white font-bold py-3 px-6 rounded-lg inline-flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd"></path>
              </svg>
              Sign in with Firenest
            </a>
          </div>
        </div>
        
        <div class="bg-gray-900 px-8 py-4 text-center text-sm text-gray-400">
          <p>This is a demo application for testing Firenest integration.</p>
        </div>
      </div>
    </main>
  </div>

  <script>
    // Check for error parameters in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    
    if (error) {
      let errorMessage = 'An error occurred during authentication.';
      
      if (error === 'auth_error') {
        errorMessage = 'Authentication error. Please try again.';
      } else if (error === 'state_mismatch') {
        errorMessage = 'Security validation failed. Please try again.';
      } else if (error === 'token_error') {
        errorMessage = 'Error obtaining access token. Please try again.';
      }
      
      // Create error alert
      const alertDiv = document.createElement('div');
      alertDiv.className = 'bg-red-600 text-white p-4 rounded-lg mb-6';
      alertDiv.textContent = errorMessage;
      
      // Insert before the premium features section
      const mainDiv = document.querySelector('main > div > div');
      mainDiv.insertBefore(alertDiv, mainDiv.firstChild);
    }
  </script>
</body>
</html>
