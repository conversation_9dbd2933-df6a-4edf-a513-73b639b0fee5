import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { Flame, Shield, AlertTriangle, CheckCircle, XCircle, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface PartnerInfo {
  id: string;
  name: string;
  company?: string;
  logoUrl?: string;
  website?: string;
  scopes?: string[];
}

interface AuthRequest {
  client_id: string;
  redirect_uri: string;
  state: string;
  scope: string;
  partner_id: string;
  partner_name: string;
}

export default function FirenestStyleAuthorize() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [partnerInfo, setPartnerInfo] = useState<PartnerInfo | null>(null);
  const [authRequest, setAuthRequest] = useState<AuthRequest | null>(null);
  const [scopes, setScopes] = useState<string[]>([]);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    const handleAuthorization = async () => {
      try {
        // Get the request parameters from the URL
        const { request } = router.query;

        if (!request) {
          setError('Missing request parameters');
          setLoading(false);
          return;
        }

        // Parse the request parameters
        const parsedRequest = JSON.parse(request as string);
        setAuthRequest(parsedRequest);

        // Parse scopes
        if (parsedRequest.scope) {
          setScopes(parsedRequest.scope.split(' '));
        }

        // Fetch partner information
        try {
          const { data: partner, error: partnerError } = await supabase
            .from('partners')
            .select('id, name, company, logo_url, website')
            .eq('id', parsedRequest.partner_id)
            .single();

          if (partnerError || !partner) {
            throw new Error('Partner not found');
          }

          setPartnerInfo({
            id: partner.id,
            name: partner.name,
            company: partner.company || '',
            logoUrl: partner.logo_url,
            website: partner.website
          });
        } catch (error) {
          console.error('Error fetching partner info:', error);
          // If we can't get partner info, at least use the name from the request
          setPartnerInfo({
            id: parsedRequest.partner_id,
            name: parsedRequest.partner_name
          });
        }

        // Check if the user is authenticated
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          // Redirect to login page with return URL
          const returnUrl = encodeURIComponent(window.location.href);
          router.push(`/login-next?returnUrl=${returnUrl}`);
          return;
        }

        setLoading(false);
      } catch (error) {
        console.error('Error handling authorization:', error);
        setError('Error handling authorization request');
        setLoading(false);
      }
    };

    if (router.isReady) {
      handleAuthorization();
    }
  }, [router.isReady, router.query]);

  const handleAuthorize = async () => {
    if (!authRequest || !user) return;

    setIsProcessing(true);

    try {
      // Generate an authorization code
      const code = uuidv4();

      // Store the authorization code in the database
      const { error: insertError } = await supabase
        .from('auth_codes')
        .insert({
          code,
          user_id: user.id,
          client_id: authRequest.client_id,
          partner_id: authRequest.partner_id,
          redirect_uri: authRequest.redirect_uri,
          scope: authRequest.scope || 'read write',
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes expiration
          used: false
        });

      if (insertError) {
        console.error('Error storing authorization code:', insertError);
        setError('Error storing authorization code');
        setIsProcessing(false);
        return;
      }

      // Redirect to the redirect URI with the authorization code
      const redirectUrl = new URL(authRequest.redirect_uri);
      redirectUrl.searchParams.append('code', code);
      redirectUrl.searchParams.append('state', authRequest.state || '');

      window.location.href = redirectUrl.toString();
    } catch (error) {
      console.error('Error authorizing application:', error);
      setError('Error authorizing application');
      setIsProcessing(false);
    }
  };

  const handleDeny = () => {
    if (!authRequest) return;

    // Construct the redirect URL with an error
    const redirectUrl = new URL(authRequest.redirect_uri);
    redirectUrl.searchParams.append('error', 'access_denied');
    redirectUrl.searchParams.append('state', authRequest.state);

    // Redirect the user back to the client application
    window.location.href = redirectUrl.toString();
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-fiery mx-auto"></div>
          <p className="mt-4 text-white/70">Loading authorization request...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <Card className="firenest-card max-w-md w-full border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-500" />
            </div>
            <CardTitle className="text-xl text-white">Authorization Error</CardTitle>
            <CardDescription className="text-white/70">
              {error}
            </CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button
              onClick={() => router.push('/')}
              className="bg-fiery hover:bg-fiery/90 text-white"
            >
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Not logged in state
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
        <Card className="firenest-card max-w-md w-full border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Lock className="w-6 h-6 text-blue-500" />
            </div>
            <CardTitle className="text-xl text-white">Authentication Required</CardTitle>
            <CardDescription className="text-white/70">
              You need to be logged in to authorize this application.
            </CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button
              onClick={() => {
                const returnUrl = encodeURIComponent(window.location.href);
                router.push(`/login-next?returnUrl=${returnUrl}`);
              }}
              className="bg-fiery hover:bg-fiery/90 text-white"
            >
              Sign In
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Main authorization page
  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-950 p-4">
      <Card className="firenest-card max-w-lg w-full border-0 shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 rounded-full bg-gradient-to-br from-fiery to-fiery/50 flex items-center justify-center">
            <Flame className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl text-white">Authorize Application</CardTitle>
          <CardDescription className="text-white/70">
            {partnerInfo?.name} {partnerInfo?.company && `by ${partnerInfo.company}`} wants to access your Firenest account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-dark-800/50 border border-white/10 rounded-lg p-4">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center mr-4 flex-shrink-0">
                <Shield className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <h3 className="text-white font-medium mb-1">This application will be able to:</h3>
                <ul className="text-white/70 text-sm space-y-2">
                  {scopes.length > 0 ? (
                    scopes.map((scope, index) => (
                      <li key={index} className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>{getScopeDescription(scope)}</span>
                      </li>
                    ))
                  ) : (
                    <>
                      <li className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>Access your basic profile information</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>Track usage of connected services</span>
                      </li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-yellow-500 font-medium mb-1">Important</h4>
                <p className="text-white/70 text-sm">
                  Only authorize applications that you trust. You can revoke access at any time from your account settings.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            className="border-white/10 hover:bg-white/5 text-white"
            onClick={handleDeny}
            disabled={isProcessing}
          >
            <XCircle className="w-4 h-4 mr-2" />
            Deny
          </Button>
          <Button
            className="bg-fiery hover:bg-fiery/90 text-white"
            onClick={handleAuthorize}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Authorizing...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Authorize
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

// Helper function to get a user-friendly description of a scope
function getScopeDescription(scope: string): string {
  const scopeDescriptions: Record<string, string> = {
    'read': 'Read your basic profile information',
    'write': 'Make changes to your account settings',
    'profile': 'Access your profile information',
    'email': 'View your email address',
    'openid': 'Verify your identity',
    'offline_access': 'Access your data when you're not using the application',
  };

  return scopeDescriptions[scope] || `Access to ${scope}`;
}
