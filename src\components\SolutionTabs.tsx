import React, { useState } from 'react';
import { Zap, IndianRupee, BarChart3 } from 'lucide-react';

type TabType = 'core' | 'startup' | 'enterprise';

interface SolutionTabsProps {
  onOpenDetailPage: (id: string) => (e: React.MouseEvent) => void;
}

const SolutionTabs: React.FC<SolutionTabsProps> = ({ onOpenDetailPage }) => {
  const [activeTab, setActiveTab] = useState<TabType>('core');

  return (
    <div>
      <div className="flex flex-wrap justify-center gap-4 mb-12 animate-fade-in-slow">
        <button 
          className={`px-6 py-3 rounded-full text-white font-medium shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-fiery/50 active:scale-95 hover:shadow-xl ${
            activeTab === 'core' 
              ? 'bg-gradient-to-r from-fiery/30 to-fiery/20 border border-fiery/40 shadow-fiery/20 hover:shadow-fiery/30' 
              : 'bg-white/10 hover:bg-white/15 border border-white/10 hover:border-fiery/30 shadow-md hover:shadow-lg'
          }`}
          onClick={() => setActiveTab('core')}
        >
          Core Features
        </button>
        <button 
          className={`px-6 py-3 rounded-full text-white font-medium shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-fiery/50 active:scale-95 hover:shadow-xl ${
            activeTab === 'startup' 
              ? 'bg-gradient-to-r from-fiery/30 to-fiery/20 border border-fiery/40 shadow-fiery/20 hover:shadow-fiery/30' 
              : 'bg-white/10 hover:bg-white/15 border border-white/10 hover:border-fiery/30 shadow-md hover:shadow-lg'
          }`}
          onClick={() => setActiveTab('startup')}
        >
          Startup Solutions
        </button>
        <button 
          className={`px-6 py-3 rounded-full text-white font-medium shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-fiery/50 active:scale-95 hover:shadow-xl ${
            activeTab === 'enterprise' 
              ? 'bg-gradient-to-r from-fiery/30 to-fiery/20 border border-fiery/40 shadow-fiery/20 hover:shadow-fiery/30' 
              : 'bg-white/10 hover:bg-white/15 border border-white/10 hover:border-fiery/30 shadow-md hover:shadow-lg'
          }`}
          onClick={() => setActiveTab('enterprise')}
        >
          Enterprise Options
        </button>
      </div>
      
      {/* Core Features Tab Content */}
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-8 transition-opacity duration-300 ${activeTab === 'core' ? 'opacity-100' : 'hidden'}`}>
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-fiery/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-fiery/20 to-fiery/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Zap className="w-6 h-6 text-fiery" />
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-fiery/80 transition-all duration-300">Pay-As-You-Go AI Tools</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">No subscriptions or commitments. Only pay for the AI tools you actually use with our flexible credit-based system.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('details')} className="text-fiery hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
        
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-cool-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-cool-500/20 to-cool-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <IndianRupee className="w-6 h-6 text-cool-500" />
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-cool-500/80 transition-all duration-300">Made for Indian Startups</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Built specifically for the needs and challenges of Indian entrepreneurs, with pricing adapted to the local ecosystem.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('details')} className="text-cool-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
        
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-amber-500/20 to-amber-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <BarChart3 className="w-6 h-6 text-amber-500" />
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-amber-500/80 transition-all duration-300">Affordable Scale</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">More affordable than subscriptions, our credit-based pricing scales perfectly with your growing business needs.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('details')} className="text-amber-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
      
      {/* Startup Solutions Tab Content */}
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-8 transition-opacity duration-300 ${activeTab === 'startup' ? 'opacity-100' : 'hidden'}`}>
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 text-3xl">
              🚀
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-purple-500/80 transition-all duration-300">For Startups</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Perfect for early-stage companies looking to maximize their runway while leveraging AI tools.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('for-startups-details')} className="text-purple-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
        
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-green-500/20 to-green-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 text-3xl">
              ⚡
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-green-500/80 transition-all duration-300">AI Tools</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Access to premium AI models without the premium subscription costs.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('ai-tools-details')} className="text-green-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
        
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-pink-500/20 to-pink-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 text-3xl">
              💡
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-pink-500/80 transition-all duration-300">Startup Resources</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Access exclusive resources, mentorship, and networking opportunities for Indian startups.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('startup-resources')} className="text-pink-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
      
      {/* Enterprise Tab Content */}
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-8 transition-opacity duration-300 ${activeTab === 'enterprise' ? 'opacity-100' : 'hidden'}`}>
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 text-3xl">
              🏢
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-blue-500/80 transition-all duration-300">Enterprise</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Custom solutions and dedicated support for larger organizations.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('enterprise-details')} className="text-blue-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
        
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-teal-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-teal-500/20 to-teal-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 text-3xl">
              🔒
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-teal-500/80 transition-all duration-300">Security & Compliance</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Enterprise-grade security with data residency, encryption, and compliance features.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('security-details')} className="text-teal-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
        
        <div className="glass-card-interactive group relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
          <div className="p-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-orange-500/20 to-orange-500/5 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 text-3xl">
              🤝
            </div>
            <h3 className="text-xl font-bold mb-4 text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-orange-500/80 transition-all duration-300">Custom Integration</h3>
            <p className="text-white/70 group-hover:text-white/90 transition-colors duration-300">Seamless integration with your existing systems through our enterprise API and dedicated support team.</p>
            <div className="mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <a href="#" onClick={onOpenDetailPage('integration-details')} className="text-orange-500 hover:text-white flex items-center gap-2">
                Learn more <span className="transform group-hover:translate-x-1 transition-transform duration-300">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SolutionTabs;
