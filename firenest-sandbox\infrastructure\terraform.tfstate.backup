{"version": 4, "terraform_version": "1.12.2", "serial": 40, "lineage": "913c2a2f-ee7c-7899-a8e8-42aab0116ebf", "outputs": {"cloudwatch_log_group_name": {"value": "/aws/ecs/firenest-sandbox", "type": "string"}, "database_name": {"value": "firenest_sandbox", "type": "string"}, "database_password": {"value": "h8H8BwyjLMo39QuJj3gD3bL5kSAH4bCG", "type": "string", "sensitive": true}, "database_username": {"value": "sandbox_admin", "type": "string", "sensitive": true}, "ecs_security_group_id": {"value": "sg-05e8b67f3d9bfb616", "type": "string"}, "private_subnet_ids": {"value": ["subnet-0c317cb8696028255", "subnet-0f799bbfab49c8d3f"], "type": ["tuple", ["string", "string"]]}, "public_subnet_ids": {"value": ["subnet-05e0fb095387debba", "subnet-0f3a2891cb6ec8536"], "type": ["tuple", ["string", "string"]]}, "rds_security_group_id": {"value": "sg-0ee9c4ba7c74212c8", "type": "string"}, "s3_bucket_arn": {"value": "arn:aws:s3:::firenest-sandbox-uploads-3bf1df2a", "type": "string"}, "s3_bucket_name": {"value": "firenest-sandbox-uploads-3bf1df2a", "type": "string"}, "sqs_dlq_url": {"value": "https://sqs.us-east-1.amazonaws.com/984158812739/firenest-sandbox-jobs-dlq", "type": "string"}, "sqs_queue_arn": {"value": "arn:aws:sqs:us-east-1:984158812739:firenest-sandbox-jobs", "type": "string"}, "sqs_queue_url": {"value": "https://sqs.us-east-1.amazonaws.com/984158812739/firenest-sandbox-jobs", "type": "string"}, "vpc_cidr_block": {"value": "10.0.0.0/16", "type": "string"}, "vpc_id": {"value": "vpc-00f47443fe5e79904", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az6", "use1-az1", "use1-az2", "use1-az4", "use1-az3", "use1-az5"]}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:984158812739:log-group:/aws/ecs/firenest-sandbox", "id": "/aws/ecs/firenest-sandbox", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/ecs/firenest-sandbox", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Name": "firenest-sandbox-logs"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-logs", "Project": "firenest-sandbox"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_db_instance", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"status": "tainted", "schema_version": 2, "attributes": {"address": null, "allocated_storage": 100, "allow_major_version_upgrade": null, "apply_immediately": false, "arn": null, "auto_minor_version_upgrade": true, "availability_zone": null, "backup_retention_period": 7, "backup_target": null, "backup_window": "03:00-04:00", "blue_green_update": [], "ca_cert_identifier": null, "character_set_name": null, "copy_tags_to_snapshot": false, "custom_iam_instance_profile": null, "customer_owned_ip_enabled": null, "database_insights_mode": null, "db_name": "firenest_sandbox", "db_subnet_group_name": "firenest-sandbox-db-subnet-group", "dedicated_log_volume": false, "delete_automated_backups": true, "deletion_protection": false, "domain": null, "domain_auth_secret_arn": null, "domain_dns_ips": null, "domain_fqdn": null, "domain_iam_role_name": null, "domain_ou": null, "enabled_cloudwatch_logs_exports": ["postgresql"], "endpoint": null, "engine": "postgres", "engine_lifecycle_support": null, "engine_version": "16", "engine_version_actual": null, "final_snapshot_identifier": null, "hosted_zone_id": null, "iam_database_authentication_enabled": null, "id": "db-OMYXBXIAXY5HLREIUXCJLS6ATQ", "identifier": "firenest-sandbox-postgres", "identifier_prefix": null, "instance_class": "db.t3.micro", "iops": null, "kms_key_id": null, "latest_restorable_time": null, "license_model": null, "listener_endpoint": null, "maintenance_window": "sun:04:00-sun:05:00", "manage_master_user_password": null, "master_user_secret": null, "master_user_secret_kms_key_id": null, "max_allocated_storage": 1000, "monitoring_interval": 0, "monitoring_role_arn": null, "multi_az": null, "nchar_character_set_name": null, "network_type": null, "option_group_name": null, "parameter_group_name": null, "password": "h8H8BwyjLMo39QuJj3gD3bL5kSAH4bCG", "password_wo": null, "password_wo_version": null, "performance_insights_enabled": false, "performance_insights_kms_key_id": null, "performance_insights_retention_period": null, "port": null, "publicly_accessible": false, "replica_mode": null, "replicas": null, "replicate_source_db": null, "resource_id": null, "restore_to_point_in_time": [], "s3_import": [], "skip_final_snapshot": true, "snapshot_identifier": null, "status": null, "storage_encrypted": true, "storage_throughput": null, "storage_type": "gp3", "tags": {"Name": "firenest-sandbox-postgres"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-postgres", "Project": "firenest-sandbox"}, "timeouts": null, "timezone": null, "upgrade_storage_config": null, "username": "sandbox_admin", "vpc_security_group_ids": ["sg-0ee9c4ba7c74212c8"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "password"}], [{"type": "get_attr", "value": "password_wo"}]], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6NDgwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMiJ9", "dependencies": ["aws_db_subnet_group.main", "aws_security_group.ecs_tasks", "aws_security_group.rds", "aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available", "random_password.db_password"]}]}, {"mode": "managed", "type": "aws_db_subnet_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:984158812739:subgrp:firenest-sandbox-db-subnet-group", "description": "Managed by Terraform", "id": "firenest-sandbox-db-subnet-group", "name": "firenest-sandbox-db-subnet-group", "name_prefix": "", "subnet_ids": ["subnet-0c317cb8696028255", "subnet-0f799bbfab49c8d3f"], "supported_network_types": ["IPV4"], "tags": {"Name": "firenest-sandbox-db-subnet-group"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-db-subnet-group", "Project": "firenest-sandbox"}, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-0c60e25919068cb9f", "arn": "arn:aws:ec2:us-east-1:984158812739:elastic-ip/eipalloc-0c60e25919068cb9f", "associate_with_private_ip": null, "association_id": "eipassoc-013b42a58e882d0a2", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-0c60e25919068cb9f", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-050be8929ef951ea4", "private_dns": "ip-10-0-0-233.ec2.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-44-215-152-96.compute-1.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Name": "firenest-sandbox-nat-eip-1"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-nat-eip-1", "Project": "firenest-sandbox"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-08978de7c71fb6a3d", "arn": "arn:aws:ec2:us-east-1:984158812739:elastic-ip/eipalloc-08978de7c71fb6a3d", "associate_with_private_ip": null, "association_id": "eipassoc-0173aa6a7e19b2246", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-08978de7c71fb6a3d", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-0830577c140e28165", "private_dns": "ip-10-0-1-94.ec2.internal", "private_ip": "*********", "ptr_record": "", "public_dns": "ec2-3-223-247-200.compute-1.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Name": "firenest-sandbox-nat-eip-2"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-nat-eip-2", "Project": "firenest-sandbox"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:internet-gateway/igw-0b3c09de448420acb", "id": "igw-0b3c09de448420acb", "owner_id": "984158812739", "tags": {"Name": "firenest-sandbox-igw"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-igw", "Project": "firenest-sandbox"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-0c60e25919068cb9f", "association_id": "eipassoc-013b42a58e882d0a2", "connectivity_type": "public", "id": "nat-0047ff875bbb00aa4", "network_interface_id": "eni-050be8929ef951ea4", "private_ip": "**********", "public_ip": "*************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-05e0fb095387debba", "tags": {"Name": "firenest-sandbox-nat-gateway-1"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-nat-gateway-1", "Project": "firenest-sandbox"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-08978de7c71fb6a3d", "association_id": "eipassoc-0173aa6a7e19b2246", "connectivity_type": "public", "id": "nat-0302b7a1f71d30a7c", "network_interface_id": "eni-0830577c140e28165", "private_ip": "*********", "public_ip": "*************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-0f3a2891cb6ec8536", "tags": {"Name": "firenest-sandbox-nat-gateway-2"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-nat-gateway-2", "Project": "firenest-sandbox"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:route-table/rtb-0482aff6f88816537", "id": "rtb-0482aff6f88816537", "owner_id": "984158812739", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0047ff875bbb00aa4", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "firenest-sandbox-private-rt-1"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-private-rt-1", "Project": "firenest-sandbox"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:route-table/rtb-00dbb6f263e81ceda", "id": "rtb-00dbb6f263e81ceda", "owner_id": "984158812739", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0302b7a1f71d30a7c", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "firenest-sandbox-private-rt-2"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-private-rt-2", "Project": "firenest-sandbox"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:route-table/rtb-06235e4eb7b3d7a4e", "id": "rtb-06235e4eb7b3d7a4e", "owner_id": "984158812739", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0b3c09de448420acb", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Name": "firenest-sandbox-public-rt"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-public-rt", "Project": "firenest-sandbox"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0ac82863e7ee7bfb3", "route_table_id": "rtb-0482aff6f88816537", "subnet_id": "subnet-0c317cb8696028255", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0feea4a8b59a29598", "route_table_id": "rtb-00dbb6f263e81ceda", "subnet_id": "subnet-0f799bbfab49c8d3f", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0f19cd9ad8ba6f398", "route_table_id": "rtb-06235e4eb7b3d7a4e", "subnet_id": "subnet-05e0fb095387debba", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0e99bb2608fdcd179", "route_table_id": "rtb-06235e4eb7b3d7a4e", "subnet_id": "subnet-0f3a2891cb6ec8536", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::firenest-sandbox-uploads-3bf1df2a", "bucket": "firenest-sandbox-uploads-3bf1df2a", "bucket_domain_name": "firenest-sandbox-uploads-3bf1df2a.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "firenest-sandbox-uploads-3bf1df2a.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "cd9a8d485ee710d5f2037fb14ee9d9e282e34e366c1a029ec1aafae26a68c6ba", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "firenest-sandbox-uploads-3bf1df2a", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": true}]}], "tags": {"Name": "firenest-sandbox-uploads"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-uploads", "Project": "firenest-sandbox"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "firenest-sandbox-uploads-3bf1df2a", "id": "firenest-sandbox-uploads-3bf1df2a", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.uploads", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "firenest-sandbox-uploads-3bf1df2a", "expected_bucket_owner": "", "id": "firenest-sandbox-uploads-3bf1df2a", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": true}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.uploads", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "firenest-sandbox-uploads-3bf1df2a", "expected_bucket_owner": "", "id": "firenest-sandbox-uploads-3bf1df2a", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.uploads", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "ecs_tasks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:security-group/sg-05e8b67f3d9bfb616", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-05e8b67f3d9bfb616", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}, {"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}], "name": "firenest-sandbox-ecs-tasks-20250623115656789200000001", "name_prefix": "firenest-sandbox-ecs-tasks-", "owner_id": "984158812739", "revoke_rules_on_delete": false, "tags": {"Name": "firenest-sandbox-ecs-tasks-sg"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-ecs-tasks-sg", "Project": "firenest-sandbox"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:security-group/sg-0ee9c4ba7c74212c8", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0ee9c4ba7c74212c8", "ingress": [{"cidr_blocks": [], "description": "", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-05e8b67f3d9bfb616"], "self": false, "to_port": 5432}], "name": "firenest-sandbox-rds-20250623115702260500000003", "name_prefix": "firenest-sandbox-rds-", "owner_id": "984158812739", "revoke_rules_on_delete": false, "tags": {"Name": "firenest-sandbox-rds-sg"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-rds-sg", "Project": "firenest-sandbox"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.ecs_tasks", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_sqs_queue", "name": "dlq", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:sqs:us-east-1:984158812739:firenest-sandbox-jobs-dlq", "content_based_deduplication": false, "deduplication_scope": "", "delay_seconds": 0, "fifo_queue": false, "fifo_throughput_limit": "", "id": "https://sqs.us-east-1.amazonaws.com/984158812739/firenest-sandbox-jobs-dlq", "kms_data_key_reuse_period_seconds": 300, "kms_master_key_id": "", "max_message_size": 262144, "message_retention_seconds": 1209600, "name": "firenest-sandbox-jobs-dlq", "name_prefix": "", "policy": "", "receive_wait_time_seconds": 0, "redrive_allow_policy": "", "redrive_policy": "", "sqs_managed_sse_enabled": true, "tags": {"Name": "firenest-sandbox-jobs-dlq"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-jobs-dlq", "Project": "firenest-sandbox"}, "timeouts": null, "url": "https://sqs.us-east-1.amazonaws.com/984158812739/firenest-sandbox-jobs-dlq", "visibility_timeout_seconds": 30}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "aws_sqs_queue", "name": "jobs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:sqs:us-east-1:984158812739:firenest-sandbox-jobs", "content_based_deduplication": false, "deduplication_scope": "", "delay_seconds": 0, "fifo_queue": false, "fifo_throughput_limit": "", "id": "https://sqs.us-east-1.amazonaws.com/984158812739/firenest-sandbox-jobs", "kms_data_key_reuse_period_seconds": 300, "kms_master_key_id": "", "max_message_size": 262144, "message_retention_seconds": 1209600, "name": "firenest-sandbox-jobs", "name_prefix": "", "policy": "", "receive_wait_time_seconds": 0, "redrive_allow_policy": "", "redrive_policy": "{\"deadLetterTargetArn\":\"arn:aws:sqs:us-east-1:984158812739:firenest-sandbox-jobs-dlq\",\"maxReceiveCount\":3}", "sqs_managed_sse_enabled": true, "tags": {"Name": "firenest-sandbox-jobs"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-jobs", "Project": "firenest-sandbox"}, "timeouts": null, "url": "https://sqs.us-east-1.amazonaws.com/984158812739/firenest-sandbox-jobs", "visibility_timeout_seconds": 30}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMH19", "dependencies": ["aws_sqs_queue.dlq"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:subnet/subnet-0c317cb8696028255", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az6", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0c317cb8696028255", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "984158812739", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "firenest-sandbox-private-subnet-1", "Type": "private"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-private-subnet-1", "Project": "firenest-sandbox", "Type": "private"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:subnet/subnet-0f799bbfab49c8d3f", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az1", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0f799bbfab49c8d3f", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "984158812739", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "firenest-sandbox-private-subnet-2", "Type": "private"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-private-subnet-2", "Project": "firenest-sandbox", "Type": "private"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:subnet/subnet-05e0fb095387debba", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az6", "cidr_block": "10.0.0.0/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-05e0fb095387debba", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "984158812739", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "firenest-sandbox-public-subnet-1", "Type": "public"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-public-subnet-1", "Project": "firenest-sandbox", "Type": "public"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:subnet/subnet-0f3a2891cb6ec8536", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az1", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0f3a2891cb6ec8536", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "984158812739", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Name": "firenest-sandbox-public-subnet-2", "Type": "public"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-public-subnet-2", "Project": "firenest-sandbox", "Type": "public"}, "timeouts": null, "vpc_id": "vpc-00f47443fe5e79904"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:984158812739:vpc/vpc-00f47443fe5e79904", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-00a8c1676fe5d90bc", "default_route_table_id": "rtb-076050f14a04291a8", "default_security_group_id": "sg-0f460eefd040e4964", "dhcp_options_id": "dopt-0cf9c25a113e5921c", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-00f47443fe5e79904", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-076050f14a04291a8", "owner_id": "984158812739", "tags": {"Name": "firenest-sandbox-vpc"}, "tags_all": {"Environment": "development", "ManagedBy": "terraform", "Name": "firenest-sandbox-vpc", "Project": "firenest-sandbox"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "random_id", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 0, "attributes": {"b64_std": "O/HfKg==", "b64_url": "O_HfKg", "byte_length": 4, "dec": "**********", "hex": "3bf1df2a", "id": "O_HfKg", "keepers": null, "prefix": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "random_password", "name": "db_password", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$/gtFUDq7Zd2WQE9W6gA02e/dbNMfoBfNihcWk767ZvU/iJrl0g3S.", "id": "none", "keepers": null, "length": 32, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "h8H8BwyjLMo39QuJj3gD3bL5kSAH4bCG", "special": false, "upper": true}, "sensitive_attributes": [[{"type": "get_attr", "value": "bcrypt_hash"}], [{"type": "get_attr", "value": "result"}]], "identity_schema_version": 0}]}], "check_results": [{"object_kind": "var", "config_addr": "var.environment", "status": "pass", "objects": [{"object_addr": "var.environment", "status": "pass"}]}]}