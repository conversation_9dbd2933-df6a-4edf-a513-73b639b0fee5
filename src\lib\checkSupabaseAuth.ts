import { supabase } from './supabase';

/**
 * This function checks if the Supabase authentication is properly set up
 * by attempting to create a test user and then deleting it.
 */
export async function checkSupabaseAuth() {
  console.log('Checking Supabase authentication setup...');
  
  try {
    // Step 1: Check if we can connect to Supabase
    console.log('Step 1: Checking Supabase connection...');
    const { data: connectionTest, error: connectionError } = await supabase.from('_test_connection').select('*').limit(1).catch(() => ({ data: null, error: { message: 'Connection failed' } }));
    
    if (connectionError) {
      console.log('Connection test failed, but this is expected if the _test_connection table doesn\'t exist');
      console.log('Connection error:', connectionError.message);
    } else {
      console.log('Connection successful');
    }
    
    // Step 2: Check if auth is enabled
    console.log('Step 2: Checking if auth is enabled...');
    const { data: authSettings, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error('Auth check failed:', authError.message);
      return { success: false, message: `Auth check failed: ${authError.message}` };
    }
    
    console.log('Auth is enabled');
    
    // Step 3: Check if the users table exists
    console.log('Step 3: Checking if users table exists...');
    const { data: usersTable, error: usersTableError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
      
    if (usersTableError) {
      console.error('Users table check failed:', usersTableError.message);
      return { 
        success: false, 
        message: `Users table check failed: ${usersTableError.message}. Make sure you've run the SQL script in the Supabase SQL Editor.` 
      };
    }
    
    console.log('Users table exists');
    
    // Step 4: Check if the user_profiles table exists
    console.log('Step 4: Checking if user_profiles table exists...');
    const { data: profilesTable, error: profilesTableError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);
      
    if (profilesTableError) {
      console.error('User profiles table check failed:', profilesTableError.message);
      return { 
        success: false, 
        message: `User profiles table check failed: ${profilesTableError.message}. Make sure you've run the SQL script in the Supabase SQL Editor.` 
      };
    }
    
    console.log('User profiles table exists');
    
    // Step 5: Check if the user_credits table exists
    console.log('Step 5: Checking if user_credits table exists...');
    const { data: creditsTable, error: creditsTableError } = await supabase
      .from('user_credits')
      .select('count')
      .limit(1);
      
    if (creditsTableError) {
      console.error('User credits table check failed:', creditsTableError.message);
      return { 
        success: false, 
        message: `User credits table check failed: ${creditsTableError.message}. Make sure you've run the SQL script in the Supabase SQL Editor.` 
      };
    }
    
    console.log('User credits table exists');
    
    // Step 6: Check if the credit_transactions table exists
    console.log('Step 6: Checking if credit_transactions table exists...');
    const { data: transactionsTable, error: transactionsTableError } = await supabase
      .from('credit_transactions')
      .select('count')
      .limit(1);
      
    if (transactionsTableError) {
      console.error('Credit transactions table check failed:', transactionsTableError.message);
      return { 
        success: false, 
        message: `Credit transactions table check failed: ${transactionsTableError.message}. Make sure you've run the SQL script in the Supabase SQL Editor.` 
      };
    }
    
    console.log('Credit transactions table exists');
    
    // All checks passed
    return { 
      success: true, 
      message: 'Supabase authentication is properly set up! You can now use the real authentication system.' 
    };
  } catch (error) {
    console.error('Unexpected error during Supabase auth check:', error);
    return { 
      success: false, 
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}
