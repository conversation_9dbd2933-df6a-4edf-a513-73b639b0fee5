import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Code, FileText, Zap, Key, BookOpen } from 'lucide-react';
import PartnerFooter from '@/components/partner/PartnerFooter';

const PartnerSDKDocumentation: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('nodejs');

  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Header */}
      <header className="bg-dark-900 border-b border-white/10 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/partner/documentation')}
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-white">Firenest SDK Documentation</h1>
                <p className="text-white/60">Comprehensive guide to integrating with Firenest using our SDKs</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          {/* Overview */}
          <Card className="firenest-card">
            <CardHeader>
              <CardTitle className="text-2xl text-white">SDK Overview</CardTitle>
              <CardDescription>
                Firenest provides SDKs for various programming languages to simplify integration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-white/80">
                The Firenest SDK provides a simple interface for integrating your application with Firenest, 
                handling authentication, authorization checks, and usage reporting. Our SDKs are designed 
                to make integration as seamless as possible, with clear documentation and examples.
              </p>
              <div className="flex flex-wrap gap-4 mt-4">
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    const element = document.getElementById('installation');
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Installation Guide
                </Button>
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    const element = document.getElementById('authentication');
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  <Key className="w-4 h-4 mr-2" />
                  Authentication
                </Button>
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    const element = document.getElementById('usage-reporting');
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Usage Reporting
                </Button>
                <Button
                  variant="outline"
                  className="border-white/10 hover:bg-white/5"
                  onClick={() => {
                    const element = document.getElementById('code-examples');
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  <Code className="w-4 h-4 mr-2" />
                  Code Examples
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Installation */}
          <Card className="firenest-card" id="installation">
            <CardHeader>
              <CardTitle className="text-2xl text-white">Installation</CardTitle>
              <CardDescription>
                Getting started with the Firenest SDK
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
                <TabsList className="firenest-card">
                  <TabsTrigger value="nodejs" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    Node.js
                  </TabsTrigger>
                  <TabsTrigger value="python" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    Python
                  </TabsTrigger>
                  <TabsTrigger value="ruby" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    Ruby
                  </TabsTrigger>
                  <TabsTrigger value="php" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    PHP
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="nodejs" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Installation</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>npm install @firenest/sdk-node</pre>
                    </div>
                  </div>
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Initialization</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`const { FirenestSDK } = require('@firenest/sdk-node');
// or using ES modules:
// import { FirenestSDK } from '@firenest/sdk-node';

const firenest = new FirenestSDK({
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  environment: 'sandbox' // or 'production'
});`}</pre>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="python" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Installation</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>pip install firenest-sdk</pre>
                    </div>
                  </div>
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Initialization</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`from firenest_sdk import FirenestSDK

firenest = FirenestSDK(
    client_id='your-client-id',
    client_secret='your-client-secret',
    environment='sandbox'  # or 'production'
)`}</pre>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="ruby" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Installation</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>gem install firenest-sdk</pre>
                    </div>
                  </div>
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Initialization</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`require 'firenest-sdk'

firenest = Firenest::SDK.new(
  client_id: 'your-client-id',
  client_secret: 'your-client-secret',
  environment: 'sandbox' # or 'production'
)`}</pre>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="php" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Installation</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>composer require firenest/sdk-php</pre>
                    </div>
                  </div>
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Initialization</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`<?php
require_once 'vendor/autoload.php';

$firenest = new Firenest\\SDK([
  'client_id' => 'your-client-id',
  'client_secret' => 'your-client-secret',
  'environment' => 'sandbox' // or 'production'
]);`}</pre>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Authentication */}
          <Card className="firenest-card" id="authentication">
            <CardHeader>
              <CardTitle className="text-2xl text-white">Authentication</CardTitle>
              <CardDescription>
                Implementing authentication with the Firenest SDK
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-white/80">
                The Firenest SDK provides methods to handle the OAuth 2.0/OpenID Connect authentication flow.
                This includes generating authorization URLs, handling callbacks, and validating tokens.
              </p>
              
              <div className="bg-dark-800 p-4 rounded-md">
                <h3 className="text-white font-medium mb-3">Generate Authorization URL</h3>
                <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                  <pre>{`// Generate a URL to redirect the user to for authentication
const authUrl = firenest.getAuthorizationUrl({
  redirectUri: 'https://your-app.com/auth/callback',
  state: 'random-state-value', // For security, generate and store this
  scope: 'openid profile email'
});

// Redirect the user to this URL
res.redirect(authUrl);`}</pre>
                </div>
              </div>
              
              <div className="bg-dark-800 p-4 rounded-md">
                <h3 className="text-white font-medium mb-3">Handle Callback</h3>
                <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                  <pre>{`// In your callback route handler
app.get('/auth/callback', async (req, res) => {
  try {
    // Exchange the authorization code for tokens
    const tokenResponse = await firenest.handleCallback({
      code: req.query.code,
      state: req.query.state, // Verify this matches what you sent
      redirectUri: 'https://your-app.com/auth/callback'
    });
    
    // Store the user information in your session
    req.session.firenestUser = {
      id: tokenResponse.userId,
      email: tokenResponse.email,
      accessToken: tokenResponse.accessToken
    };
    
    // Redirect to your application
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Authentication error:', error);
    res.redirect('/login?error=auth_failed');
  }
});`}</pre>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Reporting */}
          <Card className="firenest-card" id="usage-reporting">
            <CardHeader>
              <CardTitle className="text-2xl text-white">Usage Reporting</CardTitle>
              <CardDescription>
                Track feature usage and report it to Firenest
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-white/80">
                The Firenest SDK provides methods to report usage of your features to Firenest.
                This is essential for tracking credit consumption and providing accurate billing.
              </p>
              
              <div className="bg-dark-800 p-4 rounded-md">
                <h3 className="text-white font-medium mb-3">Basic Usage Reporting</h3>
                <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                  <pre>{`// Report usage of a feature
await firenest.reportUsage(
  firenestUserId, // The Firenest user ID
  'feature-id',   // The ID of the feature being used
  {
    // Optional metadata about the usage
    metadata: {
      action: 'generate-image',
      details: 'Generated a 1024x1024 image'
    }
  }
);`}</pre>
                </div>
              </div>
              
              <div className="bg-dark-800 p-4 rounded-md">
                <h3 className="text-white font-medium mb-3">Reporting with Custom Units</h3>
                <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                  <pre>{`// For features with dynamic costs
const imageSize = calculateImageSize(generatedImage);
const unitsConsumed = Math.ceil(imageSize / 1024); // 1 credit per KB

await firenest.reportUsage(
  firenestUserId,
  'image-generation',
  {
    unitsConsumed,
    metadata: {
      imageType: 'png',
      dimensions: '1024x1024',
      prompt: 'A beautiful sunset'
    }
  }
);`}</pre>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Code Examples */}
          <Card className="firenest-card" id="code-examples">
            <CardHeader>
              <CardTitle className="text-2xl text-white">Complete Code Examples</CardTitle>
              <CardDescription>
                Full implementation examples for different frameworks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Tabs defaultValue="express" className="space-y-4">
                <TabsList className="firenest-card">
                  <TabsTrigger value="express" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    Express.js
                  </TabsTrigger>
                  <TabsTrigger value="nextjs" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    Next.js
                  </TabsTrigger>
                  <TabsTrigger value="flask" className="data-[state=active]:bg-fiery data-[state=active]:text-white">
                    Flask
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="express" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Express.js Integration</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`const express = require('express');
const session = require('express-session');
const { FirenestSDK } = require('@firenest/sdk-node');

const app = express();
const port = 3000;

// Initialize session middleware
app.use(session({
  secret: 'your-session-secret',
  resave: false,
  saveUninitialized: false
}));

// Initialize Firenest SDK
const firenest = new FirenestSDK({
  clientId: process.env.FIRENEST_CLIENT_ID,
  clientSecret: process.env.FIRENEST_CLIENT_SECRET,
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox'
});

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.firenestUser) {
    return res.redirect('/login');
  }
  next();
};

// Login route
app.get('/login', (req, res) => {
  const authUrl = firenest.getAuthorizationUrl({
    redirectUri: \`http://localhost:\${port}/auth/callback\`,
    state: Math.random().toString(36).substring(2),
    scope: 'openid profile email'
  });
  res.redirect(authUrl);
});

// Callback route
app.get('/auth/callback', async (req, res) => {
  try {
    const tokenResponse = await firenest.handleCallback({
      code: req.query.code,
      state: req.query.state,
      redirectUri: \`http://localhost:\${port}/auth/callback\`
    });
    
    req.session.firenestUser = {
      id: tokenResponse.userId,
      email: tokenResponse.email,
      accessToken: tokenResponse.accessToken
    };
    
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Authentication error:', error);
    res.redirect('/login?error=auth_failed');
  }
});

// Protected route
app.get('/dashboard', requireAuth, (req, res) => {
  res.send(\`Welcome, \${req.session.firenestUser.email}!\`);
});

// Feature usage example
app.post('/api/generate-image', requireAuth, async (req, res) => {
  try {
    // Your feature implementation
    const image = await generateImage(req.body.prompt);
    
    // Report usage to Firenest
    await firenest.reportUsage(
      req.session.firenestUser.id,
      'image-generation',
      {
        unitsConsumed: 1,
        metadata: {
          prompt: req.body.prompt,
          dimensions: '1024x1024'
        }
      }
    );
    
    res.json({ success: true, image });
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ error: 'Failed to generate image' });
  }
});

app.listen(port, () => {
  console.log(\`Server running at http://localhost:\${port}\`);
});`}</pre>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="nextjs" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Next.js Integration</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`// pages/api/auth/login.js
import { FirenestSDK } from '@firenest/sdk-node';

const firenest = new FirenestSDK({
  clientId: process.env.FIRENEST_CLIENT_ID,
  clientSecret: process.env.FIRENEST_CLIENT_SECRET,
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox'
});

export default function handler(req, res) {
  const authUrl = firenest.getAuthorizationUrl({
    redirectUri: \`\${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/callback\`,
    state: Math.random().toString(36).substring(2),
    scope: 'openid profile email'
  });
  res.redirect(authUrl);
}`}</pre>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="flask" className="space-y-4">
                  <div className="bg-dark-800 p-4 rounded-md">
                    <h3 className="text-white font-medium mb-3">Flask Integration</h3>
                    <div className="font-mono text-sm text-white/80 bg-dark-900 p-4 rounded overflow-x-auto">
                      <pre>{`from flask import Flask, redirect, request, session
from firenest_sdk import FirenestSDK
import os
import secrets

app = Flask(__name__)
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'dev-secret-key')

# Initialize Firenest SDK
firenest = FirenestSDK(
    client_id=os.environ.get('FIRENEST_CLIENT_ID'),
    client_secret=os.environ.get('FIRENEST_CLIENT_SECRET'),
    environment='production' if os.environ.get('FLASK_ENV') == 'production' else 'sandbox'
)

@app.route('/login')
def login():
    state = secrets.token_urlsafe(16)
    session['oauth_state'] = state
    
    auth_url = firenest.get_authorization_url(
        redirect_uri=f"{request.host_url}auth/callback",
        state=state,
        scope='openid profile email'
    )
    return redirect(auth_url)

@app.route('/auth/callback')
def auth_callback():
    try:
        # Verify state
        if session.get('oauth_state') != request.args.get('state'):
            return redirect('/login?error=invalid_state')
        
        # Exchange code for tokens
        token_response = firenest.handle_callback(
            code=request.args.get('code'),
            state=request.args.get('state'),
            redirect_uri=f"{request.host_url}auth/callback"
        )
        
        # Store user info in session
        session['firenest_user'] = {
            'id': token_response.user_id,
            'email': token_response.email,
            'access_token': token_response.access_token
        }
        
        return redirect('/dashboard')
    except Exception as e:
        print(f"Authentication error: {e}")
        return redirect('/login?error=auth_failed')

@app.route('/dashboard')
def dashboard():
    if 'firenest_user' not in session:
        return redirect('/login')
    
    return f"Welcome, {session['firenest_user']['email']}!"

@app.route('/api/generate-image', methods=['POST'])
def generate_image():
    if 'firenest_user' not in session:
        return {'error': 'Unauthorized'}, 401
    
    try:
        # Your feature implementation
        # image = generate_image(request.json.get('prompt'))
        
        # Report usage to Firenest
        firenest.report_usage(
            session['firenest_user']['id'],
            'image-generation',
            units_consumed=1,
            metadata={
                'prompt': request.json.get('prompt'),
                'dimensions': '1024x1024'
            }
        )
        
        return {'success': True, 'image': 'image_url_here'}
    except Exception as e:
        print(f"Error: {e}")
        return {'error': 'Failed to generate image'}, 500

if __name__ == '__main__':
    app.run(debug=True)`}</pre>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <PartnerFooter />
    </div>
  );
};

export default PartnerSDKDocumentation;
