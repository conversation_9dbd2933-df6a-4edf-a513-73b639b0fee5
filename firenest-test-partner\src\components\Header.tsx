
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useFirenestAuth, initiateFirenestLogin } from '../lib/firenest-integration.ts';
import { Crown, Sparkles } from 'lucide-react';

const Header = () => {
  const { isAuthenticated, userId, token, logout } = useFirenestAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isFirenestUser, setIsFirenestUser] = useState(false);

  // Check if user is logged in via Firenest
  useEffect(() => {
    // If user is authenticated and has a token, they're logged in via Firenest
    if (isAuthenticated && userId && token) {
      setIsFirenestUser(true);
    } else {
      setIsFirenestUser(false);
    }
  }, [isAuthenticated, userId, token]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="bg-dark-800 border-b border-dark-600 sticky top-0 z-50">
      <div className="container flex justify-between items-center py-4">
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
            <span className="text-white font-bold text-xl">A</span>
          </div>
          <span className="text-2xl font-bold text-white">Atlas<span className="text-primary">AI</span></span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:block">
          <ul className="flex space-x-8">
            <li>
              <Link to="/" className="text-light hover:text-primary transition-colors py-2">
                Home
              </Link>
            </li>
            <li>
              <Link to="/pricing" className="text-light hover:text-primary transition-colors py-2">
                Pricing
              </Link>
            </li>
            <li>
              <Link to="/features" className="text-light hover:text-primary transition-colors py-2">
                Features
              </Link>
            </li>
            {isAuthenticated && (
              <li>
                <Link to="/dashboard" className="text-light hover:text-primary transition-colors py-2">
                  Dashboard
                </Link>
              </li>
            )}
          </ul>
        </nav>

        {/* Auth Buttons */}
        <div className="hidden md:flex items-center space-x-4">
          {isFirenestUser ? (
            <div className="flex items-center space-x-4">
              <a
                href="#pricing"
                className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300 border border-purple-400"
              >
                <Crown className="w-4 h-4" />
                <span>Premium Plus Plan</span>
                <Sparkles className="w-4 h-4 text-yellow-300" />
              </a>
              <button
                onClick={logout}
                className="btn btn-secondary"
              >
                Logout
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-4">
              {!isAuthenticated && (
                <button
                  onClick={() => initiateFirenestLogin()}
                  className="text-light-600 hover:text-white text-sm"
                >
                  Login
                </button>
              )}
              <a
                href="#pricing"
                onClick={(e) => {
                  e.preventDefault();
                  const pricingSection = document.getElementById('pricing');
                  if (pricingSection) {
                    pricingSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="btn btn-primary"
              >
                Get Started
              </a>
              {isAuthenticated && (
                <button
                  onClick={logout}
                  className="btn btn-secondary"
                >
                  Logout
                </button>
              )}
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white"
          onClick={toggleMobileMenu}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-6 h-6">
            {mobileMenuOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-dark-700 border-t border-dark-600">
          <nav className="container py-4">
            <ul className="space-y-4">
              <li>
                <Link
                  to="/"
                  className="block text-light hover:text-primary py-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/pricing"
                  className="block text-light hover:text-primary py-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Pricing
                </Link>
              </li>
              <li>
                <Link
                  to="/features"
                  className="block text-light hover:text-primary py-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Features
                </Link>
              </li>
              {isAuthenticated && (
                <li>
                  <Link
                    to="/dashboard"
                    className="block text-light hover:text-primary py-2"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Dashboard
                  </Link>
                </li>
              )}
              {isFirenestUser ? (
                <>
                  <li className="pt-4 border-t border-dark-500">
                    <a
                      href="#pricing"
                      onClick={(e) => {
                        e.preventDefault();
                        setMobileMenuOpen(false);
                      }}
                      className="w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300 border border-purple-400"
                    >
                      <Crown className="w-4 h-4" />
                      <span>Premium Plus Plan</span>
                      <Sparkles className="w-4 h-4 text-yellow-300" />
                    </a>
                  </li>
                  <li className="pt-4">
                    <button
                      onClick={() => {
                        logout();
                        setMobileMenuOpen(false);
                      }}
                      className="w-full btn btn-secondary"
                    >
                      Logout
                    </button>
                  </li>
                </>
              ) : (
                <>
                  <li className="pt-4 border-t border-dark-500">
                    <a
                      href="#pricing"
                      onClick={(e) => {
                        e.preventDefault();
                        setMobileMenuOpen(false);
                        const pricingSection = document.getElementById('pricing');
                        if (pricingSection) {
                          pricingSection.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                      className="w-full btn btn-primary block text-center"
                    >
                      Get Started
                    </a>
                  </li>
                  {isAuthenticated && (
                    <li className="pt-4">
                      <button
                        onClick={() => {
                          logout();
                          setMobileMenuOpen(false);
                        }}
                        className="w-full btn btn-secondary"
                      >
                        Logout
                      </button>
                    </li>
                  )}
                </>
              )}
            </ul>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;