import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import AuthLayout from '@/components/AuthLayout';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { notify } from '@/components/ui/notification-system';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

const VerifyEmailConfirm = () => {
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { refreshUserData } = useAuth();

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        console.log('Starting email verification process');

        // Get the URL parameters and hash fragment
        const hash = window.location.hash;
        const queryParams = new URLSearchParams(window.location.search);
        const accessToken = queryParams.get('access_token');
        const refreshToken = queryParams.get('refresh_token');
        const type = queryParams.get('type');

        console.log('URL parameters:', {
          hasHash: !!hash,
          accessToken: accessToken ? 'present' : 'not present',
          refreshToken: refreshToken ? 'present' : 'not present',
          type
        });

        // Check if we have auth info in either the hash or query params
        if (!hash && !accessToken) {
          console.error('No verification token found in URL');
          setError('No verification token found in URL. Please check your email link.');
          setIsVerifying(false);
          return;
        }

        // If we have query parameters with tokens, set the session directly
        if (accessToken && refreshToken) {
          console.log('Found access and refresh tokens in URL, setting session');
          const { error: setSessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (setSessionError) {
            console.error('Error setting session from tokens:', setSessionError);
            setError('Failed to verify your email. Invalid or expired tokens.');
            setIsVerifying(false);
            return;
          }
        }

        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Error getting session:', sessionError);
          setError('Failed to verify your email. Please try again.');
          setIsVerifying(false);
          return;
        }

        // If we have a session, the email is verified
        if (session?.user?.email_confirmed_at) {
          console.log('Email already verified:', session.user.email);
          setIsSuccess(true);
          setIsVerifying(false);

          // Refresh user data to ensure we have the latest info
          await refreshUserData();

          // Wait a moment before redirecting
          setTimeout(() => {
            navigate('/dashboard', {
              state: { message: 'Email verified successfully. Welcome to Firenest!' }
            });
          }, 2000);
          return;
        }

        // If we don't have a session yet, try to exchange the token
        console.log('No confirmed session found, trying to refresh session');
        const { error: authError } = await supabase.auth.refreshSession();

        if (authError) {
          console.error('Error confirming email:', authError);
          setError('Failed to verify your email. Please try again or contact support.');
          setIsVerifying(false);
          return;
        }

        // Check if verification was successful
        const { data: { session: updatedSession } } = await supabase.auth.getSession();

        if (updatedSession?.user?.email_confirmed_at) {
          console.log('Email verification successful:', updatedSession.user.email);
          setIsSuccess(true);

          // Refresh user data to ensure we have the latest info
          await refreshUserData();

          // Wait a moment before redirecting
          setTimeout(() => {
            navigate('/dashboard', {
              state: { message: 'Email verified successfully. Welcome to Firenest!' }
            });
          }, 2000);
        } else {
          console.error('Session exists but email is not confirmed');
          setError('Unable to verify your email. Please try again or contact support.');
        }
      } catch (err) {
        console.error('Unexpected error during email verification:', err);
        setError('An unexpected error occurred. Please try again.');
      } finally {
        setIsVerifying(false);
      }
    };

    handleEmailConfirmation();
  }, [navigate]);

  return (
    <AuthLayout
      title="Email Verification"
      subtitle="Confirming your email address"
    >
      <div className="space-y-6">
        {isVerifying ? (
          <div className="bg-blue-500/20 border border-blue-500/50 text-blue-200 px-4 py-6 rounded-md text-center">
            <Loader2 className="h-12 w-12 mx-auto mb-4 text-blue-400 animate-spin" />
            <h3 className="text-lg font-medium mb-2">Verifying Your Email</h3>
            <p>Please wait while we confirm your email address...</p>
          </div>
        ) : isSuccess ? (
          <div className="bg-green-500/20 border border-green-500/50 text-green-200 px-4 py-6 rounded-md text-center">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-400" />
            <h3 className="text-lg font-medium mb-2">Email Verified!</h3>
            <p>Your email has been successfully verified.</p>
            <p className="mt-2">Redirecting to login page...</p>
          </div>
        ) : (
          <div className="bg-red-500/20 border border-red-500/50 text-red-200 px-4 py-6 rounded-md text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-400" />
            <h3 className="text-lg font-medium mb-2">Verification Failed</h3>
            <p>{error || 'Unable to verify your email. Please try again.'}</p>
            <div className="mt-4">
              <Button
                type="button"
                onClick={() => navigate('/login')}
                className="w-full pop-button"
              >
                Return to Login
              </Button>
            </div>
          </div>
        )}
      </div>
    </AuthLayout>
  );
};

export default VerifyEmailConfirm;
