import { Flame } from 'lucide-react';
import { Link } from 'react-router-dom';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: string;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children, title, subtitle }) => {
  return (
    <div className="min-h-screen flex flex-col darker-bg text-white">
      {/* Top gradient overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-fiery/20 to-transparent pointer-events-none z-10" />
      
      {/* Geometric animated background */}
      <div className="geometric-background">
        <div className="geometric-shape geometric-shape-1"></div>
        <div className="geometric-shape geometric-shape-2"></div>
        <div className="geometric-shape geometric-shape-3"></div>
        <div className="geometric-shape geometric-shape-4"></div>
      </div>
      
      {/* Header */}
      <header className="py-6 px-6 md:px-12 w-full z-10">
        <div className="max-w-7xl mx-auto">
          <Link to="/" className="flex items-center gap-2">
            <div className="flex items-center justify-center bg-gradient-to-br from-fiery to-fiery-600 rounded-md w-8 h-8 animate-pulse-slow">
              <Flame className="text-white h-5 w-5" />
            </div>
            <h1 className="text-xl font-bold">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-fiery via-fiery to-cool-500 bg-size-200">
                <span className="text-fiery">Fir</span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-fiery to-cool">en</span>
                <span className="text-cool">est</span>
              </span>
            </h1>
          </Link>
        </div>
      </header>
      
      <main className="flex-grow flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <div className="glass-card p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-fiery/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl opacity-20 -z-10"></div>
            
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">{title}</h2>
              <p className="text-white/70">{subtitle}</p>
            </div>
            
            {children}
          </div>
        </div>
      </main>
      
      <footer className="py-6 px-6 text-center text-white/50 text-sm">
        <div className="max-w-7xl mx-auto">
          <p>© {new Date().getFullYear()} Firenest. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default AuthLayout;
