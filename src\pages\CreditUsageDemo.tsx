import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { notify } from '@/components/ui/notification-system';
import { useCredits as useCreditsHook } from '@/hooks/useCredits';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { RefreshCw, Zap, CreditCard, AlertTriangle, CheckCircle } from 'lucide-react';

// Mock AI services
const mockServices = [
  { id: 'chatgpt', name: 'ChatGPT', costPerUnit: 5, unitType: 'request' },
  { id: 'midjourney', name: 'Midjourney', costPerUnit: 10, unitType: 'image' },
  { id: 'claude', name: '<PERSON>', costPerUnit: 7, unitType: 'request' },
  { id: 'dalle', name: 'DALL-E', costPerUnit: 15, unitType: 'image' },
  { id: 'stable-diffusion', name: 'Stable Diffusion', costPerUnit: 8, unitType: 'image' },
];

const CreditUsageDemo = () => {
  const { user, credits, refreshUserData } = useAuth();
  const navigate = useNavigate();
  const { useCredits, addBonusCredits } = useCreditsHook();

  const [selectedService, setSelectedService] = useState(mockServices[0]);
  const [units, setUnits] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAddingBonus, setIsAddingBonus] = useState(false);
  const [bonusAmount, setBonusAmount] = useState(100);
  const [usageHistory, setUsageHistory] = useState<Array<{
    service: string;
    units: number;
    credits: number;
    timestamp: Date;
  }>>([]);

  // Calculate cost based on selected service and units
  const calculateCost = () => {
    return selectedService.costPerUnit * units;
  };

  // Handle service usage
  const handleUseService = async () => {
    if (!user) {
      notify.error('You must be logged in to use this service');
      return;
    }

    const cost = calculateCost();

    if (!credits || credits.availableCredits < cost) {
      notify.error(`Not enough credits. You need ${cost} credits but only have ${credits?.availableCredits || 0} available.`);
      return;
    }

    setIsProcessing(true);

    try {
      // Use the credits
      const result = await useCredits({
        userId: user.id,
        amount: cost,
        serviceId: selectedService.id,
        description: `Used ${units} ${selectedService.unitType}(s) with ${selectedService.name}`
      });

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to use credits');
      }

      // Add to usage history
      setUsageHistory(prev => [
        {
          service: selectedService.name,
          units,
          credits: cost,
          timestamp: new Date()
        },
        ...prev
      ]);

      // Show success message
      notify.success(`Successfully used ${cost} credits for ${units} ${selectedService.unitType}(s) with ${selectedService.name}`);

      // Refresh user data
      await refreshUserData();
    } catch (error) {
      console.error('Error using service:', error);
      notify.error(error instanceof Error ? error.message : 'Failed to use service. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle adding bonus credits (for demo purposes)
  const handleAddBonusCredits = async () => {
    if (!user) {
      notify.error('You must be logged in to add bonus credits');
      return;
    }

    if (bonusAmount <= 0) {
      notify.warning('Please enter a valid credit amount');
      return;
    }

    setIsAddingBonus(true);

    try {
      const result = await addBonusCredits(user.id, bonusAmount, 'Demo bonus credits');

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to add bonus credits');
      }

      notify.success(`Successfully added ${bonusAmount} bonus credits!`);
      await refreshUserData();
    } catch (error) {
      console.error('Error adding bonus credits:', error);
      notify.error(error instanceof Error ? error.message : 'Failed to add bonus credits. Please try again.');
    } finally {
      setIsAddingBonus(false);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">Credit Usage Demo</h1>
        <div className="flex items-center gap-4">
          <div className="firenest-card px-4 py-2 flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-fiery" />
            <span className="text-white font-medium">
              {credits ? credits.availableCredits : 0} credits available
            </span>
          </div>
          <Button
            variant="outline"
            className="border-white/10 hover:bg-white/5"
            onClick={refreshUserData}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            className="bg-fiery hover:bg-fiery-600 text-white"
            onClick={() => navigate('/dashboard/credits')}
          >
            <CreditCard className="h-4 w-4 mr-2" />
            Buy Credits
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Service Usage Panel */}
        <Card className="firenest-card md:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg text-white">Use AI Service</CardTitle>
            <CardDescription>Simulate using credits with AI services</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Service Selection */}
            <div className="space-y-2">
              <Label htmlFor="service" className="text-white">Select Service</Label>
              <Select
                value={selectedService.id}
                onValueChange={(value) => {
                  const service = mockServices.find(s => s.id === value);
                  if (service) setSelectedService(service);
                }}
              >
                <SelectTrigger className="firenest-card">
                  <SelectValue placeholder="Select a service" />
                </SelectTrigger>
                <SelectContent className="firenest-card border">
                  {mockServices.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name} ({service.costPerUnit} credits per {service.unitType})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Units Selection */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="units" className="text-white">
                  Number of {selectedService.unitType}s
                </Label>
                <span className="text-white/70">{units}</span>
              </div>
              <Slider
                id="units"
                min={1}
                max={10}
                step={1}
                value={[units]}
                onValueChange={(value) => setUnits(value[0])}
                className="py-4"
              />
            </div>

            {/* Cost Display */}
            <div className="firenest-card p-4 flex justify-between items-center">
              <div>
                <p className="text-white/70">Cost</p>
                <p className="text-xl font-bold text-white">{calculateCost()} credits</p>
              </div>
              <div>
                <p className="text-white/70">Service</p>
                <p className="text-white">{selectedService.name}</p>
              </div>
              <div>
                <p className="text-white/70">Units</p>
                <p className="text-white">{units} {selectedService.unitType}(s)</p>
              </div>
            </div>

            {/* Credit Check Warning */}
            {credits && credits.availableCredits < calculateCost() && (
              <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-red-400 font-medium">Not enough credits</p>
                  <p className="text-white/70 text-sm">
                    You need {calculateCost()} credits but only have {credits.availableCredits} available.
                    Please purchase more credits to continue.
                  </p>
                </div>
              </div>
            )}

            {/* Use Service Button */}
            <Button
              className="bg-fiery hover:bg-fiery-600 text-white w-full"
              onClick={handleUseService}
              disabled={isProcessing || (credits ? credits.availableCredits < calculateCost() : true)}
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Use {selectedService.name} ({calculateCost()} credits)
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Bonus Credits Panel (for demo purposes) */}
        <Card className="firenest-card">
          <CardHeader>
            <CardTitle className="text-lg text-white">Demo Tools</CardTitle>
            <CardDescription>Add bonus credits for testing</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bonus" className="text-white">Bonus Credits Amount</Label>
              <Input
                id="bonus"
                type="number"
                min="1"
                value={bonusAmount}
                onChange={(e) => setBonusAmount(parseInt(e.target.value) || 0)}
                className="firenest-card"
              />
            </div>
            <Button
              className="bg-green-600 hover:bg-green-700 text-white w-full"
              onClick={handleAddBonusCredits}
              disabled={isAddingBonus || bonusAmount <= 0}
            >
              {isAddingBonus ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Add Bonus Credits
                </>
              )}
            </Button>
            <div className="text-white/50 text-xs text-center mt-2">
              For demonstration purposes only
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage History */}
      <Card className="firenest-card">
        <CardHeader>
          <CardTitle className="text-lg text-white">Usage History</CardTitle>
          <CardDescription>Recent service usage in this session</CardDescription>
        </CardHeader>
        <CardContent>
          {usageHistory.length === 0 ? (
            <div className="text-center py-8 text-white/50">
              No usage history yet. Try using a service above.
            </div>
          ) : (
            <div className="space-y-4">
              {usageHistory.map((usage, index) => (
                <div key={index} className="firenest-card p-4 flex justify-between items-center">
                  <div>
                    <p className="text-white font-medium">{usage.service}</p>
                    <p className="text-white/70 text-sm">{formatDate(usage.timestamp)}</p>
                  </div>
                  <div>
                    <p className="text-white">{usage.units} {usage.units === 1 ? 'unit' : 'units'}</p>
                  </div>
                  <div>
                    <p className="text-fiery font-medium">-{usage.credits} credits</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CreditUsageDemo;
