import React, { useEffect, useState, useRef } from 'react';
import { useVisibilityEffect, usePageVisibility } from '@/hooks/usePageVisibility';
import { checkForUpdates, isServiceWorkerActive } from '@/utils/serviceWorkerRegistration';

interface PageVisibilityManagerProps {
  children: React.ReactNode;
}

/**
 * Component that manages page visibility and prevents unnecessary reloads
 * when switching between tabs
 */
const PageVisibilityManager: React.FC<PageVisibilityManagerProps> = ({ children }) => {
  const lastVisibleTimeRef = useRef<number>(Date.now());
  const isVisible = usePageVisibility();
  const [pageHidden, setPageHidden] = useState<boolean>(false);

  // Handle visibility changes
  useVisibilityEffect(
    // When page becomes visible
    () => {
      const now = Date.now();
      const timeSinceLastVisible = now - lastVisibleTimeRef.current;

      // If the page has been hidden for more than 5 minutes, check for service worker updates
      if (timeSinceLastVisible > 5 * 60 * 1000 && isServiceWorkerActive()) {
        checkForUpdates();
      }

      // Update the last visible time
      lastVisibleTimeRef.current = now;

      // Reset page hidden state
      setPageHidden(false);

      // Log for debugging
      console.log('Page is now visible, time since last visible:', Math.round(timeSinceLastVisible / 1000), 'seconds');
    },
    // When page becomes hidden
    () => {
      // Set page hidden state
      setPageHidden(true);

      // Save current state to sessionStorage
      try {
        sessionStorage.setItem('firenest_last_state', JSON.stringify({
          timestamp: Date.now(),
          path: window.location.pathname,
          hidden: true
        }));
      } catch (error) {
        console.error('Failed to save state to sessionStorage:', error);
      }

      // Log for debugging
      console.log('Page is now hidden');
    }
  );

  // Set up page unload handling
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Only prevent unload if it's not a deliberate navigation
      if (!event.currentTarget.performance.navigation.type) {
        // Save any important state to sessionStorage here
        sessionStorage.setItem('firenest_last_state', JSON.stringify({
          timestamp: Date.now(),
          path: window.location.pathname,
          hidden: false,
          unloaded: true
        }));
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Handle page focus/blur events
  useEffect(() => {
    const handleFocus = () => {
      console.log('Window focused');
      // Additional focus handling if needed
    };

    const handleBlur = () => {
      console.log('Window blurred');
      // Additional blur handling if needed
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, []);

  // Restore state from sessionStorage on mount
  useEffect(() => {
    const savedState = sessionStorage.getItem('firenest_last_state');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        console.log('Restored state from previous session', parsedState);

        // Check if the page was hidden or unloaded
        if (parsedState.hidden || parsedState.unloaded) {
          // Calculate time since last state save
          const timeSinceLastState = Date.now() - parsedState.timestamp;
          console.log('Time since last state:', Math.round(timeSinceLastState / 1000), 'seconds');

          // If it's been less than 5 minutes, we can assume it's a tab switch
          if (timeSinceLastState < 5 * 60 * 1000) {
            console.log('Recent tab switch detected, preserving state');
            // You can restore specific state here if needed
          }
        }
      } catch (error) {
        console.error('Failed to parse saved state', error);
      }
    }
  }, []);

  return <>{children}</>;
};

export default PageVisibilityManager;
