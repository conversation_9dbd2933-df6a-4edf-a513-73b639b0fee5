/**
 * Upload History Component
 * Displays all uploads for a project with filtering and actions
 */

import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  FileText, 
  Download, 
  Trash2, 
  Eye, 
  RefreshCw,
  Filter,
  Search
} from 'lucide-react'
import { uploadsApi } from '@/lib/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatFileSize, formatDate, getStatusColor } from '@/lib/utils'

interface UploadHistoryProps {
  projectId: string
}

export function UploadHistory({ projectId }: UploadHistoryProps) {
  const [page, setPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['uploads', projectId, page, searchTerm, statusFilter],
    queryFn: () => uploadsApi.listByProject(projectId, {
      page,
      limit: 10,
      sortBy: 'created_at',
      sortOrder: 'desc'
    }),
    enabled: !!projectId
  })

  if (isLoading) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <span className="ml-3 text-white">Loading upload history...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="firenest-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="w-5 h-5 text-red-400 mr-3" />
            <span className="text-white">Failed to load upload history</span>
          </div>
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const uploads = data?.data?.data || []
  const pagination = data?.data?.pagination

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="firenest-card">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-white">Upload History</h3>
            <p className="text-sm text-gray-400">
              {pagination?.total || 0} total uploads
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 w-full sm:w-64"
              />
            </div>
            
            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-input w-full sm:w-auto"
            >
              <option value="">All Status</option>
              <option value="UPLOADED">Uploaded</option>
              <option value="VALIDATING">Validating</option>
              <option value="VALIDATED">Validated</option>
              <option value="INVALID">Invalid</option>
            </select>
            
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Upload List */}
      {uploads.length === 0 ? (
        <div className="firenest-card">
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No uploads yet</h3>
            <p className="text-gray-400 mb-6">
              Upload your first data file to get started with pricing simulations.
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {uploads.map((upload: any) => (
            <UploadHistoryItem
              key={upload.id}
              upload={upload}
              onRefresh={() => refetch()}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="firenest-card">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} uploads
            </div>
            
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface UploadHistoryItemProps {
  upload: any
  onRefresh: () => void
}

function UploadHistoryItem({ upload, onRefresh }: UploadHistoryItemProps) {
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'validated':
        return <div className="w-3 h-3 bg-green-400 rounded-full" />
      case 'invalid':
        return <div className="w-3 h-3 bg-red-400 rounded-full" />
      case 'validating':
        return <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
      case 'uploaded':
        return <div className="w-3 h-3 bg-blue-400 rounded-full" />
      default:
        return <div className="w-3 h-3 bg-gray-400 rounded-full" />
    }
  }

  const getFileTypeLabel = (fileType: string) => {
    const labels: Record<string, string> = {
      customer_usage_data: 'Usage Data',
      billing_data: 'Billing Data',
      customer_metadata: 'Customer Metadata'
    }
    return labels[fileType] || fileType
  }

  return (
    <div className="firenest-card hover:border-white/20 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1 min-w-0">
          <FileText className="w-8 h-8 text-fiery flex-shrink-0" />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-1">
              <h4 className="text-white font-medium truncate">{upload.original_filename}</h4>
              <Badge variant="secondary" className="text-xs">
                {getFileTypeLabel(upload.file_type)}
              </Badge>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span>{formatFileSize(upload.file_size)}</span>
              <span>•</span>
              <span>{formatDate(upload.created_at)}</span>
              <span>•</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(upload.status)}
                <span className="capitalize">{upload.status.toLowerCase()}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          <Button variant="ghost" size="sm">
            <Eye className="w-4 h-4" />
          </Button>
          
          {upload.status === 'VALIDATED' && (
            <Button variant="ghost" size="sm">
              <Download className="w-4 h-4" />
            </Button>
          )}
          
          <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      {/* Validation Errors Preview */}
      {upload.validation_errors && upload.validation_errors.length > 0 && (
        <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-red-400 rounded-full" />
            <span className="text-sm font-medium text-red-400">
              {upload.validation_errors.length} validation {upload.validation_errors.length === 1 ? 'issue' : 'issues'}
            </span>
          </div>
          <p className="text-sm text-gray-300">
            {upload.validation_errors[0]?.message || 'Data validation failed'}
          </p>
        </div>
      )}
    </div>
  )
}
