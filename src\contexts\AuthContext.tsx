import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { usePageVisibility } from '@/hooks/usePageVisibility';
import { notify } from '@/components/ui/notification-system';
import {
  getSession,
  signIn,
  signOut,
  signUp,
  signInWithMagicLink,
  getUserProfile,
  getUserCredits,
  getCreditTransactions,
  updateUserProfile,
  checkEmailVerification,
  AuthUser,
  UserProfile,
  UserCredits,
  CreditTransaction,
  AuthSession
} from '@/lib/auth';
import { supabase } from '@/lib/supabase';

interface AuthContextType {
  user: AuthUser | null;
  profile: UserProfile | null;
  credits: UserCredits | null;
  transactions: CreditTransaction[];
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: any }>;
  signInWithMagicLink: (email: string) => Promise<{ success: boolean; error?: any; message?: string }>;
  register: (email: string, password: string, name?: string) => Promise<{ success: boolean }>;
  logout: () => Promise<{ success: boolean }>;
  updateProfile: (profileData: Partial<UserProfile>) => Promise<{ success: boolean }>;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  credits: null,
  transactions: [],
  isLoading: true,
  login: async () => ({ success: false }),
  signInWithMagicLink: async () => ({ success: false }),
  register: async () => ({ success: false }),
  logout: async () => ({ success: false }),
  updateProfile: async () => ({ success: false }),
  refreshUserData: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Explicitly initialize all state as null/empty to prevent flash of incorrect state
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Track page visibility to prevent unnecessary auth checks when switching tabs
  const isVisible = usePageVisibility();
  const lastVisibleTime = useRef<number>(Date.now());
  const sessionCheckedOnce = useRef<boolean>(false);

  // Force a re-check of the session if we detect issues
  const [sessionCheckCount, setSessionCheckCount] = useState(0);

  // Debug the current auth state
  useEffect(() => {
    console.log('AuthContext state:', {
      isLoading,
      user: user ? `${user.email} (${user.id})` : 'null',
      hasProfile: !!profile,
      hasCredits: !!credits,
      transactionCount: transactions.length
    });
  }, [isLoading, user, profile, credits, transactions]);

  // Function to fetch user profile, credits, and transactions
  const fetchUserData = async (userId: string) => {
    try {
      console.log('Fetching user data for userId:', userId);

      // Create an array of promises to fetch data in parallel
      const [profileResult, creditsResult, transactionsResult] = await Promise.allSettled([
        getUserProfile(userId).catch(err => {
          console.error('Error fetching profile:', err);
          return {
            success: false,
            error: err,
            data: {
              id: userId,
              userId: userId,
              createdAt: new Date().toISOString()
            }
          };
        }),
        getUserCredits(userId).catch(err => {
          console.error('Error fetching credits:', err);
          return {
            success: false,
            error: err,
            data: {
              totalCredits: 500,
              usedCredits: 0,
              availableCredits: 500
            }
          };
        }),
        getCreditTransactions(userId, 10).catch(err => {
          console.error('Error fetching transactions:', err);
          return { success: false, error: err, data: [] };
        })
      ]);

      // Handle profile result
      if (profileResult.status === 'fulfilled') {
        console.log('Profile result:', profileResult.value);
        if (profileResult.value.success && profileResult.value.data) {
          setProfile(profileResult.value.data);
        } else {
          // If we can't get the profile, create a default one
          setProfile({
            id: userId,
            userId: userId,
            createdAt: new Date().toISOString()
          });
        }
      } else {
        console.error('Profile promise rejected:', profileResult.reason);
        setProfile({
          id: userId,
          userId: userId,
          createdAt: new Date().toISOString()
        });
      }

      // Handle credits result
      if (creditsResult.status === 'fulfilled') {
        console.log('Credits result:', creditsResult.value);
        if (creditsResult.value.success && creditsResult.value.data) {
          setCredits(creditsResult.value.data);
        } else {
          // If we can't get credits, create default credits
          setCredits({
            totalCredits: 500,
            usedCredits: 0,
            availableCredits: 500
          });
        }
      } else {
        console.error('Credits promise rejected:', creditsResult.reason);
        setCredits({
          totalCredits: 500,
          usedCredits: 0,
          availableCredits: 500
        });
      }

      // Handle transactions result
      if (transactionsResult.status === 'fulfilled') {
        console.log('Transactions result:', transactionsResult.value);
        if (transactionsResult.value.success && transactionsResult.value.data) {
          setTransactions(transactionsResult.value.data);
        } else {
          // If we can't get transactions, set empty array
          setTransactions([]);
        }
      } else {
        console.error('Transactions promise rejected:', transactionsResult.reason);
        setTransactions([]);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Set default values in case of error
      setProfile({
        id: userId,
        userId: userId,
        createdAt: new Date().toISOString()
      });
      setCredits({
        totalCredits: 500,
        usedCredits: 0,
        availableCredits: 500
      });
      setTransactions([]);
    }
  };

  // Function to refresh user data
  const refreshUserData = async () => {
    if (user) {
      await fetchUserData(user.id);
    }
  };

  // Update last visible time when page becomes visible
  useEffect(() => {
    if (isVisible) {
      lastVisibleTime.current = Date.now();
    }
  }, [isVisible]);

  useEffect(() => {
    // Skip session check if page is not visible and we've already checked once
    if (!isVisible && sessionCheckedOnce.current) {
      return;
    }

    const checkSession = async () => {
      setIsLoading(true);
      try {
        console.log(`Checking session (attempt ${sessionCheckCount + 1})...`);

        // Add a timeout to the session check
        const sessionPromise = getSession();
        const timeoutPromise = new Promise<{user: null, session: null, emailVerificationNeeded?: boolean, unverifiedEmail?: string}>((resolve) => {
          setTimeout(() => {
            console.warn('Session check timed out');
            resolve({ user: null, session: null });
          }, 5000); // 5 second timeout
        });

        const sessionResult = await Promise.race([sessionPromise, timeoutPromise]) as AuthSession;
        const { user, emailVerificationNeeded, unverifiedEmail } = sessionResult;
        console.log('Session result:', user ? `User logged in: ${user.email}` : 'No user');

        if (emailVerificationNeeded) {
          console.warn('Email verification needed for:', unverifiedEmail);
          // Clear user state for unverified emails
          setUser(null);
          setProfile(null);
          setCredits(null);
          setTransactions([]);
          // We'll handle the redirection in the Login component or ProtectedRoute
        } else if (user) {
          console.log('User is logged in, setting user state and fetching data...');
          setUser(user);

          try {
            // Fetch user data with a timeout
            const fetchDataPromise = fetchUserData(user.id);
            const fetchTimeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Fetch user data timeout during session check')), 5000)
            );

            await Promise.race([fetchDataPromise, fetchTimeoutPromise]);
            console.log('User data fetched successfully during session check');
          } catch (fetchError) {
            console.error('Error or timeout fetching user data during session check:', fetchError);
            // Continue with login even if fetching data fails
            // Set default values for profile and credits
            setProfile({
              id: user.id,
              userId: user.id,
              createdAt: new Date().toISOString()
            });
            setCredits({
              totalCredits: 500,
              usedCredits: 0,
              availableCredits: 500
            });
            setTransactions([]);
          }
        } else {
          console.log('No user found, explicitly setting user to null');
          setUser(null);
          setProfile(null);
          setCredits(null);
          setTransactions([]);
        }
      } catch (error) {
        console.error('Error checking session:', error);
        // Ensure user state is cleared on error
        setUser(null);
        setProfile(null);
        setCredits(null);
        setTransactions([]);
      } finally {
        console.log('Setting isLoading to false');
        setIsLoading(false);
        sessionCheckedOnce.current = true;
      }
    };

    // Run the session check
    checkSession();

    // Set up a timer to re-check the session if loading takes too long
    const sessionCheckTimer = setTimeout(() => {
      if (isLoading) {
        console.log('Session check is taking too long, forcing a re-check...');
        setSessionCheckCount(prev => prev + 1);
      }
    }, 3000); // 3 seconds timeout

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session ? 'Session exists' : 'No session');

        // Set loading to true during auth state change
        setIsLoading(true);

        try {
          if (session?.user) {
            // Check if email is verified
            if (!session.user.email_confirmed_at) {
              console.warn('Auth state change: User email is not verified:', session.user.email);

              // Double-check with our verification function to be sure
              const email = session.user.email;
              if (email) {
                try {
                  const { verified } = await checkEmailVerification(email);
                  if (!verified) {
                    // Don't set the user state for unverified emails
                    setUser(null);
                    setProfile(null);
                    setCredits(null);
                    setTransactions([]);
                    setIsLoading(false); // Make sure to set loading to false
                    return;
                  }
                  // If verified is true, continue with setting the user despite email_confirmed_at being null
                  console.log('Email verification check passed despite session showing unverified');
                } catch (verificationError) {
                  console.error('Error checking email verification:', verificationError);
                  // Continue with login despite verification error
                  console.log('Continuing with login despite verification error');
                }
              } else {
                // No email in session, can't verify
                setUser(null);
                setProfile(null);
                setCredits(null);
                setTransactions([]);
                setIsLoading(false); // Make sure to set loading to false
                return;
              }
            }

            // If we get here, the email is verified
            const user: AuthUser = {
              id: session.user.id,
              email: session.user.email || '',
              name: session.user.user_metadata?.name,
              avatarUrl: session.user.user_metadata?.avatar_url,
            };
            console.log('Setting user from auth state change:', user);
            setUser(user);

            // Fetch user data with a timeout to prevent hanging
            console.log('Fetching user data after auth state change...');
            try {
              const fetchDataPromise = fetchUserData(user.id);
              const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Fetch user data timeout during auth state change')), 5000)
              );

              await Promise.race([fetchDataPromise, timeoutPromise]);
              console.log('User data fetched successfully during auth state change');
            } catch (fetchError) {
              console.error('Error or timeout fetching user data during auth state change:', fetchError);
              // Continue with login even if fetching data fails
              // Set default values for profile and credits
              setProfile({
                id: user.id,
                userId: user.id,
                createdAt: new Date().toISOString()
              });
              setCredits({
                totalCredits: 500,
                usedCredits: 0,
                availableCredits: 500
              });
              setTransactions([]);
            }
          } else {
            console.log('No user in session, explicitly clearing user data');
            setUser(null);
            setProfile(null);
            setCredits(null);
            setTransactions([]);
          }
        } catch (error) {
          console.error('Error handling auth state change:', error);
          // Ensure user state is cleared on error
          setUser(null);
          setProfile(null);
          setCredits(null);
          setTransactions([]);
        } finally {
          console.log('Setting isLoading to false after auth state change');
          setIsLoading(false);
        }
      }
    );

    return () => {
      clearTimeout(sessionCheckTimer);
      subscription.unsubscribe();
    };
  }, [sessionCheckCount]); // Re-run when sessionCheckCount changes

  const handleMagicLinkSignIn = async (email: string) => {
    setIsLoading(true);
    try {
      console.log('Attempting to send magic link to:', email);
      const result = await signInWithMagicLink(email);

      if (result.success) {
        notify.success(result.message || 'Magic link sent! Please check your email.', {
          title: 'Email Sent',
          duration: 4000
        });
      } else if (!result.success) {
        const errorMessage = typeof result.error === 'object' && result.error !== null
          ? result.error.message || 'Failed to send magic link'
          : 'Failed to send magic link';
        console.error('Magic link failed:', errorMessage);
        notify.error(errorMessage, {
          title: 'Magic Link Failed',
          duration: 5000
        });
      }

      return result;
    } catch (error) {
      console.error('Unexpected error during magic link login:', error);
      notify.error('An unexpected error occurred. Please try again.', {
        title: 'Error',
        duration: 5000
      });
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      console.log('Attempting login with email:', email);
      const result = await signIn(email, password);

      if (result.success && result.data?.user) {
        // Update user state immediately instead of waiting for auth state change
        let userData: AuthUser;

        // Extract user data safely regardless of the format
        const user = result.data.user;
        const id = user.id;
        const email = user.email || '';

        // Try to get name and avatarUrl from different possible locations
        let name = '';
        let avatarUrl = null;

        if (typeof user === 'object') {
          // Try to get name
          if ('name' in user && typeof user.name === 'string') {
            name = user.name;
          } else if ('user_metadata' in user &&
                    typeof user.user_metadata === 'object' &&
                    user.user_metadata &&
                    'name' in user.user_metadata) {
            name = user.user_metadata.name || '';
          }

          // Try to get avatarUrl
          if ('avatarUrl' in user && user.avatarUrl) {
            avatarUrl = user.avatarUrl;
          } else if ('user_metadata' in user &&
                    typeof user.user_metadata === 'object' &&
                    user.user_metadata &&
                    'avatar_url' in user.user_metadata) {
            avatarUrl = user.user_metadata.avatar_url || null;
          }
        }

        userData = { id, email, name, avatarUrl };

        console.log('Login successful, setting user:', userData);
        setUser(userData);

        try {
          // Fetch user data with a timeout
          const fetchDataPromise = fetchUserData(userData.id);
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Fetch user data timeout')), 5000)
          );

          await Promise.race([fetchDataPromise, timeoutPromise]);
          console.log('User data fetched successfully');
        } catch (fetchError) {
          console.error('Error or timeout fetching user data:', fetchError);
          // Continue with login even if fetching data fails
          // Set default values for profile and credits
          setProfile({
            id: userData.id,
            userId: userData.id,
            createdAt: new Date().toISOString()
          });
          setCredits({
            totalCredits: 500,
            usedCredits: 0,
            availableCredits: 500
          });
          setTransactions([]);
        }

        notify.success('Logged in successfully', {
          title: 'Welcome Back',
          duration: 4000
        });
      } else if (!result.success) {
        const errorMessage = typeof result.error === 'object' && result.error !== null
          ? result.error.message || 'Login failed'
          : 'Login failed';
        console.error('Login failed:', errorMessage);

        // Don't show toast here, let the Login component handle the error display
        // This prevents duplicate error messages
      }

      return result;
    } catch (error) {
      console.error('Unexpected error during login:', error);
      // Don't show toast here, let the Login component handle the error display
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name?: string) => {
    setIsLoading(true);
    try {
      console.log('Attempting registration with email:', email);
      const result = await signUp(email, password, name);

      if (result.success) {
        console.log('Registration successful:', result.data);
        // Don't show toast here, let the Signup component handle success messages
        // This prevents duplicate messages and allows for more context-specific messaging
      } else {
        const errorMessage = typeof result.error === 'object' && result.error !== null
          ? result.error.message || 'Registration failed'
          : 'Registration failed';
        console.error('Registration failed:', errorMessage);
        // Don't show toast here, let the Signup component handle the error display
      }

      return result;
    } catch (error) {
      console.error('Unexpected error during registration:', error);
      // Don't show toast here, let the Signup component handle the error display
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      const result = await signOut();

      if (result.success) {
        // Clear all user data
        setUser(null);
        setProfile(null);
        setCredits(null);
        setTransactions([]);

        notify.success('Logged out successfully', {
          title: 'Goodbye',
          duration: 4000
        });
      } else {
        const errorMessage = typeof result.error === 'object' && result.error !== null
          ? result.error.message || 'Logout failed'
          : 'Logout failed';
        console.error('Logout failed:', errorMessage);
        notify.error(errorMessage, {
          title: 'Logout Failed',
          duration: 5000
        });
      }

      return result;
    } catch (error) {
      console.error('Unexpected error during logout:', error);
      notify.error('An unexpected error occurred', {
        title: 'Error',
        duration: 5000
      });
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  };

  // Update user profile
  const updateProfile = async (profileData: Partial<UserProfile>) => {
    if (!user) {
      notify.error('You must be logged in to update your profile', {
        title: 'Authentication Required',
        duration: 5000
      });
      return { success: false };
    }

    const result = await updateUserProfile(user.id, profileData);

    if (result.success) {
      // Refresh profile data after update
      const profileResult = await getUserProfile(user.id);
      if (profileResult.success && profileResult.data) {
        setProfile(profileResult.data);
      }
      notify.success('Profile updated successfully', {
        title: 'Profile Updated',
        duration: 4000
      });
    }

    return result;
  };

  return (
    <AuthContext.Provider value={{
      user,
      profile,
      credits,
      transactions,
      isLoading,
      login,
  signInWithMagicLink: handleMagicLinkSignIn,
      register,
      logout,
      updateProfile,
      refreshUserData
    }}>
      {children}
    </AuthContext.Provider>
  );
};


