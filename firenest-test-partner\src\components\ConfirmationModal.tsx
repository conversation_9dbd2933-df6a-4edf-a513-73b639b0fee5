import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Shield, Check, AlertCircle, Sparkles } from 'lucide-react';

interface ConfirmationModalProps {
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ onConfirm, onCancel }) => {
  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-dark-800 rounded-xl shadow-2xl border border-dark-600 max-w-md w-full p-6 animate-fadeIn">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Premium Access Activated</h2>
          <p className="text-light-400 mb-4">
            You've successfully authenticated with Firenest. Premium features are now unlocked!
          </p>
        </div>

        <div className="space-y-3 mb-6">
          <div className="flex items-start space-x-3 bg-dark-700 p-3 rounded-lg">
            <div className="mt-0.5">
              <Check className="h-5 w-5 text-green-400" />
            </div>
            <div>
              <h3 className="text-light font-medium">Premium Content Unlocked</h3>
              <p className="text-light-500 text-sm">
                You now have access to all premium features and content.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 bg-dark-700 p-3 rounded-lg">
            <div className="mt-0.5">
              <Sparkles className="h-5 w-5 text-yellow-400" />
            </div>
            <div>
              <h3 className="text-light font-medium">Enhanced AI Capabilities</h3>
              <p className="text-light-500 text-sm">
                Experience our most advanced AI model with improved responses.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 bg-dark-700 p-3 rounded-lg">
            <div className="mt-0.5">
              <AlertCircle className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <h3 className="text-light font-medium">Usage Tracking Enabled</h3>
              <p className="text-light-500 text-sm">
                Your usage will be tracked and credits will be deducted from your Firenest account.
              </p>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onConfirm}
            className="flex-1 py-3 px-4 bg-gradient-to-r from-primary to-purple-600 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-200"
          >
            Continue to Dashboard
          </button>
          <button
            onClick={onCancel}
            className="py-3 px-4 bg-dark-700 text-light-400 font-medium rounded-lg hover:bg-dark-600 transition-all duration-200"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
