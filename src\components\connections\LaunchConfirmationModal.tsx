import React from 'react';
import Modal from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { CheckCircle, ExternalLink, Clock, CreditCard, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface LaunchConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  tool: any;
}

const LaunchConfirmationModal: React.FC<LaunchConfirmationModalProps> = ({
  isOpen,
  onClose,
  tool
}) => {
  const { credits } = useAuth();
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${tool.name} Launched`}
      size="md"
      className="firenest-card"
      contentClassName="bg-dark-900"
    >
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="h-12 w-12 rounded-full bg-green-500/20 flex items-center justify-center">
            <CheckCircle className="h-6 w-6 text-green-500" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-white">Successfully Launched</h3>
            <p className="text-white/70">
              {tool.name} has been launched in a new tab
            </p>
          </div>
        </div>
        
        <div className="bg-dark-800/50 rounded-lg border border-white/5 p-4 space-y-3">
          <h4 className="text-sm font-medium text-white/70">Usage Information</h4>
          
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-white/50" />
            <span className="text-white">
              Usage is being tracked in real-time
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <CreditCard className="h-4 w-4 text-white/50" />
            <span className="text-white">
              Credits will be deducted based on your actual usage
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-white/50" />
            <span className="text-white">
              Current credit balance: {credits?.available || 0} credits
            </span>
          </div>
        </div>
        
        <div className="flex justify-end gap-2 pt-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-white/10 hover:bg-white/5"
          >
            Close
          </Button>
          
          {tool.website && (
            <Button
              className="bg-fiery hover:bg-fiery-600"
              onClick={() => window.open(tool.website, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open Again
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default LaunchConfirmationModal;
