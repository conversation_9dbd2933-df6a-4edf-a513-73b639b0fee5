import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Loading } from '@/components/ui/loading';
import { notify } from '@/components/ui/notification-system';
import { Save, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import { getWebhookConfig, saveWebhookConfig, testWebhook } from '@/lib/partner-portal/api';

interface WebhookConfigurationProps {
  partnerId: string;
  toolId: string;
}

interface WebhookConfig {
  id?: string;
  toolId: string;
  webhookUrl: string;
  secret?: string;
  enabledEvents: {
    sessionStart: boolean;
    sessionEnd: boolean;
    featureUse: boolean;
    creditConsume: boolean;
    creditLow: boolean;
    subscriptionChange: boolean;
  };
  retryEnabled: boolean;
  maxRetries?: number;
  createdAt?: string;
  updatedAt?: string;
}

const WebhookConfiguration: React.FC<WebhookConfigurationProps> = ({ partnerId, toolId }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  
  // Webhook configuration state
  const [webhookUrl, setWebhookUrl] = useState('');
  const [webhookSecret, setWebhookSecret] = useState('');
  const [sessionStartEnabled, setSessionStartEnabled] = useState(true);
  const [sessionEndEnabled, setSessionEndEnabled] = useState(true);
  const [featureUseEnabled, setFeatureUseEnabled] = useState(true);
  const [creditConsumeEnabled, setCreditConsumeEnabled] = useState(true);
  const [creditLowEnabled, setCreditLowEnabled] = useState(true);
  const [subscriptionChangeEnabled, setSubscriptionChangeEnabled] = useState(true);
  const [retryEnabled, setRetryEnabled] = useState(true);
  const [maxRetries, setMaxRetries] = useState(3);
  
  useEffect(() => {
    const loadWebhookConfig = async () => {
      if (!toolId) return;
      
      setIsLoading(true);
      try {
        const config = await getWebhookConfig(toolId);
        
        if (config) {
          setWebhookUrl(config.webhookUrl || '');
          setWebhookSecret(config.secret || '');
          setSessionStartEnabled(config.enabledEvents?.sessionStart ?? true);
          setSessionEndEnabled(config.enabledEvents?.sessionEnd ?? true);
          setFeatureUseEnabled(config.enabledEvents?.featureUse ?? true);
          setCreditConsumeEnabled(config.enabledEvents?.creditConsume ?? true);
          setCreditLowEnabled(config.enabledEvents?.creditLow ?? true);
          setSubscriptionChangeEnabled(config.enabledEvents?.subscriptionChange ?? true);
          setRetryEnabled(config.retryEnabled ?? true);
          setMaxRetries(config.maxRetries || 3);
        }
      } catch (error) {
        console.error('Error loading webhook config:', error);
        notify.error('Failed to load webhook configuration');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadWebhookConfig();
  }, [toolId]);
  
  const handleSaveWebhook = async () => {
    if (!toolId) return;
    
    setIsSaving(true);
    
    try {
      const webhookConfig: WebhookConfig = {
        toolId,
        webhookUrl,
        secret: webhookSecret,
        enabledEvents: {
          sessionStart: sessionStartEnabled,
          sessionEnd: sessionEndEnabled,
          featureUse: featureUseEnabled,
          creditConsume: creditConsumeEnabled,
          creditLow: creditLowEnabled,
          subscriptionChange: subscriptionChangeEnabled
        },
        retryEnabled,
        maxRetries: retryEnabled ? maxRetries : undefined
      };
      
      const savedConfig = await saveWebhookConfig(webhookConfig);
      
      if (!savedConfig) {
        throw new Error('Failed to save webhook configuration');
      }
      
      notify.success('Webhook configuration saved successfully');
    } catch (error: any) {
      console.error('Error saving webhook config:', error);
      notify.error(error.message || 'Failed to save webhook configuration');
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleTestWebhook = async () => {
    if (!webhookUrl) {
      notify.error('Please enter a webhook URL to test');
      return;
    }
    
    setIsTesting(true);
    setTestResult(null);
    
    try {
      const result = await testWebhook(toolId, webhookUrl, webhookSecret);
      
      setTestResult({
        success: result.success,
        message: result.message || (result.success ? 'Webhook test successful!' : 'Webhook test failed')
      });
      
      if (result.success) {
        notify.success('Webhook test successful!');
      } else {
        notify.error('Webhook test failed. Please check the URL and try again.');
      }
    } catch (error: any) {
      console.error('Error testing webhook:', error);
      setTestResult({
        success: false,
        message: error.message || 'An error occurred while testing the webhook'
      });
      notify.error(error.message || 'Failed to test webhook');
    } finally {
      setIsTesting(false);
    }
  };
  
  const generateWebhookSecret = () => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const length = 32;
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    setWebhookSecret(result);
  };
  
  return (
    <Card className="firenest-card">
      <CardHeader>
        <CardTitle className="text-xl text-white">Webhook Configuration</CardTitle>
        <CardDescription>
          Configure webhooks to receive real-time notifications about events in your integration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loading size="lg" />
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <Label htmlFor="webhookUrl">Webhook URL</Label>
              <Input
                id="webhookUrl"
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                placeholder="https://yourtool.com/api/firenest-webhook"
              />
              <p className="text-sm text-white/60">
                The URL where Firenest will send webhook events.
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="webhookSecret">Webhook Secret</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={generateWebhookSecret}
                >
                  Generate Secret
                </Button>
              </div>
              <Input
                id="webhookSecret"
                type="password"
                value={webhookSecret}
                onChange={(e) => setWebhookSecret(e.target.value)}
                placeholder="Your webhook secret"
              />
              <p className="text-sm text-white/60">
                This secret will be used to sign webhook payloads so you can verify they came from Firenest.
              </p>
            </div>
            
            <div className="space-y-3">
              <Label>Enabled Events</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sessionStart"
                    checked={sessionStartEnabled}
                    onCheckedChange={(checked) => setSessionStartEnabled(checked === true)}
                  />
                  <Label htmlFor="sessionStart" className="font-normal cursor-pointer">
                    Session Start
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sessionEnd"
                    checked={sessionEndEnabled}
                    onCheckedChange={(checked) => setSessionEndEnabled(checked === true)}
                  />
                  <Label htmlFor="sessionEnd" className="font-normal cursor-pointer">
                    Session End
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="featureUse"
                    checked={featureUseEnabled}
                    onCheckedChange={(checked) => setFeatureUseEnabled(checked === true)}
                  />
                  <Label htmlFor="featureUse" className="font-normal cursor-pointer">
                    Feature Use
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="creditConsume"
                    checked={creditConsumeEnabled}
                    onCheckedChange={(checked) => setCreditConsumeEnabled(checked === true)}
                  />
                  <Label htmlFor="creditConsume" className="font-normal cursor-pointer">
                    Credit Consumption
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="creditLow"
                    checked={creditLowEnabled}
                    onCheckedChange={(checked) => setCreditLowEnabled(checked === true)}
                  />
                  <Label htmlFor="creditLow" className="font-normal cursor-pointer">
                    Low Credit Balance
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="subscriptionChange"
                    checked={subscriptionChangeEnabled}
                    onCheckedChange={(checked) => setSubscriptionChangeEnabled(checked === true)}
                  />
                  <Label htmlFor="subscriptionChange" className="font-normal cursor-pointer">
                    Subscription Changes
                  </Label>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="retryEnabled"
                  checked={retryEnabled}
                  onCheckedChange={(checked) => setRetryEnabled(checked === true)}
                />
                <Label htmlFor="retryEnabled" className="font-normal cursor-pointer">
                  Enable Retry for Failed Webhooks
                </Label>
              </div>
              
              {retryEnabled && (
                <div className="pl-6 space-y-2">
                  <Label htmlFor="maxRetries">Maximum Retries</Label>
                  <Input
                    id="maxRetries"
                    type="number"
                    min={1}
                    max={10}
                    value={maxRetries}
                    onChange={(e) => setMaxRetries(parseInt(e.target.value) || 3)}
                    className="w-24"
                  />
                </div>
              )}
            </div>
            
            {testResult && (
              <div className={`p-4 rounded-lg ${testResult.success ? 'bg-green-900/30 border border-green-700/50' : 'bg-red-900/30 border border-red-700/50'}`}>
                <div className="flex items-center space-x-2">
                  {testResult.success ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                  <span className={testResult.success ? 'text-green-400' : 'text-red-400'}>
                    {testResult.message}
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          className="text-white border-white/20 hover:bg-white/10 flex items-center space-x-2"
          onClick={handleTestWebhook}
          disabled={isTesting || !webhookUrl}
        >
          {isTesting ? (
            <>
              <Loading size="sm" />
              <span>Testing...</span>
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4" />
              <span>Test Webhook</span>
            </>
          )}
        </Button>
        
        <Button
          className="bg-fiery hover:bg-fiery/90 text-white flex items-center space-x-2"
          onClick={handleSaveWebhook}
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <Loading size="sm" />
              <span>Saving...</span>
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              <span>Save Configuration</span>
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default WebhookConfiguration;
